<template>
  <ModelInfo></ModelInfo>
  <el-tabs @tab-click="handleClick" v-model="activeTab">
    <el-tab-pane :label="this.$ts('Physical Position')" :name="tab1">

      <div class="container">
        <div class="table-item">
          <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">{{ this.$ts(TitleName) }}</span>
            <div style="text-align: end; margin-top: 0px;width:70%;">
              <el-button type="primary" @click="setCurrentAction('add', 'label1')"><i
                  class="el-icon-circle-plus-outline">
                  {{ this.$ts('Add') }}
                </i></el-button>
              <el-button type="primary" icon="Edit" @click="setCurrentAction('edit', 'label1')">{{
                this.$ts('Update')
              }}</el-button>
              <el-button type="success" @click="setCurrentAction('copy', 'label1')"><i class="el-icon-copy-document">
                  {{ this.$ts('Copy') }}
                </i></el-button>

              <el-button type="primary" icon="Delete" @click="setCurrentAction('delete', 'label1')">{{
                this.$ts('Delete')
              }}</el-button>


            </div>
          </div>
        </div>
      </div>

      <VolForm ref="form" :loadKey="true" :formFields="curFormFields" :formRules="curFormRules">
      </VolForm>

      <div class="form-content" v-show="IsEnable">

        <div v-show="false">


          <!-- 新增按钮弹出的内容 -->
          <VolForm ref="form2" :loadKey="true" :formFields="formFields_Position_Add"
            :formRules="formRules_Position_Add">
          </VolForm>
          <VolForm ref="form3" :loadKey="true" :formFields="formFields_Position_Edit"
            :formRules="formRules_Position_Edit">
          </VolForm>
          <VolForm ref="form4" :loadKey="true" :formFields="formFields_Position_Delete"
            :formRules="formRules_Position_Delete">
          </VolForm>
          <VolForm ref="form5" :loadKey="true" :formFields="formFields_Position_Copy"
            :formRules="formRules_Position_Copy">
          </VolForm>
        </div>

        <div style="text-align: center; width: 100%;margin-top:20px">

          <el-button type="primary" @click="formSubmit('tab1')"><i class="el-icon-check">
              {{ this.$ts('Submit') }}
            </i>
          </el-button>

          <!-- <el-cascader :props="props"></el-cascader> -->
        </div>
      </div>

    </el-tab-pane>

    <el-tab-pane :label="this.$ts('Physical Location')" :name="tab2">
      <div class="container">
        <div class="table-item">
          <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">{{
              this.$ts(TitleName_Location)
            }}</span>
            <div style="text-align: end; margin-top: 0px;width:70%;">
              <el-button type="primary" @click="setCurrentAction('add', 'label2')"><i
                  class="el-icon-circle-plus-outline">
                  {{ this.$ts('Add') }}
                </i></el-button>
              <el-button type="primary" icon="Edit" @click="setCurrentAction('edit', 'label2')">{{
                this.$ts('Update')
              }}</el-button>
              <el-button type="success" @click="setCurrentAction('copy', 'label2')"><i class="el-icon-copy-document">
                  {{ this.$ts('Copy') }}
                </i></el-button>

              <el-button type="primary" icon="Delete" @click="setCurrentAction('delete', 'label2')">{{
                this.$ts('Delete')
              }}</el-button>


            </div>
          </div>
        </div>
      </div>

      <VolForm ref="form1" :loadKey="true" :formFields="curFormFields_Location" :formRules="curFormRules_Location">
      </VolForm>

      <div class="form-content" v-show="IsEnable_Location">

        <div v-show="false">


          <!-- 新增按钮弹出的内容 -->
          <VolForm ref="form6" :loadKey="true" :formFields="formFields_Location_Add"
            :formRules="formRules_Location_Add">
          </VolForm>
          <VolForm ref="form7" :loadKey="true" :formFields="formFields_Location_Edit"
            :formRules="formRules_Location_Edit">
          </VolForm>
          <VolForm ref="form8" :loadKey="true" :formFields="formFields_Location_Delete"
            :formRules="formRules_Location_Delete">
          </VolForm>
          <VolForm ref="form9" :loadKey="true" :formFields="formFields_Location_Copy"
            :formRules="formRules_Location_Copy">
          </VolForm>
        </div>


      </div>
      <div class="form-content" v-show="true">
        <!-- <VolForm ref="form" :loadKey="true" :formFields="curFormFields_Location" :formRules="curFormRules_Location"> -->
        <div>
          <div style="margin-left: 12px;margin-top: 10px;margin-bottom: 10px;">{{
            this.$ts('Referencing Physical location info')
          }}</div>
          <div style="margin-left: 12px;margin-bottom: 10px;">
            <el-button type="primary" plain color="red" @click="delRow('table1')" class="el-icon-delete"
              round></el-button>
            <el-button type="primary" plain @click="addRow('table1')" class="el-icon-plus" round></el-button>

          </div>
          <div style="margin-left: 12px;width: 900px;">

            <vol-table ref="table1" index :tableData="tableData" :columns="PhysicalPositionList" :max-height="260"
              :clickEdit="true" :pagination-hide="true" :load-key="true" :column-index="true"></vol-table>
          </div>
        </div>
        <div style="text-align: center; width: 100%;margin-top:20px">

          <el-button type="primary" @click="formSubmit('tab2')"><i class="el-icon-check">
              {{ this.$ts('Submit') }}
            </i>
          </el-button>

          <!-- <el-cascader :props="props"></el-cascader> -->
        </div>


      </div>

    </el-tab-pane>



  </el-tabs>

</template>



<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'//先引入

//import { de } from 'element-plus/es/locale';
//import Opc_EquipBasicInfoEdit from './Opc_EquipBasicInfoEdit.vue'
export default {
  components: {
    ModelInfo,
    VolHeader,
    VolForm,
    'vol-table': VolTable,
    //  Opc_EquipBasicInfoEdit,
  },
  mixins: [GlobalElMessageBox],
  data() {
    return {
      IsEnable: false,
      IsEnable_Location: false,
      activeName: 'tab2',
      TitleName: '',
      TitleName_Location: '',
      curFormFields: {
      },
      curFormRules: [


      ],
      activeTab:"tab1",

      curFormFields_Location: {
      },
      curFormRules_Location: [],

      currentAction: '', // 当前操作
      // title: this.$ts('治工具台账维护'),
      // tableTitleOne: this.$ts('邮件通知地址'),
      // tableTitleTwo: this.$ts('适用料号'),
      //储位tab页表单字段
      formFields_Position_Add: {

        PhysicalPositionName: null,//位置/Physical Position
        Description: null,//描述
      },
      formFields_Position_Delete: {

        PhysicalPositionName: null,//位置/Physical Position
        Description: null,//描述
      },
      formFields_Position_Edit: {

        PhysicalPositionName: null,//位置/Physical Position
        NewPhysicalPositionName: null,//新位置/Physical Position
        Description: null,//描述
      },
      formFields_Position_Copy: {

        PhysicalPositionName: null,//位置/Physical Position
        NewPhysicalPositionName: null,//新位置/Physical Position
        Description: null,//描述
      },

      //储位表单验证规则
      formRules_Position_Copy:
        [
          [
            {

              url: '/api/Query/SearchPosition',
              title: this.$ts('Physical Position'),
              placeholder: this.$ts('Physical Position'),
              required: true, //设置为必选项
              type: "selectTable",
              field: "PhysicalPositionName",
              colSize: 5,
              columns: [
                { field: 'PositionName', title: this.$ts('Physical Position'), type: 'string', sort: true }
              ],
              /* onChange: (value) => {
                this.CopyCurrent_position(value);

              }, */
              onSelect: (rows) => {
                console.log(rows);
                this.formFields_Position_Copy.PhysicalPositionName = rows[0].PositionName;
                this.CopyCurrent_position(rows[0].PositionName);
              },
              loadBefore: (params, callback) => {//搜索时设置查询条件

                params.PositionName = this.formFields_Position_Copy.PhysicalPositionName;
                callback(true);
              },
              paginationHide: false,//显示分页
              height: 237,//表格高度
              single: true//单选
            },
          ],
          [

            {
              title: this.$ts('Physical Position'),
              placeholder: this.$ts('Physical Position'),
              filter: true,

              required: true, //设置为必选项
              field: "NewPhysicalPositionName",

              colSize: 5
            },
            {
              title: this.$ts('Description'),
              placeholder: this.$ts('Description'),
              filter: true,
              required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],
      formRules_Position_Edit:
        [
          [
            {
              url: '/api/Query/SearchPosition',
              title: this.$ts('Physical Position'),
              placeholder: this.$ts('Physical Position'),
              required: true, //设置为必选项
              type: "selectTable",
              field: "PhysicalPositionName",
              colSize: 5,
              columns: [
                { field: 'PositionName', title: this.$ts('Physical Position'), type: 'string', sort: true }
              ],
              onSelect: (rows) => {
                console.log(rows);
                this.formFields_Position_Edit.PhysicalPositionName = rows[0].PositionName;
                this.CopyCurrent_position(rows[0].PositionName);
              },
              loadBefore: (params, callback) => {//搜索时设置查询条件

                params.PositionName = this.formFields_Position_Edit.PhysicalPositionName;
                callback(true);
              },
              paginationHide: false,//显示分页
              height: 237,//表格高度
              single: true//单选

            },

          ],
          [

            {
              title: this.$ts('Edit New Physical Position'),
              placeholder: this.$ts('Edit New Physical Position'),
              filter: true,
              required: true, //设置为必选项
              field: "NewPhysicalPositionName",
              colSize: 5
            },
            {
              title: this.$ts('Description'),
              placeholder: this.$ts('Description'),
              filter: true,
              required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],
      formRules_Position_Delete:
        [

          [

            {
              url: '/api/Query/SearchPosition',
              title: this.$ts('Physical Position'),
              placeholder: this.$ts('Physical Position'),
              required: true, //设置为必选项
              type: "selectTable",
              field: "PhysicalPositionName",
              colSize: 5,
              columns: [
                { field: 'PositionName', title: this.$ts('Physical Position'), type: 'string', sort: true }
              ],
              /* onChange: (value) => {
                this.CopyCurrent_position(value);

              }, */
              onSelect: (rows) => {
                console.log(rows);
                this.formFields_Position_Delete.PhysicalPositionName = rows[0].PositionName;
                this.CopyCurrent_position(rows[0].PositionName);
              },
              loadBefore: (params, callback) => {//搜索时设置查询条件

                params.PositionName = this.formFields_Position_Delete.PhysicalPositionName;
                callback(true);
              },
              paginationHide: false,//显示分页
              height: 237,//表格高度
              single: true//单选
            },
            {
              title: this.$ts('Description'),
              readonly: true,
              placeholder: this.$ts('Description'),
              filter: true,
              //required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],
      formRules_Position_Add:
        [

          [
            {
              title: this.$ts('Physical Position'),
              placeholder: this.$ts('Physical Position'),
              filter: true,
              required: true, //设置为必选项
              field: "PhysicalPositionName",
              colSize: 5
            },
            {
              title: this.$ts('Description'),
              placeholder: this.$ts('Description'),
              filter: true,
              required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],


      //Physical Location
      formFields_Location_Add: {

        PhysicalLocationName: null,//位置/Physical Position
        Description: null,//描述
      },
      formFields_Location_Delete: {

        PhysicalLocationName: null,//位置/Physical Position
        Description: null,//描述
      },
      formFields_Location_Edit: {

        PhysicalLocationName: null,//位置/Physical Position
        NewPhysicalLocationName: null,//新位置/Physical Position
        Description: null,//描述
      },
      formFields_Location_Copy: {

        PhysicalLocationName: null,//位置/Physical Position
        NewPhysicalLocationName: null,//新位置/Physical Position
        Description: null,//描述
      },

      //区域库房表单验证规则
      formRules_Location_Copy:
        [
          [
            {
              url: 'api/DropdownList/SearchLocation',
              title: this.$ts('Physical Location'),
              placeholder: this.$ts('Physical Location'),
              required: true, //设置为必选项
              type: "selectTable",
              field: "PhysicalLocationName",
              colSize: 5,
              columns: [
                { field: 'LocationName', title: this.$ts('Physical Location'), type: 'string', sort: true }
              ],
              onSelect: (rows) => {
                console.log(rows);
                this.formFields_Location_Copy.PhysicalLocationName = rows[0].LocationName;
                this.CopyCurrent_location(rows[0].LocationName);
              },
              loadBefore: (params, callback) => {//搜索时设置查询条件
                params.LocationName = this.formFields_Location_Copy.PhysicalLocationName;
              },
              paginationHide: false,//显示分页
              height: 237,//表格高度
              single: true//单选
            },
          ],
          [

            {
              title: this.$ts('Edit New Physical Location'),
              placeholder: this.$ts('Edit New Physical Location'),
              filter: true,
              required: true, //设置为必选项
              field: "NewPhysicalLocationName",
              type: "input",
              colSize: 5
            },
            {
              title: this.$ts('Description'),
              placeholder: this.$ts('Description'),
              filter: true,
              required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],
      formRules_Location_Edit:
        [
          [
            {
              url: 'api/DropdownList/SearchLocation',
              title: this.$ts('Physical Location'),
              placeholder: this.$ts('Physical Location'),
              required: true, //设置为必选项
              type: "selectTable",
              field: "PhysicalLocationName",
              colSize: 5,
              columns: [
                { field: 'LocationName', title: this.$ts('Physical Location'), type: 'string', sort: true }
              ],
              onSelect: (rows) => {
                console.log(rows);
                this.formFields_Location_Edit.PhysicalLocationName = rows[0].LocationName;
                this.CopyCurrent_location(rows[0].LocationName);
              },
              loadBefore: (params, callback) => {//搜索时设置查询条件
                params.LocationName = this.formFields_Location_Edit.PhysicalLocationName;
              },
              paginationHide: false,//显示分页
              height: 237,//表格高度
              single: true//单选
            }
            ,],
          [

            {
              title: this.$ts('Edit New Physical Location'),
              placeholder: this.$ts('Edit New Physical Location'),
              filter: true,
              required: true, //设置为必选项
              field: "NewPhysicalLocationName",
              colSize: 5
            },
            {
              title: this.$ts('Description'),
              placeholder: this.$ts('Description'),
              filter: true,
              //required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],
      formRules_Location_Delete:
        [

          [

            {
              url: 'api/DropdownList/SearchLocation',
              title: this.$ts('Physical Location'),
              placeholder: this.$ts('Physical Location'),
              required: true, //设置为必选项
              type: "selectTable",
              field: "PhysicalLocationName",
              colSize: 5,
              columns: [
                { field: 'LocationName', title: this.$ts('Physical Location'), type: 'string', sort: true }
              ],
              onSelect: (rows) => {
                // console.log(rows);
                this.formFields_Location_Delete.PhysicalLocationName = rows[0].LocationName;
                // this.CopyCurrent_location(rows[0].LocationName);
              },
              loadBefore: (params, callback) => {//搜索时设置查询条件
                params.LocationName = this.formFields_Location_Delete.PhysicalLocationName;
              },
              paginationHide: false,//显示分页
              height: 237,//表格高度
              single: true//单选
            },
            {
              title: this.$ts('Description'),
              placeholder: this.$ts('Description'),
              filter: true,
              readonly: true,
              //required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],
      formRules_Location_Add:
        [

          [
            {
              title: this.$ts('Physical Location'),
              placeholder: this.$ts('Physical Location'),
              filter: true,
              required: true, //设置为必选项
              field: "PhysicalLocationName",
              colSize: 5
            },
            {
              title: this.$ts('Description'),
              placeholder: this.$ts('Description'),
              filter: true,
              required: true, //设置为必选项
              field: "Description",
              colSize: 5
            }

          ]
        ],


      PhysicalPositionList: [
        {
          url: 'api/DropdownList/SearchPosition',
          field: 'PhysicalPositionName', title: this.$ts('Physical Position'), readonly: false, width: 90, edit: { type: "selectTable", keep: true },
          columns: [
            { field: 'PositionName', title: this.$ts('Physical Position'), type: 'string', sort: true }
          ],
          onSelect: (editRows, rows) => {
            // console.log(rows);
            editRows.PhysicalPositionName = rows[0].PositionName;
            this.setPositionDescription(editRows,editRows.PhysicalPositionName);

            // this.CopyCurrent_position(rows[0].PositionName);
          },
          loadBefore: (editRow, params, callback) => {//搜索时设置查询条件

            params.PositionName = editRow.PhysicalPositionName;
            callback(true);
          },

          paginationHide: false,

          sigle: true,
          /* onChange: (editRow, rows) => {
            // console.log(editRow, 'onSelectrows');

            let postData_Condition = {
              PhysicalPositionName: editRow.PhysicalPositionName, // 表单
            };
            let cacheTable = [];
            this.http.post('api/Biz_PhysicalPosition/Biz_PhysicalPositionSearch', postData_Condition).then(res => {
              if (res.rows && res.rows.length > 0) {
                editRow.Description = res.rows[0].Description;

              }
            });
            const originalLength = this.tableData.length;
            let uniqueRows = new Map();
            this.tableData.forEach(row => uniqueRows.set(row.PhysicalPositionName, row));

            // 从Map对象中提取数据，生成没有重复行的新数组
            let filteredTableData = Array.from(uniqueRows.values());
            this.tableData = filteredTableData;
            console.log(filteredTableData);
            if (originalLength > filteredTableData.length) {
              // 如果长度有变化，说明有重复行被去除，弹出消息框提示用户
              this.$message({
                message: this.$ts('Duplicate rows have been removed'),
                type: 'error'
              });
            }


          }
 */

        },
        { field: 'Description', title: this.$ts('Description'), type: 'string', sort: true, width: 180, align: 'center' },

      ],
      tableData: [],

    }
  },
  watch: {
    'tableData': function (newValue, oldValue) {
      console.log(oldValue, 'PositionName_newValue');
    }

  },
  mounted() {


    // console.log(this.$ts('治工具台账维护'), 'title');
    //this.loadDefaultData_position();
  },
  created() {
    this.setCurrentAction('add', 'label1');
  },
  methods: {
    setPositionDescription(editRow,value) {
      let postData_Condition = {
        PhysicalPositionName: value, // 表单
      };
      let cacheTable = [];
      this.http.post('api/Query/Biz_PhysicalPositionSearch', postData_Condition).then(res => {
        if (res.rows && res.rows.length > 0) {
          editRow.Description = res.rows[0].Description;

        }
      });
      const originalLength = this.tableData.length;
      let uniqueRows = new Map();
      this.tableData.forEach(row => uniqueRows.set(row.PhysicalPositionName, row));

      // 从Map对象中提取数据，生成没有重复行的新数组
      let filteredTableData = Array.from(uniqueRows.values());
      this.tableData = filteredTableData;
      console.log(filteredTableData);
      if (originalLength > filteredTableData.length) {
        // 如果长度有变化，说明有重复行被去除，弹出消息框提示用户
        this.$message({
          message: this.$ts('Duplicate rows have been removed'),
          type: 'error'
        });
      }
    },
    handleClick(tab) {
      console.log(tab.props.label, 'tab');
      if (tab.props.label === '区域库房') {
        this.setCurrentAction('add', 'label2');
      }
      switch (this.activeTab) {
                    case 'tab1': this.GetContaienrHistroy();
                    break;
                    case 'tab2': this.GetCurrentSetupMater();
                    break;
                    // default:this.$Message.error(this.activeTab+' is not found'); break;
                }
    },

    checkAndSubmit(tableData, formFields) {
      if (this.activeName !== 'tab2') {
        return;
      }
      // 遍历tableData检查空行或必填列是否为空
      for (let row of tableData) {
        // 动态地检查必填列是否为空
        if (!row[formFields] || row[formFields].trim() === '') {
          // 如果必填列为空，弹出提示消息并返回，不执行提交
          this.$message({
            message: this.$ts('There is an empty row or the required column is empty, and it cannot be submitted.'),
            type: 'error'
          });
          return false; // 返回false，指示调用者不应继续执行
        }
      }
      return true; // 所有行都检查通过，返回true
    },

    CopyCurrent_location(item) {
      //console.log(item, 'item');
      //console.log(this.curFormFields, 'curFormFields');
      let postData_Condition = {
        PhysicalLocationName: item, // 表单
      };
      this.http.post('api/Query/Biz_PhysicalLocationSearch', postData_Condition).then(res => {
        console.log(res.rows, 'res');
        this.curFormFields_Location.NewPhysicalLocationName = 'Copy of ' + res.rows[0].PhysicalLocationName;
        this.curFormFields_Location.Description = res.rows[0].Description;
        this.tableData = res.rows[0].PhysicalPositionList;
        //console.log(this.curFormFields, 'curFormFields');
        /* Object.keys(res.rows[0]).forEach(key => {
          const formattedKey = key.charAt(0).toLowerCase() + key.slice(1);

          if (this.curFormFields_Location.hasOwnProperty(formattedKey)) {
            console.log(key, 'formattedKey');

            res.rows[0]['NewPhysicalLocationName'] = 'Copy of ' + this.curFormFields_Location['PhysicalLocationName'];
            this.curFormFields_Location[formattedKey] = res.rows[0][key];

            let tableData1 = [];

            res.rows.forEach(row => {
              // 检查每个 row 是否有 PhysicalPositionList
              if (row.PhysicalPositionList && row.PhysicalPositionList.length > 0) {
                // 遍历 PhysicalPositionList 并提取需要的信息
                row.PhysicalPositionList.forEach(position => {
                  //console.log(position.PhysicalPositionName, 'position');
                  tableData1.push({
                    PhysicalPositionName: position.PhysicalPositionName,
                    Description: position.Description
                  });
                });
              }
            });
            this.tableData = tableData1;
          }
        }); */

      }).catch(error => {

        this.$message.error(error);

      });
      //let data = {newResourceFamilyName:this.curFormFields,Description:''};
      // this.curFormFields = data;
      //this.curFormFields.Description = item.Description;
    },
    CopyCurrent_position(item) {

      let postData_Condition = {
        PhysicalPositionName: item, // 表单
      };
      this.http.post('api/Query/Biz_PhysicalPositionSearch', postData_Condition).then(res => {
        console.log(res.rows, 'res');
        console.log(this.curFormFields, 'curFormFields');
        this.curFormFields.NewPhysicalPositionName = 'Copy of ' + res.rows[0].PhysicalPositionName;
        this.curFormFields.Description = res.rows[0].Description;
        if (this.currentAction === 'edit') {
          this.curFormFields.NewPhysicalPositionName = res.rows[0].PhysicalPositionName;
        }


      }).catch(error => {

        this.$message.error(error);

      });
    },
    //label1:储位,else : 区域库房
    setCurrentAction(action, label) {
      // 定义映射操作和标签到标题、启用状态、表单字段和规则
      const actionConfig = {
        add: { title: 'Add', enable: true },
        edit: { title: 'Update', enable: true },
        copy: { title: 'Copy', enable: true },
        delete: { title: 'Delete', enable: true }
      };

      const labelConfig = {
        label1: {
          titleKey: 'TitleName',
          enableKey: 'IsEnable',
          formFieldsKey: 'curFormFields',
          formRulesKey: 'curFormRules',
          formFieldsPrefix: 'formFields_Position_',
          formRulesPrefix: 'formRules_Position_'
        },
        label2: {
          titleKey: 'TitleName_Location',
          enableKey: 'IsEnable_Location',
          formFieldsKey: 'curFormFields_Location',
          formRulesKey: 'curFormRules_Location',
          formFieldsPrefix: 'formFields_Location_',
          formRulesPrefix: 'formRules_Location_',
          resetTable: true
        }
      };

      // 设置当前操作
      this.currentAction = action;

      // 获取操作和标签配置
      const actionCfg = actionConfig[action];
      const labelCfg = labelConfig[label];

      // 设置标题和启用状态
      this[labelCfg.titleKey] = this.$ts(`${actionCfg.title} ${label === 'label1' ? 'Physical Position' : 'Physical Location'}`);
      this[labelCfg.enableKey] = actionCfg.enable;

      // 设置表单字段和规则
      this[labelCfg.formFieldsKey] = this[labelCfg.formFieldsPrefix + action.charAt(0).toUpperCase() + action.slice(1)];
      this[labelCfg.formRulesKey] = this[labelCfg.formRulesPrefix + action.charAt(0).toUpperCase() + action.slice(1)];

      // 如果需要，重置表格
      if (labelCfg.resetTable) {
        this.$refs.table1.reset();
      }
    },

    async formSubmit(tab) {
      const validateAndProcess = async (postData, apiUrl, proxyArray) => {
        // const valid = await this.$refs.form1.validate();
        // const valid2 = await this.$refs.form.validate();
        let valid = true, valid2 = true;
        if (tab === 'tab1') {
          valid2 = await this.$refs.form.validate();
        } else if (tab === 'tab2') {
          //valid2 = true;
          valid = await this.$refs.form1.validate();
        }
        if (!valid || !valid2) return;
        this.checkAndSubmit(this.tableData, "PhysicalPositionName");
        try {
          const res = await this.http.post(apiUrl, postData);
          console.log(res);
          this.resultMessageStay(res.status, res.message, "Success", "There are already duplicate items, please modify them");
          if (tab === 'tab1') {
            // proxyArray.forEach(array => this.resetItemSource(array, 'Biz_Physical_Position'));
          } else if (tab === 'tab2') {

            // const tab2ProxyArrays = getProxyArrays('Location'); // 假设这是获取tab2数据源数组的方法
            // tab2ProxyArrays.forEach(array => this.resetItemSource(array, 'Biz_PhysicalLocation'));
          }
        } catch (error) {
          this.$message.error(error);
        } finally {
          this.reset();
          if (tab === 'tab2') this.$refs.table1.reset();
        }
      };

      const getProxyArrays = (actionType) => {
        return [
          this[`formRules_${actionType}_Delete`][0][0].data,
          this[`formRules_${actionType}_Edit`][0][0].data,
          this[`formRules_${actionType}_Copy`][0][0].data
        ];
      };

      const actionUrlMap = {
        tab1: {
          add: 'api/CDO/Biz_PhysicalPositionAdd',
          edit: 'api/CDO/Biz_PhysicalPositionEdit',
          copy: 'api/CDO/Biz_PhysicalPositionAdd',
          delete: 'api/CDO/Biz_PhysicalPositionDelete',
        },
        tab2: {
          add: 'api/CDO/Biz_PhysicalLocationAdd',
          edit: 'api/CDO/Biz_PhysicalLocationEdit',
          copy: 'api/CDO/Biz_PhysicalLocationAdd',
          delete: 'api/CDO/Biz_PhysicalLocationDelete',
        }
      };

      let postData = { ...this.formFields_Add, ...this.curFormFields };
      if (tab === 'tab1') {
        postData = this.currentAction === 'edit' ? { ...this.curFormFields } : postData;
        if (this.currentAction === 'copy') {
          postData = {
            ...this.curFormFields,
            PhysicalPositionName: this.curFormFields.NewPhysicalPositionName
          };
        }
      } else if (tab === 'tab2') {
        if (this.currentAction === 'copy') {
          postData = {
            ...this.curFormFields_Location,
            PhysicalLocationName: this.curFormFields_Location.NewPhysicalLocationName,
            PhysicalPositionList: this.tableData
          };
        } else {
          postData = {
            ...this.curFormFields_Location,
            NewPhysicalLocationName: this.curFormFields_Location.NewPhysicalLocationName,
            PhysicalPositionList: this.tableData
          };
        }
      }
      console.log(postData, 'postData_TAB2');
      console.log(this.curFormFields_Location.NewPhysicalLocationName, 'this.curFormFields_Location.NewPhysicalLocationName');
      const proxyArrays = getProxyArrays(tab === 'tab1' ? 'Position' : 'Location');
      const apiUrl = actionUrlMap[tab][this.currentAction];
      console.log(apiUrl, 'apiUrl');
      await validateAndProcess(postData, apiUrl, proxyArrays);

    },

    async resetItemSource(proxyArray, dicNo) {
      try {
        let newData = await this.SysApi_GetDicData(dicNo);
        if (!Array.isArray(newData)) {
          throw new Error("返回的数据不是数组类型");
        }
        while (proxyArray.length > 0) {
          proxyArray.pop();
        }
        newData.forEach(item => {
          proxyArray.push(item);
        });
      } catch (error) {
        console.error("重置项目源时出错:", error);
      }
    },


    reset() {
      this.$refs.form1.reset();
      this.$refs.form.reset();
      //this.$Message.success("表单已重置");
    },
    addRow(tableRef) {
      // console.log(this.TitleName_Location, 'TitleName_Location11');
      if (this.TitleName_Location === null || this.TitleName_Location === '') {
        this.$message.error(this.$ts('Please select the operation type first.'));
        return;
      }
      if (tableRef === 'table1' && this.currentAction === 'delete') {
        return;
      }
      const newRow = { /* 新行的数据 */ };
      if (tableRef === 'table1') {

        this.$refs.table1.addRow(newRow)
      }
    },
    delRow(tableRef) {
      if (tableRef === 'table1' && this.currentAction === 'delete') {
        return;
      }
      if (tableRef === 'table1') {
        this.$refs.table1.delRow();
      }

    },
    rowClick(event, tableRef) {
      // 从event参数中解构出row, column
      const { row, column } = event;
      // 现在可以根据tableRef执行特定操作
      if (tableRef === 'table1') {
        console.log(event, 'row');
        this.$refs.table1.$refs.table.toggleRowSelection(row);
      }
    },

    loadingFormData(value) {

      //console.log(value, 'value');
      let postData_Condition = {
        //familyType: this.formFields_Add.familyType, // 表单
        //Description: this.curFormFields.Description, // 表单
        PhysicalLocationName: value, // 表单
      };

      console.log(postData_Condition, 'postData_Condition');
      this.http.post('api/Biz_PhysicalLocation/Biz_PhysicalLocationSearch', postData_Condition).then(res => {
        if (res.rows && res.rows.length > 0) {
          //console.log(this.curFormFields, 'rows');
          console.log(res.rows, 'rows');
          this.curFormFields_Location.NewPhysicalLocationName = res.rows[0].PhysicalLocationName;
          this.curFormFields_Location.Description = res.rows[0].Description;
          this.tableData = res.rows[0].PhysicalPositionList;
        } else {
          // 处理数据不符合预期的情况
          this.$message.error(res.error);
        }
      }).catch(error => {
        this.$message.error(error);
      });
    },

    
  }
};
</script>
<style lang="less" scoped>
.container {
  padding: 10px;
  background: #F3F7FC;

  .form-content {
    border-radius: 5px;
    padding: 10px 0;
    background: #fff;
  }

  .table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
      height: 15px;
      background: rgb(33, 150, 243);
      width: 5px;
      border-radius: 10px;
      position: relative;
      margin-right: 5px;
    }

    .table-item-text {
      font-weight: bolder;
    }

    .table-item-buttons {
      flex: 1;
      text-align: right;
    }

    .small-text {
      font-size: 12px;
      color: #2196F3;
      margin-left: 10px;
      position: relative;
      top: 2px;
    }
  }
}
</style>