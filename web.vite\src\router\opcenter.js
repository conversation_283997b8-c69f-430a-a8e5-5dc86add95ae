import path from 'path'

// 建模
let Modeling = [
{
	path: '/Opc_QualityInspectionSheet',
	name: 'Opc_QualityInspectionSheet',
	component: () => import('@/views/opcenter/modeling/Opc_QualityInspectionSheet.vue'),
	// meta: { name: '品质检验表', keepAlive: false, }
},
{
	path: '/Opc_StandardTime',
	name: 'Opc_StandardTime',
	component: () => import('@/views/opcenter/modeling/Opc_StandardTime.vue'),
	meta: { name: '标准工时', keepAlive: false, }
},
{
	path: '/Opc_Resource',
	name: 'Opc_Resource',
	component: () => import('@/views/opcenter/modeling/Opc_Resource.vue'),
	meta:{name:'设备基础信息',keepAlive:false,noCache:false}
},
{
	//塑模
	path: '/Opc_PlasticTool',
	name: 'Opc_PlasticTool',
	component: () => import('@/views/opcenter/modeling/Opc_PlasticTool.vue'),
	meta:{name:'塑膜工具',keepAlive:false,noCache:false}
},
{
	//冲模
	path: '/Opc_DieTool',
	name: 'Opc_DieTool',
	component: () => import('@/views/opcenter/modeling/Opc_DieTool.vue'),
	meta:{name:'冲模工具',keepAlive:false,noCache:false}
	
},

]

// 生产
let Production = [
	{
		path: '/Opc_WarehouseReceipt',
		name: 'Opc_WarehouseReceipt',
		component: () => import('@/views/opcenter/transactions/Opc_WarehouseReceipt.vue'),
		meta: { name: '入库单打印', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_ProductionRecordView',
		name: 'Opc_ProductionRecordView',
		component: () => import('@/views/opcenter/query/Opc_ProductionRecordView.vue'),
		meta: { name: '生产记录查看', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_WorkReport',
		name: 'Opc_WorkReport',
		component: () => import('@/views/opcenter/transactions/Opc_WorkReport.vue'),
		meta: { name: '生产报工', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_PackageLabelPrint',
		name: 'Opc_PackageLabelPrint',
		component: () => import('@/views/opcenter/transactions/Opc_PackageLabelPrint.vue'),
		meta: { name: '包装标签打印', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_LineChangeAndSetup',
		name: 'Opc_LineChangeAndSetup',
		component: () => import('@/views/opcenter/transactions/Opc_LineChangeAndSetup.vue'),
		meta: { name: '换线调机记录', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_Start',
		name: 'Opc_Start',
		component: () => import('@/views/opcenter/transactions/Opc_Start.vue'),
		meta: { name: '工单创批', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_Move',
		name: 'Opc_Move',
		component: () => import('@/views/opcenter/transactions/Opc_Move.vue'),
		meta: { name: '批次进出站', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_ESOP',
		name: 'Opc_ESOP',
		component: () => import('@/views/opcenter/transactions/Opc_ESOP.vue'),
		meta: { name: '产品ESOP查看', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_Hold',
		name: 'Opc_Hold',
		component: () => import('@/views/opcenter/transactions/Opc_Hold.vue'),
		meta:{ name: '批次Hold', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_Release',
		name: 'Opc_Release',
		component: () => import('@/views/opcenter/transactions/Opc_Release.vue'),
		meta:{name: '批次Release', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_LotSplit',
		name: 'Opc_LotSplit',
		component: () => import('@/views/opcenter/transactions/Opc_LotSplit.vue'),
		meta: { name: '批次拆分', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_LotCombine',
		name: 'Opc_LotCombine',
		component: () => import('@/views/opcenter/transactions/Opc_LotCombine.vue'),
		meta: { name: '批次合并', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_LotScrap',
		name: 'Opc_LotScrap',
		component: () => import('@/views/opcenter/transactions/Opc_LotScrap.vue'),
		meta: { name: '报废', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_PackingManagement',
		name: 'Opc_PackingManagement',
		component: () => import('@/views/opcenter/transactions/Opc_PackingManagement.vue'),
		meta: { name: '包装管理', keepAlive: false, }
	},
	//批次异常登记
	{
		path: '/Opc_LotException',
		name: 'Opc_LotException',
		component: () => import('@/views/opcenter/transactions/Opc_LotException.vue'),
		meta: { name: '批次异常登记', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_ComponentSetup',
		name: 'Opc_ComponentSetup',
		component: () => import('@/views/opcenter/transactions/Opc_ComponentSetup.vue'),
		meta: { name: '上料', keepAlive: false, }
	},
	{
		path: '/Opc_ProcessQuery',
		name: 'Opc_ProcessQuery',
		component: () => import('@/views/opcenter/query/Opc_ProcessQuery.vue'),
		meta: { name: '生产记录查询', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_InventoryInspection',
		name: 'Opc_InventoryInspection',
		component: () => import('@/views/opcenter/transactions/Opc_InventoryInspection.vue'),
		meta: { name: '入库报检', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_MaterialIssueQuery',
		name: 'Opc_MaterialIssueQuery',
		component: () => import('@/views/opcenter/query/Opc_MaterialIssueQuery.vue'),
		meta: { name: '物料消耗查询', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_LabelPrint',
		name: 'Opc_LabelPrint',
		component: () => import('@/views/opcenter/transactions/Opc_LabelPrint.vue'),
		meta: { name: '标签打印', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_LabelReprint',
		name: 'Opc_LabelReprint',
		component: () => import('@/views/opcenter/transactions/Opc_LabelReprint.vue'),
		meta: { name: '标签打印', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_Dispatch',
		name: 'Opc_Dispatch',
		component: () => import('@/views/opcenter/transactions/Opc_Dispatch.vue'),
		meta: { name: '派工', keepAlive: false, noCache: false }
	},
	{
		path:'/Opc_ProductMaintenance',
		name: 'Opc_ProductMaintenance',
		component: () => import('@/views/opcenter/transactions/Opc_ProductMaintenance.vue'),
		meta: { name: '生产维修', keepAlive: false, noCache: false }
	}
]
// 工单
let Workorder = [

]
// 质量
let Inspection = [
	{
		path: '/Opc_FAI',
		name: 'Opc_FAI',
		component: () => import('@/views/opcenter/transactions/Opc_FAI.vue'),
		meta: { name: '首末件检验', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_IPQC',
		name: 'Opc_IPQC',
		component: () => import('@/views/opcenter/transactions/Opc_IPQC.vue'),
		// meta: { name: '巡检', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_AQL',
		name: 'Opc_AQL',
		component: () => import('@/views/opcenter/transactions/Opc_AQL.vue'),
		meta: { name: '抽检', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_LotReject',
		name: 'Opc_LotReject',
		component: () => import('@/views/opcenter/transactions/Opc_LotReject.vue'),
		//meta: { name: '品质不合格品管理', keepAlive: false, }
	},
	{
		path: '/Opc_FQC',
		name: 'Opc_FQC',
		component: () => import('@/views/opcenter/transactions/Opc_FQC.vue'),
		meta: { name: 'FQC管理', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_NCP',
		name: 'Opc_NCP',
		component: () => import('@/views/opcenter/transactions/Opc_NCP.vue'),
		meta: { name: 'FQC不合格品处理', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_SelfInspection',
		name: 'Opc_SelfInspection',
		component: () => import('@/views/opcenter/transactions/Opc_SelfInspection.vue'),
		meta: { name: '生产自检', keepAlive: false, noCache: false }
	},
]
// 物料
let Materiel = [
	{
		path: '/Opc_ExcessRequestCheck',
		name: 'Opc_ExcessRequestCheck',
		component: () => import('@/views/opcenter/transactions/Opc_ExcessRequestCheck.vue'),
		meta: { name: '下料超领确认', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_MaterialRequest',
		name: 'Opc_MaterialRequest',
		component: () => import('@/views/opcenter/transactions/Opc_MaterialRequest.vue'),
		meta: { name: '工单领料申请', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_MaterialExcessRequest',
		name: 'Opc_MaterialExcessRequest',
		component: () => import('@/views/opcenter/transactions/Opc_MaterialExcessRequest.vue'),
		meta: { name: '工单物料超领', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_MaterialReturn',
		name: 'Opc_MaterialReturn',
		component: () => import('@/views/opcenter/transactions/Opc_MaterialReturn.vue'),
		meta: { name: '退料', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_ChangeQty',
		name: 'Opc_ChangeQty',
		component: () => import('@/views/opcenter/transactions/Opc_ChangeQty.vue'),
		meta: { name: '物料数量调整', keepAlive: false, noCache: false }
	},
	{
		path: '/Opc_MaterialScrap',
		name: 'Opc_MaterialScrap',
		component: () => import('@/views/opcenter/transactions/Opc_MaterialScrap.vue'),
		//meta: { name: '物料报废', keepAlive: false, }
	},
	{
		path: '/Opc_MaterialLabelReprint',
		name: 'Opc_MaterialLabelReprint',
		component: () => import('@/views/opcenter/transactions/Opc_MaterialLabelReprint.vue'),
		//meta: { name: '物料标签打印', keepAlive: false, }
	},
]
// 设备
let EquipManage = [
	{
		path: '/Opc_ResourceTool',
		name: 'Opc_ResourceTool',
		component: () => import('@/views/opcenter/transactions/Opc_ResourceTool.vue'),
		meta: { name: '模具与设备绑定/解绑', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_ResourceSwitchMaintenance',
		name: 'Opc_ResourceSwitchMaintenance',
		component: () => import('@/views/opcenter/resources/Opc_ResourceSwitchMaintenance.vue'),
		meta: { name: '设备维修单创建', keepAlive: false, noCache: false}
	},//web.vite\src\views\opcenter\resources\Opc_ResourceSwitchMaintenance.vue//
	{
		path: '/Opc_MaintenanceExecuteQuery',
		name: 'Opc_MaintenanceExecuteQuery',
		component: () => import('@/views/opcenter/resources/Opc_MaintenanceExecuteQuery.vue'),
		meta: { name: '设备维护执行记录查询', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_ResourceAssign',
		name: 'Opc_ResourceAssign',
		component: () => import('@/views/opcenter/resources/Opc_ResourceAssign.vue'),
		meta: { name: '设备维护单分配', keepAlive: false, noCache: false}
	},
		{
		path: '/Opc_ResourceProgress',
		name: 'Opc_ResourceProgress',
		component: () => import('@/views/opcenter/resources/Opc_ResourceProgress.vue'),
		meta: { name: '设备维护单处理', keepAlive: false, noCache: false}
		//维修单确认接收，处理，开始计时//
	},
		{
		path: '/Opc_ResourceClockOff',
		name: 'Opc_ResourceClockOff',
		component: () => import('@/views/opcenter/resources/Opc_ResourceClockOff.vue'),
		meta: { name: '设备维护单关闭计时', keepAlive: false, noCache: false}
		//维修单确认关闭计时//
	},
		{
		path: '/Opc_ResourceComplete',
		name: 'Opc_ResourceComplete',
		component: () => import('@/views/opcenter/resources/Opc_ResourceComplete.vue'),
		meta: { name: '设备维护单关闭', keepAlive: false, noCache: false}
		//维修单确认关闭//
	},
			{
		path: '/Opc_ResourceAssignV2',
		name: 'Opc_ResourceAssignV2',
		component: () => import('@/views/opcenter/resources/Opc_ResourceAssignV2.vue'),
		meta: { name: '设备维护单处理再分配', keepAlive: false, noCache: false}
		//设备维护单处理再分配//
	}

]

// 报表
let ReportForm = [
	{
		path: '/Opc_MaterialIssueForSAPReport',
		name: 'Opc_MaterialIssueForSAPReport',
		component: () => import('@/views/opcenter/report/Opc_MaterialIssueForSAPReport.vue'),
		meta: { name: '物料消耗报工报错查看', keepAlive: false, noCache: false}
	},
	{
		path: '/Opc_WorkorderInformationReport',
		name: 'Opc_WorkorderInformationReport',
		component: () => import('@/views/opcenter/report/Opc_WorkorderInformationReport.vue'),
		meta: { name: '工单信息查询', keepAlive: false, noCache: false}
	},
		{
		path: '/Opc_WorkorderProductionProgressReport',
		name: 'Opc_WorkorderProductionProgressReport',
		component: () => import('@/views/opcenter/report/Opc_WorkorderProductionProgressReport.vue'),
		meta: { name: '工单生产进度', keepAlive: false, noCache: false}
	},
		{
		path: '/Opc_WorkorderProductionHistoryReport',
		name: 'Opc_WorkorderProductionHistoryReport',
		component: () => import('@/views/opcenter/report/Opc_WorkorderProductionHistoryReport.vue'),
		meta: { name: '工单生产历史查询', keepAlive: false, noCache: false}
	},
]

let opcenter = [
	...Modeling,
	...Production,
	...Workorder,
	...Inspection,
	...Materiel,
	...EquipManage,
	...ReportForm,
];


export default opcenter;