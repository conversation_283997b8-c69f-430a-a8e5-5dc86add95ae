<template>
    <div class="page-header">
        <!-- 搜索条件 -->
        <div style="display: flex; margin-top: 5px">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工单</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getMfgOrder" @change="changeMfgOrder">
                        <el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工序</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchSpec" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getSpec" @change="changeSpec">
                        <el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>设备</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchResource" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getResource" @change="changeResource">
                        <el-option v-for="item in resources" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>制作时机</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchProductionTiming" clearable filterable placeholder="请选择" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getProductionTiming">
                        <el-option v-for="item in productionTimings" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>检验类型</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchInspectionType" clearable filterable placeholder="请选择" style="width: 200px">
                        <el-option v-for="item in inspectionTypes" :key="item.key" :label="item.value" :value="item.key" />
                    </el-select>
                </div>
            </div>
        </div>
        <div style="display: flex; margin-top: 5px">
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>产品编码</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled style="width: 200px" v-model="editProduct"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>产品描述</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled style="width: 410px" v-model="editDescription"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>客户编码</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled style="width: 200px" v-model="editCustomer"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>客户料号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled style="width: 200px" v-model="editCustomerProduct"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px;padding-top: 24px;">
                <el-button type="primary" @click="toQualityInspectionSheet" plain>品质检验表</el-button>
            </div>
        </div>
    </div>

    <div class="table-item">
        <div class="table-item-header">
            <!-- <div class="table-item-border"></div>-->
            <span class="table-item-text">物料清单</span>
        </div>
        <vol-table ref="masterTable" index :tableData="masterTableData" :columns="masterCols"
            :height="200" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"></vol-table>
    </div>
    <div class="table-item">
        <div class="table-item-header">
            <!-- <div class="table-item-border"></div>-->
            <span class="table-item-text">定性检验项目</span>
            <div class="table-item-buttons">
                <div>
                    <el-button type="success" icon="Check" @click="submit" plain>提交</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="editTable" index :tableData="qualitativeTableData" :columns="editCols"
            :height="200" :pagination-hide="true" :load-key="true" :column-index="true" :single="true"
            :url="apiUrl.getQualityInspectionSheet" @loadBefore="editLoadBefore" @loadAfter="editLoadAfter"
            :defaultLoadPage="false" :ck="false"></vol-table>
    </div>
    <div class="table-item" style="padding-bottom: 32px;">
        <div class="table-item-header">
            <span class="table-item-text">定量检验项目</span>
        </div>
        <vol-table ref="quantitativeTable" index :tableData="quantitativeTableData" :columns="quantitativeCols"
            :max-height="600" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
            :edit="true"></vol-table>
    </div>
    <vol-box :lazy="true" v-model="showCollect" title="定量项目实测值" :width="500" :padding="5">
        <div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>检验项目</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="collectInspectionPoint"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 80px; margin-left: 5px; font-size: 16px">
                    <span>下限</span>
                </label>
                <div style="margin-top: 5px;width: 100px;">
                    <el-input disabled v-model="collectLowerLimit"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 80px; margin-left: 5px; font-size: 16px">
                    <span>上限</span>
                </label>
                <div style="margin-top: 5px;width: 100px;">
                    <el-input disabled v-model="collectUpperLimit"></el-input>
                </div>
            </div>
        </div>
        <vol-table ref="collectTable" index :tableData="collectTableData" :columns="collectCols" :max-height="600"
            :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
            :edit="true"></vol-table>
        <template #footer>
            <div v-show="showCollectBTN">
                <el-button v-show="showIOT" type="primary" :icon="Share" size="small"
                    @click="collectIOT">获取数据</el-button>
                <el-button @click="addRow" type="primary">增加行数</el-button>
                <el-button @click="removeRow" type="danger" plain>减少行</el-button>
                <el-button type="success" icon="Check" size="small" @click="collectSave">保存</el-button>
                <el-button type="info" icon="Refresh" @click="initCollectTableData" plain>重置</el-button>

            </div>
        </template>
    </vol-box>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            //搜索
            searchMfgOrder: null,
            searchSpec: null,
            searchResource: null,
            searchProductionTiming: null,
            searchInspectionType: null,
            editProduct: null,
            editDescription: null,
            editCustomer: null,
            editCustomerProduct: null,
            mfgorders: [],
            specs: [],
            resources: [],
            productionTimings: [],
            inspectionTypes: [{ key: '1', value: '首检' }, { key: '2', value: '末检' }],
			IAResults: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }],
            masterTableData: [],
            qualitativeTableData: [{ItemResult: 'OK'},],
            quantitativeTableData: [],
            showCollect: false,
            showCollectBTN: true,

            masterCols: [
                { field: 'Container', title: '物料批次', type: 'string', width: 0, align: 'center' },
                { field: 'Product', title: '物料编码', type: 'string', width: 0, align: 'center' },
                { field: 'Description', title: '物料描述', type: 'string', width: 130, align: 'center' },
            ],

            editCols: [
                { field: 'InspectionPoint', title: '检验项目', type: 'string', width: 100, align: 'center' },
                { field: 'InspectionPointContent', title: '检验内容', type: 'string', width: 130, align: 'center' },
                { field: 'InspectionTool', title: '检验工具', type: 'string', width: 100, align: 'center' },
                // { field: 'ActualValue', title: '实际值', edit: true, type: 'string', width: 80, align: 'center' },
                {
					field: 'ItemResult', title: '检验结果', bind: { key: null, data: [] }, edit: { type: "select" }, width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
                { field: 'Notes', title: '备注', edit: { type: "string" }, type: "string", precision: 2, width: 160, align: 'center' },
            ],

                        quantitativeCols: [
                { field: 'InspectionPoint', title: '检验项目', edit: false, type: 'string', width: 130, align: 'center' },
                { field: 'InspectionPointContent', title: '检验内容', edit: false, type: 'string', width: 130, align: 'center' },
                { field: 'inspectionpointtool', title: '检验工具', edit: false, type: 'string', width: 100, align: 'center' },
                { field: 'LowerLimit', title: '下限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
                { field: 'UpperLimit', title: '上限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
                { field: 'DefaultValue', title: '标准值', edit: false, type: 'string', width: 80, align: 'center' },
                {
                    field: 'ActualValue', title: '实测值', align: 'center', width: 80, render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <el-button
                                    onClick={($e) => {
                                        this.collectInspectionPoint = row.InspectionPoint;
                                        this.collectUpperLimit = row.UpperLimit;
                                        this.collectLowerLimit = row.LowerLimit;
                                        this.currentRowIndex = index;

if (row.ActualValue != null) {
    for (let i = 0; i < row.ActualValue.length; i++) {
        const actualValue = row.ActualValue[i]?.ActualValue ?? '';
        const defect = row.ActualValue[i]?.Defect ?? ''; // 获取 Defect 字段

        if (this.collectTableData[i]) {
            // 如果该位置已存在，直接赋值
            this.collectTableData[i].ActualValue = actualValue;
            this.collectTableData[i].Defect = defect;
        } else {
            // 如果该位置不存在，push进去
            this.collectTableData.push({
                ActualValue: actualValue,
                Defect: defect
            });
        }
    }
}
                                        this.showCollect = true;
                                        if (row.FromIOT == '1') {
                                            this.showIOT = true;
                                        } else {
                                            this.showIOT = false;
                                        }
                                    }}
                                    size="small"
                                    type="primary"
                                    icon="Edit"
                                    plain >
                                </el-button>
                                {/* 这里可以接着放按钮或者其他组件 */}
                            </div>
                        );
                    }
                },
                {
                    field: 'ItemResult', title: '检验结果', edit: false, type: 'string', width: 80, align: 'center',
                    cellStyle: (row, rowIndex, columnIndex) => {
                        switch (row.ItemResult) {
                            case 'OK':
                                return { background: "#82C256", color: "#fff" };
                            case 'NG':
                                return { background: "#E93F2C", color: "#fff" };
                        }
                    },
                },
                { field: 'FromIOT', title: '来源IOT', hidden: true, edit: false, type: 'string', width: 130, align: 'center' },
                { field: 'Notes', title: '备注', edit: true, type: 'string', width: 130, align: 'center' },
            ],
            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getStartMfgOrder: "/api/query/getStartMfgOrder",
                getQualityInspectionSheet: '/api/query/getQualityInspectionSheet',
                getResourceComponent: "/api/query/getResourceComponent",
                selfInspectionOrder: '/api/cdo/SelfInspectionOrderMaint',
                getMenu: '/api/menu/getTreeMenu',
            }
        }
    },
    created() {
        this.getProductionTiming();
        this.getIAResults();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        toQualityInspectionSheet() {
			this.http.get(this.apiUrl.getMenu, {}, false).then(res => {
				let found = false;
				res.menu.forEach(item => {
					if (item.name && item.name.includes("品质检验表")) {
						found = true;
						return this.$tabs.open({
							text: '品质检验表',
							path: item.url,
							query: {
								product: this.editProduct
                        	}
						});
					}
				});
				if (!found) {
					this.$message.error('没有访问品质检验表的权限');
				}
			}).catch(err => {
				this.$message.error('获取菜单失败' + err);
			});
		},
        getMfgOrder(query) {
            if (query) {
                let params = {
                    cdo: "mfgorder",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.mfgorders = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getSpec(query) {
            if (query) {
                let params = {
                    cdo: "spec",
                    name: query
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.specs = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getResource(query) {
            if (query) {
                let params = {
                    objectCategory: "RESOURCE",
                    cdo: "resource",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.resources = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getProductionTiming() {
            let params = {
                cdo: "W_PRODUCTIONTIMING",
                //name: encodeURIComponent(query) // 对中文查询参数进行编码
            };
            this.http.get(this.apiUrl.getNameObject, params).then(res => {
                if (res.Result == 1) {
                    this.productionTimings = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        changeMfgOrder(){
            if(!this.searchMfgOrder){
                this.editProduct = null;
                this.editDescription = null;
                this.editCustomer = null;
                this.editCustomerProduct = null;
                return;
            };
            let params = {
                mfgorder: this.searchMfgOrder
            };
            this.http.post(this.apiUrl.getStartMfgOrder, params).then(res => {
                if (res.Result == 1) {
                    this.editProduct = res.Data.tableData[0].Product;
                    this.editDescription = res.Data.tableData[0].P_Description;
                    this.editCustomer = res.Data.tableData[0].CustomerReference;
                    this.editCustomerProduct = res.Data.tableData[0].CustomerProduct;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        changeSpec(){
            if(!this.searchSpec){
                this.qualitativeTableData = [];
                this.quantitativeTableData = [];
                this.$refs.editTable.rowData = [];
                this.$refs.quantitativeTable.rowData = [];
                return;
            }
            if(!this.searchMfgOrder){
                this.$message.error("请先选择工单");
                this.searchSpec = null;
                return;
            };
            let params = {
                product: this.editProduct,
                spec: this.searchSpec,
                InspectionType: 1,
            };
            this.http.post(this.apiUrl.getQualityInspectionSheet, params).then(res => {
                if (res.Result == 1) {
                    if (res.Data.tableData != null) {
                        this.qualitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '1');
                        this.quantitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '2');
                        this.$refs.editTable.rowData = this.qualitativeTableData;
                        this.$refs.editTable.paginations.total = this.qualitativeTableData.length;
                        this.$refs.quantitativeTable.rowData = this.quantitativeTableData;
                        this.$refs.quantitativeTable.paginations.total = this.quantitativeTableData.length;
                    }
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        changeResource(){
            if(!this.searchResource){
                this.masterTableData = null;
                return;
            };
            let params = {
                resource: this.searchResource
            };
            this.http.get(this.apiUrl.getResourceComponent, params).then(res => {
                if (res.Result == 1) {
                    this.masterTableData = res.Data;
                    this.$refs.masterTable.rowData = res.Data;
                    this.$refs.masterTable.paginations.total = res.Data.length;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        getIAResults() {
			this.editCols[3].bind.data = this.IAResults;
		},
        //清除数据
        reset() {
            this.searchMfgOrder = null;
            this.searchSpec = null;
            this.searchResource = null;
            this.searchProductionTiming = null;
            this.searchInspectionType = null;
            this.editProduct = null;
            this.editDescription = null;
            this.editCustomer = null;
            this.editCustomerProduct = null;
            this.masterTableData = [];
            this.qualitativeTableData = [];
            this.quantitativeTableData = [];
            this.$refs.masterTable.rowData = [];
            this.$refs.editTable.rowData = [];
            this.$refs.quantitativeTable.rowData = [];
        },
        rowClick({
            row,
            column,
            index
        }) {
            // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
            // this.$refs.masterTable.$refs.table.toggleRowSelection(row);
            this.masterTableData = [{Container:'11111'}]
        },
        submit() {
            if (!this.searchMfgOrder) {
                this.$message.error('请选择工单');
                return;
            }
            if (!this.searchSpec) {
                this.$message.error('请选择工序');
                return;
            }
            if (!this.searchResource) {
                this.$message.error('请选择设备');
                return;
            }
            if (!this.searchProductionTiming) {
                this.$message.error('请选择制作时机');
                return;
            }
            if (!this.searchInspectionType) {
                this.$message.error('请选择检验类型');
                return;
            }

            for (let i = 0; i < this.qualitativeTableData.length; i++) {
                if (!this.qualitativeTableData[i].ItemResult) {
                    this.$message.error('第' + (i + 1) + '行，请选择检验结果');
                    return;
                }
            }

            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                mfgorder: this.searchMfgOrder,
                spec: this.searchSpec,
                resource: this.searchResource,
                productionTiming: this.searchProductionTiming,
                inspectionType: this.searchInspectionType ,
                requestData: this.masterTableData,
                requestData2: this.qualitativeTableData
            };
            this.http.post(this.apiUrl.selfInspectionOrder, params, true).then(res => {
                if (res.Result == 1) {
                    this.reset();
                    this.$message.success(res.Message);
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    // .table-item-text {
    // 	font-weight: bolder;
    // 	border-bottom: 1px solid #0c0c0c;
    // }
    .table-item-text {
        margin-top: 3px;
        padding-bottom: 6px;
        font-weight: bold;
        font-size: 15px;
        color: #484848;
        white-space: nowrap;
        border-bottom: 2px solid #676767;
        margin-bottom: -1px;
        letter-spacing: 1px;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196f3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>