<template>
  <vol-box :lazy="true" v-model="visible" title="保养执行" :width="1000" :padding="5" :onModelClose="onModelClose">
    <div style="height: 100%">
      <div class="table-item">
        
        <vol-table ref="table1" index :tableData="checktable" :columns="checkList" :min-height="500" :max-height="500"
          :pagination-hide="true" :load-key="true" @rowClick="handleSelect" :column-index="true" :ck="false"
          :single="false">
        </vol-table>
        <div class="table-item-header">
          <div class="table-item-border"></div>
          <span class="table-item-text">{{ this.$ts('项目') }}</span>
        </div>
        <vol-table @loadAfter="loadAfter" ref="table" index :tableData="tableData" :columns="dataPointList"
          :min-height="500" :max-height="500" :pagination-hide="true" :load-key="true" :column-index="true"
          :clickEdit="true" :ck="false">
        </vol-table>
        <VolHeader :title="this.$ts('CheckList')"></VolHeader>
       
        <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
          <!-- <el-button type="primary" class="el-icon-check" @click="click_test">{{
          this.$ts('test') }}</el-button> -->
        </VolForm>
      </div>
    </div>
    <template #footer>
      <div>
        <el-button type="primary" class="el-icon-check" @click="click_confirm">{{
          this.$ts('Confirm')
        }}</el-button>
        <el-button type="default" class="el-icon-close" @click="closeModel">{{
          this.$ts('Close')
        }}</el-button>
      </div>
    </template>
  </vol-box>
</template>
<script lang="jsx">
import VolBox from '@/components/basic/VolBox.vue';
import VolTable from "@/components/basic/VolTable.vue";
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'
import { watch } from 'vue';
import { ElMessage, ElMessageBox, ElSteps } from 'element-plus';
import { mapState } from 'vuex'
//这里使用的vue2语法，也可以写成vue3语法
export default {
  components: {
    'vol-box': VolBox,
    VolHeader, 'vol-table': VolTable,
    VolForm
  },

  methods: {},
  data() {
    return {
      dataArry: {},
      checktable: [],
      checkList: [
        {
          field: "CHECKLISTID", // 原 field: "ChecklistId"
          title: this.$ts('ChecklistId'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
        {
          field: "INSTRUCTION", // 原 field: "Instruction"
          title: this.$ts('介绍'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
        {
          field: "W_ISRESULT", // 原 field: "YP_IsResult"
          title: this.$ts('结果'),
          type: 'checkbox',
          align: 'center',
          readonly: false,
          width: 150,
          edit: { type: "checkbox", keep: true },
          render: (h, { row, column, index }) => {
            return (
              <el-radio-group v-model={row.W_ISRESULT} size="mini">
                <el-radio label={0}>NG</el-radio>
                <el-radio label={1}>OK</el-radio>
              </el-radio-group>
            );
          },
        },
        {
          field: "DESCRIPTION", // 原 field: "Description"
          title: this.$ts('描述'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
        {
          field: "NOTES", // 原 field: "Notes"
          title: this.$ts('备注'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
        {
          field: "TXNDATE", // 原 field: "TxnDate"
          title: this.$ts('完成时间'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
        {
          field: "EMPLOYEENAME", // 原 field: "EmployeeName"
          title: this.$ts('完成人员'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
        {
          field: "EMPLOYEEGROUPNAME", // 原 field: "EmployeeGroupName"
          title: this.$ts('用户组'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
        {
          field: "DATACOLLECTIONDEFNAME", // 原 field: "DataCollectionDefName"
          title: this.$ts('采集项目名称'),
          type: 'string',
          width: 100,
          align: 'center',
          readonly: true,
        },
      ],
      visible: false,
      model: {},
      title: this.$ts('编辑'),
      currentRecord: null,
      currentRecord_Row: null,
      width: 900,
      style: { 'max-height': '500px' },
      formFields: {
        Biz_InspectAttach: null,
        Biz_ActualWorkingHour: null,
        Biz_InspectRemark: null,
        Biz_Inspector: null

      },
      formRules: [
        // [
        //   {
        //     dataKey: this.$ts("Attachment"), //后台下拉框对应的数据字典编号
        //     title: this.$ts("Attachment"),
        //     filter: true,
        //     required: false,
        //     type: "file",
        //     multiple: false,
        //     maxFile: 1,
        //     maxSize: 1,
        //     url: "api/Demo_Order/Upload",
        //     field: "Biz_InspectAttach"
        //   },
        // ],
        [
          {
            title: this.$ts('Actual working hours'),
            placeholder: this.$ts("Time: minutes"),
            filter: true,
            required: false,
            type: "number",
            colSize: 2,
            field: "Biz_ActualWorkingHour",
            validator: (rule, val) => {
              if (val === null || val === '') {
                return '';
              }
              if (val <= 0 && val !== null && val !== '') {

                return '实际工时必须大于0';
              } else {
                return "";
              }

            },


          },
        ],
        [
          {
            title: this.$ts('Remark'),
            placeholder: this.$ts("备注"),
            filter: true,
            required: false,
            type: "textarea",
            field: "Biz_InspectRemark"
          },
        ],
      ],
      //url: "api/Demo_Order/getPageData",
      dataPointList: [
        //
        {
          field: 'DataCollectionDefName', title: this.$ts('采集项目名称'), type: 'string', width: 130, sort: false
        },
        //工站
        {
          field: 'DataPointName', title: this.$ts('采集点'), type: 'string', width: 130, align: 'center',


        },
        // { field: 'DataType', title: this.$ts('内容'), type: 'string', width: 130, align: 'center', edit: { type: "input", keep: true } },
        {
          field: 'DataValue', title: this.$ts('值'), required: true, type: 'string', width: 130, align: 'center', edit: { type: "string", keep: true },

          render: (h, { row, column, index }) => {
            //这里根据api返回的数据类型来判断每一行渲染不同的组件
            // console.log(row.DataType, 'row_render');
            if (row.DataType === 'Boolean') {
              // row.DataValue = "1";
              return (
                <el-switch
                  v-model={row.DataValue}
                  active-value="1"
                  inactive-value="0"
                  active-text="Yes"
                  inactive-text="No"
                > </el-switch>
              );
            } else if (row.DataType === 'Integer' || row.DataType === 'Float' || row.DataType === 'Decimal') {
              return (
                <el-input-number
                  v-model={row.DataValue}
                  width="auto"
                  placeholder=""
                  class='vol-form-item'
                />
              );
            }
            else {
              return (
                <el-input
                  v-model={row.DataValue}
                  placeholder=""

                />
              );
            }
          },
          cellStyle: (row, rowIndex, columnIndex) => {
            if (row.LowerLimit === null || row.LowerLimit === ''
              || row.UpperLimit === null || row.UpperLimit === ''
            ) {
              return {}
            }
            //可超阈值部分,后续可能根据是否可超阈值控制提交验证,勿删!!!!!!!!
            //if (row.LimitOverrideAllowed !== null && row.LimitOverrideAllowed === '1') {
            if (row.DataValue !== null && row.DataValue !== '') {
              // 将字符串转换为数字
              const dataValue = Number(row.DataValue);
              const lowerLimit = Number(row.LowerLimit);
              const upperLimit = Number(row.UpperLimit);

              if (dataValue < lowerLimit || dataValue > upperLimit) {
                // console.log(dataValue, typeof dataValue, lowerLimit, typeof lowerLimit, upperLimit, typeof upperLimit);
                // this.$message.error(this.$ts('超出阈值区间'));

                return {
                    /*这里是花里胡哨的灯带效果 'border': '3px solid transparent',
                    'backgroundImage': 'linear-gradient(white, white), linear-gradient(to right, darkred, darkred, violet)',
                    'backgroundOrigin': 'border-box',
                    'backgroundClip': 'content-box, border-box',
                    'backgroundSize': 'auto, 400% 90%',
                    'animation': 'slideBorder 3s infinite ease-in-out',
                    // 'border-radius': '5px',
                    'border-width': '10px',
                    //  */'background': 'red',
                };
              }
            }
            //}


          },
        },
        { field: 'LowerLimit', title: this.$ts('阈值下限'), hidden: false, type: 'string' },
        { field: 'UpperLimit', title: this.$ts('阈值上限'), hidden: false, type: 'string' },
        {
          field: 'LimitOverrideAllowed', title: this.$ts('是否可超阈值'), hidden: false, type: 'string',
          formatter: (row) => {
            return row.LimitOverrideAllowed === 'True' ? '是' : '否';
          }
        },
        {
          field: 'IsRequired', title: this.$ts('是否必填'), hidden: false, type: 'string',
          formatter: (row) => {
            return row.IsRequired === 'True' ? '是' : '否';
          }

        },

      ],
      tableData: [],



    };
  },
  mixins: [GlobalElMessageBox],
  /*   watch: {
      tableData: {
        handler: function (val, oldVal) {
          val.forEach(item => {
             在这里根据上限阈值操作单元格/行变色 
  
  
            console.log('new: %s, old: %s', JSON.stringify(val, null, 2), JSON.stringify(oldVal, null, 2));
          });
        },
        deep: true // 使用深度观察
      }
    }, */
  computed: {
    ...mapState({ userInfo: (state) => state.userInfo })
  },
  methods: {
    /* click_test() {
      console.log('test')
    }, */

    async click_confirm() {
      /*let flag = this.tableData.every(item => item.DataValue === '0');
      if (flag) {*/
      var flag = true;
        ElMessageBox.confirm(this.$ts('All current values are at default') + ", " + this.$ts('Are you sure to exit?'), this.$ts('Tips'), {
          confirmButtonText: this.$ts('Confirm'),
          cancelButtonText: this.$ts('Cancel'),
          closeOnClickModal: false,
          center: false,
          type: 'warning',
          // showClose: false,
          
        }).then(() => {
          // 对话框确认后执行的代码
          const valid = this.$refs.form.validate();
          // if (!valid || this.validateTableData(this.tableData, this.dataPointList) === false) {
          //   return;
          // }
          const postdata = {
            user: this.userInfo.userName,
            password: this.userInfo.userPwd,
            ...this.formFields,
            dataPointList: this.tableData,
            ...this.currentRecord, // 将currentRecord合并到postdata中
            Biz_InspectAttach: this.formFields.Biz_InspectAttach && this.formFields.Biz_InspectAttach.length > 0 ? this.formFields.Biz_InspectAttach[0].path : null,
            Biz_Inspector: this.userInfo.userName,
            Count:this.checktable.length,
            CheckList: [],

          };

          // var rows = this.$refs.table1.getSelected();
          // if (this.checktable.length > 0) {
          //   this.checktable.forEach(row => {
          //     if(row.W_ISRESULT != null){
          //       this.dataArry = {
          //       ChecklistId: row.CHECKLISTID,
          //       Instruction: row.INSTRUCTION,
          //       W_ISRESULT: row.W_ISRESULT,
          //     }
          //     postdata.CheckList.push(this.dataArry);
          //     }              
          //   });
          // }

          if(this.currentRecord_Row){
            const row = this.currentRecord_Row;
            postdata.CheckList.push({
                ChecklistId: row.CHECKLISTID,
                Instruction: row.INSTRUCTION,
                W_ISRESULT: row.W_ISRESULT,
            });
          }

          if(flag=== true){
            this.http.post("/api/CDO/CompleteMaintenance", postdata).then((res) => {
            if (res.status == "0") {
              this.$message.error(this.$ts('Failed to complete the maintenance') + res.message);
              
            }
            else {
              this.$message.success(res.message);
              // this.closeModel();
            }


          });

          }
          
        }).catch(() => {
          this.visible = true;
        });
      //} 
      
      /*else {
        // 如果 flag 为 false，即没有检查项的值为默认值，直接执行以下代码
        const valid = await this.$refs.form.validate();
        if (!valid || this.validateTableData(this.tableData, this.dataPointList) === false) {
          return;
        }
        const postdata = {
          ...this.formFields,
          dataPointList: this.tableData,
          ...this.currentRecord,
          Biz_InspectAttach: this.formFields.Biz_InspectAttach && this.formFields.Biz_InspectAttach.length > 0 ? this.formFields.Biz_InspectAttach[0].path : null,
          Biz_Inspector: this.userInfo.userName,
          CheckList: [],
        };
        debugger;
        var rows = this.$refs.table1.getSelected();
        if (rows.length > 0) {
          rows.forEach(row => {
            let dataArry = {
              ChecklistId: row.ChecklistId,
              Instruction: row.Instruction,
            }
            postdata.CheckList.push(dataArry);
          });

        }
        console.log(postdata, 'postdata');
        this.http.post("/api/CDO/CompleteMaintenance", postdata).then((res) => {
          this.resultMessage(res.status, res.message);
        }).finally(() => {
          this.$emit('ok');

          this.closeModel();
        })

      }*/

    },

    edit(record) {
      this.tableData = [];
      this.currentRecord = record; // 将接收到的record赋值给currentRecord
      this.model = Object.assign({}, record);
      this.getChecklist({ RequirementName: record.MaintenanceReqName, RequirementRevision: record.MaintenanceRevision, ResourceName: record.ResourceName });
      this.visible = true;
      return true;
    },

    getDataPoint(record) {
      this.tableData = [];
      // console.log(record, 'record')
      this.http.post('/api/Query/SearchMaintenanceDataCollection', record).then((res) => {
        console.log(res, 'datapoint')
        // this.resultMessage(res.status, res.message);
        if (res.rows.length > 0) {
          this.tableData = res.rows;
          res.rows.forEach(item => {
            if(item.DataType == 'Boolean'){
              item.DataValue = (!item.DataValue || item.DataValue.toLocaleLowerCase() === 'true') ? '1' : '0';
            }

            /* 在这里根据上限阈值操作单元格/行变色 */
            if (item.LowerLimit !== null && item.UpperLimit !== null) {
              if (item.DataValue < item.LowerLimit || item.DataValue > item.UpperLimit) {
                item.rowStyle = { color: 'red' };
              }
            }

          });
        }


      });


    },

    // 获取表单数据
    getForm() {
      this.$refs.form.validate((err) => {
        console.log(this.formFields, '表单数据');
      })
      this.search()
    },
    //根据数据类型设置表格编辑类型  弃用
    mappingDataTypeForTable(type) {
      const dataTypeMapping = {
        'Boolean': 'switch',
        'Integer': 'number',
        'Float': 'decimal',
        'Decimal': 'decimal'
      };
      return dataTypeMapping[type] || 'text';
    },

    onModelClose() {
      // alert('弹出框右上角点击x关闭事件')
    },
    closeModel() {
      this.model = {};
      this.$refs.form.reset();
      this.$refs.table.reset();
      this.$emit('ok');
      this.visible = false;
    },

    getChecklist(params) {
      this.http.post('api/Query/GetCheckListData', params).then(res => {
        if (res.Result == 1) {
          this.checktable = res.Data.map(item => {
            item.W_ISRESULT = null;
            return item;
          });
        }
        else {
          this.$message.error(res.Message)
        }
      });
    },
    handleSelect({ row, column, event }) {
      //this.$refs.table1.$refs.table.toggleRowSelection(row);
      this.currentRecord_Row = row;
      this.tableData = null;
      if(row.DATACOLLECTIONDEFNAME!==null&&row.DATACOLLECTIONDEFNAME!==''){
        let param = {
        DataCollectName: row.DATACOLLECTIONDEFNAME.split(':')[0],
        DataCollectRev: row.DATACOLLECTIONDEFNAME.split(':')[1],
        DataCollectionDefId: row.DATACOLLECTIONDEFID,
        ResourceId: row.RESOURCEID,
      }
      this.getDataPoint(param);
      }

    }
  }
};
</script>

<style>
@keyframes slideBorder {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.borderFlow {
  border: 3px solid transparent;
  background-image: linear-gradient(white, white),
    linear-gradient(to right, red, orange, yellow, green, blue, indigo, violet);
  background-origin: border-box;
  background-clip: content-box, border-box;
  background-size: 200% 200%;
  animation: slideBorder 3s infinite linear;
}
</style>