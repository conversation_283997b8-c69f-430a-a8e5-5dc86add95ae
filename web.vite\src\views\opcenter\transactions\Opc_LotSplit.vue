<template>
    <div class="lot-split">
        <VolHeader title="批次拆分" />
        <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules" >
            <div style="text-align: end; margin-top: 0px; width: 100%">
                <div style="margin-right: 20px; margin-top: -39px">
                    <el-button type="primary" @click="submit" plain icon="Plus">拆批</el-button>
                </div>
            </div>
        </VolForm>
        <VolHeader title="相关批次" />
        <vol-table ref="table1"  index :column-index="true" :reserveSelection="true"
                        :loadKey="true" :columns="columns" :tableData="tableData" :single="false"
                        :pagination-hide="false" :height="400" :row-style="rowStyle"
                        :url="ApiUrl.GetContainerInfo" :defaultLoadPage="false"></vol-table>

    </div>
</template>

<script lang="jsx">
    import VolTable from "@/components/basic/VolTable.vue";
    import VolForm from '@/components/basic/VolForm.vue';
    import VolHeader from '@/components/basic/VolHeader.vue';
    import VolBox from '@/components/basic/VolBox.vue';
    import {
        mapState
    } from 'vuex';
    export default {
        components: {
            VolHeader,
            VolForm,
            'vol-table': VolTable,
            'vol-box': VolBox
        },
        computed: {
            ...mapState({
                //获取当前用户的信息
                userInfo: state => state.userInfo,
                //获取当前用户的权限
                permission: state => state.permission,
            })
        },
        //初始化页面
        created() {
            this.GetPrinterQueue();
            this.headerFields.employee = this.userInfo.userName;
        },

        data() {
            return {
                ApiUrl: {
                    GetRevisionObject: "/api/query/GetRevisionObject",
                    GetNameObject: "/api/query/GetNameObject",
                    GetContainerInfo: '/api/Query/GetContainerInfo', //获取容器信息
                    GetRelateContainerList: '/api/Query/GetRelateContainerList', //获取批次list信息
                    LotSplit: 'api/CDO/lotSplit', //拆批
                },
                headerFields: {
                    employee: '',
                    printQueue: '',
                    selectLot: '',
                    currentLot: '',
                    qty: '',
                    currentStatus: '',
                    currentSpec: '',
                    productNo: '',
                    productDesc: '',
                    mfgQty: '',
                    mfgOrder: '',
                    splitQty: '',
                    Comments:'',
                },
                headerRules: [
                    [
                        {
                            title: this.$ts('批次'),
                            placeholder: this.$ts(''),
                            field: "selectLot",
                            type: "text",
                            required: true,
                            colSize: 2,
                            onKeyPress: $event => { if ($event.keyCode == 13) {
                                this.onselectLot(this.headerFields.selectLot)
                            } },
                        }
                    ],
                    [
                        {
                            title: this.$ts('操作员'),
                            placeholder: this.$ts(''),
                            field: "employee",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('打印机'),
                            placeholder: this.$ts(''),
                            field: "printQueue",
                            type: "select",
                            colSize: 2,
                            data: [],
                        }
                    ],
                    [{
                            title: this.$ts('当前批次'),
                            placeholder: this.$ts(''),
                            field: "currentLot",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('批次数量'),
                            placeholder: this.$ts(''),
                            field: "qty",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('当前状态'),
                            placeholder: this.$ts(''),
                            field: "currentStatus",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('当前工序'),
                            placeholder: this.$ts(''),
                            field: "currentSpec",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        }
                    ],
                    [{
                            title: this.$ts('产品编码'),
                            placeholder: this.$ts(''),
                            field: "productNo",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('产品描述'),
                            placeholder: this.$ts(''),
                            field: "productDesc",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('工单数量'),
                            placeholder: this.$ts(''),
                            field: "mfgQty",
                            type: "text",
                            readonly: true,
                            colSize: 2,

                        },
                        {
                            title: this.$ts('生产工单'),
                            placeholder: this.$ts(''),
                            field: "mfgOrder",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        }
                    ],
                    [{
                        title: this.$ts('需要拆分数量'),
                        placeholder: this.$ts(''),
                        field: "splitQty",
                        type: "text",
                        readonly: false,
                        colSize: 2,
                    },
                    {
                        title: this.$ts('备注'),
                        placeholder: this.$ts(''),
                        field: "Comments",
                        type: "textarea",
                        readonly: false,
                        colSize: 8,
                    }
                ]
                ],
                columns: [                    
                    { field: 'mfgOrderName', title: '生产工单', type: 'string', width: 130, align: 'center'},
					{ field: 'productName', title: '产品编码', type: 'string', width: 130, align: 'center'},
					{ field: 'productDesc', title: '产品名称描述', type: 'string', width: 180, align: 'center'},
                    { field: 'containerName', title: '批次码', type: 'string', width: 150, align: 'center'},
					{ field: 'qty', title: '批次数量', type: 'string', width: 90, align: 'center'},
					{ field: 'uom', title: '单位', type: 'datetime', width: 90, align: 'center'},
                    { field: 'currentStep', title: '当前工序', type: 'string', width: 150, align: 'center'},
					{ field: 'status', title: '批次状态', type: 'string', width: 120, align: 'center'},
                    { field: 'isHold', title: '是否Hold', type: 'string', width: 120, align: 'center'},
					{ field: 'resourceName', title: '设备/Line', type: 'string', width: 120, align: 'center'},
                    { field: 'inProcess',  title: '',type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'useQueue',  title: '',type: 'string', width: 120, align: 'center',hidden:true},
                    { field: 'nextStep', title: '下工序', type: 'string', width: 150, align: 'center'},
					{ field: 'workflowStepName',title: '', type: 'string', width: 150, align: 'center',hidden:true},
                    { field: 'employeeName', title: '操作人', type: 'string', width: 120, align: 'center'},
                    { field: 'txnDatetime', title: '操作时间', type: 'string', width: 180, align: 'center'},
                    { field: 'specRevision', title: '',type: 'string', width: 150, align: 'center',hidden:true},
                    { field: 'workflowName', title: '工作流程', type: 'string', width: 120, align: 'center'},
                    { field: 'workflowRevision', title: '', type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'productRevision', type: '', width: 180, align: 'center',hidden:true},
                    { field: 'isCollectData', title: '', type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'dataCollectionDefName', type: 'string', width: 120, align: 'center',hidden:true},
                    { field: 'dataCollectionDefRevision', type: '', width: 120, align: 'center',hidden:true},
                    { field: 'WorkflowStepId', title: '', type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'IsPassed', title: '执行是否成功',type: 'string', width: 120, align: 'center'},
                    { field: 'StatusMsg', title: '执行消息!',type: 'string', width: 150, align: 'center'},
					{ field: 'mfgOrderQty', type: 'string', width: 120, align: 'center',hidden:true},
                    { field: 'printerQueue', type: '',type: 'string',  width: 120, align: 'center',hidden:true},
                    { field: 'factoryName', title: '', type: 'string', width: 120, align: 'center',hidden:true} 
                ],
                tableData: []

            }
        },
        methods: {
            //获取打印机队列
            GetPrinterQueue() {
                let params = {
                    cdo: "PrintQueue"
                }
                let dataArry = []
                this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
                    if (res.Result == 1) {
                        dataArry =  res.Data.map(item => {
                            return {
                                key: item.Description,
                                value: item.Name,
                                label: item.Name
                            }
                            // if (dataArry.find((a) => a.key === item.mfgLineName) == null) {
                            //     dataArry.push({
                            //         key: item.Name,
                            //         value: item.Name,
                            //         label: item.Name
                            //     });
                            // }
                        })
                        this.headerRules[1][1].data = dataArry;
                    } else {
                        this.$message.error(res.Message);
                    }
                })
            },
            onselectLot(val) {
                {
                    if (val) {
                        this.reset();
                        let param = {
                            Container: val,
                        }
                        this.http.get(this.ApiUrl.GetContainerInfo, param).then((res) => {
                            if (res.Result == 1) {
                                this.headerFields.currentLot = val;
                                this.headerFields.currentStatus = res.Data.status;
                                this.headerFields.qty = res.Data.qty;
                                this.headerFields.mfgQty = res.Data.mfgOrderQty;
                                this.headerFields.productNo = res.Data.productName;
                                this.headerFields.productDesc = res.Data.productDesc;
                                this.headerFields.mfgOrder = res.Data.mfgOrderName;
                                this.headerFields.currentSpec = res.Data.currentStep;
                                this.headerFields.printQueue = res.Data.printerQueue;
                            } else {
                                this.$Message.error(res.Message);
                            }
                        });
                        let param1 = {
                            Container: val,
                            PageSize:this.$refs.table1.paginations.size,
                            PageCount:this.$refs.table1.paginations.page
                        }
                        this.http.post(this.ApiUrl.GetRelateContainerList, param1).then((res) => {
                            if (res.Result == 1) {
                                //this.columns = res.Data.colums;
                                this.$refs.table1.rowData = res.Data.tableData;
                                this.$refs.table1.paginations.total = res.Data.total;
                            } else {
                                this.$Message.error(res.Message);
                            }
                        });
                    }
                }
            },
            submit() {
                if(this.headerFields.printQueue == null || this.headerFields.printQueue == ''){
                    this.$message.warning('请选择打印机');
                    return;
                }
                if (this.headerFields.currentLot == null || this.headerFields.currentLot == '') {
                    this.$message.warning('请填入批次');
                    return;
                }
                if (this.headerFields.splitQty == null || this.headerFields.splitQty == '') {
                    this.$message.warning('请填数量');
                    return;
                }
                let param = {
                    User:this.userInfo.userName,
					Password:this.userInfo.userPwd,
                    Container: this.headerFields.currentLot,
                    PrintQueue:this.headerFields.printQueue,
                    Qty: this.headerFields.splitQty,
                    Comments: this.headerFields.Comments,
                }
                this.http.post(this.ApiUrl.LotSplit, param).then((res) => {
                    if (res.Result == 1) {
                        this.$Message.success(res.Message);
                        this.reset();
                    } else {
                        this.$Message.error(res.Message);
                    }
                })
            },
            reset() {
                this.headerFields.currentLot = null;
                this.headerFields.splitQty = null;
                this.tableData = [];
            }
        },


    }
</script>