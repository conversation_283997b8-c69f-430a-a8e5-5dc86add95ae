<template>
  <VolHeader title="生产派工"></VolHeader>
  <div style="display: flex; margin-top: 5px;">
    <VolForm ref="dispatchForm" :formFields="dispatchFields" :formRules="dispatchRules">
    </VolForm>
  </div>
  
    <div class="table-item-header" style="display: flex; justify-content: flex-end;">
      <div class="table-item-buttons">
        <el-button type="primary"  @click="handleSearch">查询</el-button>
        <el-button type="success" icon="Plus" @click="handleAdd">新增</el-button>
        <el-button type="success" icon="Plus" @click="handleEdit">编辑</el-button>
        <el-button type="danger" icon="Delete" @click="handleDelete">删除</el-button>
      </div>
    </div>
    
    <vol-table 
    ref="dispatchTable" 
    index 
    :loadKey="true" 
    :tableData="tableData" 
    :columns="columns" 
    :height="500" 
    :pagination-hide="false" 
    :defaultLoadPage="false"
    :url="apiUrl.getDispatchList" 
    @loadBefore="LoadBefore"
    @loadAfter="LoadAfter"
    :single="true"
      ></vol-table>

  <!-- 新增弹窗 -->
  <vol-box v-model="dialogVisible" title="新增派工" :width="700" :height="800" style="display: flex; justify-content: center;">
    <VolForm ref="addForm" :formFields="formFields" :formRules="formRules" >
      <template #footer>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </template>
    </VolForm>
  </vol-box>

  <BarScan ref="modalScan" 
      v-model="scancode"
      @decoded="getResult"/>
</template>

<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from '@/components/basic/VolForm.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import BarScan from '@/components/basic/Scan.vue';

export default {
  components: {
    VolHeader,
    VolForm,
    'vol-table': VolTable,
    'vol-box': VolBox,
    BarScan
  },
  data() {
    return {
      apiUrl: {
        addDispatch: "/api/CDO/DispatchTxn",
        deleteDispatch: "/api/CDO/DispatchTxn",
        getDispatchList: "/api/Query/GetDispatchTask"
      },
      dispatchFields: {
        Resource: '',
        MfgOrder: '',
        Product: '',
        Tool: '',
        dateRange: []
      },
      dispatchRules: [
        [
          { title: '机台编号', field: 'Resource', type: 'text', colSize: 2,
            extra: {
                                style: "color:#2196F3;cursor: pointer;font-size:12px",
                                icon: "el-icon-search", //显示图标
                                click: () => {
                                    this.scancode = true; // Trigger the scan component
                                },
                    },
           },
          {  title: '工单号', field: 'MfgOrder', type: 'text', colSize: 2, }, 
          {  title: '产品料号', field: 'Product', type: 'text', colSize: 2, },
        ],[
          { title: '模具编号', field: 'Tool', type: 'text', colSize: 2,
              extra: {
                                style: "color:#2196F3;cursor: pointer;font-size:12px",
                                icon: "el-icon-search", //显示图标
                                click: () => {
                                    this.scancode = true; // Trigger the scan component
                                },
                    },

           },
          { title: '日期范围', field: 'dateRange', type: 'date', range:true, colSize: 3, }
        ] 
      ],
      formFields: {
        Action: '',
        Resource: '',
        Tool: '',
        MfgOrder: '',
        WaitTool: '',
        Comments: '',
        Priority: ''
      },
      formRules: [
        [
          { required: true, title: '机台编号', field: 'Resource', type: 'text', colSize: 2,
                        extra: {
                                style: "color:#2196F3;cursor: pointer;font-size:12px",
                                icon: "el-icon-search", //显示图标
                                click: () => {
                                    this.scancode = true; // Trigger the scan component
                                },
                    },
           },
          { required: true, title: '模具编号', field: 'Tool', type: 'text',colSize: 2,
                          extra: {
                                style: "color:#2196F3;cursor: pointer;font-size:12px",
                                icon: "el-icon-search", //显示图标
                                click: () => {
                                    this.scancode = true; // Trigger the scan component
                                },
                    },
           },
          { required: true, title: '工单号', field: 'MfgOrder', type: 'text',colSize: 2, },
    ],[
          { title: '待排配模具', field: 'WaitTool', type: 'text',colSize: 2, },
          { required: true, title: '优先级', field: 'Priority', type: 'select',colSize: 2,
            data: [  
                { key: 'Low', value: '低' },
                { key: 'Medium', value: '中' },
                { key: 'High', value: '高' }
              ],
          },
          { title: '备注', field: 'Comments', type: 'textarea',colSize: 5,}
        ]
      ],
      columns: [
        { field: 'DispatchHistoryId', title: 'ID', width: 120, align: 'center', hidden: true  },
        { field: 'ResourceName', title: '机台编号', width: 120, align: 'center' },
        { field: 'Description', title: '描述', width: 150, align: 'center' },
        { field: 'PressStroke', title: '冲床行程', width: 100, align: 'center' },
        { field: 'Tool', title: '模具编号', width: 120, align: 'center' },
        { field: 'MfgOrder', title: '工单号', width: 150, align: 'center' },
        { field: 'MfgOrderQty', title: '工单数量', width: 100, align: 'center' },
        { field: 'Proudct', title: '产品料号', width: 150, align: 'center' },
        { field: 'WaitTool', title: '待排配模具', width: 120, align: 'center' },
        { field: 'HolesNumber', title: '设计穴数', width: 100, align: 'center' },
        { field: 'CreateUser', title: '操作人', width: 100, align: 'center' },
        { field: 'CreateTime', title: '操作时间', width: 150, align: 'center' },
        { field: 'Priority', title: '优先级', width: 150, align: 'center' },
        { field: 'Comments', title: '备注', width: 150, align: 'center' }
      ],
      tableData: [],
      dialogVisible: false,
      scancode:false,//控制是否使用扫一扫功能
      result: '',//扫码结果信息
    }
  },
  methods: {
    handleSearch() {
      this.$refs.dispatchTable.load();
    },
    handleAdd() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.addForm.reset();
      });
    },
    handleEdit() {
      const selection = this.$refs.dispatchTable.getSelected();
      if (!selection.length) {
        return this.$message.warning('请选择要编辑的记录');
      }
      if (selection.length > 1) {
        return this.$message.warning('请选择一条记录进行编辑，不可选择多条');
      }

      // 获取选中的记录
      const editData = selection[0];
      
      // 打开对话框
      this.dialogVisible = true;
      
      // 在对话框打开后，填充表单数据
      this.$nextTick(() => {
        // 先重置表单
        this.$refs.addForm.reset();
        console.log("editData",editData)
        // 填充编辑数据到表单字段
        this.formFields = {
          Resource: editData.ResourceName,
          MfgOrder: editData.MfgOrder,
          Tool: editData.Tool || '',  // 增加默认值处理，避免undefined
          WaitTool: editData.WaitTool || '',
          Comments: editData.Comments || '',
          Priority: editData.Priority || '',
          DispatchOrderHistory: {
            DispatchHistoryId: selection[0].DispatchHistoryId} 
        };
      });
    },

    handleSubmit() {
      this.$refs.addForm.validate().then(valid => {
        if (valid) {
            this.formFields.Action = 1; // 假设这是新增操作的标识，根据实际情况调整
          this.http.post(this.apiUrl.addDispatch, this.formFields).then(res => {
            if (res.Result === 1) {
              this.$message.success('新增成功');
              this.handleSearch();
              this.dialogVisible = false;
            }
            else {
              this.$message.error(res.Message);
            }
          });
        }
      });
    },
    handleEditSubmit() {
      this.$refs.addForm.validate().then(valid => {
        if (valid) {
            this.formFields.Action = 3; // 假设这是编辑操作的标识，根据实际情况调整
            this.formFields.DispatchOrderHistory = 
          this.http.post(this.apiUrl.addDispatch, this.formFields).then(res => {
            if (res.Result === 1) {
              this.$message.success('修改成功');
              this.handleSearch();
              this.dialogVisible = false;
            }
            else {
              this.$message.error(res.Message);
            }
          });
        }
      });
    },
    handleDelete() {
      const selection = this.$refs.dispatchTable.getSelected();
      if (!selection.length) {
        return this.$message.warning('请选择要删除的记录');
      }
      
      this.http.post(this.apiUrl.deleteDispatch, {
        Action: 2, // 假设这是删除操作的标识，根据实际情况调整
        MfgOrder: selection[0].MfgOrder,
        Resource: selection[0].ResourceName,
        Tool: selection[0].Tool,
        DispatchOrderHistory:{
          DispatchHistoryId: selection[0].DispatchHistoryId
        }
      }).then(res => {
        if (res.Result === 1) {
          this.$message.success('删除成功');
          this.handleSearch();
        }
      });
    },
    LoadBefore(params, callBack) {
      let param= {
        MfgOrder: this.dispatchFields.MfgOrder,
        Resource: this.dispatchFields.Resource,
        Tool: this.dispatchFields.Tool,
        Product: this.dispatchFields.Product,
        StartDate: this.dispatchFields.dateRange ? this.dispatchFields.dateRange[0] : null,
        EndDate: this.dispatchFields.dateRange ? this.dispatchFields.dateRange[1] : null,
				PageSize: this.$refs.dispatchTable.paginations.size,
				PageCount: this.$refs.dispatchTable.paginations.page
			};
		params = Object.assign(params, param)
    console.log('LoadBefore params:', params);
            },
    LoadAfter(rows, callBack, result) {
                if (result.Result == 1) {
                    this.tableData = result.Data.tableData;
                    this.$refs.dispatchTable.rowData = result.Data.tableData;
                    this.$refs.dispatchTable.paginations.total = result.Data.total;
                }
                else {
                    this.$message.error(result.Message);
                }
                callBack(false);
            },
    getResult(result){
            if(result!==""){
                    this.scancode=false;
                    this.curFormFields.resourceName=result;
                    }
    },
  }
}
</script>