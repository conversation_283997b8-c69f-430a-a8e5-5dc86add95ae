<template>
  <div class="container">

    <div class="form-content">
      <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
        <VolHeader :title="this.$ts(title)" text="text" icon="el-icon-s-grid"></VolHeader>
        <div style="text-align: end; margin-top: 0px;">
          <el-button type="primary" plain @click="getForm">{{ this.$ts('Search') }}</el-button>
          <el-button type="success" plain @click="reset">{{ this.$ts('Reset') }}</el-button>
          <!-- <el-button type="default" style="padding: 0px 10px" size="small" :plain="true" v-if="showCustom"
            @click="showCustomModel">
            <i class="el-icon-s-grid"></i>
          </el-button> -->
        </div>
      </VolForm>
      <CustomModel ref="CustomModel" :columns="columns" :tableName="tableName"></CustomModel>
    </div>

    <div class="table-item">
      <div class="table-item-header">
        <div class="table-item-border"></div> <span class="table-item-text">{{ this.$ts(tableTitleOne) }}</span>
        <div class="table-item-buttons">
          <el-button type="primary" @click="getRow" plain>{{ this.$ts('Maint. Task Confirm') }}</el-button>
          <!-- <el-button type="primary" @click="addRow" plain>{{ this.$ts('ADD') }}</el-button> -->
          <!-- <el-button type="primary" @click="delRow" color="#f89898" plain>{{ this.$ts('Delete') }}</el-button> -->
        </div>
      </div>

      <vol-table @loadBefore="loadBefore" @loadAfter="loadAfter" ref="table" :url="url.urlOne" index
        :tableData="tableData" :columns="columns" :max-height="500" :pagination-hide="true" :load-key="true"
        :column-index="true" @rowClick="rowClick"></vol-table>
    </div>

  </div>
  <Opc_RepairAssignEdit ref="modalForm" @ok="modalFormOk"></Opc_RepairAssignEdit>

</template>
<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import CustomModel from '@/components/CustomModel/CustomModel.vue';
import Opc_RepairAssignEdit from './Opc_RepairAssignEdit.vue'
import { useStore, mapState } from 'vuex'
export default {
  components: {
    ModelInfo,
    VolHeader,
    VolForm,
    CustomModel,
    'vol-table': VolTable,
    Opc_RepairAssignEdit,
  },
  data() {
    return {
      title: this.$ts('Maint. Task Confirm'),
      tableTitleOne: this.$ts('Repair Task List'),
      tableName: 'RepairAssign',
      formFields: {
        resourceGroup: null,
        ResourceName: null,
        // YP_AssetCostCenter: null,
        YP_TaskStatus: null,
        StartTime: null,
        EndTime: null,
        UserName: null,
      },
      formRules: [
        [
          {
            dataKey: "RESOURCE_GROUP",
            data: [],
            title: this.$ts('Equipment Group'),
            placeholder: this.$ts('Equipment Group'),
            filter: true,
            required: false,
            field: "resourceGroup",
            type: "select",
            colSize: 3,
            onChange: (val, opt) => { this.handleResourceGroupChange(val, opt) },
          },
          {
            dataKey: "",
            data: [],
            title: this.$ts('Equipment code'),
            placeholder: this.$ts('Equipment code'),
            filter: true,
            required: false,
            field: "ResourceName",
            type: "select",
            colSize: 3,
          },
          {
            dataKey: "TaskStatus",
            data: [],
            title: this.$ts('Repair Order Status'),
            placeholder: this.$ts('Repair Order Status'),
            filter: true,
            required: false,
            field: "YP_TaskStatus",
            type: "select",
            colSize: 3,
          },],
        [
          {
            title: this.$ts('Start Date'),
            placeholder: this.$ts('Start Date'),
            required: false,
            field: "StartTime",
            type: "date",
            colSize: 3,
            // min: "2021-07-01", 
            // max: Date.now(),
          },
          {
            title: this.$ts('End Date'),
            placeholder: this.$ts('End Date'),
            required: false,
            field: "EndTime",
            type: "date",
            colSize: 3,
            // min: "2021-07-01", 
            // max: Date.now(),
          },
        ]
      ],
      paginationHide: true,
      url: {
        urlOne: 'api/Query/SearchJobOrder',
        ResGpNm: 'api/Query/SearchResourceGroupToResource',
      },
      columns: [
        // 维修工单号
        {
          field: 'JobOrderName', title: this.$ts('Repair Order Number'), type: 'string', width: 110, sort: true,
          render: (h, { row, column, index }) => {
            return (<div> <i style="color: #2196F3;cursor: pointer;">{row.JobOrderName}</i> </div>)
          }
        },
        // 申请人
        { field: 'YP_RepairingApplicant', title: this.$ts('Applicant'), type: 'string', width: 80, align: 'center' },
        // 申请时间
        { field: 'YP_TaskStartTime', title: this.$ts('Application Time'), type: 'string', width: 80, align: 'center' },
        // 设备编码
        { field: 'ResourceName', title: this.$ts('Equipment code'), type: 'string', width: 80, align: 'center' },
        // 设备描述
        { field: 'ResourceDescription', title: this.$ts('Equipment Description'), type: 'string', width: 80, align: 'center' },
        // 区域
        { field: 'YP_PhysicalLocation', title: this.$ts('District'), type: 'string', width: 80, align: 'center' },
        // 位置
        { field: 'YP_PhysicalPosition', title: this.$ts('Location'), type: 'string', width: 80, align: 'center' },
        // 成本中心
        { field: 'YP_AssetCostCenter', title: this.$ts('Cost Center'), type: 'string', width: 80, align: 'center' },
        // 故障描述
        { field: 'Description', title: this.$ts('Malfunction Description'), type: 'string', width: 80, align: 'center' },
        // 维修单状态
        { field: 'YP_TaskStatus', title: this.$ts('Repair Order Status'), type: 'string', width: 90, align: 'center' },
        // 维修负责人
        { field: 'YP_RepairingApplicant', title: this.$ts('Repair Owner'), type: 'string', width: 80, align: 'center' },
      ],
      tableData: [],
      showCustom: true,
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.userInfo,
    }),
  },
  mounted() {
    this.handleResourceName()
  },
  methods: {

    // 点击行数据
    handleEditRow(row, column, index) {
      console.log(row);
      this.$refs.modalForm.edit(row);
    },

    showCustomModel() {
      this.$refs.CustomModel.showCustomModel()
    },

    modalFormOk() {
      this.getForm()
    },

    // 设备组查询设备
    handleResourceGroupChange(val, opt) {
      this.formFields.ResourceName = null
      if (!val) {
        this.handleResourceName()
      } else {
        this.handleResourceName(val)
      }
    },

    // 查询设备
    handleResourceName(val) {
      let params = { resourceGroupName: null }
      if (!val) {
        params.resourceGroupName = null;
      } else {
        params.resourceGroupName = val;
      }
      this.http.post(this.url.ResGpNm, params).then(res => {
        if (res.status == 1) {
          if (res.rows !== null && res.rows.length > 0) {
            this.formRules[0][1].data = res.rows
          } else {
            this.formRules[0][1].data = []
          }
          // this.$message.success(this.$ts('Execution Success!'))
        } else {
          this.$message.error(this.$ts('Execution failed, please contact MES team.'))
          this.$message.error(res.message)
          //this.handleResourceName()
        }
      })
    },

    // 获取表单数据
    getForm() {
      this.$refs.form.validate(() => {
        this.$refs.table.load(null, true)
      })
    },

    reset() {
      this.$refs.form.reset(this.formFields);
      this.$refs.table.load(null, true)
      this.handleResourceName()
    },

    getRow() {
      const rows = this.$refs.table.getSelected();
      if (!rows.length) {
        this.$message.error(this.$ts('Please select the row'))
        return;
      } else {
        if (rows.length > 1) {
          this.$message.warning(this.$ts('Only one row can be selected'))
        } else {
          this.$refs.modalForm.edit(rows);
        }
      }
    },

    rowClick({ row, column, event }) {
      //table点击行时同时选中当前行
      this.$refs.table.$refs.table.toggleRowSelection(row);
    },

    //设置查询条件参数
    loadBefore(params, callBack) {
      console.log(this.formFields.resourceGroup, 'resourceGroup')
      params.ResourceName = this.formFields.ResourceName
      params.UserName = this.userInfo.userName
      params.YP_TaskStatus = this.formFields.YP_TaskStatus
      params.StartTime = this.formFields.StartTime
      params.EndTime = this.formFields.EndTime
      params.ServiceName = "RepairAssign"
      callBack(true)
    },
    //查询后方法
    loadAfter(rows, callBack, result) {
      callBack(true)
    },

    addRow() {
      this.$refs.table.addRow({ OrderNo: "D2022040600009" })
      this.$refs.modalForm.add({});
      //如果批量添加行。请使用：
      //this.$refs.table.rowData.push(...[{ OrderNo: "D2022040600009" },{ OrderNo: "D2022040600009" }])
    },
    delRow() {
      this.$refs.table.delRow();
      this.$message.success(this.$ts('Successfully Deleted'))
    },
    reload() {
      this.$refs.table.load(null, true);
      this.$message.success(this.$ts('Successful searching'))
    },

  }
};
</script>
<style lang="less" scoped>
.container {
  padding: 10px;
  background: #F3F7FC;

  .form-content {
    border-radius: 5px;
    padding: 10px 0;
    background: #fff;
  }

  .table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
      height: 15px;
      background: rgb(33, 150, 243);
      width: 5px;
      border-radius: 10px;
      position: relative;
      margin-right: 5px;
    }

    .table-item-text {
      font-weight: bolder;
    }

    .table-item-buttons {
      flex: 1;
      text-align: right;
    }

    .small-text {
      font-size: 12px;
      color: #2196F3;
      margin-left: 10px;
      position: relative;
      top: 2px;
    }
  }
}
</style>