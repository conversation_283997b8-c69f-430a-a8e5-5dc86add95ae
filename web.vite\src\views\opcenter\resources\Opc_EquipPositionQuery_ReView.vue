<template>
  <ModelInfo></ModelInfo>
  <el-tabs @tab-click="handleClick">
    <el-tab-pane :label="this.$ts('Physical Position')" :name="tab1">

      <div class="container">

        <div class="table-item">
          <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">
              {{ this.$ts('Edit') + this.$ts('Physical Position') }}</span>
            <el-button type="success" plain @click="addRow('table1')" color="#f89898">{{ this.$ts('Add')
              }}</el-button>
            <el-button type="info" plain @click="delRow('table1')">{{ this.$ts('Copy') + this.$ts('Add')
              }}</el-button>
            <el-button type="warning" plain @click="delRow('table1')">{{ this.$ts('Delete')
              }}</el-button>
          </div>

          <vol-table ref="table1" index @rowClick="event => rowClick(event, 'table1')" :tableData="tableData_Position"
            :ck="true" :columns="columns_Position" :max-height="500" :pagination-hide="false" :load-key="true"
            :rowKey="rowKey" :column-index="true"></vol-table>
        </div>


      </div>

    </el-tab-pane>

    <el-tab-pane :label="this.$ts('Physical Location')" :name="tab2">


    </el-tab-pane>



  </el-tabs>

</template>



<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTableEx.vue";
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'//先引入
import { v4 as uuidv4 } from 'uuid'; // 安装 uuid 库

//import { de } from 'element-plus/es/locale';
//import Opc_EquipBasicInfoEdit from './Opc_EquipBasicInfoEdit.vue'
export default {
  components: {
    ModelInfo,
    VolHeader,
    VolForm,
    'vol-table': VolTable,
    //  Opc_EquipBasicInfoEdit,
  },
  mixins: [GlobalElMessageBox],
  data() {
    return {

      columns_Position: [
        {
          url: 'api/DropdownList/SearchPosition',
          field: 'PhysicalPositionName', title: this.$ts('Physical Position'), width: 200, align: 'center', edit: { type: "selectTable", keep: true },
          columns: [
            { field: 'PositionName', title: this.$ts('Physical Position'), type: 'string', sort: true }
          ],
          onSelect: (editRows, rows) => {
            // console.log(rows);
            editRows.PhysicalPositionName = rows[0].PositionName;
            this.setPositionDescription(editRows, editRows.PhysicalPositionName);

            // this.CopyCurrent_position(rows[0].PositionName);
          },
          loadBefore: (editRow, params, callback) => {//搜索时设置查询条件

            params.PositionName = editRow.PhysicalPositionName;
            callback(true);
          },

          paginationHide: false,

          sigle: true,

        },
        { field: 'Description', title: this.$ts('Description'), width: 200, align: 'center', edit: { type: "text", keep: true } },
        {
          title: '操作',
          field: '操作',
          width: 150,
          align: 'center',// 'center',
          render: (h, { row, column, index }) => {
            return (
              <div>
                <el-button
                  onClick={($e) => {
                    $e.stopPropagation();
                    this.editClick(row, column, index);
                  }}
                  type="primary"
                  plain
                  style="height:26px; padding: 10px !important;"
                >
                  编辑
                </el-button>

                {/* 通过条件判断,要显示的按钮 */}
                {
                  /*  {
                        index % 2 === 1 
                        ?<el-button>修改</el-button>
                        : <el-button>设置</el-button>
                    } */
                }


                {/* 通过v-show控制按钮隐藏与显示
                  下面的index % 2 === 1换成：row.字段==值 */
                }
                <el-button
                  onClick={($e) => {
                    this.btn2Click(row, $e);
                  }}
                  v-show={index % 2 === 1}
                  type="success"
                  plain
                  style="height:26px;padding: 10px !important;"
                >
                  修改
                </el-button>

                <el-button
                  onClick={($e) => {
                    this.btn2Click(row, $e);
                  }}
                  v-show={index % 2 === 0}
                  type="warning"
                  plain
                  style="height:26px;padding: 10px !important;"
                >
                  设置
                </el-button>

                <el-dropdown
                  onClick={(value) => {
                    this.dropdownClick(value);
                  }}
                  trigger="click"
                  v-slots={{
                    dropdown: () => (
                      <el-dropdown-menu>
                        <el-dropdown-item>
                          <div
                            onClick={() => {
                              this.dropdownClick('京酱肉丝', row, column);
                            }}
                          >
                            京酱肉丝
                          </div>
                        </el-dropdown-item>
                        <el-dropdown-item>
                          <div
                            onClick={() => {
                              this.dropdownClick('驴肉火烧', row, column);
                            }}
                          >
                            驴肉火烧
                          </div>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    )
                  }}
                >
                  <span
                    style="font-size: 13px;color: #409eff;margin: 5px 0 0 10px;"
                    class="el-dropdown-link"
                  >
                    更多<i class="el-icon-arrow-right"></i>
                  </span>
                </el-dropdown>
              </div>
            );
          }
        },

      ],
      tableData_Position: []
    }
  },
watch: {
    // 监听当前激活的 tab
    tableData_Position:function(){
      console.log('tableData_Position');
    }
  },
  mounted() {


  },
  created() {
    this.$nextTick(() => {
      // this.loadPositionTableData();
    });
  },
  methods: {

    async formSubmit(tab) {
      const validateAndProcess = async (postData, apiUrl, proxyArray) => {
        // const valid = await this.$refs.form1.validate();
        // const valid2 = await this.$refs.form.validate();
        let valid = true, valid2 = true;
        if (tab === 'tab1') {
          valid2 = await this.$refs.form.validate();
        } else if (tab === 'tab2') {
          //valid2 = true;
          valid = await this.$refs.form1.validate();
        }
        if (!valid || !valid2) return;
        this.checkAndSubmit(this.tableData, "PhysicalPositionName");
        try {
          const res = await this.http.post(apiUrl, postData);
          console.log(res);
          this.resultMessageStay(res.status, res.message, "Success", "There are already duplicate items, please modify them");
          if (tab === 'tab1') {
            // proxyArray.forEach(array => this.resetItemSource(array, 'Biz_Physical_Position'));
          } else if (tab === 'tab2') {

            // const tab2ProxyArrays = getProxyArrays('Location'); // 假设这是获取tab2数据源数组的方法
            // tab2ProxyArrays.forEach(array => this.resetItemSource(array, 'Biz_PhysicalLocation'));
          }
        } catch (error) {
          this.$message.error(error);
        } finally {
          this.reset();
          if (tab === 'tab2') this.$refs.table1.reset();
        }
      };

      const getProxyArrays = (actionType) => {
        return [
          this[`formRules_${actionType}_Delete`][0][0].data,
          this[`formRules_${actionType}_Edit`][0][0].data,
          this[`formRules_${actionType}_Copy`][0][0].data
        ];
      };

      const actionUrlMap = {
        tab1: {
          add: 'api/Biz_PhysicalPosition/Biz_PhysicalPositionAdd',
          edit: 'api/Biz_PhysicalPosition/Biz_PhysicalPositionEdit',
          copy: 'api/Biz_PhysicalPosition/Biz_PhysicalPositionAdd',
          delete: 'api/Biz_PhysicalPosition/Biz_PhysicalPositionDelete',
        },
        tab2: {
          add: 'api/Biz_PhysicalLocation/Biz_PhysicalLocationAdd',
          edit: 'api/Biz_PhysicalLocation/Biz_PhysicalLocationEdit',
          copy: 'api/Biz_PhysicalLocation/Biz_PhysicalLocationAdd',
          delete: 'api/Biz_PhysicalLocation/Biz_PhysicalLocationDelete',
        }
      };

      let postData = { ...this.formFields_Add, ...this.curFormFields };
      if (tab === 'tab1') {
        postData = this.currentAction === 'edit' ? { ...this.curFormFields } : postData;
        if (this.currentAction === 'copy') {
          postData = {
            ...this.curFormFields,
            PhysicalPositionName: this.curFormFields.NewPhysicalPositionName
          };
        }
      } else if (tab === 'tab2') {
        if (this.currentAction === 'copy') {
          postData = {
            ...this.curFormFields_Location,
            PhysicalLocationName: this.curFormFields_Location.NewPhysicalLocationName,
            PhysicalPositionList: this.tableData
          };
        } else {
          postData = {
            ...this.curFormFields_Location,
            NewPhysicalLocationName: this.curFormFields_Location.NewPhysicalLocationName,
            PhysicalPositionList: this.tableData
          };
        }
      }
      console.log(postData, 'postData_TAB2');
      console.log(this.curFormFields_Location.NewPhysicalLocationName, 'this.curFormFields_Location.NewPhysicalLocationName');
      const proxyArrays = getProxyArrays(tab === 'tab1' ? 'Position' : 'Location');
      const apiUrl = actionUrlMap[tab][this.currentAction];
      console.log(apiUrl, 'apiUrl');
      await validateAndProcess(postData, apiUrl, proxyArrays);

    },
    setPositionDescription(editRow, value) {
      let postData_Condition = {
        PhysicalPositionName: value, // 表单
      };
      this.http.post('api/Biz_PhysicalPosition/Biz_PhysicalPositionSearch', postData_Condition).then(res => {
        if (res.rows && res.rows.length > 0) {
          editRow.Description = res.rows[0].Description;

        }
      });

    },
    rowKey(row) {
      return row.id; // 使用唯一标识符作为 row-key
    },
    loadPositionTableData() {
      this.$refs.table1.reset();

      this.http.post('api/DropdownList/SearchPosition', {}).then(res => {
        if (res.rows && res.rows.length > 0) {
          // this.tableData_Position = res.rows;
          this.tableData_Position = res.rows.map(item => ({
            ...item,
            id: uuidv4() // 生成唯一标识符
          }));
        } else {
          // 处理数据不符合预期的情况
          //this.$message.error(res.error);
        }
      }).catch(error => {
        //this.$message.error(error);
      });
    },

    reset() {
      this.$refs.form1.reset();
      this.$refs.form.reset();
      //this.$Message.success("表单已重置");
    },
    addRow(tableRef) {

      const newRow = { /* 新行的数据 */ };
      if (tableRef === 'table1') {
        console.log(this.$refs.table1, 'table1');
        this.$refs.table1.addRow(newRow)
      }
    },
    delRow(tableRef) {

      if (tableRef === 'table1') {
        this.$refs.table1.delRow();
        // this.$refs.table1.reset();
        // this.$refs.table1.clearSelection();
      }

    },
    rowClick(event, tableRef) {
      // 从event参数中解构出row, column
      const { row, column } = event;
      // 现在可以根据tableRef执行特定操作
      if (tableRef === 'table1') {
        console.log(event, 'row');
        this.$refs.table1.$refs.table.toggleRowSelection(row);
      }
    },

  }
};
</script>
<style lang="less" scoped>
.container {
  padding: 10px;
  background: #F3F7FC;

  .form-content {
    border-radius: 5px;
    padding: 10px 0;
    background: #fff;
  }

  .table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
      height: 15px;
      background: rgb(33, 150, 243);
      width: 5px;
      border-radius: 10px;
      position: relative;
      margin-right: 5px;
    }

    .table-item-text {
      font-weight: bolder;
    }

    .table-item-buttons {
      flex: 1;
      text-align: right;
    }

    .small-text {
      font-size: 12px;
      color: #2196F3;
      margin-left: 10px;
      position: relative;
      top: 2px;
    }
  }
}
</style>