<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>状态</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchStatus" clearable filterable placeholder="请选择" style="width: 100px">
						<el-option v-for="item in status" :key="item.Name" :label="item.Name" :value="item.Value" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>检验单号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchFQCNumber" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getFQCNumber">
						<el-option v-for="item in FQCNumbers" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>检验时间</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
						end-placeholder="结束" :size="size" style="width: 260px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss" />
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">不合格品信息</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="masterTable" index :tableData="masterTableData" @rowClick="rowClick" :columns="masterColumns"
			:height="360" :pagination-hide="false" :load-key="true" :column-index="true" :single="true"
			:url="apiUrl.getNPC" @loadBefore="masterLoadBefore" @loadAfter="masterLoadAfter" :defaultLoadPage="false"
			:ck="true"></vol-table>
	</div>
	<div class="table-item">
		<div style="margin-left: 10px;margin-top: 10px;">
			<label style="width: 100px; margin-left: 5px; font-size: 16px">
				<span>处理方式</span>
			</label>
			<div style="margin-top: 5px;">
				<el-radio-group v-model="processingMethod" style="background-color:lightcyan;">
					<el-radio value="1" size="large">特采</el-radio>
					<el-radio value="2" size="large">重工</el-radio>
					<el-radio value="3" size="large">报废</el-radio>
				</el-radio-group>
			</div>
		</div>
		<div style="margin-left: 10px">
			<label style="width: 100px; margin-left: 5px; font-size: 16px">
				<span>备注</span>
			</label>
			<div style="margin-top: 5px">
				<el-input v-model="notes" autosize :rows="2" type="textarea" style="width: 600px" ></el-input>
				<el-button style="margin-left: 10px;" type="success" icon="Check" @click="submit" plain>提交</el-button>
			</div>
		</div>
	</div>
	<div style="margin-left: 30px;">
	</div>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			//主页面
			searchMfgOrder: null,
			searchFQCNumber: null,
			searchTxnDate: null,
			searchStatus: null,
			status: [{ Name: '未处理', Value: '0' }, { Name: '已处理', Value: '1' }],
			mfgorders: [],
			FQCNumbers: [],
			masterTableData: [],
			tempMasterTableData: [],
			processingMethod: "1",
			notes: null,

			masterColumns: [
				{ field: 'CDOID', title: '建模ID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'CDOName', title: '检验单号', type: 'string', width: 100, align: 'center' },
				{ field: 'MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
				{ field: 'Product', title: '产品编码', type: 'string', width: 100, align: 'center' },
				{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
				{ field: 'Customer', title: '客户编码', type: 'string', width: 80, align: 'center' },
				{ field: 'MfgOrderQty', title: '工单数量', type: 'string', width: 80, align: 'center' },
				{ field: 'InventoryInspectionQty', title: '批量数', type: 'string', width: 60, align: 'center' },
				{ field: 'AQLQty', title: '抽样数', type: 'string', width: 60, align: 'center' },
				{
					field: 'Status', title: '状态', type: 'string', width: 60, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						if (row.Status == '已处理') {
							return { background: "#2196F3", color: "#fff" };
						}
					},
				},
				{
					field: 'IAResult', title: '综合判定', type: 'string', width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.IAResult) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{ field: 'CreateDate', title: '入库时间', type: 'datetime', width: 150, align: 'center' },
				{ field: 'InspectionDate', title: '检验时间', type: 'datetime', width: 150, align: 'center' },
				{ field: 'Inspector', title: '检验人', type: 'string', width: 80, align: 'center' },
				{ field: 'Notes', title: '检验备注', type: 'string', width: 200, align: 'center' },
				{
					field: 'OutCome', title: '处理结果', type: 'string', width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.OutCome) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{ field: 'NCPDate', title: '处理时间', type: 'datetime', width: 150, align: 'center' },
				{ field: 'NCPEmployee', title: '处理人', type: 'string', width: 80, align: 'center' },
				{ field: 'NCPNotes', title: '处理备注', type: 'string', width: 200, align: 'center' },
				{ field: 'ProductType', title: '产品类别', hidden: true, type: 'string', width: 200, align: 'center' },
			],

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/GetRevisionObject",
				getNameObject: "/api/query/GetNameObject",
				getNPC: '/api/query/getNPC',
				inventoryInspectionOrderMaint: '/api/cdo/inventoryInspectionOrderMaint',
			}
		}
	},
	created() {

	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	methods: {
		getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getFQCNumber(query) {
			if (query) {
				let params = {
					cdo: "W_INVENTORYINSPECTIONORDER",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.FQCNumbers = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		reset() {
			this.searchMfgOrder = null;
			this.searchFQCNumber = null;
			this.searchTxnDate = null;
			this.notes = null;
			this.masterTableData = [];
			this.tempMasterTableData = [];
			this.$refs.masterTable.rowData = [];
			this.$refs.masterTable.paginations.total = 0;
		},
		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			this.$refs.masterTable.$refs.table.toggleRowSelection(row);
		},
		queryRow() {
			if (this.searchMfgOrder == null
				&& this.searchStatus == null
				&& this.searchFQCNumber == null
				&& this.searchTxnDate == null) {
				this.$message.error('请输入查询条件');
				return;
			}
			this.masterTableData = [];
			this.tempMasterTableData = [];
			this.$refs.masterTable.rowData = [];
			this.$refs.masterTable.paginations.total = 0;
			this.$refs.masterTable.load(null, true);
		},
		masterLoadBefore(params, callBack) {
			params["MfgOrder"] = this.searchMfgOrder != null ? this.searchMfgOrder : null;
			params["FQCNumber"] = this.searchFQCNumber != null ? this.searchFQCNumber : null;
			params["Status"] = this.searchStatus != null ? this.searchStatus : null;
			params["StartTime"] = this.searchTxnDate != null ? this.searchTxnDate[0] : null;
			params["EndTime"] = this.searchTxnDate != null ? this.searchTxnDate[1] : null;
			callBack(true)
		},
		masterLoadAfter(rows, callBack, result) {
			if (result.Result == 1) {
				//this.columns = result.Data.colums;
				this.masterTableData = result.Data.tableData;
				this.tempMasterTableData = result.Data.tableData;
				this.$refs.masterTable.rowData = result.Data.tableData;
				this.$refs.masterTable.paginations.total = result.Data.total;
			}
			else {
				this.$message.error(result.Message);
			}
			callBack(false);
		},
		outputRow() {
			// this.tableData.splice(0);
			//导出
			let tableData = this.$refs.masterTable.tableData;
			let sortData = this.$refs.table.filterColumns;
			let exportData = this.handleTableSortData(tableData, sortData);
			Excel.exportExcel(exportData, "NCP" + '-' + this.base.getDate());
		},
		handleTableSortData(tableData, sortData) {
			let newArray = [];
			tableData.forEach(data => {
				let newItem = {};
				sortData.forEach(field => {
					if (data.hasOwnProperty(field.field)) {
						newItem[field.title] = data[field.field];
					}
				});
				newArray.push(newItem);
			});
			return newArray
		},
		getCurrentDateTimeString() {
			const now = new Date();
			const year = now.getFullYear();
			const month = this.padNumber(now.getMonth() + 1);
			const day = this.padNumber(now.getDate());
			const hours = this.padNumber(now.getHours());
			const minutes = this.padNumber(now.getMinutes());
			const seconds = this.padNumber(now.getSeconds());

			return `${year}${month}${day}${hours}${minutes}${seconds}`;
		},
		padNumber(num) {
			return num < 10 ? '0' + num : num;
		},
		submit(){
			const rows = this.$refs.masterTable.getSelected();
			if (!rows.length) {
				this.$message.error('请选中即将提交的数据')
				return;
			}
			if (rows[0].Status == '已处理'){
				this.$message.error('选中的数据已处理，请重新选择。')
				return;
			}
			let params = {
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				CDOName: rows[0].CDOName,
				Type: 'NCP',
				OutCome: this.processingMethod,
				NCPNotes: this.notes,
			};
			this.http.post(this.apiUrl.inventoryInspectionOrderMaint, params, true).then(res => {
				if (res.Result == 1) {
					this.reset();
					this.$message.success(res.Message);
				} else {
					this.$message.error(res.Message);
				}
			});
		}
	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	// .table-item-text {
	// 	font-weight: bolder;
	// 	border-bottom: 1px solid #0c0c0c;
	// }
	.table-item-text {
		margin-top: 3px;
		padding-bottom: 6px;
		font-weight: bold;
		font-size: 15px;
		color: #484848;
		white-space: nowrap;
		border-bottom: 2px solid #676767;
		margin-bottom: -1px;
		letter-spacing: 1px;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196f3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>