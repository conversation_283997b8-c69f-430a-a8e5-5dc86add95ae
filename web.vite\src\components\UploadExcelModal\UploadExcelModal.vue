<template>
  <vol-box :lazy="true" v-model="visible" :title="title" :width="width" :height="height" :padding="5"
    :onModelClose="onModelClose">
    <div class="upload-container">

      <div class="button-group">

        <div class="upload-group">
          <el-upload style="float: left" action="#" :limit="limit" accept=".xlsx, .xls" ref="uploadFile"
            :show-file-list="showFileList" :max-size="maxSize" :before-upload="beforeUpload"
            :http-request="handleUpload">
            <el-button size="small"><i class="el-icon-folder-opened"></i>{{ this.$ts('选择文件') }}</el-button>
          </el-upload>
        </div>

        <!-- <el-button style="margin-left: 10px" type="primary" size="small" :loading="loadingStatus" @click="exportExcel">
          <i class="el-icon-bottom"></i> {{ this.$ts('下载模板') }} </el-button> -->

        <el-button style="margin-left: 10px" type="success" size="small" @click="uploadClick" :loading="loadingStatus">
          <i class="el-icon-top"></i> {{ this.$ts('上传文件') }} </el-button>

      </div>

      <div class="alert">
        <el-alert :title="this.$ts('只能上传excel文件,文件大小不超过5M')" type="warning" :closable="false" show-icon></el-alert>
        <div v-html="desc"></div>
      </div>

      <div v-if="file">
        <h3>{{ this.$ts('文件列表') }}</h3>
        <div class="file-info">
          <span>{{ this.$ts('文件名') }}：{{ file.name }}</span>
          <span>{{ this.$ts('大小') }}{{ (file.size / 1024).toFixed(2) }}KB</span>
        </div>
      </div>

      <div v-show="message" class="v-r-message">
        <h3 class="title">{{ this.$ts('上传结果') }}</h3>
        <div class="text" :class="resultClass" v-html="message"></div>
      </div>

    </div>

  </vol-box>
</template>
<script lang="jsx">
import VolBox from '@/components/basic/VolBox.vue';
import * as XLSX from 'xlsx';

export default {
  components: {
    'vol-box': VolBox,
  },
  data() {
    return {
      title: this.$ts('UploadExcel'),
      width: 600,
      height: 250,
      visible: false,
      maxSize: 102 * 5,
      model: true,
      file: null,
      loadingStatus: false,
      message: "",
      resultClass: "",
      limit: 1,
      showFileList: true,
      desc: '',
      importUrl: {
        url: '/api/sys_user/import'
      },
    }
  },
  methods: {

    exportExcel() {

    },

    handleUpload(data) {
      this.loadingStatus = true;
      let params = data
      this.http.post(this.importUrl.url, params).then(res => {
        console.log(res, 'res')
        if (res.status) {

          this.file = null;
          this.message = res.message;
        } else {
          this.loadingStatus = false;
          this.$Message.error(res.message)
        }
        this.resultClass = res.status ? "v-r-success" : "v-r-error";
      }).finally(() => {
        this.loadingStatus = false;
      })
    },

    uploadClick() {
      let _url = this.importUrl.url;
      if (!_url) {
        return this.$Message.error(this.$ts("没有配置好Url"));
      }
      if (!this.file) {
        return this.$Message.error(this.$ts(['请选择', "文件"]))
      }
      this.Upload(this.file)
    },

    async Upload(rawFile) {
      // 3.拿到了excel文件中的数据存到数组中
      let excelData = await this.analysisExcel(rawFile);
      // console.log(excelData, 'excelData')
      this.$emit('echo', excelData)
      // this.handleUpload(excelData)
      return true;
    },

    analysisExcel(file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader();
        reader.onload = function (e) {
          const data = e.target && e.target.result;
          // 2.data为readAsBinaryString转成的值，所以type需要保持一致
          //返回的datajson为WordBook对象
          let datajson = XLSX.read(data, {
            type: 'binary',
          });
          const sheetName = datajson.SheetNames[0];
          const result = XLSX.utils.sheet_to_json(datajson.Sheets[sheetName]);
          resolve(result);
        };
        // 1.读取文件为二进制格式
        reader.readAsBinaryString(file);
      });
    },

    getFileType() {
      let fileName = this.file.name.split(".").pop().toLocaleLowerCase() || "";
      if (["numbers", "csv", "xls", "xlsx"].indexOf(fileName) == -1) {
        this.$Message.error(this.$ts("只能选择excel文件"));
        return false;
      }
      return true;
    },

    beforeUpload(file) {
      this.file = file;
      if (!this.getFileType()) {
        return false;
      }
      return false;
    },

    show() {
      this.visible = true
    },

    onModelClose() {
      this.closeModel();
    },

    closeModel() {
      this.$emit('ok')
      this.visible = false;
    },

  }
}
</script>
<style lang="less" scoped>
.upload-container {
  // min-height: 276px !important;
  display: inline-block;
  width: 100%;
  padding: 10px;
  border: 1px dashed #989898;
  min-height: 250px;
  border-radius: 5px;

  .alert {
    margin-top: 12px;
  }

  .button-group {
    .upload-group {
      width: 100px;
    }
  }

  .el-button-group>* {
    display: flex;
    justify-content: center;
  }

  h3 {
    margin: 9px 0px;
  }

  .file-info>span {
    margin-right: 20px;
  }

  .v-r-message {
    margin-top: 10px;

    .title {
      margin-bottom: 2px;
    }

    >.text {
      font-size: 13px;
    }

    .v-r-success {
      color: #02b702;
    }

    .v-r-error {
      color: #dc0909;
    }
  }
}
</style>