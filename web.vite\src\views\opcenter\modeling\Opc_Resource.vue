<template>
  <div class="page-Container">
    <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules">
      <div style="text-align: end; margin-top: 0px; width: 80%">
        <div style="margin-right: 20px; margin-top: -39px">
          <el-button type="primary" plain @click="search">查询</el-button>
          <el-button type="success" icon="Plus" @click="addRow" plain>新建</el-button>
          <el-button type="primary" icon="Edit" @click="editRow" plain>编辑</el-button>
          <el-button type="danger" icon="Delete" @click="delRow" plain>删除</el-button>
          <el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
        </div>
      </div>
    </VolForm>
    <vol-table ref="table" index :column-index="true" :loadKey="true"
      :columns="columns" :tableData="tableData" :ck="true" :pagination-hide="false" :max-height="380"
      :url="ApiUrl.QueryResourceInfo" :defaultLoadPage="false" @loadBefore="loadBefore" :single="true"
      @loadAfter="loadAfter"></vol-table>
  </div>
  <vol-box ref="box" title="设备维护" :lazy="true" v-model="showEdit" :width="1500" :height="800">
    <VolForm ref="boxform" :loadKey="true" :formFields="boxFields" :formRules="boxRules">
    </VolForm>
    <div style="text-align: center; margin-top: 100px">
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
  </vol-box>
</template>

<script lang="jsx">
import VolTable from '@/components/basic/VolTable.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolHeader from '@/components/basic/VolHeader.vue'
import VolBox from '@/components/basic/VolBox.vue'
import { mapState } from 'vuex'
export default {
  components: {
    VolHeader,
    VolForm,
    'vol-table': VolTable,
    'vol-box': VolBox
  },
  computed: {
    ...mapState({
      //获取当前用户的信息
      userInfo: (state) => state.userInfo,
      //获取当前用户的权限
      permission: (state) => state.permission
    })
  },
  data() {
    return {
      ApiUrl: {
        QueryResourceInfo: '/api/Query/QueryResourceInfoDetails', //查询设备台账信息
        MesResourceMaintenance: '/api/CDO/MesResourceMaintenance', //设备维护
        GetNameObject: "/api/query/getNameObject", //获取下拉框
      },
      showEdit: false, //编辑框是否显示
      headerFields: {
        cdoName: 'Resource',
        ResourceName: null, //设备编号
        LocationName: null, //存放地点
        // CreatUser: null, //创建人
        StarchTime: null,
      },
      headerRules: [
        [
          {
            title: this.$ts('设备编号'),
            placeholder: this.$ts(''),
            required: false,
            field: 'ResourceName',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('存放地点'),
            placeholder: this.$ts(''),
            required: false,
            field: 'LocationName',
            type: 'string',
            colSize: 2
          }
        ],
        [
          {
            title: this.$ts('选择时间'),
            placeholder: this.$ts(''),
            required: false,
            field: 'StarchTime',
            type: 'datetime',
            range: true,
            colSize: 4
          }
        ]
      ],
      columns: [
        { title: this.$ts('ID'), field: 'ResourceID', width: 100, align: 'center', hidden: true, type: 'string' },
        { field: 'ResourceName', title: '设备编号', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'Model', title: '型号', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'ProducType', title: '产品系列', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'EquipmentFunction', title: '设备功能', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'UsageStatus', title: '使用状态', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'EquipmentFamily', title: '设备族', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'AssetNumber', title: '资产编号', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'PartNumber', title: '部件编号', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'Developmenttime', title: '开发时间', type: 'dateTime', width: 130, align: 'center', hidden: false },
        { field: 'DesignCapacity', title: '设计产能', type: 'string', width: 130, align: 'center', hidden: false },
        { 
          field: 'KeyProcess', 
          title: '关键过程设备标星', 
          type: 'string', 
          width: 130, 
          align: 'center', 
          hidden: false, 
          formatter: (row) => {
            return row.KeyProcess ? '是' : '否';
          }
        },
        { field: 'Factory', title: '工厂', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'Storagelocation', title: '存放地点', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'ResourceDescription', title: '设备名称', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'Specification', title: '适用规格', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'MachineParts', title: '机器互换件', type: 'string', width: 130, align: 'center', hidden: false },
        { field: 'Comments', title: '备注', type: 'string', width: 130, align: 'center', hidden: false }
      ],
      tableData: [],

      boxFields: {
        User: null,
        Password:null,
        EventName: null, //事件名称
        ResourceID: null, //ID
        ResourceName: null, //设备编号
        NewResourceName: null, //新设备编号
        Model: null, //型号
        ProducType: null, //产品系列
        EquipmentFunction: null, //设备功能
        UsageStatus: null, //使用状态
        EquipmentFamily: null, //设备族
        AssetNumber: null, //资产编号
        PartNumber: null, //部件编号
        Developmenttime: null, //开发时间
        DesignCapacity: null, //设计产能
        KeyProcess: null, //关键过程设备标星
        Factory: null, //工厂
        Storagelocation: null, //存放地点
        ResourceDescription: null, //设备名称
        Specification: null, //适用规格
        MachineParts: null, //机器互换件
        Comments: null, //备注
      },
      boxRules: [
        [
          {
            title: this.$ts('设备编号'),
            placeholder: this.$ts(''),
            field: 'ResourceName',
            type: 'string',
            colSize: 2,
            readonly: true,
            hidden: true,
          },
          {
            title: this.$ts('设备编号'),
            placeholder: this.$ts(''),
            field: 'NewResourceName',
            type:'string',
            colSize: 2, 
            required: true,
          },
          {
            title: this.$ts('型号'),
            placeholder: this.$ts(''),
            required: false,
            field: 'Model',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('产品系列'),
            placeholder: this.$ts(''),
            required: false,
            field: 'ProducType',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('设备功能'),
            placeholder: this.$ts(''),
            required: false,
            field: 'EquipmentFunction',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('使用状态'),
            placeholder: this.$ts(''),
            required: false,
            field: 'UsageStatus',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('设备家族族'),
            placeholder: this.$ts(''),
            required: false,
            field: 'EquipmentFamily',
            type: 'select',
            colSize: 2,
            data: [
            ]
          }
        ],
        [
          {
            title: this.$ts('资产编号'),
            placeholder: this.$ts(''),
            required: false,
            field: 'AssetNumber',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('部件编号'),
            placeholder: this.$ts(''),
            required: false,
            field: 'PartNumber',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('开发时间'),
            placeholder: this.$ts(''),
            required: false,
            field: 'Developmenttime',
            type: 'datetime',
            colSize: 2
          },
          {
            title: this.$ts('设计产能'),
            placeholder: this.$ts(''),
            required: false,
            field: 'DesignCapacity',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('关键过程设备标星'),
            placeholder: this.$ts(''),
            required: false,
            field: 'KeyProcess',
            type: 'select',
            colSize: 2,
            data: [
              { key:true,value: true, label: '是' },
              { key:false,value: false, label: '否' },
            ]
          },
          {
            title: this.$ts('工厂'),
            placeholder: this.$ts(''),
            required: false,
            field: 'Factory',
            type: 'select',
            colSize: 2,
            data:[],
          }
        ],
        [
          {
            title: this.$ts('存放地点'),
            placeholder: this.$ts(''),
            required: false,
            field: 'Storagelocation',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('设备名称'),
            placeholder: this.$ts(''),
            required: false,
            field: 'ResourceDescription',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('适用规格'),
            placeholder: this.$ts(''),
            required: false,
            field: 'Specification',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('机器互换件'),
            placeholder: this.$ts(''),
            required: false,
            field: 'MachineParts',
            type: 'string',
            colSize: 2
          },
          {
            title: this.$ts('备注'),
            placeholder: this.$ts(''),
            required: false,
            field: 'Comments',
            type: 'string',
            colSize: 2
          }
        ]
      ]
    }
  },
//   修改后（使用 async/await 等待数据）
  async mounted() {
    // 获取设备家族数据
    const resourceFamilyData = await this.getNameObject("Resourcefamily");
    this.boxRules[0][6].data = resourceFamilyData; // 赋值给设备家族下拉框

    // 获取工厂数据（可选，根据需求）
    const factoryData = await this.getNameObject("Factory");
    this.boxRules[1][5].data = factoryData; // 赋值给工厂下拉框

    console.log('设备家族数据:', resourceFamilyData);
  },
  methods: {
    search() {
      this.$refs.table.load(null, true)
    },
    loadBefore(params, callBack) {
      let param = {
        startDate: this.headerFields.StarchTime && this.headerFields.StarchTime[0] ? this.headerFields.StarchTime[0] : null,
        endDate: this.headerFields.StarchTime && this.headerFields.StarchTime[1] ? this.headerFields.StarchTime[1] : null,
        ResourceName: this.headerFields.ResourceName,
        Location: this.headerFields.LocationName,
        cdoName: 'Resource',
        rows: this.$refs.table.paginations.size,
        page: this.$refs.table.paginations.page
      }
      params = Object.assign(params, param)
      callBack(true)
    },
    loadAfter(rows, callBack, result) {
      if (result.Result == 1) {
        //this.columns = result.Data.colums;
        // this.tableData = result.Data.tableData
        this.$refs.table.rowData = result.Data.tableData
        this.$refs.table.paginations.total = result.Data.total
      } else {
        this.$message.error(result.Message)
      }
      callBack(false)
    },
    addRow() {
      this.boxReset();
      this.boxFields.EventName = 'add';
      this.boxFields.User = this.userInfo.userName;
      this.boxFields.Password = this.userInfo.userPwd;
      this.showEdit = true;
    },
    editRow() {
      this.boxReset();
      let rows = this.$refs.table.getSelected();
      if (rows.length == 0) {
        this.$message.error('请选择一条数据进行编辑！');
        return;
      } 
      if (rows.length > 1) {
        this.$message.error('只能选择一条数据进行编辑！');
        return;
      }
      this.boxFields.User = this.userInfo.userName;
      this.boxFields.Password = this.userInfo.userPwd;
      this.boxFields.EventName = 'load';
      this.boxFields.ResourceID = rows[0].Id;
      this.boxFields.ResourceName = rows[0].ResourceName;
      this.boxFields.NewResourceName = rows[0].ResourceName;
      this.boxFields.Model = rows[0].Model;
      this.boxFields.ProducType = rows[0].ProducType;
      this.boxFields.EquipmentFunction = rows[0].EquipmentFunction;
      this.boxFields.UsageStatus = rows[0].UsageStatus;
      this.boxFields.EquipmentFamily = rows[0].EquipmentFamily;
      this.boxFields.AssetNumber = rows[0].AssetNumber;
      this.boxFields.PartNumber = rows[0].PartNumber;
      this.boxFields.Developmenttime = rows[0].Developmenttime;
      this.boxFields.DesignCapacity = rows[0].DesignCapacity;
      this.boxFields.KeyProcess = rows[0].KeyProcess;
      this.boxFields.Factory = rows[0].Factory;
      this.boxFields.Storagelocation = rows[0].Storagelocation;
      this.boxFields.ResourceDescription = rows[0].ResourceDescription;
      this.boxFields.Specification = rows[0].Specification;
      this.boxFields.MachineParts = rows[0].MachineParts;
      this.boxFields.Comments = rows[0].Comments;
      this.showEdit = true;
    },
    boxReset(){
    for (let key in this.boxFields) {
      if (this.boxFields.hasOwnProperty(key)) {
        this.boxFields[key] = null;
      }
    }
    },
    submit() {

      if (this.boxFields.NewResourceName == null || this.boxFields.NewResourceName == '') {
        return this.$message.error('设备名称不能为空');
      }
      this.http.post(this.ApiUrl.MesResourceMaintenance, this.boxFields,true).then((res) => {
            if (res.status == 1) {
              this.$message.success(res.message);
              this.showEdit = false;
              this.$refs.table.load(null, true); 
            }
            else {
              this.$message.error(res.message); 
            } 
          }) 
    },
    delRow() {
      let rows = this.$refs.table.getSelected();
      if (rows.length == 0) {
        this.$message.error('请选择一条数据进行删除！');
        return;
      }
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning' 
      }) 
      .then(() => {
        let params=rows[0];
        params.EventName = 'delete';
        params.User = this.userInfo.userName;
        params.Password = this.userInfo.userPwd;
        this.http.post(this.ApiUrl.MesResourceMaintenance, params,true).then((res) => {
          if (res.status == 1) {
            this.$message.success(res.message);
            this.$refs.table.load(null, true);
          }
          else {
            this.$message.error(res.message);
          }
        }) 
      })
    },
    async getNameObject(cdoName) {
      const params = { cdo: cdoName };
      try {
        const res = await this.http.get(this.ApiUrl.GetNameObject, params);
        if (res.Result === 1) {
          return res.Data.map(item => ({
            label: item.Name,
            value: item.Name,
            key: item.Name
          }));
        } else {
          this.$message.error(res.Message);
          console.log(res.Message);
          return [];
        }
      } catch (error) {
        this.$message.error('接口请求失败');
        console.error(error);
        return [];
      }
    },
  }
}
</script>