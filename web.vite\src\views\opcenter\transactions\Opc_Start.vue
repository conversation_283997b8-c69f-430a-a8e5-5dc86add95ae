<template>
	<div class="page-header" style="height: 20%;">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px;">
			<div style="margin-left: 10px;">
				<label style="width: 240px; margin-left: 5px; font-size: 16px;">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px;">
                    <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder"	:loading="loading">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>产品编码</span>
				</label>
				<div style="margin-top: 5px;">
					<el-select v-model="searchProduct" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getProduct"	:loading="loading">
						<el-option v-for="item in products" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
            <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>模具编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchTool" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getTool" :loading="loading">
						<el-option v-for="item in tools" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
            <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>组别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchGroup" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getGroup" :loading="loading">
						<el-option v-for="item in employeeGroups" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>工单计划日期</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
				</div>
			</div>
		</div>
		<div class="table-item-header">
			<div class="table-item-border"></div> <span class="table-item-text">工单信息</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="getStartMfgOrder" plain>查询</el-button>
					<el-button icon="Refresh" type="info" plain @click="reset">重置</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="mfgTable" index :tableData="mfgTableData" @rowClick="mfgRowClick" :columns="mfgColumns"
			:height="200" :pagination-hide="false" :load-key="false" :column-index="true" :single="true"
            :url="apiUrl.getStartMfgOrder" @loadBefore="mfgLoadBefore" @loadAfter="mfgLoadAfter" :defaultLoadPage="false"
            :ck="false">
        </vol-table>
	</div>
	<div class="table-item">
		<div class="table-item-header">
			<div class="table-item-border"></div> 
            <span class="table-item-text">创批信息</span>
            <div style="margin-left: 100px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>工单</span></label>
                <div style="margin-top: 5px">
                    <el-input v-model="infoMfgOrder" disabled></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>工单数量</span></label>
                <div style="margin-top: 5px">
                    <el-input v-model="infoMfgOrderQty" disabled></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>批次数量</span></label>
                <div style="margin-top: 5px">
                    <el-input v-model="infoContainerQty"
                    type="number" @input="
						form.ekHour = form.ekHour
							.replace(/[^\d|\.]/g, '')
							.replace(/^00/g, '0')
							.replace(/^\./g, '0.')
						"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>打印机</span></label>
                <div style="margin-top: 5px">
					<el-select v-model="infoPrinter" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getPrinter" :loading="loading">
						<el-option v-for="item in printers" :key="item.Name" :label="item.Name" :value="item.Description" />
					</el-select>
                </div>
            </div>
			<div class="table-item-buttons">
                <el-button type="success" icon="Check" @click="start" plain>创批</el-button>
                <el-button type="primary" icon="printer" @click="rePrint" plain>补打印</el-button>
			</div>
		</div>
		<vol-table ref="containerTable" index :tableData="containerTableData" @rowClick="containerRowClick" :columns="containerColumns"
			:url="apiUrl.getStartContainer" @loadBefore="containerLoadBefore" @loadAfter="containerLoadAfter" :defaultLoadPage="false"
            :height="200" :pagination-hide="false" :load-key="false" :column-index="true" :single="true" :ck="false">
        </vol-table>
	</div>
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
    import VolForm from "@/components/basic/VolForm.vue";
    import VolHeader from "@/components/basic/VolHeader.vue";
	import { mapState } from 'vuex';

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox,
            VolForm,
            VolHeader,
		},
		data() {
			return {
				//搜索框字段
				searchMfgOrder:'',
				searchProduct:'',
                searchTool:'',
                searchGroup:'',
				searchPlanDate:null,

				mfgorders: [],
				products: [],
				tools: [],
				employeeGroups: [],

                mfgTableData: [],
				containerTableData: [],

                infoMfgOrder: null,
                infoMfgOrderQty: null,
                infoContainerQty: null,
                infoPrinter: null,
                printers: [],

				mfgColumns: [
					{ field: 'MfgOrder', title: '工单', type: 'string', width: 130, align: 'center' },
					{ field: 'Qty', title: '工单数量', type: 'int', width: 80, align: 'center' },
					{ field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center' },
					{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
					{ field: 'PlannedStartDate', title: '计划开始日期', type: 'string', width: 150, align: 'center' },
					{ field: 'PlannedCompletionDate', title: '计划结束日期', type: 'string', width: 150, align: 'center' },
					// { field: 'Tool', title: '模具', type: 'string', width: 100, align: 'center' },
					{ field: 'EmployeeGroup', title: '组别', type: 'string', width: 100, align: 'center' },
					// { field: 'IsDispatch', title: '是否派工', type: 'string', width: 80, align: 'center' },
					{ field: 'Notes', title: '备注', type: 'string', width: 120, align: 'center' }
				],
				containerColumns: [
					{ field: 'MfgOrder', title: '工单', type: 'string', width: 130, hidden: true, align: 'center' },
					{ field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center' },
					{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
					{ field: 'Container', title: '批次码', type: 'string', width: 130, align: 'center' },
					{ field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
					// { field: 'IsPrint', title: '是否打印', type: 'string', width: 80, align: 'center' },
					// { field: 'PrintCount', title: '打印次数', type: 'string', width: 80, align: 'center' },
					{ field: 'Operator', title: '创建人', type: 'string', width: 100, align: 'center' },
					{ field: 'CreateDate', title: '创建时间', type: 'string', width: 150, align: 'center' },
					{ field: 'LabelHistorySummaryId',hidden:true, title: '打印id', type: 'string', width: 150, align: 'center' },
				],

				//创建窗口字段
				ActionFlag: 0,
				Info_WorkCenter: null,
				Info_ReleaseQty: null,
				Info_PlanDate: null,
				Info_Priority: null,
				Info_Notes: null,

				//接口地址
				apiUrl: {
                    getRevisionObject: "/api/query/GetRevisionObject",
                    getNameObject: "/api/query/GetNameObject",
					getStartMfgOrder: '/api/query/getStartMfgOrder',
					getStartContainer: '/api/query/getStartContainer',
					getDefaultPrinter: '/api/query/getDefaultPrinter',
					start: '/api/CDO/start',
					ERPReprint: '/api/CDO/ERPReprint',
				},
			}
		},
		created() {
			this.getDefaultPrinter();
		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
            getMfgOrder(query) {
                if (query) {
                    let params = {
                        cdo: "mfgorder",
                        name: query
                    };
                    this.http.get(this.apiUrl.getNameObject, params).then(res => {
                        if (res.Result == 1) {
                            this.mfgorders = res.Data;
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },
            getProduct(query) {
                if (query) {
                    let params = {
                        cdo: "product",
                        name: query
                    };
                    this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                        if (res.Result == 1) {
                            this.products = res.Data;
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },
            getTool(query) {
                if (query) {
                    let params = {
                        cdo: "resource",
                        name: query,
                        objectCategory: "TOOL"
                    };
                    this.http.get(this.apiUrl.getNameObject, params).then(res => {
                        if (res.Result == 1) {
                            this.tools = res.Data;
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },
            getGroup(query) {
                if (query) {
                    let params = {
                        cdo: "employeegroup",
                        name: query,
                    };
                    this.http.get(this.apiUrl.getNameObject, params).then(res => {
                        if (res.Result == 1) {
                            this.employeeGroups = res.Data;
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },
            getPrinter(query) {
                if (query) {
                    let params = {
                        cdo: "PrintQueue",
                        name: query
                    };
                    this.http.get(this.apiUrl.getNameObject, params).then(res => {
                        if (res.Result == 1) {
                            this.printers = res.Data;
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },
			getDefaultPrinter(){
				if (this.userInfo.userName) {
                    let params = {
                        username:  this.userInfo.userName
                    };
                    this.http.post(this.apiUrl.getDefaultPrinter, params).then(res => {
                        if (res.Result == 1) {
                            this.printers = [{ Name: res.Data.Printer, Description: res.Data.Description }];
                            this.infoPrinter = res.Data.Description; // 设置默认选中第一个打印机
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
			},
			reset() {				
				this.searchMfgOrder = '',
				this.searchProduct = '',
				this.searchTool = '',
				this.searchGroup = '',
				this.searchPlanDate = null,
                this.infoMfgOrder='',
                this.infoMfgOrderQty='',
                this.infoContainerQty='',
                this.infoPrinter='',
				this.mfgTableData = [],
				this.containerTableData = [],
				this.$refs.mfgTable.rowData = [];
				this.$refs.mfgTable.paginations.total = 0;
				this.$refs.containerTable.rowData = [];
				this.$refs.containerTable.paginations.total = 0;
			},
			getStartMfgOrder() {
                if (!this.searchMfgOrder 
                    && !this.searchProduct 
                    && !this.searchTool
                    && !this.searchGroup 
                    && !this.searchPlanDate
                ) {
                    this.$message.error('请选择查询条件。')
                    return;
                }
                this.$refs.mfgTable.load(null, true);
			},
            mfgLoadBefore(params, callBack) {
                params["mfgorder"] = this.searchMfgOrder;
                params["product"] = this.searchProduct;
                params["tool"] = this.searchTool;
                params["group"] = this.searchGroup;
                params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
                params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
                callBack(true)
            },
            mfgLoadAfter(rows, callBack, result) {
                if (result.Result == 1) {
                    this.mfgTableData = result.Data.tableData;
                    this.$refs.mfgTable.rowData = result.Data.tableData;
                    this.$refs.mfgTable.paginations.total = result.Data.total;
                }
                else {
                    this.$message.error(result.Message);
                }
                callBack(false);
            },
			mfgRowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行')
				this.$refs.mfgTable.$refs.table.toggleRowSelection(row);
				this.infoMfgOrder = row.MfgOrder;
				this.infoMfgOrderQty = row.Qty;
                this.$refs.containerTable.load(null, true);
			},
            containerLoadBefore(params, callBack) {
			    const rows = this.$refs.mfgTable.getSelected();
                params["mfgorder"] = rows[0].MfgOrder;
                callBack(true)
            },
            containerLoadAfter(rows, callBack, result) {
                if (result.Result == 1) {
                    this.containerTableData = result.Data.tableData;
                    this.$refs.containerTable.rowData = result.Data.tableData;
                    this.$refs.containerTable.paginations.total = result.Data.total;
                }
                else {
                    this.$message.error(result.Message);
                }
                callBack(false);
            },
			start() {
				const rows = this.$refs.mfgTable.getSelected();
				if (!rows.length) {
				    this.$message.error('请选中创批的工单。')
				    return;
				}
				if(!this.infoContainerQty){
					this.$message.error('请输入批次数量。')
					return;
				}
				if(!this.infoPrinter){
					this.$message.error('请选择打印机。')
					return;
				}
				let params = {
					User: this.userInfo.userName,
					Password: this.userInfo.userPwd,
					UserTrueName: this.userInfo.userTrueName,
					PrintQueue: this.infoPrinter,
					MfgOrder: rows[0].MfgOrder,
					Product: rows[0].Product,
					Qty: this.infoContainerQty,
				};
				this.http.post(this.apiUrl.start, params, true).then(res => {
					if (res.Result == 1) {
						this.$refs.containerTable.load(null, true);
						this.$message.success(res.Message);
					} else {
						this.$refs.containerTable.load(null, true);
						this.$message.error(res.Message);
					}
				});	
			},		
            containerRowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行')
				this.$refs.containerTable.$refs.table.toggleRowSelection(row);
			},
			rePrint() {
				const rows = this.$refs.containerTable.getSelected();
				if (!rows.length) {
					this.$message.error('请选中补印的批次。')
					return;
				}
				if(!this.infoPrinter){
					this.$message.error('请选择打印机。')
					return;
				}
				let params = {
					User:this.userInfo.userName,
					Password:this.userInfo.userPwd,
					Container:rows[0].Container,
					Printer:this.infoPrinter,
					Type: "start"
				};
				this.http.post(this.apiUrl.ERPReprint, params, true).then(res => {
					if (res.Result == 1) {
						this.$refs.containerTable.load(null, true);
						this.$message.success(res.Message);
					} else {
						this.$refs.containerTable.load(null, true);
						this.$message.error(res.Message);
					}
				});	
			}
		}
	}
</script>
<style lang="less" scoped>
	.table-item-header {
		display: flex;
		align-items: center;
		padding: 6px;

		.table-item-border {
			height: 15px;
			background: rgb(33, 150, 243);
			width: 5px;
			border-radius: 10px;
			position: relative;
			margin-right: 5px;
		}

		.table-item-text {
			font-weight: bolder;
		}

		.table-item-buttons {
			flex: 1;
			text-align: right;
            margin-top: 20px;
		}

		.small-text {
			font-size: 12px;
			color: #2196F3;
			margin-left: 10px;
			position: relative;
			top: 2px;
		}
	}
</style>