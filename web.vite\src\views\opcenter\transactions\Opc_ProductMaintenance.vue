<template>
    <div class="product-maintenance">
        <VolHeader title="生产维修" />

        <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules">
            <div style="text-align: end; margin-top: 0px; width: 100%">
                <div style="margin-right: 20px; margin-top: -39px">
                    <el-button type="primary" @click="submit" plain icon="Plus">保存</el-button>
                </div>
            </div>
        </VolForm>
        <VolHeader title="批次信息" />
        <VolForm ref="infoForm" :loadKey="true" :formFields="infoFields" :formRules="infoRules" />
        <VolForm ref="repairForm" :loadKey="true" :formFields="repairFields" :formRules="repairRules" />
        <VolHeader title="维修记录" />
        <VolForm ref="queryForm" :loadKey="true" :formFields="queryFields" :formRules="queryRules" />
        <div style="text-align: end; margin-top: 10px; margin-bottom: 10px">
            <el-button type="primary" @click="search" plain icon="Search">查询</el-button>
            <el-button type="primary" @click="exportExcel" plain icon="Download">导出</el-button>
        </div>
        <vol-table ref="table" index :column-index="true" :reserveSelection="true" :loadKey="true" :columns="columns"
            :tableData="tableData" :single="false" :pagination-hide="false" :height="400" :url="ApiUrl.GetRepairRecords" 
            :defaultLoadPage="false"  @loadBefore="loadBefore" :ck="false"
      @loadAfter="loadAfter"></vol-table>
    </div>
</template>

<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from '@/components/basic/VolForm.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import {
    mapState
} from 'vuex';
export default {
    components: {
        VolHeader,
        VolForm,
        'vol-table': VolTable
    },
    computed: {
        ...mapState({
            userInfo: state => state.userInfo,
            permission: state => state.permission,
        })
    },

    data() {
        return {
            ApiUrl: {
                GetContainerInfo: '/api/Query/GetContainerInfo',
                SaveRepairRecord: 'api/CDO/ProductMaintanceTxn',
                GetRepairRecords: 'api/Query/GetProductMaintanceInfo',
                GetNamObjByType: 'api/Query/GetNamObjByType',
            },
            headerFields: {
                containerNo: ''
            },
            headerRules: [
                [
                    {
                        title: this.$ts('批次扫描'),
                        placeholder: this.$ts('请扫描或输入批次码'),
                        field: "containerNo",
                        type: "text",
                        required: true,
                        colSize: 4,
                        onKeyPress: ($event) => {
                            // this.onKeySearch($event, this.formFields)
                            if ($event.key === 'Enter' && $event.keyCode == 13) {
                                this.getContainerInfo(this.headerFields.containerNo)
                            }

                        }
                    }
                ]
            ],
            infoFields: {
                mfgOrder: '',
                productNo: '',
                productDesc: '',
                qty: '',
                currentStep: '',
                mfgQty: ''
            },
            infoRules: [
                [
                    {
                        title: this.$ts('生产工单'),
                        field: "mfgOrder",
                        type: "text",
                        readonly: true,
                        colSize: 2
                    },
                    {
                        title: this.$ts('产品编码'),
                        field: "productNo",
                        type: "text",
                        readonly: true,
                        colSize: 2
                    },
                    {
                        title: this.$ts('产品描述'),
                        field: "productDesc",
                        type: "text",
                        readonly: true,
                        colSize: 2
                    }
                ],
                [
                    {
                        title: this.$ts('批次数量'),
                        field: "qty",
                        type: "text",
                        readonly: true,
                        colSize: 2
                    },
                    {
                        title: this.$ts('当前工序'),
                        field: "currentStep",
                        type: "text",
                        readonly: true,
                        colSize: 2
                    },
                    {
                        title: this.$ts('工单数量'),
                        field: "mfgQty",
                        type: "text",
                        readonly: true,
                        colSize: 2
                    }
                ]
            ],
            queryFields: {
                productNo: '',
                mfgOrder: '',
                startDate: '',
                endDate: ''
            },
            queryRules: [
                [
                    {
                        title: this.$ts('产品编码'),
                        field: "productNo",
                        type: "text",
                        colSize: 2
                    },
                    {
                        title: this.$ts('生产工单'),
                        field: "mfgOrder",
                        type: "text",
                        colSize: 2
                    },
                    {
                        title: this.$ts('维修时间'),
                        field: "startDate",
                        type: "date",
                        colSize: 2
                    },
                    {
                        title: this.$ts('至'),
                        field: "endDate",
                        type: "date",
                        colSize: 2
                    }
                ]
            ],
            repairFields: {
                defectCode: '',
                defectLocation: '',
                repairMethod: '',
                repairHours: '',
                improvement: ''
            },
            repairRules: [
                [
                    {
                        title: this.$ts('不良代码'),
                        field: "defectCode",
                        type: "select",
                        required: true,
                        colSize: 2,
                        data: []
                    },
                    {
                        title: this.$ts('不良位置'),
                        field: "defectLocation",
                        type: "text",
                        required: true,
                        colSize: 2
                    },
                    {
                        title: this.$ts('维修方式'),
                        field: "repairMethod",
                        type: "select",
                        required: true,
                        colSize: 2,
                        data: []
                    }
                ],
                [
                   
                    {
                        title: this.$ts('维修工时'),
                        field: "repairHours",
                        type: "text",
                        required: true,
                        colSize: 2
                    }
                ],
                [
                    {
                        title: this.$ts('改善对策'),
                        field: "improvement",
                        type: "textarea",
                        required: true,
                        colSize: 8
                    }
                ]
            ],
            columns: [
                { field: 'Container', title: '批次码', type: 'string', width: 150, align: 'center' },
                { field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center' },
                { field: 'P_Description', title: '产品描述', type: 'string', width: 180, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 90, align: 'center' },
                { field: 'SpecName', title: '维修工序', type: 'string', width: 130, align: 'center' },
                { field: 'JobCauseCode', title: '不良代码', type: 'string', width: 120, align: 'center' },
                { field: 'BadPostion', title: '不良位置', type: 'string', width: 120, align: 'center' },
                { field: 'MaintenancemethodName', title: '维修方式', type: 'string', width: 120, align: 'center' },
                { field: 'WorkIngHours', title: '维修工时', type: 'string', width: 120, align: 'center' },
                { field: 'Improvement', title: '改善对策', type: 'string', width: 180, align: 'center' },
                { field: 'EmployeeName', title: '维修人', type: 'string', width: 120, align: 'center' },
                { field: 'TxnDate', title: '维修时间', type: 'string', width: 180, align: 'center' }
            ],
            tableData: []
        }
    },
    created() {
        // 初始化不良代码和维修方式
        this.initRepairRules();
    },
    methods: {
        async initRepairRules() {
            // 初始化不良代码和维修方式
            this.repairRules[0][0].data = await this.GetNamObjByType('JOBCAUSECODE', 'JobCauseCode', null, 'A_');
            this.repairRules[0][2].data = await this.GetNamObjByType('W_MaintenanceMethod', 'W_MaintenanceMethod', null, null);
        },
        getContainerInfo(containerNo) {
            // 调用接口获取批次信息
            this.http.get(this.ApiUrl.GetContainerInfo, { container: containerNo }).then(res => {
                if (res.Result == 1) {
                    this.infoFields = {
                        mfgOrder: res.Data.mfgOrderName,
                        productNo: res.Data.productName,
                        productDesc: res.Data.productDesc,
                        qty: res.Data.qty,
                        currentStep: res.Data.currentStep,
                        mfgQty: res.Data.mfgOrderQty
                    }
                }
            })
        },
        submit() {
            // 保存维修记录
            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                Container: this.headerFields.containerNo,
                Improvement: this.repairFields.improvement,
                JobCauseCode: this.repairFields.defectCode,
                WorkingHours: this.repairFields.repairHours,
                BadPosition: this.repairFields.defectLocation,
                W_MaintenanceMethod: this.repairFields.repairMethod,
            }
            this.http.post(this.ApiUrl.SaveRepairRecord, params).then(res => {
               if (res.Result == 1) {
                    this.$message.success('保存成功');
                    // 清空表单
                    this.resetFields();
                } else {
                    this.$message.error(res.Message);
                
               }
            })
        },
        search() {
            // 查询维修记录
            this.$refs.table.load(null, true);
        },
         //导出
              exportExcel(){
                  let tableData = this.$refs.table.tableData
                  let sortData = this.$refs.table.filterColumns
                  let exportData = this.handleTableSortData(tableData, sortData)
                  Excel.exportExcel(exportData, "批次异常登记详情" + '-' + this.base.getDate());
              },
              handleTableSortData(tableData, sortData) {
                  let newArray = [];
                  tableData.forEach(data => {
                      let newItem = {};
                      sortData.forEach(field => {
                          if (data.hasOwnProperty(field.field)) {
                              newItem[field.title] = data[field.field];
                          }
                      });
                      newArray.push(newItem);
                  });
                  return newArray
              },
        async GetNamObjByType(cdo, type, name, perCdo) {
            let params = { cdo, type, name, perCdo };
            let dataArry = [];
            const res = await this.http.get(this.ApiUrl.GetNamObjByType, params);
            if (res.Result == 1) {
                dataArry = res.Data.map(item => ({
                    key: item.Name,
                    value: item.Name
                }));
            } else {
                this.$message.error(res.Message);
            }
            return dataArry;
        },
        resetFields() {
            // 重置表单
            this.$refs.form.resetFields();
            this.$refs.infoForm.resetFields();
            this.$refs.repairForm.resetFields();
            this.$refs.queryForm.resetFields();
        } ,
        
        loadBefore(params, callBack) {
      let param = {
        mfgorder: this.queryFields.mfgOrder,
        product:this.queryFields.productNo,
        StartDate: this.queryFields.startDate,
        EndDate: this.queryFields.endDate,
        rows: this.$refs.table.paginations.size,
        page: this.$refs.table.paginations.page
      }
      params = Object.assign(params, param);
      callBack(true);
    },
    loadAfter(rows, callBack, res) {
      if (res.Result == 1) {
        // this.columns = res.Data.colums;
        this.tableData = res.Data.tableData;
        this.$refs.table.rowData = res.Data.tableData;
        this.$refs.table.paginations.total = res.Data.total;
      } else {
        this.$Message.error(res.Message);
      }
      callBack(false);
    }
    }
}
</script>

<style scoped>
.product-maintenance {
    padding: 10px;
}
</style>