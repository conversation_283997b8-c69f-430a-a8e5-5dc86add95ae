<template>
	<div style="display: flex;margin-top: 5px;">
		<div style="margin-left: 10px;">
			<label style="width: 240px; margin-left: 5px; font-size: 16px;">
				<span>内箱标签</span>
			</label>
			<div style="margin-top: 5px;">
				<el-input ref="input" style="width: 240px" v-model="searchInnerLabelId" placeholder="请输入"
					autofocus="true"></el-input>
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 240px; margin-left: 5px; font-size: 16px;">
				<span>外箱标签</span>
			</label>
			<div style="margin-top: 5px;">
				<el-input ref="input" style="width: 240px" v-model="searchOuterLabelId" placeholder="请输入"
					autofocus="true"></el-input>
			</div>
		</div>
		<div style="margin-left: 10px">
			<label style="width: 200px; margin-left: 5px; font-size: 16px">
				<span>工单</span>
			</label>
			<div style="margin-top: 5px">
				<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
					remote-show-suffix :remote="true" :remote-method="getMfgOrder">
					<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
				</el-select>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<div class="table-item-border"></div> <span class="table-item-text">入库报检列表</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="getRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="success" icon="Check" @click="submitRow" plain>入库报检</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" :pagination="pagination" @rowClick="rowClick"
			:columns="columns" :height="500" :pagination-hide="false" :load-key="true" :defaultLoadPage="false"
			:url="apiUrl.getInventoryInspection" @loadBefore="loadBefore" @loadAfter="loadAfter" :column-index="true"
			:ck="true"></vol-table>
	</div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			searchInnerLabelId: null,
			searchOuterLabelId: null,
			searchMfgOrder: null,
			searchTxnDate: null,
			mfgorders: [],

			columns: [
				{ field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'ERPInspectionOrder', title: '检验单号', type: 'string',hidden: true, width: 130, align: 'center' },
				{ field: 'InnerLabelId', title: '内箱标签', type: 'string', width: 130, align: 'center' },
				{ field: 'Status', title: '状态', type: 'string', width: 60, align: 'center' },
				{ field: 'InnerQty', title: '内箱数量', type: 'string', width: 80, align: 'center' },
				{ field: 'OuterLabelId', title: '外箱标签', type: 'string', width: 130, align: 'center' },
				{ field: 'Product', title: '产品编码', type: 'string', width: 100, align: 'center' },
				{ field: 'P_Description', title: '产品描述', type: 'string', width: 100, align: 'center' },
				{ field: 'MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
				{ field: 'MfgOrderQty', title: '工单数量', type: 'string', width: 80, align: 'center' },
				{ field: 'Operator', title: '创建人', type: 'string', width: 80, align: 'center' },
				{ field: 'CreateDate', title: '创建时间', type: 'string', width: 130, align: 'center' },
			],
			tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/GetRevisionObject",
				getNameObject: "/api/query/GetNameObject",
				getInventoryInspection: "/api/query/getInventoryInspection",
				inventoryInspection: "/api/cdo/InventoryInspection",
			},
		}
	},
	created() {
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	methods: {
		getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		reset() {
			this.searchInnerLabelId = null;
			this.searchOuterLabelId = null;
			this.searchMfgOrder = null;
			this.searchTxnDate = null;
			this.tableData = [];
			this.$refs.table.rowData = [];
			this.$refs.table.paginations.total = 0;
		},
		rowClick({
			row,
			column,
			index
		}) {
			this.$refs.table.$refs.table.toggleRowSelection(row);
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			// this.$refs.table.$refs.table.toggleRowSelection(row);
		},
		getRow() {
			if (this.searchInnerLabelId == null
				&& this.searchOuterLabelId == null
				&& this.searchMfgOrder == null
				&& this.searchTxnDate == null) {
				this.$message.error('请输入查询条件');
				return;
			}
			this.tableData = [];
			this.$refs.table.load(null, true);
		},
		loadBefore(params, callBack) {
			params["InnerLabelId"] = this.searchInnerLabelId;
			params["OuterLabelId"] = this.searchOuterLabelId;
			params["Mfgorder"] = this.searchMfgOrder;
			// params["StartTime"] = this.searchTxnDate != null ? this.searchTxnDate[0] : null;
			// params["EndTime"] = this.searchTxnDate != null ? this.searchTxnDate[1] : null;
			callBack(true)
		},
		loadAfter(rows, callBack, result) {
			if (result.Result == 1) {
				this.tableData = result.Data.tableData;
				this.$refs.table.rowData = result.Data.tableData;
				this.$refs.table.paginations.total = result.Data.total;
			}
			else {
				this.$message.error(result.Message);
			}
			callBack(false);
		},
		submitRow() {
			const rows = this.$refs.table.getSelected();
			if (rows.length == 0) {
				this.$message.error('请选中入库报检标签行')
				return;
			}
			let params = {
				User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
				status: "1",//入库报检将状态改为1:入库报检
				requestData: rows
			};
			this.http.post(this.apiUrl.inventoryInspection, params, true).then(res => {
				if (res.Result == 1) {
					this.reset();
					this.$message.success(res.Message);
				} else {
					this.$message.error(res.Message);
				}
			});
		}
	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	.table-item-text {
		font-weight: bolder;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196F3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>