<template>
  <div class="lot-exception">
    <VolHeader title="批次异常登记" />
    <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules">
      <div style="text-align: end; margin-top: 0px; width: 100%">
        <div style="margin-right: 20px; margin-top: -39px">
          <el-button type="primary" @click="submit">提交登记</el-button>
        </div>
      </div>
    </VolForm>
    <VolHeader title="批次异常登记详情" />
    <div style="display: flex; margin-top: 5px; margin-bottom: 10px">
      <div style="margin-left: 20px">
        <label style="width: 200px; margin-left: 5px; font-size: 16px">
          <span>产品编码</span>
        </label>
        <div style="margin-top: 5px">
          <el-select
            v-model="Search_Product"
            clearable
            filterable
            placeholder="请选择"
            style="width: 200px"
            @input="GetProduct"
          >
            <el-option
              v-for="item in Products"
              :key="item.Name"
              :label="item.Name"
              :value="item.Name"
            />
          </el-select>
        </div>
      </div>
      <div style="margin-left: 20px">
        <label style="width: 200px; margin-left: 5px; font-size: 16px">
          <span>生产订单</span>
        </label>
        <div style="margin-top: 5px">
          <el-select
            v-model="Search_Order"
            clearable
            filterable
            placeholder="请选择"
            style="width: 200px"
          >
            <el-option
              v-for="item in Orders"
              :key="item.Name"
              :label="item.Name"
              :value="item.Name"
            />
          </el-select>
        </div>
      </div>
      <div style="margin-left: 20px">
        <label style="width: 200px; margin-left: 5px; font-size: 16px">
          <span>登记时间范围</span>
        </label>
        <div style="margin-top: 5px">
          <el-date-picker
            v-model="Search_CreateDate"
            type="datetimerange"
            range-separator="到"
            start-placeholder="开始"
            end-placeholder="结束"
            :size="size"
            style="width: 350px"
          />
        </div>
      </div>
      <div style="margin-left: 80px">
        <label style="width: 200px; margin-left: 5px; font-size: 16px">
          <span> </span>
        </label>
        <div style="margin-top: 5px">
          <el-button type="primary" plain @click="search">查询</el-button>
          <el-button type="success" plain @click="outputRow">导出</el-button>
        </div>
      </div>
    </div>
    <vol-table
      ref="table"
      index
      :loadKey="true"
      :ck="false"
      :column-index="true"
      :columns="columns"
      :tableData="tableData"
      :pagination-hide="true"
      :reserveSelection="true" 
      :max-height="280"
    ></vol-table>
  </div>
</template>
<script lang="jsx">
    import VolTable from "@/components/basic/VolTable.vue";
    import VolForm from '@/components/basic/VolForm.vue';
    import VolHeader from '@/components/basic/VolHeader.vue';
    import VolBox from '@/components/basic/VolBox.vue';
    import { mapState } from 'vuex';
    import Common from "@/uitils/common.js";
    import axios from 'axios'; 
    import Excel from '@/uitils/xlsl.js';
    export default {
        components: {
            VolHeader,
            VolForm,
            'vol-table': VolTable,
            'vol-box': VolBox
        },
        computed: {
            ...mapState({
                //获取当前用户的信息
                userInfo: state => state.userInfo,
                //获取当前用户的权限
                permission: state => state.permission,
            })
        },
        //初始化页面
        created() {
            this.GetExceptionReason();
            // this.headerFields.employee = this.userInfo.userName;
            // this.headerFields.selectLot = this.$route.query.Search_Container;
            // this.headerFields.exceptionReason = this.$route.query.Resion;
            // this.headerFields.exceptionRemark = this.$route.query.Comments;
            // if(this.headerFields.selectLot){
            //     this.onselectLot(this.headerFields.selectLot)
            // }

        },

        data() {
            return {
                ApiUrl: {
                    GetRevisionObject: "/api/query/GetRevisionObject",
                    GetNameObject: "/api/query/GetNameObject",
                    GetContainerInfo: '/api/Query/GetContainerInfo', //获取容器信息
                    GetLotException: 'api/Query/GetLotException', //获取批次异常登记信息
                    LotExceptionTxn: '/api/CDO/LotExceptionTxn', //批次异常登记
                },
                headerFields: {
                    employee: '',
                    selectLot: '',
                    currentLot: '',
                    currentStatus: '',
                    qty:'',
                    uom:'',
                    currentSpec: '',
                    nextSpec: '',
                    product:'',
                    productName: '',
                    productRevison: '',
                    productDesc: '',
                    mfgQty: '',
                    mfgOrder: '',
                    resource: '',
                    workflow:'',
                    workflowName: '',
                    workflowRevison: '',
                    exceptionReason:'',
                    exceptionRemark:'',
                },
                headerRules: [
                    [{
                            title: this.$ts('扫描批次码'),
                            placeholder: this.$ts(''),
                            field: "selectLot",
                            type: "text",
                            colSize: 2,
                            onKeyPress: ($event) => {
                                if ($event.keyCode == 13 && $event.type == 'keyup') {
                                    this.onselectLot(this.headerFields.selectLot);
                                }
							}
                        },
                        {
                            title: this.$ts('操作员'),
                            placeholder: this.$ts(''),
                            field: "employee",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },
                        {
                            title: this.$ts('当前批次'),
                            placeholder: this.$ts(''),
                            field: "currentLot",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('当前状态'),
                            placeholder: this.$ts(''),
                            field: "currentStatus",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('批次数量'),
                            placeholder: this.$ts(''),
                            field: "qty",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('单位'),
                            placeholder: this.$ts(''),
                            field: "uom",
                            type: "text",
                            readonly: true,
                            hidden: true,
                            colSize: 2,
                        },
                    ],
                    [
                    {
                            title: this.$ts('生产工单'),
                            placeholder: this.$ts(''),
                            field: "mfgOrder",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('工单数量'),
                            placeholder: this.$ts(''),
                            field: "mfgQty",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('产品编码'),
                            placeholder: this.$ts(''),
                            field: "product",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('产品编码名称'),
                            placeholder: this.$ts(''),
                            field: "productName",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },
                        {
                            title: this.$ts('产品编码版本号'),
                            placeholder: this.$ts(''),
                            field: "productRevision",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },
                        {
                            title: this.$ts('产品描述'),
                            placeholder: this.$ts(''),
                            field: "productDesc",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        }, 
                        {
                            title: this.$ts('工艺流程'),
                            placeholder: this.$ts(''),
                            field: "workflow",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                    ],
                    [
                        {
                            title: this.$ts('工艺流程名称'),
                            placeholder: this.$ts(''),
                            field: "workflowName",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },
                        {
                            title: this.$ts('工艺流程版本'),
                            placeholder: this.$ts(''),
                            field: "workflowRevision",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },        
                        {
                            title: this.$ts('当前工序'),
                            placeholder: this.$ts(''),
                            field: "currentSpec",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('下工序'),
                            placeholder: this.$ts(''),
                            field: "nextSpec",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                        },
                        {
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            dataKey: '', //后台下拉框对应的数据字典编号
                            title: this.$ts('异常原因'),
                            placeholder: this.$ts(''),
                            field: "exceptionReason",
                            type: "select",
                            readonly: false,
                            colSize: 2,
                        }
                    ],
                    [{
                            title: this.$ts('异常备注'),
                            placeholder: this.$ts(''),
                            field: "exceptionRemark",
                            type: "textarea",
                            readonly: false,
                            colSize: 10,
                        }
                    ]
                ],
                Search_Product:null,
                Search_Order:null,
                Search_CreateDate:null,
                Search_Container:null,
                Products: [],
                Orders:[],
                columns: [
					{ field: 'Container', title: '批次码', type: 'string', width: 150, align: 'center'},
					{ field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center'},
					{ field: 'ProductDesc', title: '产品名称描述', type: 'string', width: 100, align: 'center'},
                    { field: 'Qty', title: '批次数量', type: 'string', width: 130, align: 'center'},
                    { field: 'CurrentStep', title: '当前工序', type: 'string', width: 130, align: 'center' },
                    { field: 'ExceptionReason', title: '异常原因', type: 'string', width: 130, align: 'center' },
                    // { field: 'Resource', title: '设备/line', type: 'string', width: 80, align: 'center' },
                    { field: 'MfgOrder', title: '生产工单', type: 'string', width: 130, align: 'center' },
                    { field: 'MfgQty', title: '工单数量', type: 'string', width: 130, align: 'center' },
                    { field: 'Workflow', title: '工作流程', type: 'string', width: 130, align: 'center' },
                    { field: 'EmployeeName', title: '异常登记人', type: 'string', width: 130, align: 'center'},
                    { field: 'CreateTime', title: '异常登记时间', type: 'datetime', width: 150, align: 'center'},
                    { field: 'ExceptionRemark', title: '异常备注', type: 'string', width: 130, align: 'center' },
                ],
                tableData: []

            }
        },
        methods: {
            //获取班次
            async GetCurrentShift() {
                this.http.get(this.ApiUrl.GetCurrentShift).then(res => {
                    if (res.Result == 1) {
                        this.headerFields.shift = res.Data[0].Name;
                    } else {
                        this.$message.error(res.Message);
                    }
                }).catch(err => {
                    this.$message.error(err);
                })
            },
            //获取异常原因
            async GetExceptionReason() {
				let params = {
					cdo: "holdReason"
				};
				this.http.get(this.ApiUrl.GetNameObject,params).then(res => {
					if (res.Result == 1) {
                        this.headerRules[2][4].data = res.Data.map((item)=> {return {key:item.Name,value:item.Name}});
					} else {
						this.$message.error(res.Message);
					}
				});
            },
            // //获取产品
            // async GetProduct(val){
			// 	let params = {
			// 		cdo: "Product",
            //         name: val
			// 	};
			// 	this.http.get(this.ApiUrl.GetRevisionObject,params).then(res => {
			// 		if (res.Result == 1) {
			// 			this.Products = res.Data;
			// 		} else {
			// 			this.$message.error(res.Message);
			// 		}
			// 	});
			// },
            // //获取工单
            // async GetOrder(){
			// 	let params = {
			// 		cdo: "MfgOrder"
			// 	};
			// 	this.http.get(this.ApiUrl.GetNameObject,params).then(res => {
			// 		if (res.Result == 1) {
			// 			this.Orders = res.Data;
			// 		} else {
			// 			this.$message.error(res.Message);
			// 		}
			// 	});
			// },
            //扫描批次
            onselectLot(val) {
                if (val) {
                    let data = {
                        Container: val,
                        }
                        this.http.get(this.ApiUrl.GetContainerInfo, data).then((res) => {
                            if (res.Result == 1) {
                                this.headerFields.currentLot = val;
                                this.headerFields.currentStatus = res.Data.status;
                                this.headerFields.qty = res.Data.qty;
                                this.headerFields.uom = res.Data.uom;
                                this.headerFields.product = res.Data.productName +":"+ res.Data.productRevision;
                                this.headerFields.productName = res.Data.productName;
                                this.headerFields.productRevision = res.Data.productRevision;
                                this.headerFields.productDesc = res.Data.productDesc;
                                this.headerFields.mfgOrder = res.Data.mfgOrderName;
                                this.headerFields.mfgQty = res.Data.mfgOrderQty;
                                this.headerFields.currentSpec = res.Data.currentStep;
                                this.headerFields.nextSpec = res.Data.nextStep;
                                this.headerFields.workflow = res.Data.workflowName +':'+res.Data.workflowRevision;
                                this.headerFields.workflowName = res.Data.workflowName;
                                this.headerFields.workflowRevision = res.Data.workflowRevision;
                                // this.headerFields.resource = res.Data.resourceName;
                            } else {
                                this.$message.error(res.Message);
                            }
                        })
                }
            },
            //提交
            submit() {
                if (this.headerFields.currentLot == null || this.headerFields.currentLot == '') {
                    this.$message.warning('请填入批次');
                    return;
                }
                if (this.headerFields.exceptionReason == null || this.headerFields.exceptionReason == '') {
                    this.$message.warning('请选择异常原因');
                    return;
                }
                let param = {
                    User:this.userInfo.userName,
                    Password:this.userInfo.userPwd,
                    EmployeeName: this.userInfo.userName,
                    ContainerName: this.headerFields.currentLot,
                    Qty: this.headerFields.qty,
                    UomName: this.headerFields.uom,
                    Status: this.headerFields.currentStatus,
                    MfgOrderName: this.headerFields.mfgOrder,
                    MfgQty: this.headerFields.mfgQty,
                    ProductName: this.headerFields.productName,
                    ProductRevision: this.headerFields.productRevision,
                    ProductDesc: this.headerFields.productDesc,
                    WorkflowName: this.headerFields.workflowName,
                    WorkflowRevision: this.headerFields.workflowRevision,
                    CurrentStepName: this.headerFields.currentSpec,
                    NextStepName: this.headerFields.nextSpec,
                    // ResourceName: this.headerFields.resource,
                    ExceptionReasonName: this.headerFields.exceptionReason,
                    ExceptionRemark: this.headerFields.exceptionRemark,
                }
                this.http.post(this.ApiUrl.LotExceptionTxn, param).then((res) => {
                    if (res.Result == 1) {
                        this.$Message.success('异常批次('+this.headerFields.currentLot+')登记成功');
                        this.reset();
                    } else {
                        this.$Message.error(res.Message);
                    }
                })
            },
            //查询
            search(){
                let params = {
					product: this.Search_Product,
					order: this.Search_Order,
					startTime: this.Search_CreateDate != null?this.Search_CreateDate[0] : null,
					endTime: this.Search_CreateDate != null?this.Search_CreateDate[1] : null,
                    Container:this.Search_Container,
				};
                this.http.get(this.ApiUrl.GetLotException, params, true).then(res => {
					if (res.Result == 1) {
						this.tableData = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
            },
            reset() {
                this.headerFields.selectLot = null;
                this.headerFields.currentLot = null;
                this.headerFields.currentStatus = null;
                this.headerFields.qty = null;
                this.headerFields.uom = null;
                this.headerFields.product = null;
                this.headerFields.productName = null;
                this.headerFields.productRevision = null;
                this.headerFields.productDesc = null;
                this.headerFields.mfgOrder = null;
                this.headerFields.mfgQty = null;
                this.headerFields.currentSpec = null;
                this.headerFields.nextSpec = null;
                this.headerFields.workflow = null;
                this.headerFields.workflowName = null;
                this.headerFields.workflowRevision = null;
                this.headerFields.resource = null;
                this.headerFields.exceptionReason = null;
                this.headerFields.exceptionRemark = null;
            },
            //导出
            outputRow(){
                let tableData = this.$refs.table.tableData
				let sortData = this.$refs.table.filterColumns
				let exportData = this.handleTableSortData(tableData, sortData)
				Excel.exportExcel(exportData, "批次异常登记详情" + '-' + this.base.getDate());
            },
            handleTableSortData(tableData, sortData) {
				let newArray = [];
				tableData.forEach(data => {
					let newItem = {};
					sortData.forEach(field => {
						if (data.hasOwnProperty(field.field)) {
							newItem[field.title] = data[field.field];
						}
					});
					newArray.push(newItem);
				});
				return newArray
			},
        },


    }
</script>