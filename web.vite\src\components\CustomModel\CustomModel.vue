<template>
  <vol-box :on-model-close="closeCustomModel" v-model="viewModel" :height="520" :width="500" :padding="0" :lazy="true"
    :title="title">
    <template #content>
      <el-alert :title="$ts('拖动列名可调整表格列显示顺序')" type="success" :show-icon="false">
      </el-alert>
      <div class="view-column view-column-title">
        <div class="view-column-index">#</div>
        <div class="view-column-left">{{ $ts("列名") }}</div>
        <div class="view-column-right">{{ $ts("是否显示") }}</div>
      </div>
      <draggable class="list-group" tag="transition-group" :component-data="componentData" :list="viewColumns"
        v-bind="dragOptions" item-key="order">
        <transition-group class="drag-center-item">
          <div class="view-column" v-for="(column, index) in viewColumns" :key="index">
            <div class="view-column-index">{{ index + 1 }}</div>
            <div class="view-column-left">{{ $ts(column.title) }}</div>
            <div class="view-column-right">
              <el-checkbox v-model="column.show">
                <div style="height: 100%; width: 250px"></div>
              </el-checkbox>
            </div>
          </div>
        </transition-group>
      </draggable>
      <!-- <custom-column :view-columns="viewColumns"></custom-column> -->
    </template>
    <template #footer>
      <div style="text-align: center">
        <el-button type="default" size="small" @click="closeCustomModel"><i class="el-icon-close"></i>{{ $ts("取消")
          }}</el-button>
        <!-- <el-button type="success" size="small" @click="initViewColumns(true)"><i class="el-icon-refresh"></i>{{
          $ts("重置") }}</el-button> -->
        <el-button type="primary" size="small" @click="saveColumnConfig"><i class="el-icon-check"></i>{{ $ts("确定")
          }}</el-button>
      </div>
    </template>
  </vol-box>
</template>
<script>
import { VueDraggableNext } from "vue-draggable-next";
import VolBox from '@/components/basic/VolBox.vue';
// import ViewGridCustomColumn from '@/components/basic/ViewGrid/ViewGridCustomColumn.vue'

export default {
  components: {
    'vol-box': VolBox,
    draggable: VueDraggableNext,
    // "custom-column": ViewGridCustomColumn,
  },
  props: {
    columns: {
      type: Array,
      default: () => {
        return [];
      },
    },
    tableName: {
      type: String,
      required: true,
      default: () => "",
    },
  },
  data() {
    return {
      title: this.$ts('setup'),
      showCustom: true,
      viewModel: false, //查看表结构的弹出框
      viewColumns: [], //查看表结构的列数据
      dragOptions: {
        animation: 200,
        group: "description",
        disabled: false,
        ghostClass: "ghost",
      },
      componentData: {
        tag: "ul",
        type: "transition-group",
      },
    }
  },
  mounted() {
    // console.log(this.columns, 'columns')
    // console.log(this.tableName, 'tableName')
  },
  methods: {

    showCustomModel() {
      if (!this.viewColumns.length) {
        this.initViewColumns();
      }
      this.viewColumnsClone = JSON.parse(JSON.stringify(this.viewColumns));
      this.viewModel = true;
    },

    closeCustomModel() {
      this.viewModel = false;
      if (this.checkColumnChanged()) {
        this.viewColumns = JSON.parse(JSON.stringify(this.viewColumnsClone));
      }
    },

    initViewColumns(isReset) {
      if (this.columns.some(c => { return c.children })) {
        this.showCustom = false;
        return;
      }
      //初始化自定列配置
      if (isReset) {
        this.resetViewColumns();
      }
      if (!this.orginColumnFields) {
        this.orginColumnFields = this.columns.map((c) => {
          return c.field;
        });
      }
      this.viewColumns = this.columns.filter((c) => {
        return !c.hidden && !c.render;
      }).map((c) => {
        return { field: c.field, title: c.title, show: !c.hidden };
      });
      if (isReset) {
        return;
      }
      this.getCacheViewColumn();
    },

    resetViewColumns() {
      localStorage.setItem(this.getViewCacheKey(), '')
      if (!this.orginColumnFields) {
        return;
      }
      let _columns = [];
      this.orginColumnFields.forEach((x) => {
        _columns.push(
          this.columns.find((c) => {
            return c.field == x;
          })
        );
      });
      let otherColumns = this.columns.filter((c) => {
        return !this.orginColumnFields.some((s) => {
          return c.field == s;
        });
      });
      _columns.push(...otherColumns);
      this.columns.splice(0);
      this.columns.push(..._columns);
    },

    getCacheViewColumn() {
      try {
        let columns = localStorage.getItem(this.getViewCacheKey());
        if (!columns) return;
        columns = JSON.parse(columns);
        if (columns.some(x => { return !this.viewColumns.some(c => { return c.field == x.field }) }) ||
          this.viewColumns.some(x => { return !columns.some(c => { return c.field == x.field }) })
        ) {
          localStorage.removeItem(this.getViewCacheKey())
          return;
        }
        let sortTableColumns = [];
        //弹出框的列
        let _viewColumns = [];
        this.columns.forEach((column) => {
          let _column = this.viewColumns.find((c) => {
            return c.field == column.field;
          });
          if (_column) {
            _column.show = column.show;
            _viewColumns.push(_column);
          }
          let tableColumn = this.columns.find((c) => {
            return c.field == column.field;
          });
          if (tableColumn) {
            tableColumn.hidden = !column.show;
            sortTableColumns.push(tableColumn);
          }
        });
        //重新排版弹出框自定义列
        let otherColumns = this.viewColumns.filter((c) => {
          return !_viewColumns.some((s) => {
            return c.field == s.field;
          });
        });
        //重新排版弹出框自定义列
        _viewColumns.push(...otherColumns);
        this.viewColumns.splice(0);
        this.viewColumns.push(..._viewColumns);

        this.sortViewColumns(sortTableColumns);
      } catch (error) {
        console.log('设置默认自定义列异常:' + error.message);
      }
    },

    getViewCacheKey() {
      // return 'custom:column' + this.table.name;
      return 'custom:column' + this.tableName;
    },

    sortViewColumns(sortColumns) {
      if (sortColumns.length) {
        let hiddenColumns = this.columns.filter((c) => {
          return !sortColumns.some((s) => {
            return c.field == s.field;
          });
        });
        sortColumns.push(...hiddenColumns);
        this.columns.splice(0);
        this.columns.push(...sortColumns);
      }
    },

    checkColumnChanged() {
      return (
        JSON.stringify(this.viewColumns) != JSON.stringify(this.viewColumnsClone)
      );
    },

    saveColumnConfig() {
      let hasShowColumn = this.viewColumns.some((x) => {
        return x.show;
      });
      if (!hasShowColumn) {
        return this.$message.error('至少选择一列显示');
      }
      this.viewModel = false;
      if (this.checkColumnChanged()) {
        let sortColumns = [];
        this.viewColumns.forEach((column) => {
          let _column = this.columns.find((c) => {
            return c.field == column.field;
          });
          if (_column) {
            _column.hidden = !column.show;
            sortColumns.push(_column);
          }
        });
        this.sortViewColumns(sortColumns);
      }
      try {
        localStorage.setItem(this.getViewCacheKey(), JSON.stringify(this.viewColumns));
      } catch (error) {
        console.log('获取自定义列异常:' + error.message);
      }
    },

  },
}
</script>
<style lang="less" scoped>
.view-column {
  cursor: pointer;
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #f3f3f3;

  .view-column-index {
    width: 50px;
  }

  .view-column-left {
    width: 120px;
    padding: 0 10px;
  }

  .view-column-right {
    flex: 1;
  }
}

.view-column-title {
  font-weight: bold;
}

.view-column:last-child {
  border-bottom: 0;
}
</style>