<template>
    <div style="display: flex;margin-top: 5px;">
		  <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>设备课别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="classname" clearable filterable placeholder="键入搜索"
                     style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getclassname" :loading="loading">
						<el-option v-for="item in classnames" 
						:key="item.classname" 
						:label="item.classname" 
						:value="item.classname" />
					</el-select>
				</div>
			</div>
        <div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>设备/模具编码</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="Resource" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getResource">
                    <el-option v-for="item in Resources" 
					:key="item.Name" 
					:label="item.Name" 
					:value="item.Name" />
                </el-select>
            </div>
        </div>
		<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>日期筛选</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
			</div>
		</div>
    </div>
    <!-- 数据执行 -->
    <div class="table-item">
        <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">设备/模具维修单待维修主管关闭列表</span>
            <div class="table-item-buttons">
                <div>
                    <el-button icon="Search" @click="getRow" plain>查询</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="400"
            :pagination-hide="false" :load-key="true" :defaultLoadPage="false" :single="true"
            :url="apiUrl.GetEquipmentComplete"
            @loadBefore="loadBefore" @loadAfter="loadAfter" :column-index="true" :ck="true">
        </vol-table>

        <div style="display: flex;margin-top: 5px;">
            <div class="table-item-buttons" style="margin-top: 28px; margin-left: 10px;">
                <div>
                    <el-button type="success" icon="Plus" @click="addRow" plain>确认关闭维修单</el-button>
                </div>
            </div>
        </div>
    </div>
    	<!-- 编辑弹出框 -->
	<vol-box :lazy="true" v-model="show" title="设备维修单关闭" :width="800" :padding="5"
	 :onModelClose="onModelClose">
		<div style="margin-left: 10px; margin-top: 10px">
			<label style="width: 200px; margin-left: 5px; font-size: 16px"> <span>维修处理关闭备注</span></label>
			<div style="margin-top: 5px">
				<el-input v-model="Info_memo" type="textarea"></el-input>
			</div>
            <div style="margin-top: 15px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>维修分数</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input-number v-model="Info_number" :min="0" :controls="false"></el-input-number>
                </div>
            </div>
		</div>
		<template #footer>
			<div>
				<el-button type="save" icon="Check" size="small" @click="submitRow">保存</el-button>
				<el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            //searchMfgOrder: null,
            classname:'',
            classnames:[],
            searchPlanDate:null,
			Resource: null,
            Info_memo:'',
            Info_number:'',
            Name: null,
            show: false,

            columns: [
				{ field: 'OrderName', title: '维修单', type: 'string', width: 130, align: 'center' },
                //{ field: 'classname', title: '课别', type: 'string', width: 120, align: 'center' },
				{ field: 'Description', title: '设备/模具名称', type: 'string', width: 130, align: 'center' },					
				{ field: 'ResourceName', title: '设备/模具编码', type: 'string', width: 110, align: 'center' },
                { field: 'classnamename', title: '设备课别', type: 'string', width: 110, align: 'center' },
                
                { field: 'Jobmodelname', title: '设备/故障描述', type: 'string', width: 120, align: 'center' },
                { field: 'CREATENAME', title: '登记/分配人', type: 'string', width: 120, align: 'center' },  
                { field: 'CreateDate', title: '登记时间', type: 'datetime', width: 120, align: 'center' },

                { field: 'PROGRESSNAME', title: '维修负责/被分配人', type: 'string', width: 120, align: 'center' },
				//{ field: 'CREATENAME', title: '分配时间', type: 'string', width: 120, align: 'center' },
                { field: 'CLOCKONDATE', title: '维修执行/开始计时时间', type: 'string', width: 120, align: 'center' },
                { field: 'COMMENTS', title: '维修执行备注', type: 'datetime', width: 120, align: 'center' },

                { field: 'CLOCKOFFNAME', title: '结束计时确认人', type: 'string', width: 120, align: 'center' },
                { field: 'LASTCLOCKOFFDATE', title: '结束计时确认时间', type: 'string', width: 120, align: 'center' },

            ],
            tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },
			Resource: null,
            Name: null,
            Names: [],
			Resources:[],
            
            

            //接口地址
            apiUrl: {
                GetNameObject: "/api/query/GetNameObject",
                GetEmployee: "/api/query/GetEmployee",
                Getclassname: "/api/query/Getclassname",
                GetEquipmentComplete: "/api/query/GetEquipmentComplete",
                EquipmentRepairCompleteMaint: "/api/cdo/EquipmentRepairCompleteMaint",
            },
        }
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        addRow() {
				//this.reset();
				this.show = true;
		},
        close() {
				this.show = false;
		},
		getResource(query) {
				if (query) {
					let params = {
						objectCategory: "RESOURCE",
						cdo: "RESOURCE",
						name: query
					};
					this.http.get(this.apiUrl.GetNameObject, params).then(res => {
						if (res.Result == 1) {
							this.Resources = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
        GetEmployee(query) {
            if (query) {
                let params = {
                    objectCategory: "name",
                    //cdo: "PrintQueue",
                    name: query
                };
                this.http.get(this.apiUrl.GetEmployee, params).then(res => {
                    if (res.Result == 1) {
                        this.Names = res.Data;

                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        		 getclassname(query) {
				if (query) {
					let params = {
						//objectCategory: "RESOURCE",
						//cdo: "RESOURCE",
						classname: query
					};
					this.http.get(this.apiUrl.Getclassname, params).then(res => {
						if (res.Result == 1) {
							this.classnames = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
        reset() {
            this.classname = '',
            this.Info_memo= '',
            this.Info_number= '';
            this.tableData = [];
            this.$refs.table.rowData = [];
            this.$refs.table.paginations.total = 0;
			this.Resource = '';
            this.searchPlanDate = null;
        },
        rowClick({
            row,
            column,
            index
        }) {
            this.$refs.table.$refs.table.toggleRowSelection(row);
        },
        getRow() {
      
            this.$refs.table.load(null, true);
        },
        loadBefore(params, callBack) {
            //params["container"] = this.searchContainer;
            params["Resource"] = this.Resource;
            //params["Name"] = this.Name;
            params["classname"] = this.classname;
            params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
            params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
            callBack(true)
        },
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                this.tableData = result.Data.tableData;
                this.$refs.table.rowData = result.Data.tableData;
                this.$refs.table.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },

        submitRow() {
            const rows = this.$refs.table.getSelected();
            if (rows.length == 0) {
                this.$message.error('请选择需要关闭的维修单')
                return;
            }
            if (!this.Info_memo) {
				    this.$message.error('处理备注是必填项！')
				    return;
				}
            if (!this.Info_number) {
				    this.$message.error('维修分数是必填项！')
				    return;
				}
            const validRows = rows.filter(row => row.OrderName && row.ResourceName);

            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                OrderName: rows[0].OrderName,
				Resource: rows[0].ResourceName,
                JobModel:rows[0].Jobmodelname,
                Notes: this.Info_memo,
                JobCore: this.Info_number,
				//stagename: rows[0].stagename,
                //requestData: validRows
            };
            this.http.post(this.apiUrl.EquipmentRepairCompleteMaint, params, true).then(res => {
                if (res.Result == 1) {
                    this.reset();
                    this.$message.success("关闭计时成功");
                } else {
                    this.$message.error(res.Message);
                }
            });
        }
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>