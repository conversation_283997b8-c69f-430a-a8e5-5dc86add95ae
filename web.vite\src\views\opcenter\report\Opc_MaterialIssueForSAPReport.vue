<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder"	:loading="loading">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>物料批次</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="searchContainer" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getContainer"	:loading="loading">
						<el-option v-for="item in containers" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
            <div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>时间</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">物料消耗报工异常</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" :columns="columns" :height="518"
			:pagination-hide="false" :load-key="true" :column-index="true" :url="apiUrl.getMaterialIssueForSAPReport"
			@loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false" :ck="false"></vol-table>
	</div>	
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
	import { mapState } from 'vuex';
	import Excel from '@/uitils/xlsl.js'

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox
		},
		data() {
			return {
				show: false,
				columns: [
					{ field: 'CDOID', hidden: true, title: 'Id', type: 'string', width: 120, align: 'center' },
					{ field: 'CDOName', hidden: true, title: 'CDOName', type: 'string', width: 120, align: 'center' },
					{ field: 'Indexs', hidden: true, title: 'Indexs', type: 'string', width: 120, align: 'center' },
					{ field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },		
					{ field: 'Container', title: '物料批次', type: 'string', width: 120, align: 'center' },
					{ field: 'SAPLot', title: 'SAP批次', type: 'string', width: 120, align: 'center' },
					{ field: 'Material', title: '物料编号', type: 'string', width: 120, align: 'center' },
					{ field: 'M_Description', title: '物料品名', type: 'string', width: 120, align: 'center' },
					{ field: 'Qty', title: '数量', type: 'string', width: 80, align: 'center' },
					{ field: 'Status', title: '状态', type: 'string', width: 100, align: 'center' },
					{ field: 'Message', title: '报错信息', type: 'string', width: 100, align: 'center' },
					{ field: 'ReportDate', title: '更新时间', type: 'string', width: 100, align: 'center' },
					{ field: 'CreateDate', title: '创建时间', type: 'string', width: 100, align: 'center' },
				],
				tableData: [],
				pagination: { total: 0, size: 30, sortName: "" },

				//搜索框字段
				searchMfgOrder: null,
				searchContainer: null,
				searchDate: null,
				containers: [],
				mfgorders:[],

				//接口地址
				apiUrl: {
					getRevisionObject: "/api/query/getRevisionObject",
					getNameObject: "/api/query/getNameObject",
					getContainer: "/api/query/getContainer",
					getMaterialIssueForSAPReport: '/api/query/getMaterialIssueForSAPReport',
				}

			}
		},
		created() {
            
		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
			getMfgOrder(query) {
				if (query) {
					let params = {
						cdo: "mfgorder",
						name: query
					};
					this.http.get(this.apiUrl.getNameObject, params).then(res => {
						if (res.Result == 1) {
							this.mfgorders = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			getContainer(query) {
				if (query) {
					let params = {
						level: "",
						container: query
					};
					this.http.get(this.apiUrl.getContainer, params).then(res => {
						if (res.Result == 1) {
							this.containers = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
            queryRow() {
                if (!this.searchMfgOrder && !this.searchContainer && !this.searchDate) {
                    this.$message.warning('请输入至少一个查询条件');
                    return;
                }
				this.$refs.table.load(null, true);
			},	
			//清除数据
			reset() {
				this.searchMfgOrder = null;
				this.searchContainer = null;
				this.searchDate = null;
				this.tableData = [];
				this.$refs.table.rowData = [];
				this.$refs.table.paginations.total = 0;
			}, 	
			loadBefore(params, callBack) {	
				params["mfgorder"] = this.searchMfgOrder;			
				params["container"] = this.searchContainer;			
				params["planstart"] = this.searchDate != null ? this.searchDate[0] : '';			
				params["planend"] = this.searchDate != null ? this.searchDate[1] : '';			
				callBack(true)
			},
			loadAfter(rows, callBack, result) {
				if (result.Result == 1) {
					//this.columns = result.Data.colums;
					this.tableData = result.Data.tableData;
					this.$refs.table.rowData = result.Data.tableData;
					this.$refs.table.paginations.total = result.Data.total;
				}
				else {
					this.$message.error(result.Message);
				}
				callBack(false);
			},
		}
	}
</script>
<style lang="less" scoped>
.table-item-header {
  display: flex;
  align-items: center;
  padding: 6px;

  .table-item-border {
    height: 15px;
    background: rgb(33, 150, 243);
    width: 5px;
    border-radius: 10px;
    position: relative;
    margin-right: 5px;
  }

  // .table-item-text {
  // 	font-weight: bolder;
  // 	border-bottom: 1px solid #0c0c0c;
  // }
  .table-item-text {
    margin-top: 3px;
    padding-bottom: 6px;
    font-weight: bold;
    font-size: 15px;
    color: #484848;
    white-space: nowrap;
    border-bottom: 2px solid #676767;
    margin-bottom: -1px;
    letter-spacing: 1px;
  }

  .table-item-buttons {
    flex: 1;
    text-align: right;
  }

  .small-text {
    font-size: 12px;
    color: #2196f3;
    margin-left: 10px;
    position: relative;
    top: 2px;
  }
}
</style>