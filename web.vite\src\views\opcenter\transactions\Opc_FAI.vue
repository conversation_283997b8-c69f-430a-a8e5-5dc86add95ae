<template>
    <div class="page-header">
        <!-- 搜索条件 -->
        <div style="display: flex; margin-top: 5px">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>检验单号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchSelfInspection" clearable filterable placeholder="键入搜索"
                        style="width: 200px" remote-show-suffix :remote="true" :remote-method="getSelfInspection">
                        <el-option v-for="item in selfInspections" :key="item.Name" :label="item.Name"
                            :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>检验类型</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchInspectionType" clearable filterable placeholder="键入搜索"
                        style="width: 200px" remote-show-suffix :remote="true" :remote-method="getInspectionType">
                        <el-option v-for="item in inspectionTypes" :key="item.Name" :label="item.Name"
                            :value="item.Value" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工单</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getMfgOrder">
                        <el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工序</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchSpec" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getSpec">
                        <el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>机台</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchResource" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getResource">
                        <el-option v-for="item in resources" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
        </div>
        <div style="display: flex; margin-top: 5px">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>组别</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchEmployeeGroup" clearable filterable placeholder="键入搜索"
                        style="width: 200px" remote-show-suffix :remote="true" :remote-method="getEmployeeGroup">
                        <el-option v-for="item in employeeGroups" :key="item.Name" :label="item.Name"
                            :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>状态</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchStatus" clearable filterable placeholder="请选择" style="width: 100px">
                        <el-option v-for="item in status" :key="item.Name" :label="item.Name" :value="item.Value" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px; ">
                <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                    <span>检验时间</span>
                </label>
                <div style="margin-top: 5px;">
                    <el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
                        end-placeholder="结束" :size="size" style="width: 260px" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD hh:mm:ss" />
                </div>
            </div>
             <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>课别</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="className" clearable filterable placeholder="键入搜索"
                        style="width: 200px" remote-show-suffix :remote="true" :remote-method="getClassName">
                        <el-option v-for="item in classNames" :key="item.Name" :label="item.Name"
                            :value="item.Name" />
                    </el-select>
                </div>
            </div>
        </div>
    </div>
    <!-- 数据执行 -->
    <div class="table-item">
        <div class="table-item-header">
            <!-- <div class="table-item-border"></div>-->
            <span class="table-item-text">检验单信息</span>
            <div class="table-item-buttons">
                <div>
                    <el-button icon="Search" @click="queryRow" plain>查询</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                    <el-button type="primary" icon="FolderChecked" @click="receiveRow" plain>接收</el-button>
                    <el-button type="success" icon="Check" @click="editRow" plain>执行</el-button>
                    <el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="masterTable" index :tableData="masterTableData" @rowClick="rowClick" @rowDbClick="rowDbClick"
            :columns="masterColumns" :height="478" :pagination-hide="false" :load-key="true" :column-index="true"
            :single="true" :url="apiUrl.getSelfInspectionOrder" @loadBefore="masterLoadBefore"
            @loadAfter="masterLoadAfter" :defaultLoadPage="false" :ck="true" :linkView="openDetail"></vol-table>
    </div>
    <!-- 编辑弹出框 -->
    <vol-box :lazy="true" v-model="showExe" :title="classNameStr+'首检' + '检验报表'" :width="1200" :padding="5">
        <div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>检验单号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoSelfInspection"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>检验类型</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoInspectionType"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>工单</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoMfgOrder"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>产品编号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoProduct"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>产品描述</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoDescrition"></el-input>
                </div>
            </div>
        </div>
        <div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>注塑生产穴数</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoCavityCount"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>客户编码</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoCustomerReference"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>客户料号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="infoCustomerProduct"></el-input>
                </div>
            </div>
            <div style="margin-top: 25px">
            <el-button 
            icon="ArrowRight" 
            type="primary" 
            size="small"  @click="goToTargetPage" style="margin-left: 10px;">品质检验表
            </el-button>
            </div>
        </div>

        <div class="table-item-header">
            <span class="table-item-text">物料清单核对</span>
        </div>
        <vol-table ref="materialTable" index :tableData="materialTableData" :columns="materialTableCols"
            :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
            :columnIndex="false" ></vol-table>

        <div class="table-item-header">
            <span class="table-item-text">定性检验项目</span>
        </div>
        <vol-table ref="qualitativeTable" index :tableData="qualitativeTableData" :columns="qualitativeCols"
            :max-height="600" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
            :edit="true"></vol-table>

        <div class="table-item-header">
            <span class="table-item-text">定量检验项目</span>
        </div>
        <vol-table ref="quantitativeTable" index :tableData="quantitativeTableData" :columns="quantitativeCols"
            :max-height="600" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
            :edit="true"></vol-table>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>单据检测</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="IAResult"style="width: 200px">
						<el-option v-for="item in InepectResults" 
                        :key="item.key" :label="item.value" :value="item.key" />
					</el-select>
					<!-- <el-input style="width: 200px;" disabled v-model="InepectResult" placeholder=" "></el-input> -->
				</div>
			</div>
        <template #footer>
            <div v-show="showExeBtn">
                <el-button type="success" icon="Check" size="small" @click="exeSave">保存</el-button>
                <!-- <el-button icon="Close" size="small" @click="exeClose">关闭</el-button> -->
            </div>
        </template>
    </vol-box>

    <vol-box :lazy="true" v-model="showCollect" title="定量项目实测值" :width="500" :padding="5">
        <div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>检验项目</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input disabled v-model="collectInspectionPoint"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 80px; margin-left: 5px; font-size: 16px">
                    <span>下限</span>
                </label>
                <div style="margin-top: 5px;width: 100px;">
                    <el-input disabled v-model="collectLowerLimit"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 80px; margin-left: 5px; font-size: 16px">
                    <span>上限</span>
                </label>
                <div style="margin-top: 5px;width: 100px;">
                    <el-input disabled v-model="collectUpperLimit"></el-input>
                </div>
            </div>
        </div>
        <vol-table ref="collectTable" index :tableData="collectTableData" :columns="collectCols" :max-height="600"
            :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
            :edit="true"></vol-table>
        <template #footer>
            <div v-show="showCollectBTN">
                <el-button v-show="showIOT" type="primary" :icon="Share" size="small"
                    @click="collectIOT">获取数据</el-button>
                <el-button @click="addRow" type="primary">增加行数</el-button>
                <el-button @click="removeRow" type="danger" plain>减少行</el-button>
                <el-button type="success" icon="Check" size="small" @click="collectSave">保存</el-button>
                <el-button type="info" icon="Refresh" @click="initCollectTableData" plain>重置</el-button>

            </div>
        </template>
    </vol-box>
    <vol-box :lazy="true" v-model="showResult" title="生产自检结果" :width="500" :padding="5" ref="props"  >
        <VolForm ref="formHeader"
          :loadKey="true"
          :formFields="propsFields"
          :formRules="propsRules">
        </VolForm>
        <vol-table ref="propsTable" :tableData="propsTableData" :columns="propsColums"
            :pagination-hide="true" :ck="false"
            :edit="false"></vol-table>
    </vol-box>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from "@/components/basic/VolForm.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox,
        'VolForm': VolForm,
    },
    data() {
        return {
            //主页面
            searchSelfInspection: null,
            searchInspectionType: null,
            searchMfgOrder: null,
            searchSpec: null,
            searchResource: null,
            searchEmployeeGroup: null,
            searchStatus: null,
            searchTxnDate: null,
            className: null,
            inspectionTypes: [{ Name: '首件', Value: '1' }, { Name: '末件', Value: '2' }],
            status: [{ Name: '待接收', Value: '0' }, { Name: '已接收', Value: '1' }, { Name: '已检验', Value: '2' }],
            selfInspections: [],
            mfgorders: [],
            specs: [],
            resources: [],
            employeeGroups: [],
            classNames:[],
            classNameStr:null,
            userName: null,
            
            masterTableData: [],
            tempMasterTableData: [],

            //执行页面
            showExeBtn: true,
            showExe: false,
            infoSelfInspection: null,
            infoInspectionType: null,
            infoMfgOrder: null,
            infoProduct: null,
            infoDescrition: null,
            infoCavityCount: null,
            infoCustomerReference: null,
            infoCustomerProduct: null,
            IAResult:'',
            InepectResults:[{key: '1', value: '合格'}, {key: '0', value: '不合格'}],
            IAResults: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }],
            materialTableData: [],
            qualitativeTableData: [{ItemResult: 'OK'},],
            quantitativeTableData: [],
            collectTableData: Array(7).fill({ ActualValue: '', Defect: '' }),
            //数采页面
            currentRowIndex: null,
            showIOT: false,
            showCollect: false,
            showCollectBTN: true,
            

            masterColumns: [
                { field: 'CDOID', title: '建模ID', type: 'string', width: 0, hidden: true, align: 'center' },
                { field: 'CDOName', title: '检验单号', link:true,type: 'string', width: 100, align: 'center' },
                { field: 'InspectionType', title: '检验类型', type: 'string', width: 100, align: 'center' },
                { field: 'EmployeeGroup', title: '组别', type: 'string', width: 100, align: 'center' },
                { field: 'MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
                { field: 'Product', title: '产品编码', type: 'string', width: 100, align: 'center' },
                { field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
                { field: 'Spec', title: '工序', type: 'string', width: 100, align: 'center' },
                { field: 'ResourceName', title: '机台', type: 'string', width: 100, align: 'center' },
                { field: 'ProductionTiming', title: '制作时机', type: 'string', width: 100, align: 'center' },
                { field: 'CustomerReference', title: '客户编码', type: 'string', width: 80, align: 'center' },
                { field: 'CustomerProduct', hidden: true, title: '客户料号', type: 'string', width: 80, align: 'center' },
                { field: 'Qty', title: '工单数量', type: 'string', width: 80, align: 'center' },
                {
                    field: 'Status', title: '状态', type: 'string', width: 60, align: 'center',
                    cellStyle: (row, rowIndex, columnIndex) => {
                        switch (row.Status) {
                            case '已接收':
                                return { background: "#F2F9EC", color: "#414141" };
                            case '已检验':
                                return { background: "#2196F3", color: "#fff" };
                        }
                    },
                },
                {
                    field: 'OutCome', title: '最终判定', type: 'string', width: 80, align: 'center',//'OutCome'
                    cellStyle: (row, rowIndex, columnIndex) => {
                        switch (row.OutCome) {
                            case '合格':
                                return { background: "#82C256", color: "#fff" };
                            case '不合格':
                                return { background: "#E93F2C", color: "#fff" };
                        }
                    },
                },
                { field: 'ReportDate', title: '自检时间', type: 'datetime', width: 120, align: 'center' },
                { field: 'InspectionDate', title: '检验时间', type: 'datetime', width: 120, align: 'center' },
                { field: 'Inspector', title: '检验人', type: 'string', width: 80, align: 'center' },
                // { field: 'Notes', title: '备注', type: 'string', width: 200, align: 'center' },
            ],

            materialTableCols: [
                { field: 'Container', title: '物料批次', type: 'string', width: 0, align: 'center' },
                { field: 'Product', title: '物料编码', type: 'string', width: 0, align: 'center' },
                { field: 'P_Description', title: '物料描述', type: 'string', width: 130, align: 'center' },
                {
                    field: 'ItemResult', title: '实物与清单核对', bind: { key: null, data: [] }, 
                    edit: { type: "select",
                options: [
                    { label: 'OK', value: 'OK' }, 
                     { label: 'NG', value: 'NG' }
                      ] },
                width: 120,
                align: 'center',
                    cellStyle: (row, rowIndex, columnIndex) => {
                        switch (row.ItemResult) {
                            case 'OK':
                                return { background: "#82C256", color: "#fff" };
                            case 'NG':
                                return { background: "#E93F2C", color: "#fff" };
                        }
                    },
                },
                { field: 'Notes', title: '备注', edit: true, type: 'string', width: 150, align: 'center' }
            ],

            qualitativeCols: [
                { field: 'InspectionPoint', title: '检验项目', edit: false, type: 'string', width: 130, align: 'center' },
                { field: 'InspectionPointContent', title: '检验内容', edit: false, type: 'string', width: 130, align: 'center' },
                {
                    field: 'ItemResult', title: '检验结果', bind: { key: null, data: [] }, edit: { type: "select" }, width: 80, align: 'center',
                    cellStyle: (row, rowIndex, columnIndex) => {
                        switch (row.ItemResult) {
                            case 'OK':
                                return { background: "#82C256", color: "#fff" };
                            case 'NG':
                                return { background: "#E93F2C", color: "#fff" };
                        }
                    },
                },
                // { field: 'Defect', title: '不良明细', edit: true, type: 'string', width: 130, align: 'center' },
                { field: 'Notes', title: '备注', edit: true, type: 'string', width: 130, align: 'center' }
            ],

            quantitativeCols: [
                { field: 'InspectionPoint', title: '检验项目', edit: false, type: 'string', width: 130, align: 'center' },
                { field: 'InspectionPointContent', title: '检验内容', edit: false, type: 'string', width: 130, align: 'center' },
                { field: 'inspectionpointtool', title: '检验工具', edit: false, type: 'string', width: 100, align: 'center' },
                { field: 'LowerLimit', title: '下限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
                { field: 'UpperLimit', title: '上限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
                { field: 'DefaultValue', title: '标准值', edit: false, type: 'string', width: 80, align: 'center' },
                {
                    field: 'ActualValue', title: '实测值', align: 'center', width: 80, render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <el-button
                                    onClick={($e) => {
                                        this.collectInspectionPoint = row.InspectionPoint;
                                        this.collectUpperLimit = row.UpperLimit;
                                        this.collectLowerLimit = row.LowerLimit;
                                        this.currentRowIndex = index;
                                        this.initCollectTableData();

if (row.ActualValue != null) {
    for (let i = 0; i < row.ActualValue.length; i++) {
        const actualValue = row.ActualValue[i]?.ActualValue ?? '';
        const defect = row.ActualValue[i]?.Defect ?? ''; // 获取 Defect 字段

        if (this.collectTableData[i]) {
            // 如果该位置已存在，直接赋值
            this.collectTableData[i].ActualValue = actualValue;
            this.collectTableData[i].Defect = defect;
        } else {
            // 如果该位置不存在，push进去
            this.collectTableData.push({
                ActualValue: actualValue,
                Defect: defect
            });
        }
    }
}
                                        this.showCollect = true;
                                        if (row.FromIOT == '1') {
                                            this.showIOT = true;
                                        } else {
                                            this.showIOT = false;
                                        }
                                    }}
                                    size="small"
                                    type="primary"
                                    icon="Edit"
                                    plain >
                                </el-button>
                                {/* 这里可以接着放按钮或者其他组件 */}
                            </div>
                        );
                    }
                },
                {
                    field: 'ItemResult', title: '检验结果', edit: false, type: 'string', width: 80, align: 'center',
                    cellStyle: (row, rowIndex, columnIndex) => {
                        switch (row.ItemResult) {
                            case 'OK':
                                return { background: "#82C256", color: "#fff" };
                            case 'NG':
                                return { background: "#E93F2C", color: "#fff" };
                        }
                    },
                },
                { field: 'FromIOT', title: '来源IOT', hidden: true, edit: false, type: 'string', width: 130, align: 'center' },
                { field: 'Notes', title: '备注', edit: true, type: 'string', width: 130, align: 'center' },
            ],
            collectCols: [
            { field: 'ActualValue', title: '实测值', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
            { field: 'Defect', title: '备注', edit: { type: 'string' }, type: 'string', width: 100, align: 'center' },
            ],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getSelfInspectionOrder: '/api/query/GetSelfInspectionOrder',
                getSelfInspectionMaterils: '/api/query/GetSelfInspectionMaterils',
                getQualityInspectionSheet: '/api/query/getQualityInspectionSheet',
                selfInspectionOrderMaint: '/api/cdo/selfInspectionOrderMaint',
                getSelfInspectionCollect: '/api/query/GetSelfInspectionCollect',
                getSelfInspectionCollectV2: '/api/query/GetSelfInspectionCollectV2',
                getSelfInspectionDetails: '/api/query/GetSelfInspectionDetails',
                GetClassNamebyEmployee: '/api/query/GetClassNamebyEmployee',
            },
            propsFields:{
                MfgOrder:null,
                Spec:null,
                Resource:null,
                ProductionTiming:null,
                InspectionType: null,
                Product:null,
                P_Description:null,
                CustomerCode:null,
                CustomerProduct:null,
                Inspecter:null,
                InspectDate: null,
            },
            propsRules:[
                [
                    {
                        title: '工单',
                        field: 'MfgOrder',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '工序',
                        field: 'Spec',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '机台',
                        field: 'Resource',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '制作时机',
                        field: 'ProductionTiming',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '检验类型',
                        field: 'InspectionType',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '产品编码',
                        field: 'Product',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    }
                ],
                    [
                    {
                        title: '产品描述',
                        field: 'P_Description',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '客户编码',
                        field: 'CustomerCode',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '客户料号',
                        field: 'CustomerProduct',
                        type: 'string',
                        required: false,
                        readonly:true,
                        colSize:2
                    },
                    {
                        title: '检验人',
                        field: 'Inspecter',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    },
                    {
                        title: '检验时间',
                        field: 'InspectDate',
                        type: 'string',
                        required: false,
                        readonly: true,
                        colSize:2
                    }
                ],
            ],
            propsColums:[
                { field: 'InspectionPoint', title: '检验项目', type: 'string', width: 100, align: 'center' },
                { field: 'InspectionPointContent', title: '检验内容', type: 'string', width: 130, align: 'center' },
                { field: 'inspectionpointtool', title: '检验工具', type: 'string', width: 100, align: 'center' },
                { field:'ItemResult',title:'检验结果',type:'string',width:100,align:'center'},
                { field: 'Notes', title: '备注', type: "string",  width: 160, align: 'center' },
            ],
            propsTableData: [],
            showResult: false,
        }
    },
   async created() {
        await this.getIAResults();
        this.initCollectTableData();
        this.getClassNameStr();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
    goToTargetPage() {
        if (!this.infoProduct) {
            this.$message.warning('产品为空，无法跳转');
            return;
        }

        // 假设你要跳转到 /target-page 并携带参数
        this.$tabs.open({
            path: '/Opc_QualityInspectionSheet',
            text: '品质检验表',
            query: {
                InspectionType: this.searchInspectionType,
                product: this.infoProduct,
            }
        });
    },
    removeRow() {
        if (this.collectTableData.length > 1) {
            this.collectTableData.pop();
        } else {
            this.$message.warning('至少保留一行');
        }
    },
    addRow() {
        this.collectTableData.push({ ActualValue: null });
    },
        getMfgOrder(query) {
            if (query) {
                let params = {
                    cdo: "mfgorder",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.mfgorders = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getSelfInspection(query) {
            if (query) {
                let params = {
                    cdo: "W_SELFINSPECTIONORDER",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.selfInspections = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getSpec(query) {
            if (query) {
                let params = {
                    cdo: "spec",
                    name: query,
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.specs = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getResource(query) {
            if (query) {
                let params = {
                    cdo: "resource",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.resources = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getEmployeeGroup(query) {
            if (query) {
                let params = {
                    cdo: "employeeGroup",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.employeeGroups = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getIAResults() {
            this.materialTableCols[3].bind.data = this.IAResults;
            this.qualitativeCols[2].bind.data = this.IAResults;
        },
initCollectTableData() {
    if (this.savedCollectData && this.savedCollectData.length > 0) {
        // 如果已有保存的数据，则用它来初始化
        this.collectTableData = [...this.savedCollectData];
    } else {
        // 否则初始化为 7 行空数据
        this.collectTableData = [];
        for (let i = 0; i < 7; i++) {
            this.collectTableData.push({ ActualValue: null });
        }
    }
        },
        reset() {
            this.searchSelfInspection = null;
            this.searchInspectionType = null;
            this.searchMfgOrder = null;
            this.searchSpec = null;
            this.searchResource = null;
            this.searchEmployeeGroup = null;
            this.searchStatus = null;
            this.searchTxnDate = null;
            this.masterTableData = [];
            this.$refs.masterTable.rowData = [];
            this.$refs.masterTable.paginations.total = 0;
        },
        //清除数据
        resetMaster() {
            this.masterTableData = [];
            this.tempMasterTableData = [];
            this.$refs.masterTable.rowData = [];
            this.$refs.masterTable.paginations.total = 0;
        },
        resetEdit() {
            this.editFQCNumber = null;
            this.editMfgOrder = null;
            this.editProduct = null;
            this.editDescrition = null;
            this.editOrderQty = null;
            this.editInventoryInspectionQty = null;
            this.editAQLQty = null;
            this.editCustomer = null;
            this.editNotes = null;
            this.qualitativeTableData = [];
            this.quantitativeTableData = [];
        },
        rowClick({
            row,
            column,
            index
        }) {
            // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
            this.$refs.masterTable.$refs.table.toggleRowSelection(row);
        },
        rowDbClick({
            row,
            column,
            index
        }) {
            if (row.Status == '已检验') {
                this.getSelfInspectionMaterils(row);
                this.getQualityInspectionSheetV2(row);
                this.infoSelfInspection = row.CDOName;
                this.infoInspectionType = row.InspectionType;
                this.infoMfgOrder = row.MfgOrder;
                this.infoProduct = row.Product;
                this.infoDescrition = row.P_Description;
                this.infoCavityCount = row.CavityCount;
                this.infoCustomerReference = row.CustomerReference;
                this.infoCustomerProduct = row.CustomerProduct;

                this.showCollectBTN = false;
                this.showExeBtn = false;
                this.showExe = true;
            }
            else {
                this.$message.error('此检验单未检验');
                return;
            }
        },
        queryRow() {
            if (this.searchMfgOrder == null
                && this.searchStatus == null
                && this.searchSelfInspection == null
                && this.searchInspectionType == null
                && this.searchSpec == null
                && this.searchResource == null
                && this.searchEmployeeGroup == null
                && this.searchTxnDate == null
                && this.className == null
            ) {
                this.userName = this.userInfo.userName;
            }
            else {
                this.userName = null;
            }
            this.resetMaster();
            this.$refs.masterTable.load(null, true);
        },
        masterLoadBefore(params, callBack) {
            params["MfgOrder"] = this.searchMfgOrder != null ? this.searchMfgOrder : null;
            params["CDOName"] = this.searchSelfInspection != null ? this.searchSelfInspection : null;
            params["InspectionType"] = this.searchInspectionType != null ? this.searchInspectionType : null;
            params["spec"] = this.searchSpec != null ? this.searchSpec : null;
            params["resource"] = this.searchResource != null ? this.searchResource : null;
            params["EmployeeGroup"] = this.searchEmployeeGroup != null ? this.searchEmployeeGroup : null;
            params["Status"] = this.searchStatus != null ? this.searchStatus : null;
            params["StartTime"] = this.searchTxnDate != null ? this.searchTxnDate[0] : null;
            params["EndTime"] = this.searchTxnDate != null ? this.searchTxnDate[1] : null;
            params["UserName"] = this.userName;
            params["ClassName"] = this.className != null ? this.className : null;
            callBack(true)
        },
        masterLoadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                //this.columns = result.Data.colums;
                this.masterTableData = result.Data.tableData;
                this.tempMasterTableData = result.Data.tableData;
                this.$refs.masterTable.rowData = result.Data.tableData;
                this.$refs.masterTable.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },
        receiveRow() {
            const rows = this.$refs.masterTable.getSelected();
            if (!rows.length) {
                this.$message.error('请选中即将接收的行')
                return;
            }
            if (rows[0].Status != '待接收') {
                this.$message.error('只允许待接收的自检单')
                return;
            }
            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                CDOName: rows[0].CDOName,
                type: 'receive',
                status: "1"
            };
            this.http.post(this.apiUrl.selfInspectionOrderMaint, params, true).then(res => {
                if (res.Result == 1) {
                    this.queryRow();
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        editRow() {
            const rows = this.$refs.masterTable.getSelected();
            if (!rows.length) {
                this.$message.error('请选中即将执行的行')
                return;
            }
            if (rows[0].Status == '待接收') {
                this.$message.error('请先接收检验单')
                return;
            }
            if (rows[0].Status == '已检验') {
                this.$message.error('检验单已检验')
                return;
            }
            this.resetEdit();
            //查找检验表
            this.getSelfInspectionMaterils(rows[0]);
            this.getQualityInspectionSheet(rows[0]);
            this.infoSelfInspection = rows[0].CDOName;
            this.infoInspectionType = rows[0].InspectionType;
            this.infoMfgOrder = rows[0].MfgOrder;
            this.infoProduct = rows[0].Product;
            this.infoDescrition = rows[0].P_Description;
            this.infoCavityCount = rows[0].CavityCount;
            this.infoCustomerReference = rows[0].CustomerReference;
            this.infoCustomerProduct = rows[0].CustomerProduct;
            this.showCollectBTN = true;
            this.showExeBtn = true;
            this.showExe = true;
        },
        getSelfInspectionMaterils(row) {
            let params = {
                cdoname: row.CDOName
            };
            this.http.post(this.apiUrl.getSelfInspectionMaterils, params, true).then(res => {
                if (res.Result == 1) {
                    // 当showExeBtn为true时，重置ItemResult为null
                    if (this.showExeBtn) {
                        res.Data.tableData.forEach(item => {
                            item.ItemResult = 'OK';
                            
                        });
                    }
                    this.materialTableData = res.Data.tableData;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        setCollectDetails(row) {
            let params = {
                cdoname: row.CDOName
            };
            this.http.post(this.apiUrl.getFQCCollectDetails, params, true).then(res => {
                if (res.Result == 1) {
                    //给检验表赋值
                    this.qualitativeTableData.forEach(item => {
                        const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
                        if (tempData != null) {
                            item.ItemResult = tempData.ItemResult == "0" ? 'NG' : 'OK';
                            item.Notes = tempData.Notes;
                        }
                    });
                    this.quantitativeTableData.forEach(item => {
                        const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
                        if (tempData != null) {
                            item.ItemResult = tempData.ItemResult == "0" ? 'NG' : 'OK';
                            item.Notes = tempData.Notes;
                            item.ActualValue = tempData.Detail;
                        }
                    });
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        getQualityInspectionSheet(row) {
            //查找检验表
            let params = {
                product: row.Product,
                spec: row.Spec,
                inspectionType: '2',//FAI
            };
            this.http.post(this.apiUrl.getQualityInspectionSheet, params, true).then(res => {
                if (res.Result == 1) {
                    if (res.Data.tableData != null) {
                        this.qualitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '1');
                        this.quantitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '2');
                        this.setCollectDetails(row);
                    }
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        getQualityInspectionSheetV2(row) {
            //查找检验表
            let params = {
               // product: row.Product,
                //spec: row.Spec,
                cdoname: row.CDOName,
                //inspectionType: '2',//FAI
            };
            this.http.post(this.apiUrl.getSelfInspectionCollectV2, params, true).then(res => {
                if (res.Result == 1) {
                    if (res.Data.tableData != null) {
                        this.qualitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType  == '1');
                        this.quantitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '2');
                        this.setCollectDetails(row);
                    }
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        setCollectDetails(row) {
            let params = {
                cdoname: row.CDOName
            };
            this.http.post(this.apiUrl.getSelfInspectionCollect, params, true).then(res => {
                if (res.Result == 1) {
                    //给检验表赋值
                    this.qualitativeTableData.forEach(item => {
                        const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
                     if (tempData != null) {
                    // 如果 ItemResult 存在且是 "0"，则为 NG；否则默认 OK
                     item.ItemResult = tempData.ItemResult === "0" ? 'NG' : 'OK';
                     item.Notes = tempData.Notes || '';
                     } else {
                      // 没有找到对应数据时，默认设置为 OK
                      item.ItemResult = 'OK';
                      item.Notes = '';
                      }
                    });
                    this.quantitativeTableData.forEach(item => {
                        const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
                        if (tempData != null) {
                            item.ItemResult = tempData.ItemResult == "0" ? 'NG' : 'OK';
                            item.Notes = tempData.Notes;
                            item.ActualValue = tempData.Detail;
                            item.Defect = tempData.Defect;
                        }
                    });
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        outputRow() {
            // this.tableData.splice(0);
            //导出
            let tableData = this.$refs.masterTable.tableData;
            let sortData = this.$refs.masterTable.filterColumns;
            let exportData = this.handleTableSortData(tableData, sortData);
            Excel.exportExcel(exportData, "检验单" + '-' + this.base.getDate());
        },
        handleTableSortData(tableData, sortData) {
            let newArray = [];
            tableData.forEach(data => {
                let newItem = {};
                sortData.forEach(field => {
                    if (data.hasOwnProperty(field.field)) {
                        newItem[field.title] = data[field.field];
                    }
                });
                newArray.push(newItem);
            });
            return newArray
        },
        exeSave() {
            // 检查 qualitativeTableData 是否都有检验结果
            for (let item of this.materialTableData) {
                if (!item.ItemResult) {
                    this.$message.error('物料中有未填写的检验结果');
                    return;
                }
            //  if (item.ItemResult == 'NG') {
                 //   this.IAResult = 0;
              //  }
            }

            // 检查 qualitativeTableData 是否都有检验结果
            for (let item of this.qualitativeTableData) {
                if (!item.ItemResult) {
                    this.$message.error('定性检验项目中有未填写的检验结果');
                    return;
                }
                //if (item.ItemResult == 'NG') {
                //    this.IAResult = 0;
              //  }
            }

            // 检查 quantitativeTableData 是否都有检验结果
            for (let item of this.quantitativeTableData) {
                if (!item.ItemResult) {
                    this.$message.error('定量检验项目中有未填写的检验结果');
                    return;
                }
              //  if (item.ItemResult == 'NG') {
             //       this.IAResult = 0;
             //   }
            }
                if (!this.IAResult) {
                    this.$message.error('单据检测结果未填写');
                    return;
                }        

            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                CDOName: this.infoSelfInspection,
                OutCome: this.IAResult,
                Status: 2,//2: 已检验
                Type: 'inspection',
                RequestData: this.materialTableData,
                CollectDetails: this.quantitativeTableData.concat
                (this.qualitativeTableData).map(item => ({...item,Defect: item.Defect || '' // ✅ 显式添加 Defect 字段
       }))
       };
            this.http.post(this.apiUrl.selfInspectionOrderMaint, params, true).then(res => {
                if (res.Result == 1) {
                    this.showExe = false;
                    this.queryRow();
                    this.$message.success(res.Message);
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        exeClose() {
            this.showExe = false;
        },
        collectSave() {
            // 赋值回 quantitativeTableData
            if (this.currentRowIndex != null) {

        this.quantitativeTableData[this.currentRowIndex].ActualValue = this.collectTableData
            .filter(item => item.ActualValue !== null && item.ActualValue !== undefined)
            .map(item => ({
                ActualValue: item.ActualValue,
                Defect: item.Defect // 保存备注字段
            }));
                //判断上下限
                for (let item of this.collectTableData) {
                    if (parseFloat(item.ActualValue) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
                        || parseFloat(item.ActualValue) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)) {
                        this.quantitativeTableData[this.currentRowIndex].ItemResult = "NG"
                        //this.IAResult = 0;
                        break;
                    }
                    else {
                        this.quantitativeTableData[this.currentRowIndex].ItemResult = "OK"
                    }
                }
                if (this.quantitativeTableData[this.currentRowIndex].ActualValue.length == 0) {
                    this.quantitativeTableData[this.currentRowIndex].ItemResult = null;
                }
            }
            this.showCollect = false;
        },
        collectClose() {
            this.showCollect = false;
        },
        getCurrentDateTimeString() {
            const now = new Date();
            const year = now.getFullYear();
            const month = this.padNumber(now.getMonth() + 1);
            const day = this.padNumber(now.getDate());
            const hours = this.padNumber(now.getHours());
            const minutes = this.padNumber(now.getMinutes());
            const seconds = this.padNumber(now.getSeconds());

            return `${year}${month}${day}${hours}${minutes}${seconds}`;
        },
        padNumber(num) {
            return num < 10 ? '0' + num : num;
        },
        collectIOT() {

        },
         openDetail(row, column) {
            // 这里可以用弹窗、路由跳转等方式打开详情页
            // 例如用弹窗
            this.propsFields.MfgOrder = row.MfgOrder;
            this.propsFields.Spec = row.Spec;
            this.propsFields.Resource = row.ResourceName;
            this.propsFields.ProductionTiming = row.ProductionTiming;
            this.propsFields.InspectionType = row.InspectionType;
            this.propsFields.Product = row.Product;
            this.propsFields.P_Description = row.P_Description;
            this.propsFields.CustomerCode = row.CustomerReference;
            this.propsFields.CustomerProduct = row.CustomerProduct;
            this.propsFields.Inspecter = row.Inspector;
            this.propsFields.InspectDate = row.InspectionDate;
            this.http.get(this.apiUrl.getSelfInspectionDetails, { cdoName: row.CDOName }).then(res => {
                if (res.Result == 1) {
                    // this.$refs.rowData = res.Data;
                    this.propsTableData = res.Data;
                this.detailRow = row;
                this.showResult = true; 
                } else {
                    this.$message.error(res.Message);
                }
            });

        },
        getClassName(query) {
            if (query) {
                let params = {
                    cdo: "W_ClassName",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.classNames = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getClassNameStr() {
            this.http.get(this.apiUrl.GetClassNamebyEmployee, {employName:this.userInfo.userName}, false).then(res => {
				if (res.Result == 1) {
					this.classNameStr = res.Data.Name;
				} else {
					this.$message.error(res.Message);
				}
			}).catch(err => {
				this.$message.error('获取班组名称失败' + err);
			});
		},
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    // .table-item-text {
    // 	font-weight: bolder;
    // 	border-bottom: 1px solid #0c0c0c;
    // }
    .table-item-text {
        margin-top: 3px;
        padding-bottom: 6px;
        font-weight: bold;
        font-size: 15px;
        color: #484848;
        white-space: nowrap;
        border-bottom: 2px solid #676767;
        margin-bottom: -1px;
        letter-spacing: 1px;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196f3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>