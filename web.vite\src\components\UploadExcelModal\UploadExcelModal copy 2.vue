<template>
  <vol-box :lazy="true" v-model="visible" :title="title" :width="width" :height="height" :padding="5"
    :onModelClose="onModelClose">

    <el-button size="mini" type="success" style="margin-top: 10px" :disabled="disabled" class="el-dialog-position">
      <span v-if="importStatus === false">
        导入<i class="el-icon-upload el-icon--right" />
        <input ref="files" type="file" v-if="!disabled" class="excelFile" @change="excelFileMethod" />
      </span>
      <span v-if="importStatus === true">
        导入中<i class="el-icon-loading el-icon--right" />
      </span>
    </el-button>

  </vol-box>
</template>
<script>
import VolBox from '@/components/basic/VolBox.vue';

export default {
  name: 'UploadExcelModal',
  components: {
    'vol-box': VolBox,
  },
  data() {
    return {
      title: this.$ts('UploadExcel'),
      width: 600,
      height: 350,
      visible: false,
      upload: {
        //导入上传excel对象
        excel: false, //导入的弹出框是否显示
        url: "/api/Demo_Order/Import", //导入的路径,如果没有值，则不渲染导入功能
        template: {
          //下载模板对象
          url: "/api/Demo_Order/DownLoadTemplate", //下载模板路径
          fileName: "未定义文件名", //模板下载的中文名
        },
        init: false, //是否有导入权限，有才渲染导入组件
      },
      maxSize: 102 * 5,
      model: true,
      file: null,
      loadingStatus: false,
      message: "",
      resultClass: "",
      importStatus: false
    }
  },
  methods: {

    // 处理excel文件
    excelFileMethod(e) {
      // 导入状态和文件信息
      var _this = this
      _this.importStatus = true
      const excelFile = e.target.files
      // 构建fileReader对象
      const fileReader = new FileReader()
      // 该事件为读取完成时触发
      fileReader.onload = function (ev) {
        try {
          const data = ev.target.result
          const workbook = XLSX.read(data, { type: 'binary' })
          const list = ''
          const listNew = list.concat(XLSX.utils.sheet_to_json(workbook.Sheets['sheets1'], { header: 1 }))
          _this.excelData.list = listNew.slice(6).split(',')
          // 得到的数据发送axios请求
          importExcel(_this.excelData).then(res => {
            console.log(res)
            _this.importStatus = false
            if (res.code === 200) {
              _this.$alert(res.data.msg, '导入成功', {
                confirmButtonText: '确定',
                callback: () => {
                  // 确认后做什么
                }
              })
            } else {
              _this.$alert(res.data.msg, '导入失败', {
                confirmButtonText: '确定',
                callback: () => {
                  // 确认后做什么
                }
              })
            }
          })
        } catch (e) {
          _this.$message({ message: '文件类型不正确', type: 'warning' })
        }
      }
      // 读取数据
      fileReader.readAsBinaryString(excelFile[0])
    },

    show() {
      this.visible = true
    },

    onModelClose() {
      this.closeModel();
    },
    closeModel() {
      this.$emit('ok')
      this.visible = false;
    },

  }
}
</script>
<style lang="less" scoped>
.upload-container {
  min-height: 276px !important;
  display: inline-block;
  width: 100%;
  padding: 10px;
  border: 1px dashed #989898;
  min-height: 250px;
  border-radius: 5px;

  .alert {
    margin-top: 12px;
  }

  .el-button-group>* {
    display: flex;
  }

  h3 {
    margin: 9px 0px;
  }

  .file-info>span {
    margin-right: 20px;
  }

  .v-r-message {
    margin-top: 10px;

    .title {
      margin-bottom: 2px;
    }

    >.text {
      font-size: 13px;
    }

    .v-r-success {
      color: #02b702;
    }

    .v-r-error {
      color: #dc0909;
    }
  }
}
</style>