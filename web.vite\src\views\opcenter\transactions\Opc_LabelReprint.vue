<template>
    <div class="table-item-header">
        <div class="table-item-border"></div> <span class="table-item-text">标签打印</span>
    </div>
    <!-- 搜索条件 -->
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>批次条码</span></label>
            <div style=" margin-top: 5px;">
                <el-input style="width: 200px;" v-model="searchContainer" clearable placeholder="请输入"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span> </span>
            </label>
            <div style="margin-top: 7px;">
                <el-button icon="Search" @click="queryRow" plain>查询</el-button>
                <el-button icon="Refresh" type="info" plain @click="reset">重置</el-button>
            </div>
        </div>
    </div>
    <el-divider />
    <div class="table-item">
        <!-- <div class="table-item-header">
            <div class="table-item-buttons">
                <div>
                    <el-button icon="Check" @click="gotoMaterialReceive" plain>物料接收</el-button>
                    <el-button type="warning" icon="Check" @click="gotoMaterialReturn" plain>物料退料</el-button>
                    <el-button type="primary" icon="Check" @click="gotoChangeQty" plain>数量调整</el-button>
                    <el-button type="danger" icon="Check" @click="gotoLotScrap" plain>物料报废</el-button>
                </div>
            </div>
        </div> -->
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="420"
            :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"></vol-table>
    </div>
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>标签模板</span>
            </label>
            <!-- <div style="margin-top: 5px;"> -->
                <!-- <el-select v-model="printerLabelDefinition" clearable filterable placeholder="请选择" style="width: 200px"> -->
                    <!-- <el-option v-for="item in printerLabelDefinitions" :key="item.Name" :label="item.Name" -->
                        <!-- :value="item.Name" /> -->
                <!-- </el-select> -->
            <!-- </div> -->
            <div style=" margin-top: 5px;">
                <el-input style="width: 200px;" v-model="printerLabelDefinition" disabled clearable placeholder=""></el-input>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>打印机</span>
            </label>
            <div style="margin-top: 5px;">
                <!-- <el-input v-model="Spec"></el-input> -->
                <el-select v-model="printQueue" clearable filterable placeholder="请选择" style="width: 200px">
                    <el-option v-for="item in printQueues" :key="item.Name" :label="item.Name" :value="item.Name" />
                </el-select>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span></span>
            </label>
            <div style="margin-top: 7px;">
                <el-button type="primary" icon="printer" @click="PrintContainerLabel" plain>打印</el-button>
            </div>
        </div>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from "@/components/basic/VolForm.vue";
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        VolForm,
    },
    data() {
        return {
            containerTemp: '',//传递选择的container
            searchContainer: '',
            searchWorkcenter: '',
            searchProduct: '',
            searchTxnDate: [new Date().toLocaleDateString(), this.addDays(new Date().toLocaleDateString(), 7)],
            printQueue: '',
            printerLabelDefinition: '',

            workcenters: [],
            products: [],
            printerLabelDefinitions: [],
            printQueues: [],

            formFields: {
                department: null,
                workcenter: null,
                inventory: null,
                container: null,
                product: null,
                productType: null,
                modelNumber: null,
                uom: null,
                vendor: null,
                description: null,
                qty: null,
                mfgorder: null,
                comments: null
            },

            columns: [
                { field: 'LabelHistorySummaryId', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
                { field: 'Container', title: '批次条码', type: 'string', width: 80, align: 'center' },
                { field: 'CDOName', title: '事务名', type: 'string', width: 80, align: 'center' },
                { field: 'TxnDate', title: '打印时间', type: 'string', width: 100, align: 'center' },
                { field: 'LabelDefinition', title: '标签名称', type: 'string', width: 100, align: 'center' },
                { field: 'LabelCount', title: '打印数量', type: 'string', width: 50, align: 'center' },
                { field: 'PrintQueueName', title: '打印机', type: 'string', width: 80, align: 'center' },
            ],
            tableData: [],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getLabelInfo: "/api/query/getLabelInfo",
                ReprintContainerLabel:"/api/CDO/ReprintContainerLabel",
            },            
        }
    },
    created() {
        this.getPrintQueue();
        this.getPrinterLabelDefinition();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        addDays(dateStr, days) {
            var date = new Date(dateStr);
            date.setDate(date.getDate() + days);
            return date.toLocaleDateString();
        },
        remoteMethod(query) {
            if (query) {
                let params = {
                    cdo: "Product",
                    name: query
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.products = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        async getPrinterLabelDefinition() {
            let params = {
                cdo: "PrinterLabelDefinition"
            };
            this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                if (res.Result == 1) {
                    this.printerLabelDefinitions = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        async getPrintQueue() {
            let params = {
                cdo: "PrintQueue"
            };
            this.http.get(this.apiUrl.getNameObject, params).then(res => {
                if (res.Result == 1) {
                    this.printQueues = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        reset() {				
            this.searchContainer = '';
            this.searchProduct = '';
            this.searchWorkcenter = '';
            this.tableData = null;
        },
        rowClick({ row, column, event }) {
            this.$refs.table.$refs.table.toggleRowSelection(row);
            this.printerLabelDefinition = row.LabelDefinition;
            this.printQueue = row.PrintQueueName;
        },
        queryRow() {
            if (this.searchContainer == '' && this.searchProduct == '') {
                this.$message.error('请输入查询条件。')
                return;
            }
            let params = {
                type: 'reprint',
                container: this.searchContainer
            };
            this.http.get(this.apiUrl.getLabelInfo, params, true).then(res => {
                if (res.Result == 1) {
                    this.tableData = res.Data;
                    this.$refs.table.rowData = res.Data.tableData;
                    this.$refs.table.paginations.total = res.Data.length;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        outputRow() {
            // this.tableData.splice(0);
            //导出
            let tableData = this.$refs.table.tableData
            let sortData = this.$refs.table.filterColumns
            let exportData = this.handleTableSortData(tableData, sortData)
            Excel.exportExcel(exportData, "批次" + '-' + this.base.getDate());
        },
        handleTableSortData(tableData, sortData) {
            let newArray = [];
            tableData.forEach(data => {
                let newItem = {};
                sortData.forEach(field => {
                    if (data.hasOwnProperty(field.field)) {
                        newItem[field.title] = data[field.field];
                    }
                });
                newArray.push(newItem);
            });
            return newArray
        },
        PrintContainerLabel() {
            const rows = this.$refs.table.getSelected();
            if (!rows.length) {
                this.$message.error('请选中行')
                return;
            }
            if ((this.printQueue == '' || this.printQueue == null) 
            && (this.printerLabelDefinition == '' || this.printerLabelDefinition == null)){
                this.$message.error('标签/打印机不能为空')
                return;
            }
            let params = {
                User:this.userInfo.userName,
                Password:this.userInfo.userPwd,
                Container: rows[0].Container,
                Printer:this.printQueue,
                Qty:'1',
                LabelHistorySummaryId:rows[0].LabelHistorySummaryId
            };
            this.http.post(this.apiUrl.ReprintContainerLabel, params, true).then(res => {
                if (res.Result == 1) {
                    this.$message.success(res.Message);
                } else {
                    this.$message.error(res.Message);
                }
            });	
        },

    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>