<template>
	<div class="page-header" style="height: 20%;">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px;">
		  <div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>课别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="classname" clearable filterable placeholder="键入搜索" style="width: 100px"
						remote-show-suffix :remote="true" :remote-method="getclassname" :loading="loading">
						<el-option v-for="item in classnames" 
						:key="item.classname" 
						:label="item.classname" 
						:value="item.classname" />
					</el-select>
				</div>
			</div>
		<div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>组别</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="Workcentername" clearable filterable placeholder="键入搜索" style="width: 100px"
                    remote-show-suffix :remote="true" :remote-method="GetWorkcenter">
                    <el-option v-for="item in Workcenternames" 
					:key="item.Workcentername" 
					:label="item.Workcentername" 
					:value="item.Workcentername" />
                </el-select>
            </div>
        </div>
		    <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工单号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getMfgOrder">
                        <el-option v-for="item in mfgorders" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
                    </el-select>
                </div>
            </div>  
					    <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>批次号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchLot" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getLots">
                        <el-option v-for="item in Lots" 
						:key="item.containername" 
						:label="item.containername" 
						:value="item.containername" />
                    </el-select>
                </div>
            </div>  
				<div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>入库单号</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchWAREHOUSERECEIPTNAME" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="GetWAREHOUSERECEIPTNAME">
                        <el-option v-for="item in WAREHOUSERECEIPTNAMEs" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
                    </el-select>
                </div>
            </div>  
				<div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工序</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchSpec" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="GetSpecs">
                        <el-option v-for="item in Specs" 
						:key="item.Spec" 
						:label="item.Spec" 
						:value="item.Spec" />
                    </el-select>
                </div>
            </div>  
				<div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>产品编码</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchProduct" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getProduct">
                        <el-option v-for="item in products" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
                    </el-select>
                </div>
            </div>  
			<!--<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>日期筛选</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
				</div>
			</div>-->
		</div>
		<div class="table-item-header">
			<div class="table-item-border"></div> <span class="table-item-text">主表数据</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="GetEquipmentAcknowledge" plain>查询</el-button>
					<el-button icon="Refresh" type="info" plain @click="reset">重置</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="AcknowedgeTable" index :tableData="AcknowedgeTableData" @rowClick="AckRowClick" 
		    :columns="AssignedColumns"
			:height="200" :pagination-hide="false" :load-key="false" :column-index="true" :single="true"
            :url="apiUrl.GetWorkorderProductionHistoryReport" @loadBefore="AckLoadBefore" @loadAfter="AckLoadAfter" 
			:defaultLoadPage="false"
            :ck="true">
        </vol-table>
	</div>
	<div class="table-item">
		<div class="table-item-header">
			<div class="table-item-border"></div> 
            <span class="table-item-text">明细表数据</span>
			<div class="table-item-buttons">
			</div>
		</div>
		<vol-table ref="InprogressTable" index :tableData="InprogressTableData"
		    @rowClick="InprogressTableRowClick" 
		    :columns="InprogressColumns"
			:url="apiUrl.GetWorkorderProductionHistoryReportdetail" @loadBefore="InprogressTableLoadBefore"
			@loadAfter="InprogressTableLoadAfter" 
			:defaultLoadPage="false"
            :height="250" :pagination-hide="false" :load-key="false" :column-index="true" :single="true" :ck="true">
        </vol-table>
	</div>
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
    import VolForm from "@/components/basic/VolForm.vue";
    import VolHeader from "@/components/basic/VolHeader.vue";
	import { mapState } from 'vuex';

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox,
            VolForm,
            VolHeader,
		},
		data() {
			return {
				//搜索框字段
				//searchPlanDate:null,
				Workcenternames:[],
				Workcentername:'',
				WAREHOUSERECEIPTNAMEs: [],
				searchWAREHOUSERECEIPTNAME	:'',
				searchMfgOrder: null,
				mfgorders: [],
				Specs: [],
				searchSpec: null,
				searchLot: null,
				containername: '',
				Lots: [],
				Name: null,
                Names: [],
				classnames:[],
				classname: null,
				searchProduct: null,
				products: [],
				show: false,
				AssignedColumns: [
					{ field: 'classnamename', title: '课别', type: 'string', width: 130, align: 'center' },
					{ field: 'workcentername', title: '组别', type: 'int', width: 80, align: 'center' },
					{ field: 'mfgordername', title: '工单号', type: 'string', width: 130, align: 'center' },
					{ field: 'product', title: '产品编码', type: 'string', width: 130, align: 'center' },
					//{ field: 'Jobmodelname', title: '维修模式', type: 'string', width: 150, align: 'center' },
					{ field: 'description', title: '产品名称', type: 'string', width: 120, align: 'center' },
					{ field: 'orderstatusname', title: '工单状态', type: 'string', width: 150, align: 'center' },
					{ field: 'containername', title: '批次号', type: 'string', width: 100, align: 'center' },
					{ field: 'qty', title: '批次数量', type: 'string', width: 150, align: 'center' },
					{ field: 'issplit', title: '是否拆批', type: 'string', width: 120, align: 'center' },
					{ field: 'mcontainername', title: '父批次', type: 'string', width: 100, align: 'center' },
					{ field: 'passtime', title: '已生产时长', type: 'string', width: 120, align: 'center' },
					{ field: 'createtime', title: '批次报检时间', type: 'string', width: 120, align: 'center' },
					{ field: 'ispassorder', title: '是否审单', type: 'string', width: 120, align: 'center' },
					{ field: 'w_warehousereceiptname', title: '入库单', type: 'string', width: 120, align: 'center' },
					{ field: 'InnerQty', title: '入库数量', type: 'string', width: 120, align: 'center' },
					//{ field: 'COMMENTS', title: '分配描述', type: 'string', width: 120, align: 'center' },

				],
				InprogressColumns: [
					{ field: 'spec', title: '工序', type: 'string', width: 130, align: 'center' },
					{ field: 'intime', title: '进站时间', type: 'int', width: 80, align: 'center' },
					{ field: 'stdtime', title: '出站时间', type: 'string', width: 130, align: 'center' },
					{ field: 'passtime', title: '过站时长', type: 'string', width: 150, align: 'center' },
					{ field: 'w_actualworktime', title: '实际人工工时', type: 'string', width: 130, align: 'center' },
					{ field: 'capacity', title: '标准产能', type: 'string', width: 130, align: 'center' },
					{ field: 'Theoryqty', title: '理论产能', type: 'string', width: 120, align: 'center' },
					{ field: 'moveallqty', title: '生产数量', type: 'string', width: 130, align: 'center' },
					{ field: 'ScrapQty', title: '报废数量', type: 'string', width: 130, align: 'center' },
					{ field: 'rate', title: '达成率', type: 'string', width: 130, align: 'center' },
				],

				//创建窗口字段
				ActionFlag: 0,
				Info_WorkCenter: null,
				Info_ReleaseQty: null,
				Info_PlanDate: null,
				Info_Priority: null,
				Info_Notes: null,


				//接口地址
				apiUrl: {
                   
                    GetNameObject: "/api/query/GetNameObject",
					Getclassname: "/api/query/Getclassname",
                    GetWorkcenter: "/api/query/GetWorkcenter",
					GetLots: "/api/query/GetLots",
					GetSpecs: "/api/query/GetSpecs",
					GetWAREHOUSERECEIPTNAME	: "/api/query/GetWAREHOUSERECEIPTNAME",
					getRevisionObject: "/api/query/GetRevisionObject",

					GetWorkorderProductionHistoryReport:'/api/query/GetWorkorderProductionHistoryReport',
					GetWorkorderProductionHistoryReportdetail:'/api/query/GetWorkorderProductionHistoryReportdetail',
				},
			}
		},
		created() {//this.getDefaultPrinter();
		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
        GetWorkcenter(query) {
            if (query) {
                let params = {
                    //cdo: "mfgorder",
                    Workcentername: query,
                };
                this.http.get(this.apiUrl.GetWorkcenter, params).then(res => {
                    if (res.Result == 1) {
                        this.Workcenternames = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getclassname(query) {
				if (query) {
					let params = {
						//objectCategory: "RESOURCE",
						//cdo: "RESOURCE",
						classname: query
					};
					this.http.get(this.apiUrl.Getclassname, params).then(res => {
						if (res.Result == 1) {
							this.classnames = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
		GetWAREHOUSERECEIPTNAME(query) {
				if (query) {
					let params = {
						//objectCategory: "RESOURCE",
						//cdo: "RESOURCE",
						name: query
					};
					this.http.get(this.apiUrl.GetWAREHOUSERECEIPTNAME, params).then(res => {
						if (res.Result == 1) {
							this.WAREHOUSERECEIPTNAMEs = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},

		getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query
				};
				this.http.get(this.apiUrl.GetNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		GetSpecs(query) {
			if (query) {
				let params = {
					//cdo: "mfgorder",
					Spec: query
				};
				this.http.get(this.apiUrl.GetSpecs, params).then(res => {
					if (res.Result == 1) {
						this.Specs = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},		
		getLots(query) {
			if (query) {
				let params = {
					//cdo: "mfgorder",
					containername: query
				};
				this.http.get(this.apiUrl.GetLots, params).then(res => {
					if (res.Result == 1) {
						this.Lots = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getProduct(query) {
                if (query) {
                    let params = {
                        cdo: "product",
                        name: query
                    };
                    this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                        if (res.Result == 1) {
                            this.products = res.Data;
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },  
			reset() {	
				this.searchMfgOrder = '',
				this.searchSpec = '',
				this.searchWAREHOUSERECEIPTNAME = '',
				this.searchLot = '',			
				this.searchProduct = '',
				this.Workcentername = '',
				this.classname = '',
				this.AcknowedgeTableData = [],
				this.InprogressTableData = [],
				this.$refs.AcknowedgeTable.rowData = [];
				this.$refs.AcknowedgeTable.paginations.total = 0;
				this.$refs.InprogressTable.rowData = [];
				this.$refs.InprogressTable.paginations.total = 0;
			},
			GetEquipmentAcknowledge() {

                this.$refs.AcknowedgeTable.load(null, true);
				this.$refs.InprogressTable.load(null, true);
			},
            AckLoadBefore(params, callBack) {
				params["mfgordername"] = this.searchMfgOrder;
				//params["Spec"] = this.searchSpec;
				params["WAREHOUSERECEIPTNAME"] = this.searchWAREHOUSERECEIPTNAME;
				params["containername"] = this.searchLot;
				params["product"] = this.searchProduct;
				params["classname"] = this.classname;
                params["workcentername"] = this.Workcentername;
               // params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
                //params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
                callBack(true)
            },
            AckLoadAfter(rows, callBack, result) {
                if (result.Result == 1) {
                    this.AcknowedgeTableData = result.Data.tableData;
                    this.$refs.AcknowedgeTable.rowData = result.Data.tableData;
                    this.$refs.AcknowedgeTable.paginations.total = result.Data.total;
                }
                else {
                    this.$message.error(result.Message);
                }
                callBack(false);
            },
			AckRowClick({
				row,
				column,
				index
			}) {
				this.$refs.AcknowedgeTable.$refs.table.toggleRowSelection(row);
				this.searchLot = row.containername;
                this.$refs.InprogressTable.load(null, true);
			},
            InprogressTableLoadBefore(params, callBack) {
            // 获取主表选中行数据
              const selectedRows = this.$refs.AcknowedgeTable.getSelected();
              if (selectedRows && selectedRows.length > 0) {
            // 使用主表选中行的批次号作为筛选条件
              params["containername"] = selectedRows[0].containername;
              } else {
              // 如果没有选中行，使用搜索框中的批次号
              params["containername"] = this.searchLot;
              }
    
              params["Spec"] = this.searchSpec;
              callBack(true)
              },         
            InprogressTableLoadAfter(rows, callBack, result) {
                if (result.Result == 1) {
                    this.InprogressTableData = result.Data.tableData;
                    this.$refs.InprogressTable.rowData = result.Data.tableData;
                    this.$refs.InprogressTable.paginations.total = result.Data.total;
                }
                else {
                    this.$message.error(result.Message);
                }
                callBack(false);
            },
            addRow() {
				// this.tableData.push({ OrderNo: "D2022040600009" })
				//this.reset();
				this.show = true;
			},
            InprogressTableRowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行')
				this.$refs.InprogressTable.$refs.table.toggleRowSelection(row);
			},
			close() {
				this.show = false;
			},

		}
	}
</script>
<style lang="less" scoped>
	.table-item-header {
		display: flex;
		align-items: center;
		padding: 6px;

		.table-item-border {
			height: 15px;
			background: rgb(33, 150, 243);
			width: 5px;
			border-radius: 10px;
			position: relative;
			margin-right: 5px;
		}

		.table-item-text {
			font-weight: bolder;
		}

		.table-item-buttons {
			flex: 1;
			text-align: right;
            margin-top: 20px;
		}

		.small-text {
			font-size: 12px;
			color: #2196F3;
			margin-left: 10px;
			position: relative;
			top: 2px;
		}
	}
</style>