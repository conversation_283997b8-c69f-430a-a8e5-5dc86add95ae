<template>
  <div class="login-container">
    <div class="project-name"><img style="width:70%;" src="/static/logo.png" /></div>

    <div class="login-form">
      <div class="form-user" @keypress="loginPress">

        <div class="login-text">
          <div>
            <div>{{ $ts('账号登录') }}</div>
            <div class="login-line"></div>
          </div>
          <div style="flex: 1"></div>
        </div>

        <div class="login-text-small"></div>
        <div class="item">
          <div class="input-icon el-icon-user"></div>
           <el-input type="text" v-focus v-model="userInfo.userName" :placeholder="$ts(['请输入账号'])"></el-input>
        </div>

        <div class="item">
          <div class="input-icon el-icon-lock"></div>          
           <el-input v-model="userInfo.password" 
            type="text" class="no-autofill-pwd"
            :placeholder="$ts(['请输入密码'])">
              <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
          </el-input>
        </div> 

        <!-- <div class="item">
          <div class="input-icon el-icon-mobile"></div>
          <input v-focus type="text" v-model="userInfo.verificationCode" :placeholder="$ts(['请输入', '验证码'])" />
          <div class="code" @click="getVierificationCode">
            <img v-show="codeImgSrc != ''" :src="codeImgSrc" />
          </div>
        </div> -->

        <div class="item" style="align-items: center;" v-if="false">
          <div class="input-icon el-icon-setting"></div>
          <el-tree-select v-focus type="text" v-model="userInfo.LoginType" :data="treeSelectData"
            :render-after-expand="false" :placeholder="$ts(['请选择', '登录平台'])" style="width: 100%;" />
        </div>

        <div class="item">
          <div class="input-icon el-icon-setting"></div>
          <div v-if="$global.lang" class="app-lang">
            <lang color="#409eff"></lang>
          </div>
        </div>


      </div>

      <div class="loging-btn">
        <el-button size="large" :loading="loading" color="#3a6cd1" :dark="true" @click="login" long>
          <span v-if="!loading">{{ $ts('登录') }}</span>
          <span v-else>{{ $ts('defualte Login') }}...</span>
        </el-button>
      </div>

      <div class="item">
		<!-- 修改密码 -->
        <div class="app-lang">
<!--          <a
            href="https://">
            {{ $ts('Modify Password') }}
          </a> -->
        </div>
      </div>

    </div>
    <!-- 页面底部 -->
    <img class="login-bg" src="/static/login.png" />
  </div>
</template>

<script>
import {
  defineComponent,
  ref,
  reactive,
  toRefs,
  getCurrentInstance
} from 'vue';
import { useRouter, useRoute } from 'vue-router';
import store from '../store/index';
import http from '@/../src/api/http.js';
import lang from '@/components/lang/lang';
export default defineComponent({
  components: {
    lang
  },
  setup(props, context) {
    const loading = ref(false);
    const codeImgSrc = ref('');
    const userInfo = reactive({
      userName: '',
      password: '',
      //verificationCode: '',
      UUID: undefined,
      LoginType: 'Local',
    });
    const treeSelectData = reactive(
      [
        { value: 'Local', label: 'Local' },
        {
          value: 'Opcenter',
          label: 'Opcenter'
        },
      ]
    );

    // const getVierificationCode = () => {
    //   http.get('/api/User/getVierificationCode').then((x) => {
    //     codeImgSrc.value = 'data:image/png;base64,' + x.img;
    //     userInfo.UUID = x.uuid;
    //   });
    // };
    // getVierificationCode();

    let appContext = getCurrentInstance().appContext;
    let $message = appContext.config.globalProperties.$message;
    let router = useRouter();
    let $ts = appContext.config.globalProperties.$ts;
    const login = () => {
      //console.log(userInfo.LoginType, 'selectVal');

      if (!userInfo.userName) return $message.error($ts(['请输入', '账号']));
      if (!userInfo.password) return $message.error($ts(['请输入', '密码']));
      // if (!userInfo.verificationCode) {
      //   return $message.error($ts(['请输入', '验证码']));
      // }
      loading.value = true;
      http
        .post('/api/user/login', userInfo, $ts('正在登录') + '....')
        .then((result) => {
          if (!result.status) {
            loading.value = false;
            //getVierificationCode();
            return $message.error(result.message);
          }
          //  $message.success($ts("登录成功,正在跳转!"));
          store.commit('setUserInfo', result.data);
          router.push({ path: '/' });
        });

    };
    const loginPress = (e) => {
      if (e.keyCode == 13) {
        login();
      }
    };
    const openUrl = (url) => {
      window.open(url, '_blank');
    };
    return {
      loading,
      codeImgSrc,
      //getVierificationCode,
      login,
      userInfo,
      loginPress,
      openUrl,
      treeSelectData,
    };
  },
  directives: {
    focus: {
      inserted: function (el) {
        el.focus();
      }
    }
  }
});
</script>
<style lang="less" scoped>
/* 去除边框 */
:deep(.el-select .el-input__wrapper) {
  box-shadow: none !important;
}

/* 去除选中时蓝色边框（下面两个都要加上） */
:deep(.el-input .el-input__wrapper.is-focus) {
  box-shadow: none !important;
}

:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  border-color: #DCDFE6 !important;
  box-shadow: none !important;
}

.login-container {
  display: flex;
  width: 100%;
  height: 100%;
  background: rgb(246, 247, 252);
  justify-content: flex-end;
  align-items: center;
}

.login-form {
  align-items: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  // margin-right: 150px;
  z-index: 999;

  .form-user {
    // margin: 25px 0;

    .item {
      border-radius: 5px;
      border: 1px solid #ececec;
      display: flex;
      margin-bottom: 30px;
      background: #ffff;
      height: 45px;
      padding-left: 20px;
      display: flex;

      .code {
        position: relative;
        cursor: pointer;
        width: 74px;
        padding: 5px 10px 0 0;
      }

      .input-icon {
        line-height: 45px;
        color: #7a7a7a;
        padding-right: 20px;
      }
    }
  }

  input:-webkit-autofill {
    box-shadow: 0 0 0px 1000px white inset;
    -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  }

  input {
    background: white;
    display: block;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    margin: 0;
    padding: 0;
    color: #323233;
    line-height: inherit;
    text-align: left;
    border: 0;
    outline: none;
    font-size: 16px;
    line-height: 20px;
  }
}

.form-user,
.loging-btn {
  width: 400px;
}

.loging-btn {
  box-shadow: 2px 4px 11px #a4c2ff;
  margin-top: 10px;

  button {
    padding: 21px;
    font-size: 14px !important;
    width: 100%;
  }
}

.login-text {
  font-weight: bolder;
  font-size: 20px;
  letter-spacing: 2px;
  color: darkorange;
  position: relative;
  display: flex;

  .login-line {
    z-index: -1;
    padding: 5px;
    position: relative;
    top: -8px;
    width: 100%;
    background-image: linear-gradient(to right, #6598ff, white);
  }
}

.login-text-small {
  margin-bottom: 20px;
  font-size: 13px;
  color: #7d7c7c;
}

.login-bg {
  left: 0;
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 0;
}

.project-name {
  position: absolute;
  top: 20px;
  right: 50px;
  margin: 0px;
  z-index: 9999;
  text-align: right;
}

.no-autofill-pwd {
  /deep/ .el-input__inner {
    -webkit-text-security: disc !important;
  }
}
</style>
<style lang="less" scoped>
.app-link {
  // font-weight: bolder;
  text-align: center;
  padding-top: 5px;
  font-size: 14px;
  width: 400px;
  padding-left: 40px;
  display: flex;

  a {
    flex: 1;
    position: relative;
    cursor: pointer;
    width: 70px;
    color: #666666;
    margin: 20px 20px 0 0;
  }

  img {
    display: none;
  }

  a:hover {
    color: #0545f6 !important;

    img {
      display: block;
      position: absolute;
      z-index: *********;
      top: -130px;
      width: 120px;
      left: -22px;

      border: 1px solid #b1b1b1;
    }
  }
}

.login-footer {
  position: absolute;
  width: 50%;
  bottom: 0.5rem;
  font-size: 15px;
  text-align: center;
  padding-bottom: 10px;
  color: #4f4f4f;

  a {
    margin-right: 10px;
    font-size: 15px;
    color: #4f4f4f;
  }

  div {
    margin-bottom: 5px;
  }

  a:hover {
    cursor: pointer;
    color: #0540e3 !important;
  }
}

.account-info {
  font-size: 12px;
  color: #636363;
  margin-top: 15px;
}

.app-lang {
  display: flex;
  justify-content: left;
  align-items: center;
  flex: 1;
}
</style>

<style lang="less" scoped>
@media screen and (max-width: 700px) {

  .login-bg,
  .account-info,
  .app-link,
  .login-footer{
    display: none;
  }

  .login-container {
    padding: 2rem;
    justify-content: center;
  }

  .login-form {
    width: 100%;
  }

  .form-user,
  .loging-btn {
    width: 100%;
  }
}
</style>
