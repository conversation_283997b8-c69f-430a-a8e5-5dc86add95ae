<template>
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>流转批次</span>
            </label>
            <div style="margin-top: 5px;">
                <el-input ref="input" style="width: 200px" v-model="searchContainer" placeholder="请输入"
                    autofocus="true"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>工单</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getMfgOrder">
                    <el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
                </el-select>
            </div>
        </div>
    </div>
    <!-- 数据执行 -->
    <div class="table-item">
        <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">流转批次列表</span>
            <div class="table-item-buttons">
                <div>
                    <el-button icon="Search" @click="getRow" plain>查询</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="400"
            :pagination-hide="false" :load-key="true" :defaultLoadPage="false" :url="apiUrl.getWarehouseContainers"
            @loadBefore="loadBefore" @loadAfter="loadAfter" :column-index="true" :ck="true"></vol-table>
        <div style="display: flex;margin-top: 5px;">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>入库单数量</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input ref="input" style="width: 100px" v-model="searchInventoryQty" @change="changeInventoryQty"
                        placeholder="请输入" autofocus="true" type="number"></el-input>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>打印机</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="infoPrinter" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getPrinter" :loading="loading">
                        <el-option v-for="item in printers" :key="item.Name" :label="item.Name"
                            :value="item.Description" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>入库地点</span>
                </label>
                <div style="margin-top: 5px">
                    <el-input ref="input" style="width: 100px" v-model="infoInventoryLocation"
                        placeholder="请输入"></el-input>
                </div>
            </div>
            <div class="table-item-buttons" style="margin-top: 28px; margin-left: 10px;">
                <div>
                    <el-button type="success" icon="Check" @click="submitRow" plain>入库</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            searchContainer: null,
            searchInventoryQty: null,
            searchMfgOrder: null,
            mfgorders: [],
            infoPrinter: null,
            printers: [],
            infoInventoryLocation: null,

            columns: [
                { field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
                { field: 'MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
                { field: 'Container', title: '流转批次', type: 'string', width: 100, align: 'center' },
                { field: 'Spec', title: '当前工序', type: 'string', width: 100, align: 'center' },
                { field: 'Product', title: '产品编码', type: 'string', width: 100, align: 'center' },
                { field: 'InventoryLocation', title: '库存地点', type: 'string', width: 100, align: 'center' },
                { field: 'Uom', title: '单位', type: 'string', width: 100, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
                { field: 'ResidualQty', title: '未入库数量', type: 'string', width: 80, align: 'center' },
                { field: 'InventoryQty', title: '入库数量', type: 'string', width: 80, align: 'center',
                    cellStyle: (row, rowIndex, columnIndex) => {
						if  (row.InventoryQty > 0) {
							return { background: "#82C256", color: "#fff" };
						}
					},
                },
            ],
            tableData: [],

            //接口地址
            apiUrl: {
                getNameObject: "/api/query/GetNameObject",
                getDefaultPrinter: '/api/query/getDefaultPrinter',
                getWarehouseContainers: "/api/query/GetWarehouseContainers",
                warehouseReceiptTxn: "/api/cdo/warehouseReceiptTxn",
            },
        }
    },
    created() {
        this.getDefaultPrinter();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        getMfgOrder(query) {
            if (query) {
                let params = {
                    cdo: "mfgorder",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.mfgorders = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getPrinter(query) {
            if (query) {
                let params = {
                    cdo: "PrintQueue",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.printers = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getDefaultPrinter() {
            if (this.userInfo.userName) {
                let params = {
                    username: this.userInfo.userName
                };
                this.http.post(this.apiUrl.getDefaultPrinter, params).then(res => {
                    if (res.Result == 1) {
                        this.printers = [{ Name: res.Data.Printer, Description: res.Data.Description }];
                        this.infoPrinter = res.Data.Description; // 设置默认选中第一个打印机
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        reset() {
            this.searchContainer = null;
            this.searchInventoryQty = null;
            this.searchMfgOrder = null;
            this.tableData = [];
            this.$refs.table.rowData = [];
            this.$refs.table.paginations.total = 0;
        },
        changeInventoryQty() {
            if (this.searchInventoryQty) {
                if (this.tableData.length === 0) {
                    this.$message.error('请先查询出流转批次')
                    return;
                }
                const rows = this.$refs.table.getSelected();
                if (rows.length === 0) {
                    this.$message.error('请选中列表中批次。')
                    return;
                };
                if (rows.length === 0) return;

                let remainingQty = parseFloat(this.searchInventoryQty);

                for (let i = 0; i < rows.length; i++) {
                    const row = rows[i];
                    const residualQty = parseFloat(row.ResidualQty);

                    if (remainingQty <= 0) {
                        row.InventoryQty = '0';
                        continue;
                    }

                    if (remainingQty <= residualQty) {
                        row.InventoryQty = remainingQty.toString();
                        remainingQty = 0;
                    } else {
                        row.InventoryQty = residualQty.toString();
                        remainingQty -= residualQty;
                    }
                }

                // 仅更新数据而不替换整个rowData，保持勾选状态
                this.$refs.table.$refs.table.clearSelection();
                rows.forEach(row => {
                    this.$refs.table.$refs.table.toggleRowSelection(row, true);
                });
            }
        },
        rowClick({
            row,
            column,
            index
        }) {
            this.$refs.table.$refs.table.toggleRowSelection(row);
            // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
            // this.$refs.table.$refs.table.toggleRowSelection(row);
            
            this.changeInventoryQty();
        },
        getRow() {
            if (this.searchContainer == null && this.searchMfgOrder == null) {
                this.$message.error('请输入查询条件');
                return;
            }
            this.tableData = [];
            this.$refs.table.load(null, true);
        },
        loadBefore(params, callBack) {
            params["container"] = this.searchContainer;
            params["mfgorder"] = this.searchMfgOrder;
            callBack(true)
        },
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                this.infoInventoryLocation = result.Data.tableData[0].InventoryLocation;
                this.tableData = result.Data.tableData;
                this.$refs.table.rowData = result.Data.tableData;
                this.$refs.table.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },
        submitRow() {
            if (this.searchInventoryQty == null) {
                this.$message.error('请输入入库数量')
                return;
            }
            const rows = this.$refs.table.getSelected();
            if (rows.length == 0) {
                this.$message.error('请选中流转批次')
                return;
            }
            if (!this.infoPrinter) {
                this.$message.error('请选择打印机。')
                return;
            }
            if (!this.infoInventoryLocation){
                this.$message.error('请输入入库地点。')
                return;
            }
            // 校验InventoryQty总和是否等于searchInventoryQty
            const totalQty = rows.reduce((sum, row) => {
                return sum + (parseFloat(row.InventoryQty) || 0);
            }, 0);
            if (totalQty !== parseFloat(this.searchInventoryQty)) {
                this.$message.error(`入库数量总和必须等于${this.searchInventoryQty}，当前总和为${totalQty}`);
                return;
            }

            // 只筛选InventoryQty不为0的行
            const validRows = rows.filter(row => parseFloat(row.InventoryQty) > 0);

            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                inventoryOrderQty: this.searchInventoryQty,
                printer: this.infoPrinter,
                type: "add",
                inventoryLocation: this.infoInventoryLocation,
                requestData: validRows
            };
            this.http.post(this.apiUrl.warehouseReceiptTxn, params, true).then(res => {
                if (res.Result == 1) {
                    this.reset();
                    this.$message.success("提交成功");
                } else {
                    this.$message.error(res.Message);
                }
            });
        }
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>