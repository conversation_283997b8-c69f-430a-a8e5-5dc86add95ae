<template>
    <div class="opc-lot-move">
        <div class="opc-lot-move__header">
            <VolHeader :title="'批次信息'"></VolHeader>
            <VolForm ref="containerForm" :loadKey="true" :formFields="containerFields" :formRules="containerRules">
                <div style="text-align: left; width: 100%">
                    <VolHeader :title="'工单产品信息'"> </VolHeader>
                    <VolForm ref="mfgForm" :loadKey="true" :formFields="mfgFields" :formRules="mfgRules">
                    </VolForm>
                </div>
                <div style="text-align: right; width: 100%; margin-top: 5px; margin-bottom: 10px; margin-right: 5px;">
                    <el-button type="primary" icon="ShoppingCart" :disabled="isMoveIn" @click="lotMoveIn">进站</el-button>
                    <el-button type="success" icon="Finished" :disabled="isMoveOut" @click="lotMoveOut">出站</el-button>
                    <el-button type="info" icon="Checked" @click="setReadonlyStaus(true)">生产检验</el-button>
                    <!-- <el-button type="danger" icon="WarnTriangleFilled" @click="setReadonlyStaus(false)">异常登记</el-button>
                    <el-button type="warning" icon="Close" @click="setReadonlyStaus(true)">批次报废</el-button> -->
                </div>
            </VolForm>
        </div>
        <div class="opc-lot-move__body">
            <el-tabs v-model="activeTab" type="border-card" @click="onTabClick" style="height: auto">
                <el-tab-pane label="批次过站记录" name="first">
                    <vol-table ref="moveHistoryTable" :loadKey="true" :ck="false"
                        :tableData="moveHistoryTableData" :columns="moveHistoryColumns"></vol-table>
                </el-tab-pane>
                <el-tab-pane label="设备已上物料" name="second">
                    <vol-table ref="componentTable" :loadKey="true" :ck="false" 
                    :tableData="componentTableData" :columns="componentColumns"></vol-table>
                </el-tab-pane>
                <el-tab-pane label="批次产品SOP" name="third">
                    <vol-table ref="sopTable" :loadKey="true" :ck="false" 
                    :tableData="sopTableData" :columns="sopColumns"></vol-table>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from '@/components/basic/VolForm.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolBox from '@/components/basic/VolBox.vue';
import {
    mapState
} from 'vuex';
export default {
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    components: {
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            apiUrl: {
                getRevisionObject: "/api/query/getRevisionObject",
                getNameObject: "/api/query/getNameObject",
                getContainerInfo: '/api/Query/getContainerInfo', //获取批次信息
                getResourceBySpec: '/api/Query/getResourceBySpec', //获取设备
                moveInTxn: '/api/CDO/MoveInTxn', //进站
                moveStdTxn: '/api/CDO/MoveStdTxn', //出站
                getMoveHistory: '/api/Query/getMoveHistory', //获取过站历史
                getResourceComponent: '/api/Query/getResourceComponent', //获取当前工单
                getDocumentSet: '/api/Query/getDocumentSet', //获取产品文档
            },
            isMoveIn: true,
            isMoveOut: true,
            activeTab: 'first',
            tableFields: [],

            containerFields: {
                selectLot: null,
                currentStatus: null,
                currentQty: null,
                resource: null,
                workFlow: null,
                currentSpec: null,
                nextSpec: null,
                specId: null,
                productId:null
            },
            containerRules: [
                [
                    {
                        title: this.$ts('扫描批次'), placeholder: " ", required: true, field: "selectLot", type: "text",
                        onKeyPress: $event => {
                            if ($event.keyCode == 13) {
                                this.onSelectLot(this.containerFields.selectLot)
                            }
                        }, colSize: 2,
                    },
                    { title: this.$ts('设备编号'), placeholder: " ", required: false, field: "resource", type: "select", data: [], colSize: 2,
                        onChange: (value) => {
                            this.getResourceComponent(value)
                        }
                    },
                    { title: this.$ts('数量'), placeholder: " ", required: false, field: "currentQty", type: "text", colSize: 1, readonly: true, },
                    { title: this.$ts('工艺流程'), placeholder: " ", field: "workFlow", type: "text", readonly: true, colSize: 2, },
                    { title: this.$ts('当前工序'), placeholder: " ", field: "currentSpecName", type: "text", readonly: true, colSize: 2, },
                    { title: this.$ts('下一工序'), placeholder: " ", field: "nextSpecName", type: "text", readonly: true, colSize: 2, }
                ]
            ],
            mfgFields: {
                mfgOrder: null,
                productNo: null,
                productDesc: null,
                actualWorkTime: null,
                standardWorkTime: null
            },
            mfgRules: [
                [
                    { title: this.$ts('生产工单'), placeholder: " ", field: "mfgOrder", type: "text", readonly: true, colSize: 2, },
                    { title: this.$ts('产品编码'), placeholder: " ", field: "productNo", type: "text", readonly: true, colSize: 2, },
                    { title: this.$ts('产品描述'), placeholder: " ", field: "productDesc", type: "text", readonly: true, colSize: 2, },
                    { title: this.$ts('标准人工工时'), placeholder: " ", field: "standardWorkTime", type: "text", readonly: true, colSize: 2, },
                    { title: this.$ts('实际人工工时'), placeholder: " ", field: "actualWorkTime", type: "number", colSize: 2, },
                ]
            ],
            moveHistoryColumns: [
                { field: 'WorkFlow', title: '工艺流程', type: 'string', width: 150, align: 'center' },
                { field: 'Spec', title: '从工序', type: 'string', width: 150, align: 'center' },
                { field: 'ToStep', title: '到工序', type: 'string', width: 150, align: 'center' },
                { field: 'MoveInDate', title: '进站时间', type: 'string', width: 150, align: 'center' },
                { field: 'MoveOutDate', title: '出站时间', type: 'string', width: 150, align: 'center' },
                { field: 'ResourceName', title: '设备', type: 'string', width: 130, align: 'center' },
                { field: 'TotalQty', title: '数量', type: 'string', width: 80, align: 'center' },
                { field: 'CycleTime', title: '周期时间', type: 'string', width: 80, align: 'center' },
            ],
            componentColumns: [
                { field: 'Container', title: '物料批次', type: 'string', width: 130, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
				// { field: 'RealQty', title: '剩余实物数量', width: 100, align: 'center',edit: { type: "number",min: 0 } },
                { field: 'Product', title: '物料编号', type: 'string', width: 100, align: 'center' },
                { field: 'Description', title: '物料描述', type: 'string', width: 100, align: 'center' },
                { field: 'ProductType', title: '物料类型', type: 'string', width: 100, align: 'center' },
                { field: 'Vendor', title: '供应商', type: 'string', width: 80, align: 'center' },
                { field: 'W_MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
            ],

            moveHistoryTableData: [],
            componentTableData: [],
            sopTableData: [],
            sopColumns: [],
        }


    },
    created() {

    },

    methods: {

        lotMoveIn() {
            if (!this.containerFields.selectLot) {
                this.$message.warning(this.$ts('请输入批次'));
                return;
            }

            let param = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                Container: this.containerFields.selectLot,
                Resource: this.containerFields.resource,
                TaskContainer: this.containerFields.selectLot
            }
            this.http.post(this.apiUrl.moveInTxn, param, true).then(res => {
                if (res.Result == 1) {
                    this.$message.success(res.Message);
                    this.reset();
                } else {
                    this.$message.error(res.Message);
                }
            })
        },

        lotMoveOut() {
            if (!this.containerFields.selectLot) {
                this.$message.warning(this.$ts('请输入批次'));
                return;
            }
            if (this.containerRules[0][1].data.length > 0 && !this.containerFields.resource) {
                return this.$message.warning(this.$ts('请选择设备'));
            }
            if (!this.mfgFields.actualWorkTime) {
                return this.$message.warning(this.$ts('请填写实际人工工时'));
            }

            let param = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                Container: this.containerFields.selectLot,
                Resource: this.containerFields.resource,
                TaskContainer: this.containerFields.selectLot,
                actualWorkTime: this.mfgFields.actualWorkTime
            }

            this.http.post(this.apiUrl.moveStdTxn, param, true).then(res => {
                if (res.Result == 1) {
                    this.$message.success(res.Message);
                    this.reset();
                } else {
                    this.$message.error(res.Message);
                }
            })
        },

        onSelectLot(val) {
            if (val) {
                this.containerFields.currentQty = null;
                this.containerFields.currentSpec = null;
                this.containerFields.nextSpec = null;
                this.containerFields.workFlow = null;
                this.containerFields.resource = null;
                this.containerFields.specId = null;
                this.$refs.mfgForm.reset();
                this.$refs.moveHistoryTable.reset();
                this.$refs.componentTable.reset();
                this.$refs.sopTable.reset();
                this.containerRules[0][1].data = [];
                let param = {
                    container: val
                };
                this.http.get(this.apiUrl.getContainerInfo, param, true).then((res) => {
                    if (res.Result == 1) {
                        if (res.Data == null) {
                            this.isMoveIn = true;
                            this.isMoveOut = true;
                            return this.$Message.error("批次" + val + "不存在");
                        }
                        else {
                            this.containerFields.currentQty = res.Data.qty;
                            this.containerFields.currentSpec = res.Data.currentStep;
                            this.containerFields.nextSpec = res.Data.nextStep;
                            this.containerFields.workFlow = res.Data.workflowName;
                            this.containerFields.resource = res.Data.resourceName;
                            this.containerFields.specId = res.Data.SpecId;
                            this.containerFields.productId = res.Data.ProductId;
                            this.mfgFields.productNo = res.Data.productName;
                            this.mfgFields.productDesc = res.Data.productDesc;
                            this.mfgFields.mfgOrder = res.Data.mfgOrderName;
                            this.mfgFields.standardWorkTime = res.Data.StandardWorkTime;

                            if (res.Data.IsMoveIn == 1) {
                                this.isMoveOut = true;
                                this.isMoveIn = false;
                            }
                            else {
                                this.isMoveIn = true;
                                this.isMoveOut = false;
                            }
                            this.getResource();
                            this.activeTab = 'first';
                            this.getMoveHistory();
                        }
                    } else {
                        this.$Message.error(res.Message);
                    }
                });
            }
        },
        getResource() {
            if (!this.containerFields.specId) {
                return;
            }
            let param = {
                specID: this.containerFields.specId,
            }
            let dataArry = [];
            this.http.get(this.apiUrl.getResourceBySpec, param).then((res) => {
                if (res.Result == 1) {
                    res.Data.forEach((item) => {
                        dataArry.push({ key: item.Name, value: item.Name });
                    })
                    this.containerRules[0][1].data = dataArry;
                    if (this.containerRules[0][1].data.length > 0) {
                        this.containerRules[0][1].required = true;
                    }
                } else {
                    this.$Message.error(res.Message);
                }
            }).catch((error) => {
                this.$Message.error(error);
            });
        },
        //tab点击事件
        onTabClick(val) {
            switch (this.activeTab) {
                case 'first': this.getMoveHistory();
                    break;
                case 'second': this.getResourceComponent();
                    break;
                case 'third': this.getDocumentSet();
                    break;
                default: this.$Message.error(this.activeTab + ' is not found'); break;

            }
        },
        getMoveHistory() {
            if (!this.containerFields.selectLot) {
                return;
            }
            let param = {
                container: this.containerFields.selectLot
            }
            this.http.get(this.apiUrl.getMoveHistory, param).then(
                (res) => {
                    if (res.Result == 1) {
                        this.moveHistoryTableData = res.Data;
                    }
                    else {
                        this.$Message.error(res.Message);
                    }
                }
            )
        },
        getResourceComponent(val) {
            if(!val){
                return;
            };
            let params = {
                resource: val
            };
            this.http.get(this.apiUrl.getResourceComponent, params, true).then(res => {
                if (res.Result == 1) {
                    this.componentTableData = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        getDocumentSet() {
            let param = {
                ProductId: this.containerFields.productId,
                SpecId: this.containerFields.specId
            }
            this.http.get(this.apiUrl.getDocumentSet, param, true).then((res) => {
                if (res.Result == 1) {
                    this.sopColumns = res.Data.columns;
                    this.sopColumns.push({
                        title: "操作",
                        field: '操作',
                        width: 150,
                        align: 'center',
                        render: (h, { row, column, index }) => {
                            return (
                                <div>
                                    <el-button
                                        onClick={($e) => {
                                            $e.stopPropagation();
                                            this.editClick(row, column, index);
                                        }}
                                        type="primary"
                                        plain
                                        style="height:26px; padding: 10px !important;"
                                    >
                                        查看
                                    </el-button>
                                </div>
                            );
                        }

                    });

                    this.sopTableData = res.Data.tableData;
                }
                else {
                    this.$Message.error(res.Message)
                }
            })
        },
        reset() {
            this.$refs.containerForm.reset();
            this.$refs.mfgForm.reset();
        },
        editClick(row, column, index) {
            window.open(row.Url, 'Doucment');
        },
        deleteClick(row, column, index) {
            this.DialogtableData.splice(index, 1);
        },
    }
}
</script>