{"name": "vol-vue3vite", "version": "0.0.1", "private": true, "scripts": {"serve": "vite --host", "dev": "vite --host --mode test", "dev:debug": "vite --host --mode debug", "buildWithCheck": "run-p type-check build-only", "preview": "vite preview", "test:unit": "vitest", "build": "vite build --mode production", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "clean": "rimraf node_modules", "build:debug": "vite build --mode debug", "build:uat": "vite build --mode test", "build:prod": "vite build --mode production"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@microsoft/signalr": "^7.0.3", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "ali-oss": "^6.17.1", "axios": "^1.3.4", "core-js": "^3.29.0", "echarts": "^5.4.1", "el-table-infinite-scroll": "^3.0.6", "element-plus": "^2.2.32", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jsqr": "^1.4.0", "less": "^4.1.3", "pinia": "^2.0.32", "qrcode": "^1.5.3", "rollup-plugin-commonjs": "^10.1.0", "uuid": "^10.0.0", "vite-plugin-commonjs": "^0.10.1", "vue": "^3.2.47", "vue-draggable-next": "^2.1.1", "vue-grid-layout": "^3.0.0-beta1", "vue-plugin-hiprint": "^0.0.56", "vue-qrcode-reader": "^5.7.2", "vue-router": "^4.1.6", "vuex": "^4.1.0", "wangeditor": "^4.7.15", "webrtc-adapter": "^9.0.3", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "^7.21.3", "@rushstack/eslint-patch": "^1.2.0", "@types/jsdom": "^21.1.0", "@types/node": "^18.14.2", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^2.3.0", "@vue/tsconfig": "^0.1.3", "eslint": "^8.36.0", "eslint-plugin-vue": "^9.9.0", "jsdom": "^21.1.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "rimraf": "^4.1.2", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.89.0", "stylus": "^0.59.0", "typescript": "~4.8.4", "vite": "^4.2.0", "vite-plugin-require-transform": "^1.0.21", "vitest": "^0.29.1", "vue-tsc": "^1.2.0"}}