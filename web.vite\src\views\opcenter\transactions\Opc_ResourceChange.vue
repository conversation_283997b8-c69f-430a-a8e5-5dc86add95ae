<template>
    <div class="lot-exception">
        <VolHeader title="设备生产换型（切换）操作" />
        <VolForm ref="form1" :loadKey="true" :formFields="headerFields" :formRules="headerRules">
            <div style="text-align: end; margin-top: 0px; width: 100%">
                <div style="margin-right: 20px; margin-top: -39px">
                    <el-button type="primary" @click="submit">保存</el-button>
                </div>
            </div>
        </VolForm>
        <VolHeader title="设备生产换型记录" />
        <VolForm ref="form2" :loadKey="true" :formFields="searchFields" :formRules="searchRules">
            <div style="text-align: end; margin-top: 0px; width: 100%">
                <div style="margin-right: 20px; margin-top: -39px">
                    <el-button type="primary" plain @click="search">查询</el-button>
                    <!-- <el-button type="success" plain @click="outputRow">导出</el-button> -->
                </div>
            </div>
        </VolForm>
        <vol-table ref="table" index :loadKey="true" :ck="false" :column-index="true" :columns="columns"
            :tableData="tableData" :pagination-hide="true" :reserveSelection="true" height="280"></vol-table>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from '@/components/basic/VolForm.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Common from "@/uitils/common.js";
import axios from 'axios';
import Excel from '@/uitils/xlsl.js';
export default {
    components: {
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    //初始化页面
    created() {
        this.getSpecs();
        //this.getResources();
        this.getProducts();
        //this.headerFields.employee = this.userInfo.userName;
    },

    data() {
        return {
            ApiUrl: {
                GetRevisionObject: "/api/query/GetRevisionObject",
                GetNameObject: "/api/query/GetNameObject",
                GetResourceBySpec: "/api/query/GetResourceBySpec",
                GetResourceChangeInfo: '/api/Query/GetResourceChangeInfo', //获取当前设备生产换型（切换）
                GetResourceChangeRecord: '/api/Query/GetResourceChangeRecord', //获取设备生产换型记录
                ResourceChangeTxn: 'api/CDO/ResourceChangeTxn', //设备生产换型（切换）
            },
            headerFields: {
                spec:"",
                resource:"",
                resourceDesc:"",
                product:"",
                productDesc:"",
                programName:"",
                changeProduct:"",
                changeProductDesc:"",
                changeProgramName:"",
                changeDate:"",
                remark:"",
            },
            headerRules: [
                [{
                    title: this.$ts('工序'),
                    placeholder: this.$ts(''),
                    field: "spec",
                    type: "select",
                    dataKey: "",
                    data: [],
                    colSize: 2,
                    onChange: (val, item) => {
                        this.getResources(val,"header");
                    }
                },
                {
                    title: this.$ts('设备编码'),
                    placeholder: this.$ts(''),
                    field: "resource",
                    type: "select",
                    dataKey: "",
                    data: [],
                    colSize: 2,
                    Required:true,
                    onChange: (val, item) => {
                        this.ResourceChange(val);
                    }
                },
                {
                    title: this.$ts('设备名称'),
                    placeholder: this.$ts(''),
                    field: "resourceDesc",
                    type: "text", 
                    readonly: true,                   
                    colSize: 2,
                }],
                [{
                    title: this.$ts('当前生产的产品编码'),
                    placeholder: this.$ts(''),
                    field: "product",
                    type: "select",
                    dataKey: "",
                    data: [],
                    readonly: true,
                    colSize: 2,
                },
                {
                    title: this.$ts('当前生产的产品名称'),
                    placeholder: this.$ts(''),
                    field: "productDesc",
                    type: "text",
                    readonly: true,
                    colSize: 2
                },
                {
                    title: this.$ts('当前生产的运行程式名称'),
                    placeholder: this.$ts(''),
                    field: "programName",
                    type: "text",
                    readonly: true,
                    colSize: 2,
                }],
                [ {
                    title: this.$ts('换型生产的产品编码'),
                    placeholder: this.$ts(''),
                    field: "changeProduct",
                    type: "select",
                    dataKey: "",
                    data: [],
                    colSize: 2,
                    Required:true,
                    onChange: (val, item) => {
                        this.headerFields.changeProductDesc=item.find(a=>a.key==val).label;
                    }
                },
                {
                    title: this.$ts('换型生产的产品名称'),
                    placeholder: this.$ts(''),
                    field: "changeProductDesc",
                    type: "text",
                    readonly: true,
                    colSize: 2
                },
                {
                    title: this.$ts('换型生产的运行程式名称'),
                    placeholder: this.$ts(''),
                    field: "changeProgramName",
                    type: "text",
                    colSize: 2,
                },
                {
                    title: this.$ts('换型开始时间'),
                    placeholder: this.$ts(''),
                    field: "changeDate",
                    type: "time",
                    colSize: 2,
                    //range: true,
                }],
                [{
                    title: this.$ts('设备生产换型备注'),
                    placeholder: this.$ts(''),
                    field: "remark",
                    type: "textarea",
                    readonly: false,
                    colSize: 10,
                }]
            ],
            searchFields: {
                spec:"",
                resource:"",
                product:"",
                systemDate:"",
            },
            searchRules: [
                [{
                    title: this.$ts('工序'),
                    placeholder: this.$ts(''),
                    field: "spec",
                    type: "select",
                    dataKey: "",
                    data: [],
                    colSize: 2,
                    onChange: (val, item) => {
                        this.getResources(val,"search");
                    }
                },{
                    title: this.$ts('设备编码'),
                    placeholder: this.$ts(''),
                    field: "resource",
                    type: "select",
                    dataKey: "",
                    data: [],
                    colSize: 2,
                },{
                    title: this.$ts('生产产品编码'),
                    placeholder: this.$ts(''),
                    field: "product",
                    type: "select",
                    dataKey: "",
                    data: [],
                    colSize: 2,
                },{
                    title: this.$ts('时间范围'),
                    placeholder: this.$ts(''),
                    field: "systemDate",
                    type: "datetime",
                    colSize: 4,
                    range: true,
                },],
            ],
            columns: [
                { field: 'Resource', title: '设备', type: 'string', width: 130, align: 'center' },
                { field: 'ResourceDesc', title: '设备名称', type: 'string', width: 180, align: 'center' },
                { field: 'Product', title: '生产产品编码', type: 'string', width: 130, align: 'center' },
                { field: 'ProductDesc', title: '生产产品名称', type: 'string', width: 180, align: 'center' },
                { field: 'ProgramName', title: '生产运行程式名', type: 'string', width: 130, align: 'center' },
                { field: 'Remark', title: '设备生产换型备注', type: 'string', width: 180, align: 'center' },
                { field: 'ChangeStartDate', title: '换型开始时间', type: 'datetime', width: 180, align: 'center' },
                { field: 'ChangeEndDate', title: '换型结束时间', type: 'datetime', width: 180, align: 'center' },
                { field: 'Employee', title: '换型操作人', type: 'string', width: 130, align: 'center' },
                { field: 'SystemDate', title: '操作时间', type: 'datetime', width: 180, align: 'center' }
            ],
            tableData: []
        }
    },
    methods: {
        //获取工序
        async getSpecs(){
				let params = {
					cdo: "Spec"
				};
				this.http.get(this.ApiUrl.GetRevisionObject,params).then(res => {
					if (res.Result == 1) {
                        var list=res.Data.map((item)=> {return {key:item.Name, value:item.Name,label:item.Description}}); 
						this.headerRules[0][0].data  = list;
                        this.searchRules[0][0].data  = list;
					} else {
						this.$message.error(res.Message);
					}
				});
		},
        //获取设备
        async getResources(val,type){
				let params = {
					spec: val
				};
				this.http.get(this.ApiUrl.GetResourceBySpec,params).then(res => {
					if (res.Result == 1) {
                        var list=res.Data.map((item)=> {return {key:item.Name, value:item.Name,label:item.Description}});  
                        if(type=="header"){
                            this.headerRules[0][1].data = list;
                        }
                        else if (type=="search"){
                            this.searchRules[0][1].data = list;
                        }
                        else{
                            this.headerRules[0][1].data = list;
                            this.searchRules[0][1].data = list;
                        }
					} else {
						this.$message.error(res.Message);
					}
				});
		},
        //获取产品
        async getProducts() {
            let params = {
                cdo: "Product"
            };
            this.http.get(this.ApiUrl.GetRevisionObject, params).then(res => {
                if (res.Result == 1) {
                    var list=res.Data.map((item)=> {return {key:item.Name, value:item.Name,label:item.Description}});  
                    this.headerRules[1][0].data =list;
                    this.headerRules[2][0].data =list;
                    this.searchRules[0][2].data =list;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        //扫描批次
        ResourceChange(val) {
            if (val) {
                let param = {
                    resource: val,
                }
                this.http.get(this.ApiUrl.GetResourceChangeInfo, param).then((res) => {
                    if (res.Result == 1) {
                        this.headerFields.resourceDesc = res.Data.ResourceDesc;
                        this.headerFields.product = res.Data.Product;
                        this.headerFields.productDesc = res.Data.ProductDesc;
                        this.headerFields.programName = res.Data.ProgramName;
                    } else {
                        this.$message.error(res.Message);
                    }
                })
            }
        },
        //提交
        submit() {
            if (this.headerFields.resource == null || this.headerFields.resource == '') {
                this.$message.warning('请选择设备');
                return;
            }
            if (this.headerFields.changeProduct == null || this.headerFields.changeProduct == '') {
                this.$message.warning('请选择换型生产产品');
                return;
            }            
            let param = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                Resource:this.headerFields.resource,
                Product:this.headerFields.changeProduct,
                ProgramName:this.headerFields.changeProgramName,
                ChangeStartDate:this.headerFields.changeDate,
                ChangeEndDate:new Date().toLocaleString(),
                Employee:this.userInfo.userName,
                Remark:this.headerFields.remark,
            }
            this.http.post(this.ApiUrl.ResourceChangeTxn, param).then((res) => {
                if (res.Result == 1) {
                    this.$Message.success('设备(' + this.headerFields.resource + ')换型成功');
                    this.reset();
                } else {
                    this.$Message.error(res.Message);
                }
            })
        },
        //查询
        search() {
            let params = {
                resource: this.searchFields.resource,
                product: this.searchFields.product,
                startTime: this.searchFields.systemDate != null?this.searchFields.systemDate[0] : null,
                endTime: this.searchFields.systemDate != null?this.searchFields.systemDate[1] : null,
            };
            this.http.get(this.ApiUrl.GetResourceChangeRecord, params, true).then(res => {
                if (res.Result == 1) {
                    this.tableData = res.Data;
                    this.reset();
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        //重置
        reset() {
            this.$refs.form1.reset(this.headerFields);
        },
        //导出
        outputRow() {
            let tableData = this.$refs.table.tableData
            let sortData = this.$refs.table.filterColumns
            let exportData = this.handleTableSortData(tableData, sortData)
            Excel.exportExcel(exportData, "设备生产换型记录" + '-' + this.base.getDate());
        },
        handleTableSortData(tableData, sortData) {
            let newArray = [];
            tableData.forEach(data => {
                let newItem = {};
                sortData.forEach(field => {
                    if (data.hasOwnProperty(field.field)) {
                        newItem[field.title] = data[field.field];
                    }
                });
                newArray.push(newItem);
            });
            return newArray
        },
    },


}
</script>