import http from '@/api/http.js'
import { mapState } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus';

// ElMessageBox.confirm('确定要报废此数据吗?', '提示', {
//   confirmButtonText: '确定',
//   cancelButtonText: '取消',
//   center: false,
//   type: 'warning',
//   showClose: false,
// }).then(() => {
//   this.$http.post(null, null, true).then((x) => {
//     ElMessage({
//       type: x.status ? 'success' : 'error',
//       message: x.message
//     });
//     if (!x.status) {
//       return;
//     }
//   });
// });

export const listMixins = {
  data() {
    return {

    }
  },
  computed: {
    ...mapState({
      //获取当前用户的信息
      userInfo: state => state.userInfo,
      //获取当前用户的权限
      permission: state => state.permission,
      //获取当前用户的角色
      // role: state => state.role,
      //获取当前用户的部门
      // dept: state => state.dept
    })
  },
  created() {
    this.loadData()
  },
  mounted() {
    window.addEventListener('keydown', this.onKeySearch);
    // console.log(this.userInfo, 'mixins')
  },
  methods: {
    // 页面按钮权限
    getBtn(permission) {
      const _result = (this.$store.state.permission || []).find((x) => {
        return x.url == '/' + this.$route.name
      })
      // console.log(_result, _result.permission, '按钮')
      return _result && _result.permission.some((x) => x == permission)
    },

    modalFormOk() {
      console.log('modalFormOk', 'mixins')
    },

    loadData() {
      console.log('loadData', 'mixins')
      // if (!this.url) {
      //   console.log(this.url);
      //   ElMessage({ type: 'warning', message: '请设置url属性' });
      //   return
      // }
    },

  },
  beforeUnmount() {
    window.removeEventListener('keydown', this.onKeySearch);
  },
}

export const viewColumnList = {
  components: {

  },
  data() {
    return {

    }
  },
  mounted() {

  },
  methods: {


  },
}

export const GlobalElMessageBox = {
  data() {
    return {
      visible: true,
    }
  },
  mounted() {

  },
  methods: {

    handleGlobalMessageBox() {
      ElMessageBox.confirm(this.$ts('Are you sure to exit?'), this.$ts('Tips'), {
        confirmButtonText: this.$ts('Confirm'),
        cancelButtonText: this.$ts('Cancel'),
        closeOnClickModal: false,
        center: false,
        type: 'warning',
        showClose: false,
      }).then(() => {
        this.closeModel()
      }).catch(() => {
        this.visible = true
      })
    },


    async getPositionByLocation(value) {
      let postData = {
        PhysicalLocationName: value
      };
      try {
        const res = await this.http.post('api/DropdownList/SearchPhysicalPosition', postData);
        if (res.rows && res.rows.length > 0) {
          // API有数据且不为空值，返回数据
          return res.rows;
        } else {
          // API返回的数据为空
          return null;
        }
      } catch (error) {
        console.error('请求失败或其他错误:', error);
        return null; // 发生错误时返回null
      }
    },

    resultMessageStay(status, msg) {
      if (status === '1') {
        this.$message({
          message: this.$ts('Execution Success!'),
          type: 'success'
        });
        this.$message({
          message: msg,
          type: 'success',
          showClose: true,
          duration: 0
        });
        /*  this.$message.success(this.$ts('Execution Success!'));
         this.$message.success(msg); */
        return true;
      } else {
        this.$message({
          message: this.$ts('Execution failed, please contact MES team.'),
          type: 'error'
        });
        this.$message({
          message: msg,
          type: 'error',
          showClose: true,
          duration: 0
        });
        /* this.$message.error(this.$ts('Execution failed, please contact MES team.'));
        this.$message.error(msg); */
        return false;
      }

    },
    resultMessage(status, msg) {
      if (status === '1') {
        this.$message.success(this.$ts('Execution Success!'));
        this.$message.success(msg);
        return true;
      } else {
        this.$message.error(this.$ts('Execution failed, please contact MES team.'));
        this.$message({
          message: msg,
          type: 'error',
          showClose: true,
          duration: 5000
        });
        return false;
      }

    },

    // 表格的列必填验证方法: tableData: 表格数据，columns: 表格列定义
    validateTableData(tableData, columns) {
      let loopCount = 0;
      const requiredColumns = columns.filter(col => col.required);

      for (const row of tableData) {
        for (const col of requiredColumns) {
          loopCount++; // 循环计数
          if (row[col.field] === undefined || row[col.field] === null || row[col.field] === '') {
            const errorMessage = `${this.$ts(col.title)} - ${this.$ts('Content cannot be null')}`;
            this.$message.error(errorMessage);
            return false;
          }
        }
      }

      console.log(`${loopCount}`, 'loopCount');
      return true;
    },

    async SysApi_GetDicData(dicNo) {
      let dicData = [dicNo];
      try {
        const res = await this.http.post('api/Sys_Dictionary/GetVueDictionary', dicData);
        console.log(res[0].data, 'res');
        if (res[0].data.length > 0 && res[0].data !== null) {
          return res[0].data;
        } else {
          return [];
        }
      } catch (error) {
        this.$message.error(error);
        throw error;
      }
    }


  },
}