<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>课别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchWorkcenter" clearable filterable placeholder="请选择" style="width: 150px"
						remote-show-suffix>
						<el-option v-for="item in workcenters" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>组别</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="searchEmployeeGroup" clearable filterable placeholder="请选择" style="width: 150px"
						remote-show-suffix>
						<el-option v-for="item in employeeGroups" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span></label>
				<div style="margin-top: 5px">
					<el-input v-model="searchMfgorder" placeholder="请输入" style="width: 150px" />
				</div>
			</div>
            <div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>工单计划日期</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
				</div>
			</div>
            <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>出勤工时(H)</span></label>
				<div style="margin-top: 5px">
                    <el-input style="width: 100px" v-model="searchWork" placeholder="请输入"
                    type="number" @input="
						form.ekHour = form.ekHour
							.replace(/[^\d|\.]/g, '')
							.replace(/^00/g, '0')
							.replace(/^\./g, '0.')
						"></el-input>
				</div>
            </div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>领料天数(天)</span></label>
				<div style="margin-top: 5px">
					<el-input style="width: 100px" v-model="searchDay" placeholder="请输入"
                    type="number" @input="
						form.ekHour = form.ekHour
							.replace(/[^\d|\.]/g, '')
							.replace(/^00/g, '0')
							.replace(/^\./g, '0.')
						"></el-input>
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">工单物料领料</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="success" icon="Check" @click="submitRow" plain>领料</el-button>
					<el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="518"
			:pagination-hide="false" :load-key="true" :column-index="true" :single="false" :url="apiUrl.getMaterialRequestInfo"
			@loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false" :ck="true"></vol-table>
	</div>	
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
	import { mapState } from 'vuex';
	import Excel from '@/uitils/xlsl.js'

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox
		},
		data() {
			return {
				show: false,
				columns: [
					{ field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },
					{ field: 'Product', title: '产品编号', type: 'string', width: 120, align: 'center' },
					{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },					
					{ field: 'P_Revision',hidden: true, title: '产品版本', type: 'string', width: 130, align: 'center' },					
					{ field: 'OrderQty', title: '工单数量', type: 'string', width: 80, align: 'center' },
					{ field: 'StandardCapacity', title: '小时标准产能', type: 'string', width: 100, align: 'center' },
					{ field: 'CapacityByDay', title: '日产能', type: 'string', width: 80, align: 'center' },
					{ field: 'Production', title: '生产量', type: 'string', width: 80, align: 'center' },
					{ field: 'Material', title: '物料编号', type: 'string', width: 120, align: 'center' },
					{ field: 'M_Description', title: '物料品名', type: 'string', width: 120, align: 'center' },
					{ field: 'M_Revision',hidden: true, title: '物料版本', type: 'string', width: 120, align: 'center' },
					{ field: 'RequiredQty', title: '总需求数', type: 'string', width: 80, align: 'center' },
					{ field: 'RequestQty', title: '领料数', width: 100, align: 'center',edit: { type: "number",min: 0 } },
					{ field: 'RequestedQty', title: '已领数', type: 'string', width: 80, align: 'center' },
					{ field: 'ReturnQty', title: '已退数', type: 'string', width: 80, align: 'center' },
					{ field: 'NetDemandQty', title: '净需求数', type: 'string', width: 100, align: 'center' },
					{ field: 'QtyRequired', title: 'BOM用量', type: 'string', width: 100, align: 'center' },
					{ field: 'PackQty', title: '包装量取整', type: 'string', width: 100, align: 'center' },
					{ field: 'ProductionDemandQty', title: '生产需求数', type: 'string', width: 100, align: 'center' },
					{ field: 'IntegerQty', title: '需求数取整', type: 'string', width: 100, align: 'center' },
					{ field: 'MiniPackageQty', title: '最小包装量', type: 'string', width: 100, align: 'center' },
					{ field: 'WorkCenter', title: '课别', type: 'string', width: 100, align: 'center' },
					{ field: 'InventoryLocation', title: '库存地点', type: 'string', width: 100, align: 'center' },
					{ field: 'LastApplicationTime', title: '申请时间', type: 'string', width: 150, align: 'center' },
				],
				tableData: [],
				pagination: { total: 0, size: 30, sortName: "" },

				//搜索框字段
				searchWorkcenter: '',
				searchEmployeeGroup: '',
				searchPlanDate: null,
				searchWork:'',
				searchDay:'',
				workcenters: [],
				employeeGroups: [],
				searchMfgorder: '',


				//接口地址
				apiUrl: {
					getNameObject: "/api/query/GetNameObject",
					getMaterialRequestInfo: '/api/query/GetMaterialRequestInfo',
					materialRequest: '/api/CDO/materialRequest',
				}

			}
		},
		created() {
            this.getWorkCenter();
            this.getEmployeeGroup();
		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
            async getWorkCenter() {
                let params = {
                    cdo: "WorkCenter"
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.workcenters = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
			},
			async getEmployeeGroup() {				
                let params = {
                    cdo: "EmployeeGroup"
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.employeeGroups = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });				
			},
            queryRow() {
				this.$refs.table.load(null, true);
			},	
			//清除数据
			reset() {
				this.searchWorkcenter = '';
				this.searchEmployeeGroup = '';
				this.searchPlanDate = null;
                this.searchWork = '';
                this.searchDay = '';
				this.tableData = [];
				this.$refs.table.rowData = [];
				this.$refs.table.paginations.total = 0;
			}, 
            submitRow(){
				let selectedRows = this.$refs.table.getSelected();
				if (selectedRows.length == 0) {
					this.$message.error('请至少选择一行数据');
					return;
				}
                let params = {
					User:this.userInfo.userName,
					Password:this.userInfo.userPwd,
                    requestData: selectedRows,
					type: 'Z011'
                };
                this.http.post(this.apiUrl.materialRequest, params, true).then(res => {
                    if (res.Result == 1) {
						this.reset();
						this.$message.success(res.Message);
                    } else {
                        this.$message.error(res.Message);
                    }
                });	
            },
			rowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
				this.$refs.table.$refs.table.toggleRowSelection(row);
			},		
			outputRow() {
				// this.tableData.splice(0);
				//导出
				let tableData = this.$refs.table.tableData
				let sortData = this.$refs.table.filterColumns
				let exportData = this.handleTableSortData(tableData, sortData)
				Excel.exportExcel(exportData, "工单物料请求" + '-' + this.base.getDate());
			},
			handleTableSortData(tableData, sortData) {
				let newArray = [];
				tableData.forEach(data => {
					let newItem = {};
					sortData.forEach(field => {
						if (data.hasOwnProperty(field.field)) {
							newItem[field.title] = data[field.field];
						}
					});
					newArray.push(newItem);
				});
				return newArray
			},
			getCurrentDateTimeString() {
				const now = new Date();
				const year = now.getFullYear();
				const month = this.padNumber(now.getMonth() + 1);
				const day = this.padNumber(now.getDate());
				const hours = this.padNumber(now.getHours());
				const minutes = this.padNumber(now.getMinutes());
				const seconds = this.padNumber(now.getSeconds());
			
				return `${year}${month}${day}${hours}${minutes}${seconds}`;
			},
			padNumber(num) {
				return num < 10 ? '0' + num : num;
			},
			loadBefore(params, callBack) {	
				params["work"] = this.searchWork;			
				params["day"] = this.searchDay;			
				params["workcenter"] = this.searchWorkcenter;
				params["group"] = this.searchEmployeeGroup;
				params["mfgorder"] = this.searchMfgorder;
				params["planstart"] = this.searchPlanDate != null ? this.searchPlanDate[0] : ''
				params["planend"] = this.searchPlanDate != null ? this.searchPlanDate[1] : ''
				callBack(true)
			},
			loadAfter(rows, callBack, result) {
				if (result.Result == 1) {
					console.log("result",result)
					//this.columns = result.Data.colums;
					result.Data.tableData.forEach(row => {
						row.ProductionDemandQty = parseFloat((row.Production * row.QtyRequired).toFixed(3));
						row.IntegerQty = (row.NetDemandQty / row.ProductionDemandQty) > 2 ? row.ProductionDemandQty : row.NetDemandQty;
						row.RequestQty = row.IntegerQty == row.NetDemandQty ? row.IntegerQty : row.MiniPackageQty;
						row.PackQty = row.MiniPackageQty!=null&&row.MiniPackageQty !== 0? Math.ceil(row.IntegerQty / row.MiniPackageQty) * row.MiniPackageQty : 0;
					});

					this.tableData = result.Data.tableData;
					this.$refs.table.rowData = result.Data.tableData;
					//默认全选this.$refs.table.rowData 
					this.$refs.table.$refs.table.toggleAllSelection();
					
					this.$refs.table.paginations.total = result.Data.total;
				}
				else {
					this.$message.error(result.Message);
				}
				callBack(false);
			},
		}
	}
</script>
<style lang="less" scoped>
.table-item-header {
  display: flex;
  align-items: center;
  padding: 6px;

  .table-item-border {
    height: 15px;
    background: rgb(33, 150, 243);
    width: 5px;
    border-radius: 10px;
    position: relative;
    margin-right: 5px;
  }

  // .table-item-text {
  // 	font-weight: bolder;
  // 	border-bottom: 1px solid #0c0c0c;
  // }
  .table-item-text {
    margin-top: 3px;
    padding-bottom: 6px;
    font-weight: bold;
    font-size: 15px;
    color: #484848;
    white-space: nowrap;
    border-bottom: 2px solid #676767;
    margin-bottom: -1px;
    letter-spacing: 1px;
  }

  .table-item-buttons {
    flex: 1;
    text-align: right;
  }

  .small-text {
    font-size: 12px;
    color: #2196f3;
    margin-left: 10px;
    position: relative;
    top: 2px;
  }
}
</style>