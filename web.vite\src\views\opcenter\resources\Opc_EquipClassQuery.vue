<template>
    <ModelInfo></ModelInfo>

    <div class="container">
        <div class="form-content">
            <VolForm ref="form" :formFields="formFields_Add" :formRules="formRules_Add">

            </VolForm>
        </div>
        <div class="table-item">
            <div class="table-item-header">
                <div class="table-item-border"></div> <span class="table-item-text">{{ this.$ts(TitleName)
                    }}</span>
                <div style="text-align: end; margin-top: 0px;width:70%;">
                    <el-button type="primary" @click="setCurrentAction('add')"><i class="el-icon-circle-plus-outline">
                            {{ this.$ts('Add') }}
                        </i></el-button>
                    <el-button type="primary" icon="Edit" @click="setCurrentAction('edit')">{{ this.$ts('Update')
                        }}</el-button>
                    <el-button type="success" @click="setCurrentAction('copy')"><i class="el-icon-copy-document">
                            {{ this.$ts('Copy') }}
                        </i></el-button>

                    <el-button type="primary" icon="Delete" @click="setCurrentAction('delete')">{{ this.$ts('Delete')
                        }}</el-button>


                </div>
            </div>


        </div>
        <div class="form-content" v-show="IsEnable">
            <VolForm ref="form1" :loadKey="true" :formFields="curFormFields" :formRules="curFormRules">

            </VolForm>



            <div v-show="false">
                <!-- 新增按钮弹出的内容 -->
                <VolForm ref="form2" :loadKey="true" :formFields="formFieldsContent_Add"
                    :formRules="formRulesContent_Add">
                </VolForm>
                <VolForm ref="form3" :loadKey="true" :formFields="formFieldsContent_Edit"
                    :formRules="formRulesContent_Edit">


                </VolForm>
                <VolForm ref="form4" :loadKey="true" :formFields="formFieldsContent_Delete"
                    :formRules="formRulesContent_Delete">
                </VolForm>
                <VolForm ref="form5" :loadKey="true" :formFields="formFieldsContent_Copy"
                    :formRules="formRulesContent_Copy">
                </VolForm>
            </div>


        </div>


    </div>
    <div style="text-align: center; width: 100%;margin-top:20px">

        <el-button type="primary" @click="formSubmit"><i class="el-icon-check">
                {{ this.$ts('Submit') }}
            </i>
        </el-button>

        <!-- <el-cascader :props="props"></el-cascader> -->
    </div>
    <!-- <Opc_EquipClassEdit ref="modalForm2" @ok="modalFormOk" @confirm="handleConfirm"></Opc_EquipClassEdit> -->
</template>



<script lang="jsx">

import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
// import Opc_EquipClassEdit from './Opc_EquipClassEdit.vue'
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'//先引入
export default {
    components: {
        ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        // Opc_EquipClassEdit,
    },
    mixins: [GlobalElMessageBox],
    data() {
        return {
            /* props: {
                lazy: true,
                lazyLoad(node, resolve) {
                    let id = 0;
                    const { level } = node;
                    setTimeout(() => {
                        const nodes = Array.from({ length: level + 1 })
                            .map(item => ({
                                value: ++id,
                                label: `选项${id}`,
                                leaf: level >= 2
                            }));
                        // 通过调用resolve将子节点数据返回，通知组件数据加载完成
                        resolve(nodes);
                    }, 1000);
                }
            }, *//* 层级下拉框 */


            currentAction: '', // 当前操作
            TitleName: '',
            curFormFields: {
            },
            curFormRules: [],
            formFields_Add: {
                FamilyType: null,//Code type
                //ResourceFamilyName: null,//Classification Code

            },
            formRules_Add: [
                [
                    {
                        dataKey: "familyType", //后台下拉框对应的数据字典编号
                        data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                        title: "Code type",
                        placeholder: "Code type",
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "FamilyType",
                        type: "select",

                        colSize: 3,
                        /* onChange: (value) => {
                            this.changeFamilyType(value,this.currentAction);
                        } */
                    }
                ]
            ],

            IsEnable: false,

            //Add
            formFieldsContent_Add: {
                Description: null,//Description
                ResourceFamilyName: null,//Code type
                NewResourceFamilyName: null,//新分类代码
                Biz_EquipDailyCheck: null,//设备检数采表
                Biz_EquipDailyCheckResvision: "1",//设备检数采表版本,默认为1
                Biz_InProcessCheck: null,//制程检数采表
                Biz_InProcessCheckResvision: "1",//制程检数采表版本号,默认为1
                Biz_EquipMaintL1: null,//保养一数采表名称
                Biz_EquipMaintL2: null,//保养二数采表名称
                Biz_EquipMaintL3: null,//保养三数采表名称
                Biz_EquipMaintL1Resvision: "1",//保养一数采表版本号
                Biz_EquipMaintL2Resvision: "1",//保养二数采表版本号
                Biz_EquipMaintL3Resvision: "1",//保养三数采表版本号
                Biz_EquipHealthCheck: null,//状态检数采表
                Biz_EquipHealthCheckResvision: "1",//状态检数采表版本号
            },

            formRulesContent_Add:
                [
                    [

                        {
                            title: this.$ts('Classification Code'),
                            placeholder: this.$ts('Classification Code'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceFamilyName",
                            colSize: 3
                        },
                        {
                            title: "Description",
                            placeholder: "Description",
                            filter: true,
                            required: false, //设置为必选项
                            field: "Description",
                            colSize: 3
                        }

                    ],

                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Equipment inspection form'),
                            placeholder: this.$ts('Equipment inspection form'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipDailyCheck",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Process Inspection Sheet",
                            placeholder: "Process Inspection Sheet",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_InProcessCheck",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 1'),
                            placeholder: this.$ts('Maintenance table level 1'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL1",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Maintenance table level 2",
                            placeholder: "Maintenance table level 2",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipMaintL2",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 3'),
                            placeholder: this.$ts('Maintenance table level 3'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL3",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Status Checklist",
                            placeholder: "Status Checklist",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipHealthCheck",
                            colSize: 3
                        }

                    ],

                ],


            //编辑
            formFieldsContent_Edit: {

                ResourceFamilyName: null,//Classification Code
                Description: null,//Description
                FamilyType: null,//Code type
                NewResourceFamilyName: null,//新分类代码
                Biz_EquipDailyCheck: null,//设备检数采表
                Biz_EquipDailyCheckResvision: "1",//设备检数采表版本,默认为1
                Biz_InProcessCheck: null,//制程检数采表
                Biz_InProcessCheckResvision: "1",//制程检数采表版本号,默认为1
                Biz_EquipMaintL1: null,//保养一数采表
                Biz_EquipMaintL2: null,//保养二数采表
                Biz_EquipMaintL3: null,//保养三数采表
                Biz_EquipMaintL1Resvision: "1",//保养一数采表版本号
                Biz_EquipMaintL2Resvision: "1",//保养二数采表版本号
                Biz_EquipMaintL3Resvision: "1",//保养三数采表版本号
                Biz_EquipHealthCheck: null,//状态检数采表
                Biz_EquipHealthCheckResvision: "1",//状态检数采表版本号
            },

            formRulesContent_Edit:
                [

                    [
                        {
                            dataKey: "ResourceFamilyName", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceFamilyName",
                            type: "select",
                            onChange: (value) => {
                                //console.log(value, 'value');
                                let postData_Condition = {
                                    //FamilyType: this.formFields_Add.FamilyType, // 表单
                                    //Description: this.curFormFields.Description, // 表单
                                    ResourceFamilyName: value, // 表单
                                };
                                this.http.post('api/ResourceFamily/ResourceFamilyInfoSearch', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {
                                        //console.log(this.curFormFields, 'rows');
                                        console.log(res.rows, 'ResourceFamilyInfoSearch');
                                        this.curFormFields = res.rows[0];
                                        this.curFormFields.NewResourceFamilyName = res.rows[0].ResourceFamilyName;
                                        // this.curFormFields.Description = res.rows[0].Description;

                                    }
                                }).catch(error => {

                                    this.$message.error(error);

                                });
                            },

                            colSize: 3
                        }
                    ],
                    [

                        {
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: false, //设置为必选项
                            field: "NewResourceFamilyName",
                            colSize: 3
                        },
                        {
                            title: "Description",
                            placeholder: "Description",
                            filter: true,
                            required: false, //设置为必选项
                            field: "Description",
                            colSize: 3
                        }

                    ],


                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Equipment inspection form'),
                            placeholder: this.$ts('Equipment inspection form'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipDailyCheck",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Process Inspection Sheet",
                            placeholder: "Process Inspection Sheet",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_InProcessCheck",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 1'),
                            placeholder: this.$ts('Maintenance table level 1'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL1",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Maintenance table level 2",
                            placeholder: "Maintenance table level 2",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipMaintL2",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 3'),
                            placeholder: this.$ts('Maintenance table level 3'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL3",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Status Checklist",
                            placeholder: "Status Checklist",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipHealthCheck",
                            colSize: 3
                        }

                    ],


                ],
            //Copy
            formFieldsContent_Copy: {
                ResourceFamilyName: null,//Classification Code
                Description: null,//Description
                FamilyType: null,//Code type
                NewResourceFamilyName: null,//新分类代码
                Biz_EquipDailyCheck: null,//设备检数采表
                Biz_EquipDailyCheckResvision: "1",//设备检数采表版本,默认为1
                Biz_InProcessCheck: null,//制程检数采表
                Biz_InProcessCheckResvision: "1",//制程检数采表版本号,默认为1
                Biz_EquipMaintL1: null,//保养一数采表
                Biz_EquipMaintL2: null,//保养二数采表
                Biz_EquipMaintL3: null,//保养三数采表
                Biz_EquipMaintL1Resvision: "1",//保养一数采表版本号
                Biz_EquipMaintL2Resvision: "1",//保养二数采表版本号
                Biz_EquipMaintL3Resvision: "1",//保养三数采表版本号
                Biz_EquipHealthCheck: null,//状态检数采表
                Biz_EquipHealthCheckResvision: "1",//状态检数采表版本号
            },

            formRulesContent_Copy:
                [

                    [
                        {
                            dataKey: "resourceFamilyName", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceFamilyName",
                            type: "select",
                            onChange: (value) => {
                                this.CopyCurrent_resourceFamilyName(value);
                            },

                            colSize: 3
                        }
                    ],
                    [

                        {
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: false, //设置为必选项
                            field: "NewResourceFamilyName",
                            colSize: 3
                        },
                        {
                            title: "Description",
                            placeholder: "Description",
                            filter: true,
                            required: false, //设置为必选项
                            field: "Description",
                            colSize: 3
                        }

                    ],


                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Equipment inspection form'),
                            placeholder: this.$ts('Equipment inspection form'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipDailyCheck",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Process Inspection Sheet",
                            placeholder: "Process Inspection Sheet",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_InProcessCheck",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 1'),
                            placeholder: this.$ts('Maintenance table level 1'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL1",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Maintenance table level 2",
                            placeholder: "Maintenance table level 2",
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipMaintL2",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 3'),
                            placeholder: this.$ts('Maintenance table level 3'),
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL3",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Status Checklist",
                            placeholder: "Status Checklist",
                            filter: false,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipHealthCheck",
                            colSize: 3
                        }

                    ],
                ],
            formFieldsContent_Delete: {
                ResourceFamilyName: null,//Classification Code
                Description: null,//Description
                FamilyType: null,//Code type
                NewResourceFamilyName: null,//新分类代码
                Biz_EquipDailyCheck: null,//设备检数采表
                Biz_EquipDailyCheckResvision: "1",//设备检数采表版本,默认为1
                Biz_InProcessCheck: null,//制程检数采表
                Biz_InProcessCheckResvision: "1",//制程检数采表版本号,默认为1
                Biz_EquipMaintL1: null,//保养一数采表
                Biz_EquipMaintL2: null,//保养二数采表
                Biz_EquipMaintL3: null,//保养三数采表
                Biz_EquipMaintL1Resvision: "1",//保养一数采表版本号
                Biz_EquipMaintL2Resvision: "1",//保养二数采表版本号
                Biz_EquipMaintL3Resvision: "1",//保养三数采表版本号
                Biz_EquipHealthCheck: null,//状态检数采表
                Biz_EquipHealthCheckResvision: "1",//状态检数采表版本号
            },

            formRulesContent_Delete:
                [

                    [
                        {
                            dataKey: "resourceFamilyName", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceFamilyName",
                            type: "select",
                            onChange: (value) => {
                                //console.log(value, 'value');
                                let postData_Condition = {
                                    //FamilyType: this.formFields_Add.FamilyType, // 表单
                                    //Description: this.curFormFields.Description, // 表单
                                    ResourceFamilyName: value, // 表单
                                };
                                this.http.post('api/ResourceFamily/ResourceFamilyInfoSearch', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {
                                        this.curFormFields = res.rows[0];
                                        this.curFormFields.NewResourceFamilyName = res.rows[0].ResourceFamilyName;
                                    }
                                }).catch(error => {
                                    this.$message.error(error);
                                });
                            },
                            colSize: 3
                        }
                    ],
                    [

                        {
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            readonly: true,
                            filter: true,
                            required: true, //设置为必选项
                            field: "NewResourceFamilyName",
                            colSize: 3
                        },
                        {
                            title: "Description",
                            placeholder: "Description",
                            filter: true,
                            readonly: true,
                            required: false, //设置为必选项
                            field: "Description",
                            colSize: 3
                        }

                    ],


                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Equipment inspection form'),
                            placeholder: this.$ts('Equipment inspection form'),
                            filter: true,
                            required: false, //设置为必选项
                            readonly: true,

                            field: "Biz_EquipDailyCheck",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Process Inspection Sheet",
                            placeholder: "Process Inspection Sheet",
                            filter: true,
                            readonly: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_InProcessCheck",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 1'),
                            placeholder: this.$ts('Maintenance table level 1'),
                            readonly: true,
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL1",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Maintenance table level 2",
                            placeholder: "Maintenance table level 2",
                            readonly: true,
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipMaintL2",
                            colSize: 3
                        }

                    ],
                    [

                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Maintenance table level 3'),
                            placeholder: this.$ts('Maintenance table level 3'),
                            filter: true,
                            readonly: true,
                            required: false, //设置为必选项
                            field: "Biz_EquipMaintL3",
                            type: "select",
                            colSize: 3
                        },
                        {
                            dataKey: "项目表名", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Status Checklist",
                            placeholder: "Status Checklist",
                            readonly: true,
                            filter: true,
                            required: false, //设置为必选项
                            type: "select",
                            field: "Biz_EquipHealthCheck",
                            colSize: 3
                        }

                    ],
                ],


        }
    },
    watch: {
        'currentAction': function (val) {
            // console.log(this.formFields_Add.FamilyType, 'val');
            console.log(val.charAt(0).toUpperCase() + val.slice(1), 'val');
            this.changeFamilyType(this.formFields_Add.FamilyType, val.charAt(0).toUpperCase() + val.slice(1));
        },
        'formFields_Add.FamilyType': function (val1) {
            console.log(val1, 'val1');
            console.log(this.currentAction.charAt(0).toUpperCase() + this.currentAction.slice(1), 'currentAction');
            if (this.currentAction !== '') {
                this.changeFamilyType(val1, this.currentAction.charAt(0).toUpperCase() + this.currentAction.slice(1));

            }
        }

    },
    mounted() {
        /* console.log(this.http.get(), 'get');
        this.http.get(this.url, {}).then(res => {
            console.log(res, 'res');
        }) */
    },
    methods: {
        /* handleConfirm(callback) {
            // 检查是否接收到回调函数
            console.log(callback, "AAA");
            this.curFormFields = Object.assign({}, callback);
            // ResourceFamilyName
            // Description
            // 执行回调函数
            // callback(this);
        }, */

        CopyCurrent_resourceFamilyName(item) {
            //console.log(item, 'item');
            console.log(this.curFormFields, 'curFormFields');
            let postData_Condition = {
                ResourceFamilyName: item, // 表单
            };
            this.http.post('api/ResourceFamily/ResourceFamilyInfoSearch', postData_Condition).then(res => {
                /* console.log(res.rows, 'res');
                console.log(this.curFormFields, 'curFormFields');
                Object.keys(res.rows[0]).forEach(key => {
                    const formattedKey = key.charAt(0).toLowerCase() + key.slice(1);

                    if (this.curFormFields.hasOwnProperty(formattedKey)) {
                        console.log(key, 'formattedKey');

                        res.rows[0]['NewResourceFamilyName'] = 'Copy of ' + this.curFormFields['ResourceFamilyName'];
                        this.curFormFields[formattedKey] = res.rows[0][key];
                    }
                }); */
                this.curFormFields = res.rows[0];
                this.curFormFields.NewResourceFamilyName = 'Copy of ' + this.curFormFields.ResourceFamilyName;


            }).catch(error => {

                this.$message.error(error);

            });
            //let data = {NewResourceFamilyName:this.curFormFields,Description:''};
            // this.curFormFields = data;
            //this.curFormFields.Description = item.Description;
        },

        setCurrentAction(action) {
            if (this.formFields_Add.FamilyType == null) {
                this.$message.error(this.$ts('Please select the code type first.'));
                return;
            }
            this.currentAction = action;

            const actionMappings = {
                add: { titleKey: this.$ts('Add classification information'), fields: 'Add', enable: true, dicNo: 'addDicNo' },
                edit: { titleKey: this.$ts('Update classification information'), fields: 'Edit', enable: true, dicNo: 'editDicNo' },
                copy: { titleKey: this.$ts('Copy classification information'), fields: 'Copy', enable: true, dicNo: 'copyDicNo' },
                delete: { titleKey: this.$ts('Delete classification information'), fields: 'Delete', enable: true, dicNo: 'deleteDicNo' }
            };
            /* const FamilyType = {
                "RESOURCE": 'familyType_Resource',
                "TOOL": 'familyType_Tool',
                "RESOURCEMATERIALPART": 'familyType_Part',
            };
            const datakey = FamilyType[this.formFields_Add.FamilyType];
 */
            const config = actionMappings[action];
            if (config) {
                this.TitleName = this.$ts(config.titleKey);
                this.IsEnable = config.enable;
                this.curFormFields = this[`formFieldsContent_${config.fields}`];
                this.curFormRules = this[`formRulesContent_${config.fields}`];

                // // 重载对应的数据字典
                // this.resetItemSource(this[`formRulesContent_${config.fields}`][0][0].data, datakey);
                // console.log(this[`formRulesContent_${config.fields}`], 'curFormFields');
            }
            //this.changeFamilyType(this.formFields_Add.FamilyType);
        },
        //根据代码类型重载数据源
        changeFamilyType(type_code, action) {
            const FamilyType = {
                "设备": { dic: 'familyType_Resource'/* , fields: 'Edit' */ },
                "治工具": { dic: 'familyType_Tool'/* , fields: 'Copy' */ },
                "配件": { dic: 'familyType_Part'/* , fields: 'Delete' */ }
            };
            const datakey = FamilyType[type_code];
            if (this[`formRulesContent_${action}`][0][0]) {
                this.resetItemSource(this[`formRulesContent_${action}`][0][0].data, datakey.dic);

            }
        },
        async resetItemSource(proxyArray, dicNo) {
            try {
                let newData = await this.SysApi_GetDicData(dicNo);
                if (!Array.isArray(newData)) {
                    throw new Error("返回的数据不是数组类型");
                }
                while (proxyArray.length > 0) {
                    proxyArray.pop();
                }
                newData.forEach(item => {
                    proxyArray.push(item);
                });
            } catch (error) {
                console.error("重置项目源时出错:", error);
            }
        },
        async formSubmit() {
            const validateAndProcess = async (postData, apiUrl, proxyArrays) => {
                const valid = await this.$refs.form1.validate();
                if (!valid) return;

                try {
                    const res = await this.http.post(apiUrl, postData);
                    console.log(res);
                    const parts = res.message.match(/ResourceFamilyName must match the pattern:|Length:|\d+;|start with:|\d+( or \d+)?/g);
                    console.log(parts);
                    this.resultMessageStay(res.status, this.$ts(parts[0]) + this.$ts(parts[1]) + this.$ts(parts[2]) + this.$ts(parts[3]) + this.$ts(parts[4]));
                    //proxyArrays.forEach(array => this.resetItemSource(array, 'resourceFamilyName'));
                } catch (error) {
                    this.$message.error(error);
                } finally {
                    this.reset();
                }
            };

            const getProxyArrays = () => {
                return [
                    this[`formRulesContent_Edit`][0][0].data,
                    this[`formRulesContent_Copy`][0][0].data,
                    this[`formRulesContent_Delete`][0][0].data
                ];
            };

            if (this.currentAction === '') {
                this.$message.error(this.$ts('Please select the code type first.'));
                return;
            }

            let actionUrlMap = {
                'add': 'api/ResourceFamily/ResourceFamilyAdd',
                'edit': 'api/ResourceFamily/ResourceFamilyEdit',
                'copy': 'api/ResourceFamily/ResourceFamilyAdd', // 注意：复制使用的是添加的API
                'delete': 'api/ResourceFamily/ResourceFamilyDelete'
            };

            let postData = {
                ...this.formFields_Add,
                ...this.curFormFields,
            };
            //定义不同动作的提交数据
            let actionSpecificData = {
                'add': {
                    ...this.formFields_Add,
                    ...this.curFormFields,
                    //FamilyType: this.formFields_Add.FamilyType,
                },
                'edit': {
                    /* FamilyType: this.formFields_Add.FamilyType,
                    ResourceFamilyName: this.curFormFields.ResourceFamilyName,
                    NewResourceFamilyName: this.curFormFields.NewResourceFamilyName,
                    Description: this.curFormFields.Description, */
                    ...this.formFields_Add,
                    ...this.curFormFields,
                    FamilyType: this.formFields_Add.FamilyType,
                },
                'copy': {
                    FamilyType: this.formFields_Add.FamilyType,
                    ResourceFamilyName: this.curFormFields.NewResourceFamilyName,
                    NewResourceFamilyName: this.curFormFields.NewResourceFamilyName,
                    Description: this.curFormFields.Description,
                },
                'delete': {
                    FamilyType: this.formFields_Add.FamilyType,
                    ResourceFamilyName: this.curFormFields.ResourceFamilyName,
                    NewResourceFamilyName: this.curFormFields.NewResourceFamilyName,
                    Description: this.curFormFields.Description,
                }
            };

            if (['edit', 'copy', 'delete', 'add'].includes(this.currentAction)) {
                postData = actionSpecificData[this.currentAction];
            }

            const proxyArrays = getProxyArrays();
            const apiUrl = actionUrlMap[this.currentAction];

            await validateAndProcess(postData, apiUrl, proxyArrays);

            console.log(this.formFields_Add, 'this.formFields_Add');
            console.log(postData, 'postData');
        },

        reset() {
            this.$refs.form1.reset(this.curFormFields);
            //this.$Message.success("表单已重置");
        },


    }
};
</script>
<style lang="less" scoped>
.container {
    padding: 10px;
    background: #F3F7FC;

    .form-content {
        border-radius: 5px;
        padding: 10px 0;
        background: #fff;
    }

    .table-item-header {
        display: flex;
        align-items: center;
        padding: 6px;

        .table-item-border {
            height: 15px;
            background: rgb(33, 150, 243);
            width: 5px;
            border-radius: 10px;
            position: relative;
            margin-right: 5px;
        }

        .table-item-text {
            font-weight: bolder;
        }

        .table-item-buttons {
            flex: 1;
            text-align: right;
        }

        .small-text {
            font-size: 12px;
            color: #2196F3;
            margin-left: 10px;
            position: relative;
            top: 2px;
        }
    }
}
</style>