<template>
  <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules"></VolForm>
  <div style="text-align: end; margin-top: 10px; width: 100%">
    <div style="margin-right: 20px">
      <el-button type="primary" icon="Search" plain @click="search">查询</el-button>
      <el-button type="success" icon="Plus" @click="addRow" plain>新建</el-button>
      <el-button type="primary" icon="Edit" @click="getRow" plain>编辑</el-button>
      <el-button type="danger" icon="Delete" @click="delRow" plain>删除</el-button>
      <el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
    </div>
  </div>
  <vol-table
    ref="table"
    index
    :column-index="true"
    :reserveSelection="true"
    :loadKey="true"
    :columns="columns"
    :tableData="tableData"
    :ck="true"
    :pagination-hide="false"
    :max-height="380"
    :url="ApiUrl.QueryResourceInfo"
    :defaultLoadPage="false"
    :single="true"
    @rowClick="rowClick"
    @loadBefore="loadBefore"
    @loadAfter="loadAfter"
  ></vol-table>

  <!-- 编辑弹出框 -->
  <vol-box :lazy="true" v-model="show" title="Material Part" :width="800" :padding="5">
    <VolForm ref="form1" :loadKey="true" :formFields="boxFields" :formRules="boxRules"></VolForm>
    <template #footer>
      <div>
        <el-button type="success" icon="Check" size="small" @click="save">保存</el-button>
        <el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
      </div>
    </template>
  </vol-box>
</template>
	
	<script lang="jsx">
import VolTable from '@/components/basic/VolTable.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolHeader from '@/components/basic/VolHeader.vue'
import VolBox from '@/components/basic/VolBox.vue'
import { mapState } from 'vuex'
import Excel from '@/uitils/xlsl.js'
export default {
  components: {
    VolHeader,
    VolForm,
    'vol-table': VolTable,
    'vol-box': VolBox
  },
  computed: {
    ...mapState({
      //获取当前用户的信息
      userInfo: (state) => state.userInfo,
      //获取当前用户的权限
      permission: (state) => state.permission
    })
  },
  data() {
    return {
      ApiUrl: {
        QueryResourceInfo: '/api/Query/QueryResouceMaterialPart', //查询设备台账信息
        GetResourceObject: "/api/Query/GetResourceByType",//获取NameObject
        GetRevisionObject: "/api/query/GetRevisionObject",
        AddPart: 'api/CDO/MaterialPartModeingTxn', //创建Part
        EidtPart: 'api/CDO/MaterialPartModeingTxn' //修改Part
      },
      headerFields: {
        cdoName: 'Part',
        StartTime: null,
        EndTime: null
      },
      headerRules: [
        [
          {
            title: this.$ts('开始时间'),
            placeholder: this.$ts(''),
            required: false,
            field: 'StartTime',
            type: 'datetime',
            colSize:3
          },
          {
            title: this.$ts('结束时间'),
            placeholder: this.$ts(''),
            required: false,
            field: 'EndTime',
            type: 'datetime',
            colSize:3
          }
        ]
      ],
      columns: [
        {
          field: 'productName',
          title: '名称',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'ProductRevision',
          title: '版本',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'Description',
          title: '描述',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'EmailGroupName',
          title: '邮件组',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'MinQty',
          title: '最小数量',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'PartType',
          title: '类型',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'ProductId',
          title: 'ID',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: true
        },
        {
          field: 'Notes',
          title: '备注',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: true
        },
        {
          field: 'Qty',
          title: 'PartQty',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'CreationDate',
          title: '创建时间',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'CreationUserName',
          title: '创建人员',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        }
      ],
      tableData: [],
      show: false,
      boxFields: {
        ID:null,
        Name: null,
        Description: null,
        Revision: null,
        PartType: null,
        MinQty: null,
        MinQtyReorderEmailGroup: null,
        CommentStr: null,
      },
      boxRules: [
        [
          {
            //dataKey: 'Biz_EmailGroup', //后台下拉框对应的数据字典编号
            // data: [],
            title: this.$ts('备件编号'),
            placeholder: this.$ts(''),
            filter: true,
            required: true, //设置为必选项
            field: 'Name',
            colSize: 3,
            type: 'text'
          },
          {
            title: this.$ts('规格型号'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'Description',   
            colSize: 3,
            type: 'text'
          },],
          [
          {
            title: this.$ts('版本'),
            placeholder: this.$ts(''),
            filter: true,
            required: true, //设置为必选项
            field: 'Revision',
            colSize: 3,
            type: 'text'
          },
          {
            title: this.$ts('类型'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'PartType',
            colSize: 3,
            type: 'select',
            data: [{key:1,value:"Serialized"},{key:2,value:"Bulk"},{key:3,value:"NonSerialized"}]
          },],
          [{
            title: this.$ts('最小库存数'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'MinQty',
            colSize: 3,
            type: 'text'
          },
          {
            title: this.$ts('邮件通知组'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'MinQtyReorderEmailGroup',
            colSize: 3,
            type: 'select',
            data: []
          }
        ],
        [
          {
            title: this.$ts('备注'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'CommentStr',
            colSize: 10,
            type: 'textarea',
            hidden:'true',
          }
        ]
      ],
      eventName: null
    }
  },
  methods: {
    search() {
      this.ResetInfo()
      this.$refs.table.load(null, true)
    },
    loadBefore(params, callBack) {
      let param = {
        startDate: this.headerFields.StartTime,
        endDate: this.headerFields.EndTime,
        cdoName: 'ResourceMaterialPart',
        PageSize: this.$refs.table.paginations.size,
        PageCount: this.$refs.table.paginations.page
      }
      params = Object.assign(params, param)
      callBack(true)
    },
    loadAfter(rows, callBack, result) {
      if (result.Result == 1) {
        //this.columns = result.Data.colums;
        this.tableData = result.Data.tableData
        this.$refs.table.rowData = result.Data.tableData
        this.$refs.table.paginations.total = result.Data.total
      } else {
        this.$message.error(result.Message)
      }
      callBack(false)
    },
    addRow() {
      // this.tableData.push({ OrderNo: "D2022040600009" })
      this.ResetInfo()
      ;(this.eventName = 'New'), (this.show = true)
    },
    ResetInfo() {
      this.reset(this.boxFields)
      this.reset(this.headerFields)
      // this.selectLocation(); //加载区域
       this.boxRules[2][1].data =  this.selectResource("EmailGroup","EmailGroup","A_");//供应商
      // this.selectProdct();
    },
    reset(object) {
      console.log('重置')
      for (const key in object) {
        object[key] = ''
      }
    },
    rowClick({ row, column, index }) {
      // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
      this.$refs.table.$refs.table.toggleRowSelection(row);
    },
    getRow() {
      const rows = this.$refs.table.getSelected()
      if (!rows.length) {
        this.$message.error('请选中行')
        return
      }
      ;this.eventName = 'Load',
        // this.$message.success(JSON.stringify(rows))
        this.boxFields.Name = rows[0].productName
        this.boxFields.Revision = rows[0].ProductRevision
        this.boxFields.Description = rows[0].Description
        this.boxFields.MinQtyReorderEmailGroup = rows[0].EmailGroupName
        this.boxFields.PartType = rows[0].PartType
        this.boxFields.MinQty = rows[0].MinQty
        this.boxFields.ID = rows[0].ProductId
        this.boxFields.CommentStr = rows[0].Notes
        this.show = true
    },

    outputRow() {
      // this.tableData.splice(0);
      //导出
      let tableData = this.$refs.table.tableData
      let sortData = this.$refs.table.filterColumns
      let exportData = this.handleTableSortData(tableData, sortData)
      Excel.exportExcel(exportData, '备品备件信息' + '-' + this.base.getDate())
    },
    handleTableSortData(tableData, sortData) {
      let newArray = []
      tableData.forEach((data) => {
        let newItem = {}
        sortData.forEach((field) => {
          if (data.hasOwnProperty(field.field)) {
            newItem[field.title] = data[field.field]
          }
        })
        newArray.push(newItem)
      })
      return newArray
    },

    save() {
      //保存
      let params = {
        User: this.userInfo.userName,
        Password: this.userInfo.userPwd,
        Comments: this.boxFields.CommentStr,
        EventName: this.eventName,
        ID:this.boxFields.ID,
        Name:this.boxFields.Name,
        Revision:this.boxFields.Revision,
        detailsData:{
          Description:this.boxFields.Description,
          PartType:this.boxFields.PartType,
          MinQtyReorderEmailGroup:{Name:this.boxFields.MinQtyReorderEmailGroup},
          MinQty:this.boxFields.MinQty,
        },
      }
      if (this.eventName == 'New') {
        this.http.post(this.ApiUrl.AddPart, params).then((res) => {
          if (res.Result == 1) {
            this.show = false
            this.ResetInfo()
            this.search()
            this.$message.success(res.Message)
          } else {
            this.$message.error(res.Message)
          }
        })
      } else if (this.eventName == 'Load') {
        this.http.post(this.ApiUrl.EidtPart, params, true).then((res) => {
          if (res.Result == 1) {
            this.show = false
            this.ResetInfo()
            this.search()
            this.$message.success(res.Message)
          } else {
            this.$message.error(res.Message)
          }
        })
      } else {
        this.$message.error('未知操作')
      }
    },
    close() {
      this.show = false
    },
    selectResource(cdoName,typeName,perStr){
      let param={
                // cdo:"ResourceFamily",
                cdo:cdoName,
                type:typeName,
                perCdo:perStr
            }
            let dataArr=[]
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        
                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
               
            )
            return dataArr;
    },

    
    delRow() {
				// this.$message.success('删除成功')
				const rows = this.$refs.table.getSelected();
				if (!rows.length) {
				    this.$message.error('请选中行')
				    return;
				}
				if(rows.length > 0){
					let params = {
						User:this.userInfo.userName,
						Password:this.userInfo.userPwd,
						EventName: 'DeleteAllRevisions',
            ID:rows[0].ProductId,
            Name:rows[0].productName,
            Revision:rows[0].ProductRevision,
					};
					this.http.post(this.ApiUrl.EidtPart, params).then(res => {
						if (res.Result == 1) {
							this.$refs.table.delRow();	
							this.$message.success(res.Message);
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
  }
}
</script>