<template>
	<div class="app-container">
		<el-button @click="getInfo">getInfo</el-button>
		<el-row>
			<el-col :span="12" class="card-box">
				<el-card>
					<template #header><span>CPU</span></template>
					<div class="el-table el-table--enable-row-hover el-table--medium">
						<table cellspacing="0" style="width: 100%;">
							<thead>
								<tr>
									<th class="el-table__cell is-leaf">
										<div class="cell">属性</div>
									</th>
									<th class="el-table__cell is-leaf">
										<div class="cell">值</div>
									</th>
								</tr>
							</thead>
							<tbody>
								<!-- <tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">核心数</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.sys">{{ server.sys.cpuNum }}</div>
									</td>
								</tr> -->
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">逻辑数量</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.cpu">{{ server.cpu.cpuNum || server.cpu.cpuTotal }}
										</div>
									</td>
								</tr>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">系统使用率</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.cpu">
											<span v-if="server.cpu.cpuRate">{{ server.cpu.cpuRate }}%</span>
											<span v-else>
												{{ Math.floor((server.cpu.used / server.cpu.total) * 100) }}%
											</span>
										</div>
									</td>
								</tr>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">当前空闲率</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.cpu">
											{{ Math.floor((100 - server.cpu.cpuRate)) }}%
											<!-- {{ Math.floor((server.cpu.free/server.cpu.total)*100) }}% -->
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</el-card>
			</el-col>

			<el-col :span="12" class="card-box">
				<el-card>
					<template #header><span>内存</span></template>
					<div class="el-table el-table--enable-row-hover el-table--medium">
						<table cellspacing="0" style="width: 100%;">
							<thead>
								<tr>
									<th class="el-table__cell is-leaf">
										<div class="cell">属性</div>
									</th>
									<th class="el-table__cell is-leaf">
										<div class="cell">内存</div>
									</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">总内存</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.cpu">{{ server.cpu.totalRAM }}</div>
									</td>
								</tr>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">已用内存</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.cpu">{{ server.cpu.usedRam }}</div>
									</td>
								</tr>
								<!-- <tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">剩余内存</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.cpu">{{ server.cpu.freeRam }}</div>
									</td>
								</tr> -->
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">使用率</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.cpu"
											:class="{ 'text-danger': server.cpu.ramRate > 80 }">{{
												server.cpu.ramRate }}
										</div>
									</td>

								</tr>
							</tbody>
						</table>
					</div>
				</el-card>
			</el-col>

			<el-col :span="24" class="card-box">
				<el-card>
					<template #header><span>服务器信息</span></template>
					<div class="el-table el-table--enable-row-hover el-table--medium">
						<table cellspacing="0" style="width: 100%;">
							<tbody>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">服务器名称</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.sys">{{ server.sys.computerName }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">操作系统</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.sys">{{ server.sys.osName }}</div>
									</td>
								</tr>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">服务器IP</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.sys">{{ server.sys.serverIP }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">系统架构</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.sys">{{ server.sys.osArch }}</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</el-card>
			</el-col>

			<el-col :span="24" class="card-box">
				<el-card>
					<template #header><span>应用信息</span></template>
					<div class="el-table el-table--enable-row-hover el-table--medium">
						<table cellspacing="0" style="width: 100%;table-layout:fixed;">
							<tbody>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">应用环境</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.app">{{ server.app.name }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">应用版本</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.app">{{ server.app.version }}</div>
									</td>
								</tr>
								<tr>
									<td class="el-table__cell is-leaf">
										<div class="cell">启动时间</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.app">{{ server.app.startTime }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">运行时长</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" v-if="server.app">{{ server.app.runTime }}</div>
									</td>
								</tr>
								<tr>
									<td colspan="1" class="el-table__cell is-leaf">
										<div class="cell">安装路径</div>
									</td>
									<td colspan="3" class="el-table__cell is-leaf">
										<div class="cell" v-if="server.app">{{ server.app.rootPath }}</div>
									</td>
								</tr>
								<tr>
									<td colspan="1" class="el-table__cell is-leaf">
										<div class="cell">项目路径</div>
									</td>
									<td colspan="3" class="el-table__cell is-leaf">
										<div class="cell" v-if="server.app">{{ server.app.webRootPath }}</div>
									</td>
								</tr>
								<tr>
									<td colspan="1" class="el-table__cell is-leaf">
										<div class="cell">运行参数</div>
									</td>
									<td colspan="3" class="el-table__cell is-leaf">
										<div class="cell" v-if="server.app">{{ server.app.name }}</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</el-card>
			</el-col>

			<el-col :span="24" class="card-box">
				<el-card>
					<template #header><span>磁盘状态</span></template>
					<div class="el-table el-table--enable-row-hover el-table--medium">
						<table cellspacing="0" style="width: 100%;">
							<thead>
								<tr>
									<th class="el-table__cell el-table__cell is-leaf">
										<div class="cell">盘符路径</div>
									</th>
									<th class="el-table__cell is-leaf">
										<div class="cell">盘符类型</div>
									</th>
									<th class="el-table__cell is-leaf">
										<div class="cell">总大小</div>
									</th>
									<th class="el-table__cell is-leaf">
										<div class="cell">可用大小</div>
									</th>
									<th class="el-table__cell is-leaf">
										<div class="cell">已用大小</div>
									</th>
									<th class="el-table__cell is-leaf">
										<div class="cell">已用百分比</div>
									</th>
								</tr>
							</thead>
							<tbody v-if="server.disk">
								<tr v-for="(sysFile, index) in server.disk" :key="index">
									<td class="el-table__cell is-leaf">
										<div class="cell">{{ sysFile.diskName }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">{{ sysFile.typeName }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">{{ sysFile.totalSize }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">{{ sysFile.availableFreeSpace }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell">{{ sysFile.used }}</div>
									</td>
									<td class="el-table__cell is-leaf">
										<div class="cell" :class="{ 'text-danger': sysFile.availablePercent > 80 }">
											{{ sysFile.availablePercent }}%
										</div>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</el-card>
			</el-col>


		</el-row>
	</div>
</template>

<script>
import {
	defineComponent,
	ref,
	reactive,
	getCurrentInstance
} from "vue";
//import http from "../../api/http.js";
import http from "@/../src/api/http.js";

const server = ref([]);
export default defineComponent({
	setup() {
		const url = '/api/sysMonitor/getInfo';
		const getInfo = () => {
			http.post(url, {}, true).then(result => {
				server.value = result;
			});
		}
		getInfo();
		return {
			server,
			getInfo
		};
	},
});
</script>

<style lang="less" scoped>
.card-box {
	padding: 6px;
}
</style>