<template>
  <div class="ModelInfo-Container">
    <div class="item time">
      <div>{{ $ts('Time:') }} <span>{{ modelInfo.time }}</span></div>
    </div>
    <div class="item branch">
      <div>{{ $ts('Shift:') }} <span>{{ modelInfo.branch }}</span></div>
    </div>
    <div class="item user">
      <div>{{ $ts('User:') }} <span>{{ modelInfo.user }}</span></div>
      <!-- <div>{{ $ts('User:') }} <span>{{ userInfo.userName }}</span></div> -->
    </div>
    <div class="item line">
      <div>{{ $ts('Line:') }} <span>{{ modelInfo.line }}</span></div>
    </div>
    <div class="item process">
      <!-- <div>{{ $ts('Process:') }} <span>{{ modelInfo.process }}</span></div> -->
      <div>{{ $ts('Spec:') }} <span>{{ fileData.value }}</span></div>
    </div>
    <div class="item process">
      <!-- <input type="file" id="fileInput" @change="handleFile"> -->
      <!-- <button @click="loadFile">加载文件</button> -->
    </div>
  </div>
</template>
<script>
import {
  defineComponent,
  ref,
  onMounted,
  computed,
  reactive,
  toRefs,
  onBeforeUnmount,
  getCurrentInstance
} from 'vue';
// import common from '@/uitils/common.js';
import { formateDate } from '@/uitils/common.js';
import store from '@/store/index';
import { mapState } from 'vuex'
import { useStore } from 'vuex'
import data from '@/views/builder/builderData';
import methods from '../basic/ViewGrid/methods';
export default defineComponent({
  components: {
  },
  data() {
    return {
      url: {
        shift: '',
        spec: '',
      }
    }
  },
  mounted() {

  },
  methods: {

  },
  setup() {

    let appContext = getCurrentInstance().appContext;
    let $ts = appContext.config.globalProperties.$ts;

    const modelInfo = ref({
      time: null,
      branch: null,
      user: null,
      line: null,
      process: null,
      file: null,
      fileContent: ''
    });

    const store = useStore();
    const userInfo = computed(() => store.state.userInfo);
    const fileData = computed(() => store.state.fileData);
    // let { getters } = useStore();
    // const fileData = computed(() => getters['getFileData']);
    // console.log(fileData.value,'getters')

    const handleFile = (event) => {
      modelInfo.value.file = event.target.files[0]
      setTimeout(() => {
        loadFile()
      }, 1000);
    }

    const loadFile = () => {
      const reader = new FileReader()
      reader.onload = (event) => {
        modelInfo.value.fileContent = event.target.result
        store.commit('handleFile', JSON.parse(modelInfo.value.fileContent))
      }
      reader.readAsText(modelInfo.value.file)
    }

    let timer = 0

    onMounted(() => {
      // modelInfo.value.time = common.getDate(true);
      timer = setInterval(() => {
        modelInfo.value.time = formateDate()
      }, 1000)
      modelInfo.value.user = userInfo.value.userName;
      // 获取shift
      handleShift()
    })

    const handleShift = () => {
      console.log(appContext, 'appContext');
    }

    onBeforeUnmount(() => {
      clearInterval(timer) //清除定时器
      timer = 0
    })

    return {
      modelInfo,
      userInfo,
      fileData,
      handleFile,
      loadFile,
    };
  },
});
</script>
<style lang="less" scoped>
.ModelInfo-Container {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  align-items: end;
  padding: 0 1%;
  height: 40px;
  font-size: 14px;
  color: #333;
  background-color: #ffff;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  .item {
    min-width: 10%;
    margin: 0 1% 0 0%;
  }
}
</style>