<template>
    <div class="table-item-header">
        <div class="table-item-border"></div> <span class="table-item-text">标签打印</span>
    </div>
    <!-- 搜索条件 -->
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>批次条码</span></label>
            <div style=" margin-top: 5px;">
                <el-input style="width: 200px;" v-model="searchContainer" clearable placeholder="请输入"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>产品(物料)编码</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="searchProduct" clearable filterable placeholder="请选择" remote-show-suffix
                    :remote="true" :remote-method="remoteMethod" :loading="loading" style="width: 200px">
                    <el-option v-for="item in products" :key="item.Name" :label="item.Name" :value="item.Name" />
                </el-select>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span> </span>
            </label>
            <div style="margin-top: 7px;">
                <el-button icon="Search" @click="queryRow" plain>查询</el-button>
                <el-button icon="Refresh" type="info" plain @click="reset">重置</el-button>
            </div>
        </div>
    </div>
    <el-divider />
    <div class="table-item">
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="420"
            :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="true"></vol-table>
    </div>
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>标签模板</span>
            </label>
            <div style="margin-top: 5px;">
                <!-- <el-input v-model="Spec"></el-input> -->
                <el-select v-model="printerLabelDefinition" clearable filterable placeholder="请选择" style="width: 200px">
                    <el-option v-for="item in printerLabelDefinitions" :key="item.Name" :label="item.Name"
                        :value="item.Name" />
                </el-select>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>打印机</span>
            </label>
            <div style="margin-top: 5px;">
                <!-- <el-input v-model="Spec"></el-input> -->
                <el-select v-model="printQueue" clearable filterable placeholder="请选择" style="width: 200px">
                    <el-option v-for="item in printQueues" :key="item.Name" :label="item.Name" :value="item.Name" />
                </el-select>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span></span>
            </label>
            <div style="margin-top: 7px;">
                <el-button type="primary" icon="printer" @click="PrintContainerLabel" plain>打印</el-button>

            </div>
        </div>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from "@/components/basic/VolForm.vue";
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        VolForm,
    },
    data() {
        return {
            containerTemp: '',//传递选择的container
            searchContainer: '',
            searchWorkcenter: '',
            searchProduct: '',
            searchTxnDate: [new Date().toLocaleDateString(), this.addDays(new Date().toLocaleDateString(), 7)],
            printQueue: '',
            printerLabelDefinition: '',

            workcenters: [],
            products: [],
            printerLabelDefinitions: [],
            printQueues: [],

            formFields: {
                department: null,
                workcenter: null,
                inventory: null,
                container: null,
                product: null,
                productType: null,
                modelNumber: null,
                uom: null,
                vendor: null,
                description: null,
                qty: null,
                mfgorder: null,
                comments: null
            },
            formRules: [
                [
                    { type: "textarea", title: "备注", readonly: false, required: false, placeholder: " ", field: "comments", colSize: 11 },
                ],
                [
                    { type: "text", title: "物料编号", readonly: true, required: false, placeholder: " ", field: "product", colSize: 2 },
                    { type: "text", title: "物料描述", readonly: true, required: false, placeholder: " ", field: "description", colSize: 3 },
                    { type: "text", title: "规格型号", readonly: true, required: false, placeholder: " ", field: "modelNumber", colSize: 3 },
                    { type: "text", title: "物料类型", readonly: true, required: false, placeholder: " ", field: "productType", colSize: 1 },
                    { type: "text", title: "批次数量", readonly: true, required: false, placeholder: " ", field: "qty", colSize: 1 },
                    { type: "text", title: "单位", readonly: true, required: false, placeholder: " ", field: "uom", colSize: 1 },
                ],

                [
                    { type: "text", title: "生产部门", readonly: true, required: false, placeholder: " ", field: "department", colSize: 2 },
                    { type: "text", title: "生产车间", readonly: true, required: false, placeholder: " ", field: "workcenter", colSize: 2 },
                    { type: "text", title: "线边仓", readonly: true, required: false, placeholder: " ", field: "location", colSize: 2 },
                    { type: "text", title: "供应商", readonly: true, required: false, placeholder: " ", field: "vendor", colSize: 2 },
                ],
            ],

            columns: [
                { field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
                { field: 'Container', title: '批次条码', type: 'string', width: 130, align: 'center' },
                { field: 'Status', title: '批次状态', type: 'string', width: 130, align: 'center' },
                { field: 'Department', title: '生产部门', type: 'string', width: 80, align: 'center' },
                { field: 'WorkCenter', title: '生产车间', type: 'string', width: 80, align: 'center' },
                { field: 'Vendor', title: '供应商', type: 'string', width: 80, align: 'center' },
                { field: 'Product', title: '产品(物料)编号', type: 'string', width: 100, align: 'center' },
                { field: 'Description', title: '产品(物料)描述', type: 'string', width: 100, align: 'center' },
                { field: 'ProductType', title: '产品(物料)类型', type: 'string', width: 100, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
                { field: 'Uom', title: '单位', type: 'string', width: 100, align: 'center' }
            ],
            tableData: [],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getLabelInfo: "/api/query/getLabelInfo",
                PrintContainerLabel:"/api/CDO/PrintContainerLabel",
            },
        }
    },
    created() {
        this.getPrintQueue();
        this.getPrinterLabelDefinition();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        addDays(dateStr, days) {
            var date = new Date(dateStr);
            date.setDate(date.getDate() + days);
            return date.toLocaleDateString();
        },
        remoteMethod(query) {
            if (query) {
                let params = {
                    cdo: "Product",
                    name: query
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.products = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        async getPrinterLabelDefinition() {
            let params = {
                cdo: "PrinterLabelDefinition"
            };
            this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                if (res.Result == 1) {
                    this.printerLabelDefinitions = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        async getPrintQueue() {
            let params = {
                cdo: "PrintQueue"
            };
            this.http.get(this.apiUrl.getNameObject, params).then(res => {
                if (res.Result == 1) {
                    this.printQueues = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        reset() {				
            this.searchContainer = '';
            this.searchProduct = '';
            this.searchWorkcenter = '';
            this.tableData = null;
        },
        rowClick({ row, column, event }) {
            this.$refs.table.$refs.table.toggleRowSelection(row);
        },
        queryRow() {
            if ((this.searchContainer == '' && this.searchProduct == '') || (this.searchContainer == null && this.searchProduct == null)) {
                this.$message.error('请输入查询条件。')
                return;
            }
            let params = {
                type: 'print',
                container: this.searchContainer,
                product: this.searchProduct
            };
            this.http.get(this.apiUrl.getLabelInfo, params, true).then(res => {
                if (res.Result == 1) {
                    this.tableData = res.Data;
                    this.$refs.table.rowData = res.Data.tableData;
                    this.$refs.table.paginations.total = res.Data.length;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        outputRow() {
            // this.tableData.splice(0);
            //导出
            let tableData = this.$refs.table.tableData
            let sortData = this.$refs.table.filterColumns
            let exportData = this.handleTableSortData(tableData, sortData)
            Excel.exportExcel(exportData, "批次" + '-' + this.base.getDate());
        },
        handleTableSortData(tableData, sortData) {
            let newArray = [];
            tableData.forEach(data => {
                let newItem = {};
                sortData.forEach(field => {
                    if (data.hasOwnProperty(field.field)) {
                        newItem[field.title] = data[field.field];
                    }
                });
                newArray.push(newItem);
            });
            return newArray
        },
        PrintContainerLabel() {
            const rows = this.$refs.table.getSelected();
            if (!rows.length) {
                this.$message.error('请选中行')
                return;
            }
            if ((this.printQueue == '' || this.printQueue == null) 
            && (this.printerLabelDefinition == '' || this.printerLabelDefinition == null)){
                this.$message.error('标签/打印机不能为空')
                return;
            }
            let params = {
                User:this.userInfo.userName,
                Password:this.userInfo.userPwd,
                Container: rows[0].Container,
                Printer:this.printQueue,
                Qty:'1',
                PrintLabel:this.printerLabelDefinition
            };
            this.http.post(this.apiUrl.PrintContainerLabel, params, true).then(res => {
                if (res.Result == 1) {
                    this.$message.success(res.Message);
                } else {
                    this.$message.error(res.Message);
                }
            });	
        }

    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>