<template>
  <div class="container">
    <div class="form-content">
      <VolForm ref="form1" :loadKey="true" :formFields="formFields1" :formRules="formRules1">
      </VolForm>
      <div style="text-align: end">
        <el-button type="success" plain @click="add">{{ this.$ts('Add') }}</el-button>
        <el-button type="primary" plain @click="copyClick">{{ this.$ts('Copy') }}</el-button>
        <el-button type="success" plain @click="saveRequirement">{{ this.$ts('Save') }}</el-button>
        <el-button type="primary" plain @click="reset_all">{{ this.$ts('Reset') }}</el-button>
        <el-button type="primary" plain @click="delRequirement" color="#f89898">{{
          this.$ts('Delete')
        }}</el-button>
      </div>
    </div>
    <div class="form-content" v-show="!MyDivHieedn">
      <!-- 
			<el-button type="primary" plain @click="reset_all">{{ this.$ts('Reset') }}</el-button>
			<el-button type="primary" plain @click="delRow" color="#f89898">{{ this.$ts('Delete') }}</el-button> -->
      <VolForm ref="form2" :loadKey="true" :formFields="formFields2" :formRules="formRules2">
        <div>
          <!-- <el-button type="primary" @click="onModelOpen" plain>{{ this.$ts('弹框') }}</el-button> -->
        </div>
        <!-- <VolHeader :title="this.$ts(title)" text="text" icon="el-icon-s-grid"></VolHeader> -->
        <!-- <VolHeader :title="title" text="text" icon="el-icon-s-grid"></VolHeader> -->
      </VolForm>
      <div v-show="isVisible" style="display: flex; margin-top: 5px">
        <div style="width: 600px; margin-left: 10px">
          <label style="width: 200px; margin-left: 5px; font-size: 16px">
            <span style="color: red">* </span>
            <span> {{ this.$ts('Warning Margin') }}</span>
          </label>
          <div style="margin-top: 5px">
            <el-input v-model="WarningPeriod" @input="addSymbols1" required></el-input>
          </div>
        </div>
        <div style="width: 600px; margin-left: 10px">
          <label style="width: 200px; margin-left: 5px; font-size: 16px">
            <span style="color: red">* </span>
            <span> {{ this.$ts('Disable Tolerance') }}</span></label
          >
          <div style="margin-top: 5px">
            <el-input v-model="TolerancePeriod" @input="addSymbols2" required></el-input>
          </div>
        </div>
      </div>
      <div v-show="emailListIsVisible">
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <div style="margin-left: 12px; margin-top: 10px">
            {{ this.$ts('采集列表') }}
          </div>
        </div>
        <div style="margin-left: 12px; margin-top: 10px">
          <el-button
            type="primary"
            plain
            @click="addRow('table')"
            class="el-icon-plus"
            round
          ></el-button>
          <el-button
            type="primary"
            plain
            color="red"
            @click="delete_select_rows('table')"
            class="el-icon-delete"
            round
          ></el-button>
        </div>
        <div style="display: flex; align-items: stretch; margin-bottom: 10px">
          <div style="margin-left: 12px; width: 100%">
            <vol-table
              ref="table"
              index="true"
              :columns="columns"
              :tableData="tableData"
              :pagination-hide="true"
              :max-height="300"
              :load-key="true"
              :column-index="true"
            ></vol-table>
          </div>
        </div>
        <!-- The Pending Reminder Mail -->
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <div style="margin-left: 12px; margin-top: 10px">
            {{ this.$ts('The Pending Reminder Mail') }}
          </div>
          <div style="margin-left: 12px; margin-top: 10px">
            <el-button
              type="primary"
              plain
              @click="addRow('table3')"
              class="el-icon-plus"
              round
            ></el-button>
            <el-button
              type="primary"
              plain
              color="red"
              @click="delete_select_rows('table3')"
              class="el-icon-delete"
              round
            ></el-button>
          </div>
        </div>

        <div style="display: flex; align-items: stretch; margin-bottom: 10px">
          <div style="margin-left: 12px; width: 100%">
            <vol-table
              ref="table3"
              index
              :tableData="tableData3"
              :columns="columns3"
              :max-height="300"
              :pagination-hide="true"
              :load-key="true"
              :column-index="true"
            ></vol-table>
          </div>
        </div>
        <VolForm ref="form3" :loadKey="true" :formFields="formFields3" :formRules="formRules3">
        </VolForm>

        <!-- The Expiry Reminder Mail -->
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <div style="margin-left: 12px; margin-top: 10px">
            {{ this.$ts('The Expiry Reminder Mail') }}
          </div>
          <div style="margin-left: 12px; margin-top: 10px">
            <el-button
              type="primary"
              plain
              @click="addRow('table4')"
              class="el-icon-plus"
              round
            ></el-button>
            <el-button
              type="primary"
              plain
              color="red"
              @click="delete_select_rows('table4')"
              class="el-icon-delete"
              round
            ></el-button>
          </div>
        </div>
        <div style="display: flex; align-items: stretch; margin-bottom: 10px">
          <div style="margin-left: 12px; width: 100%">
            <vol-table
              ref="table4"
              index
              :tableData="tableData4"
              :columns="columns4"
              :max-height="300"
              :pagination-hide="true"
              :load-key="true"
              :column-index="true"
            ></vol-table>
          </div>
        </div>
        <VolForm ref="form4" :loadKey="true" :formFields="formFields4" :formRules="formRules4">
        </VolForm>

        <!-- The Disable Reminder Mail -->
        <div style="display: flex; align-items: center; margin-bottom: 10px">
          <div style="margin-left: 12px; margin-top: 10px">
            {{ this.$ts('The Disable Reminder Mail') }}
          </div>
          <div style="margin-left: 12px; margin-top: 10px">
            <el-button
              type="primary"
              plain
              @click="addRow('table5')"
              class="el-icon-plus"
              round
            ></el-button>
            <el-button
              type="primary"
              plain
              color="red"
              @click="delete_select_rows('table5')"
              class="el-icon-delete"
              round
            ></el-button>
          </div>
        </div>
        <div style="display: flex; align-items: stretch; margin-bottom: 10px">
          <div style="margin-left: 12px; width: 100%">
            <vol-table
              ref="table5"
              index
              :tableData="tableData5"
              :columns="columns5"
              :max-height="300"
              :pagination-hide="true"
              :load-key="true"
              :column-index="true"
            ></vol-table>
          </div>
        </div>
        <VolForm ref="form5" :loadKey="true" :formFields="formFields5" :formRules="formRules5">
        </VolForm>
      </div>
    </div>
  </div>
</template>
<script lang="jsx">

import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolForm from '@/components/basic/VolForm.vue';
import VolTable from "@/components/basic/VolTable.vue";
export default {
	components: {
		ModelInfo,
		VolHeader,
		VolForm,
		'vol-table': VolTable,
	},
	data() {
		return {
			emailListIsVisible: true,
			isVisible: false,
			MyDivHieedn: true,
			columns:[
				{
					field:"ChecklistId",
					title:this.$ts('采集ID'),
					type: 'string', 
					width: 100, 
					align: 'center', 
					readonly: false, 
					edit: { type: "text" },
					bind: { data: [] },
				},
				{
					field:"Instruction",
					title:this.$ts('采集名称'),
					type: 'string', 
					width: 100, 
					align: 'center', 
					readonly: false, 
					edit: { type: "text" },
					bind: { data: [] },
				},
				{
					field:"Description",
					title:this.$ts('Description'),
					type: 'string', 
					width: 100, 
					align: 'center', 
					readonly: false, 
					edit: { type: "text" },
					bind: { data: [] },
				},
				{
					field:"Notes",
					title:this.$ts('备注'),
					type: 'string', 
					width: 100, 
					align: 'center', 
					readonly: false, 
					edit: { type: "text" },
					bind: { data: [] },
				},
				{
					field:"SingleOnly",
					title:this.$ts('SingleOnly'),
					type: 'string', 
					width: 100, 
					align: 'center', 
					readonly: false, 
					edit: { type: "radio" },
					bind: { data: [] },
					hidden: true,
				},
				{
					field:"EmployeeGroupName",
					title:this.$ts('员工组'),
					type: 'string', 
					width: 100, 
					align: 'center', 
					readonly: false, 
					edit: { type: "select" },
					bind: {key:"EmployeeGroup", data: [] },
					// hidden: true,
				},
				{
					field:"DataCollectionDefName",
					title:this.$ts('采集项'),
					bind: {
					    data:[],
					},
					type: 'string', 
					width: 100, 
					align: 'center', 
					readonly: false, 
					edit: {key:"DataCollectionDef", type: "select" },
				}
			],
			tableData: [],
			deleteData: [],
			columns3: [
				{
					field: 'EmailNotificationName', title: this.$ts('Mail'),
					bind: {
						key: 'emailNotification',
						data: [],
					},
					type: 'string', 
					width: 200, 
					align: 'center', 
					readonly: false, 
					edit: { type: "select" },
					colSize: 3,
				},
			],
			columns4: [
				{
					field: 'EmailNotificationName', title: this.$ts('Mail'),
					bind: {
						key: 'emailNotification',
						data: [],
					},
					type: 'string', width: 200, align: 'center', readonly: false, edit: { type: "select" },
					colSize: 3,
				},
			],
			columns5: [
				{
					field: 'EmailNotificationName', title: this.$ts('Mail'),
					bind: {
						key: 'emailNotification',
						data: [],
					},
					type: 'string', width: 200, align: 'center', readonly: false, edit: { type: "select" },
					colSize: 3,
				},
			],


			tableData3: [],
			tableData4: [],
			tableData5: [],
			value: null,
			WarningPeriod: '000:00:00:00',
			TolerancePeriod: '000:00:00:00',
			formFields1: {
				RequirementType: null,
				RequirementName: null,
			},
			formRules1: [
				[
					{
						dataKey: null,
						data: [
							{ key: "重复周期", value: "重复周期" },
							{ key: "使用寿命", value: "使用寿命" },
							{ key: "指定日期", value: "指定日期" },],
						title: this.$ts('Required Type'),
						placeholder: this.$ts('Required Type'),
						filter: true,
						required: true,
						field: "RequirementType",
						type: "select",
						colSize: 6,

						onChange: (val) => {
							// 清空栏位
							this.formFields1.RequirementName = null;
							this.reset_all();
							this.hidden_all();

							this.formRules2[0][1].readonly = true;
							let postdata = {
								"requirementType": this.formFields1.RequirementType,
							};

							this.MaintenanceReqSelect();

							if (this.formFields1.RequirementName) {
								this.ShowAllForms();
							}
						}
					},
				], [
					{
						dataKey: null,
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Requirement Name"),
						filter: true,
						field: "RequirementName",
						placeholder: this.$ts('Requirement Name'),
						type: "select",
						colSize: 6,
						onChange: (val) => {
							if (val == null || val == '') {
								return;
							}
							if (this.formFields1.RequirementType == null) {
								this.$message.error(this.$ts('Required Type must be selected first!'));
								this.formFields1.RequirementName = null;
								return;
							}
							this.ShowAllForms();
							this.reset_all();
							this.formRules2[0][1].readonly = true;
							this.queryForm();

						}
					},
				],
			],
			formFields2: {
				NewRequirementName: null,
				RequirementRevision: null,
				RequirementDescription: null,
				MaintenanceReason: null,
				RecurringDatePattern: null,
				Qty: null,
				WarningQty: null,
				ToleranceQty: null,
				ScheduleDate: null,
				DayOfWeek: null,
				DayOfMonth: null,
				MonthOfYear: null,
				Frequency: null,
				SeedDate: null,
				Occurrences: null,
				EndDate: null,
				// WarningPeriod: null,
				// TolerancePeriod: null,
				// PendingEmailTargetList: null,
				// EmailNotificationName: null,
				// PendingEmailText: null,
				// DueEmailTargetList: null,
				// DueEmailText: null,
				// PastDueEmailTargetList: null,
				// PastDueEmailText: null,

			},
			formRules2: [
				// list[0]
				[
					{
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Requirement Name"),
						filter: true,
						field: "NewRequirementName",
						placeholder: this.$ts('Requirement Name'),
						type: "text",
						required: true,
						colSize: 6,
					},
					{
						title: this.$ts("Requirement Description"),
						filter: true,
						field: "RequirementDescription",
						placeholder: this.$ts('Requirement Description'),
						type: "text",
						// required: true,
						colSize: 6,
						readonly: true,
					},
				],
				// list[1]
				[
					{
						data: [{ key: 1, value: 1 }], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Version"),
						placeholder: this.$ts("Version"),
						filter: true,
						field: "RequirementRevision",
						type: 'select',
						required: true,
						colSize: 6,
					},
					{
						dataKey: "MaintenanceReason", //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						data: [],
						title: this.$ts("Maintenance Reason"),
						placeholder: this.$ts("Maintenance Reason"),
						filter: true,
						field: "MaintenanceReason",
						type: 'select',
						required: true,
						colSize: 6,
					},
				],

				// list[2]
				[
					{
						//dataKey: "RecurringDatePattern",
						// data: [{ key: 1, value: "Dayily" },{ key: 2, value: "Weekily" },{ key: 3, value: "Monthly" },{ key: 4, value: "Yearly"}], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						data: [{ key: 1, value: "天" },{ key: 2, value: "周" },{ key: 3, value: "月" },{ key: 4, value: "年"}], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Repeating Method"),
						placeholder: this.$ts("Repeating Method"),
						filter: true,
						field: "RecurringDatePattern",
						hidden: true,
						type: 'select',
						required: true,
						colSize: 6,
						onChange: (val) => {
							this.switchRecurringDatePattern(this.formFields2.RecurringDatePattern);
						}
					}, {
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Frequency"),
						filter: true,
						field: "Frequency",
						hidden: true,
						type: "number",
						min: 1,
						required: true,
						colSize: 6,
					},
				],
				// list[3]
				[

					// element[0]
					{
						dataKey: "DayOfWeek",
						data: [{key:1,value:"Sunday"},{key:2,value:"Monday"},{key:3,value:"Tuesday"},{key:4,value:"Wednesday"},{key:5,value:"Thursday"},{key:6,value:"Friday"},{key:7,value:"Saturday"}], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Day of Week"),
						filter: true,
						field: "DayOfWeek",
						type: "select",
						hidden: true,
						required: true,
						colSize: 6,
					},

					// element[1]
					{
						// dataKey: "DayOfMonth",
						// data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Date"),
						placeholder: this.$ts("Date"),
						filter: true,
						field: "DayOfMonth",
						type: "text",
						min: 0,//限制数字大小
						hidden: true,
						required: true,
						colSize: 12,
					},

					// element[2]
					{
						// dataKey: "MonthOfYear",
						// data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Months"),
						placeholder: this.$ts("Months"),
						filter: true,
						field: "MonthOfYear",
						type: "text",
						hidden: true,
						required: true,
						colSize: 12,
					},],

				// list[4]
				[

					{
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Start Date"),
						filter: true,
						field: "SeedDate",
						type: "datetime",
						hidden: true,
					},
					{
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Repeating Times"),
						filter: true,
						field: "Occurrences",
						hidden: true,
						type: "number",
						min: 0,//限制数字大小
					}, {
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("End Date"),
						filter: true,
						field: "EndDate",
						type: "datetime",
						hidden: true,
					},],

				// list[5]
				[
					{
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Specified Date"),
						filter: true,
						field: "ScheduleDate",
						type: "datetime",
						hidden: true,
					},
				],

				// list[6]
				[
					{
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Quantity"),
						filter: true,
						field: "Qty",
						type: "number",
						min: 1,//限制数字大小
						hidden: true,
						required: true,
					},],
				// list[7]
				[
					{
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Warning Margin"),
						filter: true,
						field: "WarningQty",
						type: "number",
						min: 0,//限制数字大小
						hidden: true,
					},
					{
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("Disable Tolerance"),
						filter: true,
						field: "ToleranceQty",
						type: "number",
						min: 0,//限制数字大小
						hidden: true,
					},
				],
				
			],
			formFields3:
			{
				PendingEmailText: null,

			},

			formRules3: [[
				{
					title: this.$ts("The Pending Reminder Mail Text"),
					field: "PendingEmailText",
					placeholder: this.$ts("The Pending Reminder Mail Text"),
					type: "textarea",
					colSize: 12, //设置宽度100%

					maxRows: 10,
				},]
			],
			formFields4:
			{
				DueEmailText: null,

			},

			formRules4: [[
				{
					title: this.$ts("The Expiry Reminder Mail Text"),
					field: "DueEmailText",
					placeholder: this.$ts("The Expiry Reminder Mail Text"),
					type: "textarea",
					colSize: 12, //设置宽度100%
				},]
			],
			formFields5:
			{
				PastDueEmailText: null,

			},

			formRules5: [[
				{
					title: this.$ts("The Disable Reminder Mail Text"),
					field: "PastDueEmailText",
					placeholder: this.$ts("The Disable Reminder Mail Text"),
					type: "textarea",
					colSize: 12, //设置宽度100%
				},]
			],

			ApiUrl: {
        QueryResourceInfo: '/api/Query/QueryResourceInfoDetails', //查询设备台账信息
        SearchPosition:'api/Query/SearchPosition', //查询设备位置
        SearchLocation:'api/Query/SearchLocation', //查询设备区域
        GetResourceObject: "/api/Query/GetResourceByType",//获取NameObject
        GetRevisionObject: "/api/query/GetRevObjByType",
        AddPart: 'api/CDO/PartCreatTxn', //创建Part
        EidtPart: 'api/CDO/PartEidtTxn' //修改Part
      },
		}
	},
	mounted() {
	this.columns[5].bind.data= this.selectResource("EmployeeGroup","EmployeeGroup","A_");
	this.columns[6].bind.data= this.selectProdct("DataCollectionDef","UserDataCollectionDef",null);
	},
	methods: {
		// //隐藏前两个表单
		// Mhidden_all() {
		//     for (let i = 0; i < 2; i++) {
		// 		for (let j = 0; j < this.formRules2[i].length; j++) {
		// 			this.formRules2[i][j].hidden = true;
		// 		}
		// 	}
		// 	this.formRules3[0][0].hidden = true;
		// 	this.formRules4[0][0].hidden = true;
		// 	this.formRules5[0][0].hidden = true;
		// },
		formattedDateTime(input) {
			// 创建一个 Date 对象
			const date = new Date(input);

			// 提取年月日和时分秒
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');

			// 格式化为 yyyy-mm-dd hh:mm:ss
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
		add() {
			this.reset_all();
			this.ShowAllForms();
			this.formRules2[0][1].readonly = false;
			this.formFields1.RequirementName = null;
			this.WarningPeriod = '000:00:00:00';
			this.TolerancePeriod = '000:00:00:00';
			this.$message.warning(this.$ts('Please fill in all the data and then click the Save button.'));
		},
		ShowAllForms() {
			let val = this.formFields1.RequirementType;
			// let list = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
			// for (let i = 2; i < this.formRules2.length; i++) {
			// 	for (let j = 0; j < this.formRules2[i].length; j++) {
			// 		this.formRules2[i][j].hidden = true;
			// 	}
			// }
			for (let i = 0; i < 2; i++) {
				for (let j = 0; j < this.formRules2[i].length; j++) {
					this.formRules2[i][j].hidden = false;
				}
			}
			// this.formRules3[0][0].hidden = false;
			// this.formRules4[0][0].hidden = false;
			// this.formRules5[0][0].hidden = false;
			

			if (val == '重复周期') {
				let list = [2, 4];
				list.forEach(element => {
					for (let i = 0; i < this.formRules2[element].length; i++) {
						this.formRules2[element][i].hidden = false;
					}
				});

			} else if (val == '使用寿命') {
				let list = [6, 7];
				list.forEach(element => {
					for (let i = 0; i < this.formRules2[element].length; i++) {
						this.formRules2[element][i].hidden = false;
					}
				});
			} else if (val == '指定日期') {
				let list = [5];
				list.forEach(element => {
					for (let i = 0; i < this.formRules2[element].length; i++) {
						this.formRules2[element][i].hidden = false;
					}
				});
			}
			this.emailListIsVisible = true;
			this.toggleVisibility();
			this.formFields2.RequestName = this.formFields1.RequestName;
			this.MyDivHieedn = false; //显示div
		},
		MaintenanceReqSelect() {
			// console.log('MaintenanceReqSelect');
			let postdata = {
				"requirementType": this.formFields1.RequirementType,
			};
			this.http.post("api/Query/MaintenanceReqSelect", JSON.stringify(postdata)).then(res => {
				// console.log(res.rows, 'res.rows');
				let temp = [];

				res.rows.forEach(element => {
					temp.push({ key: element.key, value: element.value });
				});
				// console.log(temp, 'temp');
				this.formRules1[1][0].data = temp;
				// console.log(this.formRules1[1]c.data, '[1]');
				// console.log(this.formRules1[0][1].data, 'formRules1[0][1].data');

			}).catch(
				error => {
					this.$message.error(this.$ts('Execution failed, please contact MES team.'))
					this.$message.error(error.message);
				}
			).finally(() => {
				// this.formFields.UserDataCollectionName = this.formFields.NewUserDataCollectionName;
			});
		}
		,
		switchRecurringDatePattern(val) {
			// console.log('switchRecurringDatePattern');
			if (this.formFields1.RequirementType != "重复周期") {
				return;
			}
			for (let i = 0; i < this.formRules2[3].length; i++) {
				this.formRules2[3][i].hidden = true;
			}

			let flag = val;
			console.log("switch pattern");

			console.log(flag, 'flag');
			// 1: Daily; 2: Weekly; 3: Monthly; 4: Yearly; 5: Hourly;
			switch (flag) {
				case 2:
					this.formRules2[3][0].hidden = false;
					console.log("switch Weekly");
					break;
				case 3:
					this.formRules2[3][1].hidden = false;
					console.log("switch Monthly");
					break;
				case 4:
					this.formRules2[3][1].hidden = false;
					this.formRules2[3][2].hidden = false;
					console.log("switch Yearly");
					break;
				default:
					break;
			}
		},
		toggleVisibility() {
			let type = this.formFields1.RequirementType;
			if (type == "指定日期" || type == "重复周期") {
				this.isVisible = true;
				console.log('isVisible=true');
			} else {
				this.isVisible = false;

				console.log('isVisible=false');
			}
		},
		queryForm() {
			let postdata = {
				"RequirementName": this.formFields1.RequirementName,
				"RequirementRevision": 1,
			};
			// console.log(val, ',toolName');
			this.http.post("api/Query/SearchRequirement", JSON.stringify(postdata)).then(res => {
				console.log(res, 'query');
				// console.log(res.rows[0].NewRequirementName, 'test');
				if (res.status === '1') {
					this.formFields2.NewRequirementName = res.rows[0].NewRequirementName;
					this.formFields2.RequirementRevision = res.rows[0].RequirementRevision;
					this.formFields2.RequirementDescription = res.rows[0].RequirementDescription;
					this.formFields2.MaintenanceReason = res.rows[0].MaintenanceReason;
					this.formFields2.RecurringDatePattern = res.rows[0].RecurringDatePattern;
					this.formFields2.Qty = res.rows[0].Qty;
					this.formFields2.WarningQty = res.rows[0].WarningQty;
					this.formFields2.ToleranceQty = res.rows[0].ToleranceQty;


					let tempScheduleDate = res.rows[0].ScheduleDate ?? null;
					if (!tempScheduleDate) {
						this.formFields2.ScheduleDate = null;
					} else {

						this.formFields2.ScheduleDate = this.formattedDateTime(tempScheduleDate);
					}
					this.formFields2.DayOfWeek = res.rows[0].DayOfWeek;
					this.formFields2.DayOfMonth = res.rows[0].DayOfMonth;
					this.formFields2.MonthOfYear = res.rows[0].MonthOfYear;
					this.formFields2.Frequency = res.rows[0].Frequency;

					let tempSeedDate = res.rows[0].SeedDate ?? null;
					if (!tempSeedDate) {
						this.formFields2.SeedDate = null;
					} else {

						this.formFields2.SeedDate = this.formattedDateTime(tempSeedDate);
					}

					let tempEndDate = res.rows[0].EndDate ?? null;
					if (!tempEndDate) {
						this.formFields2.EndDate = null;
					} else {

						this.formFields2.EndDate = this.formattedDateTime(tempEndDate);
					}

					this.formFields2.Occurrences = res.rows[0].Occurrences;
					this.WarningPeriod = res.rows[0].WarningPeriod;
					this.TolerancePeriod = res.rows[0].TolerancePeriod;


					// res.rows[0].PendingEmailTargetList

					// this.tableData3.push({});
					this.formFields3.PendingEmailTargetList = this.splitPendingEmailTargetList(res.rows[0].PendingEmailTargetList);
					this.formFields3.PendingEmailText = res.rows[0].PendingEmailText;

					this.formFields4.DueEmailTargetList = this.splitDueEmailTargetList(res.rows[0].DueEmailTargetList);
					this.formFields4.DueEmailText = res.rows[0].DueEmailText;


					this.formFields5.PastDueEmailTargetList = this.splitPastDueEmailTargetList(res.rows[0].PastDueEmailTargetList);
					this.formFields5.PastDueEmailText = res.rows[0].PastDueEmailText;


					this.switchRecurringDatePattern(res.rows[0].RecurringDatePattern);
					this.getChecklist(postdata);
				} else {

					this.$message.error(this.$ts('Execution failed, please contact MES team.'))
					this.$message.error(res.message);
				}

				// res.rows[0].UserDataPoints.forEach(element => {

				// 	// console.log(element, 'element');
				// 	let queryData =
				// 	{
				// 		"DataPointName": element.DataPointName,
				// 		"DataType": element.DataType,
				// 		"RowPosition": element.RowPosition,
				// 		"ColumnPosition": element.ColumnPosition,
				// 		"LowerLimit": element.LowerLimit,
				// 		"UpperLimit": element.UpperLimit,
				// 	};
				// 	this.tableData.push(queryData);
				// });
			}).catch(
				error => {

					this.$message.error(this.$ts('Execution failed, please contact MES team.'))
					this.$message.error(error.message);
				}
			);


		},
//获取CheckList数据
		getChecklist(params) {
		    this.http.post('api/Query/GetCheckListData', params).then(res=>{
				if (res.Result==1) {
				    this.tableData = res.Data;
				}
				else{
					this.$message.error(res.Message)
				}
			});
		},
		addSymbols1() {
			let formattedValue = this.WarningPeriod.replace(/\D/g, '');
			// if (formattedValue.length > 2) {
			//   formattedValue = formattedValue.replace(/(\d{3})(\d{2})(\d{2})(\d{2})/, '$1:$2:$3:$4');
			// }
			let len = formattedValue.length;
			if (len <= 9) {
				if (len <= 3) {
					this.WarningPeriod = formattedValue;
				} else if (len <= 5) {
					this.WarningPeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3);
				} else if (len <= 7) {
					this.WarningPeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3, 5) + ':' + formattedValue.substring(5);
				} else {
					this.WarningPeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3, 5) + ':' + formattedValue.substring(5, 7) + ':' + formattedValue.substring(7);
				}
			} else {
				this.WarningPeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3, 5) + ':' + formattedValue.substring(5, 7) + ':' + formattedValue.substring(7, 9);
			}

		}, addSymbols2() {
			let formattedValue = this.TolerancePeriod.replace(/\D/g, '');
			// if (formattedValue.length > 2) {
			//   formattedValue = formattedValue.replace(/(\d{3})(\d{2})(\d{2})(\d{2})/, '$1:$2:$3:$4');
			// }
			let len = formattedValue.length;
			if (len <= 9) {
				if (len <= 3) {
					this.TolerancePeriod = formattedValue;
				} else if (len <= 5) {
					this.TolerancePeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3);
				} else if (len <= 7) {
					this.TolerancePeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3, 5) + ':' + formattedValue.substring(5);
				} else {
					this.TolerancePeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3, 5) + ':' + formattedValue.substring(5, 7) + ':' + formattedValue.substring(7);
				}
			} else {
				this.TolerancePeriod = formattedValue.substring(0, 3) + ':' + formattedValue.substring(3, 5) + ':' + formattedValue.substring(5, 7) + ':' + formattedValue.substring(7, 9);
			}

		},
		splitPendingEmailTargetList(list) {
			list.forEach(email => {
				console.log(email.EmailNotificationName);
				this.tableData3.push({ "EmailNotificationName": email.EmailNotificationName });
			});
			// let emailNames = list.map(item => item.EmailNotificationName).join(', ');
			// return emailNames;
		},

		splitDueEmailTargetList(list) {
			list.forEach(email => {
				console.log(email.EmailNotificationName);
				this.tableData4.push({ "EmailNotificationName": email.EmailNotificationName });
			});
		},

		splitPastDueEmailTargetList(list) {
			list.forEach(email => {
				console.log(email.EmailNotificationName);
				this.tableData5.push({ "EmailNotificationName": email.EmailNotificationName });
			});
		},

		onModelOpen() {
			this.$refs.form.openModel();
		},
		getForm() {
			console.log(this.$refs.form.getForm());
			alert(JSON.stringify(this.formFields))
		},
		reset_formFields2() {
			this.$refs.form.reset(this.formFields2);
		},
		//隐藏所有表单
		hidden_all() {
			for (let i = 2; i < this.formRules2.length; i++) {
				for (let j = 0; j < this.formRules2[i].length; j++) {
					this.formRules2[i][j].hidden = true;
				}
			}

			this.isVisible = false;
			this.emailListIsVisible = true;
		},
		reset_all() {
			// console.log('Reset button clicked');
			this.TolerancePeriod = null;
			this.WarningPeriod = null;
			this.$refs.form2.reset();
			this.$refs.form3.reset();
			this.$refs.form4.reset();
			this.$refs.form5.reset();
			this.tableData3 = [];
			this.tableData4 = [];
			this.tableData5 = [];
			// this.Mhidden_all();
			// this.MyDivHieedn = true; // 隐藏表格
		},
		delRequirement() {
			let postdata = {

				"RequirementType": this.formFields1.RequirementType,
				"RequirementName": this.formFields1.RequirementName,
				"RequirementRevision": this.formFields2.RequirementRevision,
			}
			console.log(JSON.stringify(postdata));
			this.http.post("api/CDO/DeleteRequirement", JSON.stringify(postdata)).then(res => {
				console.log(JSON.stringify(res));
				if (res.status === '1') {
					this.$message.success(this.$ts('Execution Success!'))
					this.$message.success(res.message);
					this.reset_all();
					this.formFields1.RequirementName = null;
				} else {
					this.$message.error(this.$ts('Execution failed, please contact MES team.'))
					this.$message.error(res.message);
				}
			}).catch(
				error => {
					this.$message.error(this.$ts('Execution failed, please contact MES team.'))
					this.$message.error(error.message);
				}
			).finally(() => {
				this.MaintenanceReqSelect();
			});
		},
		saveRequirement() {

			if (this.formFields1.RequirementType == null) {
				this.$message.error(this.$ts('Required Type must be selected first!'));
				return;
			} else if (this.isVisible == true && (this.WarningPeriod == null || this.WarningPeriod.trim() == '' || this.TolerancePeriod == null || this.TolerancePeriod.trim() == '')) {
				this.$message.error(this.$ts('Both Warning Margin and Disable Tolerance have values!'));
				return;
			} else if (this.formFields2.NewRequirementName == null) {
				this.$message.error(this.$ts('Please fill in Requirement Name!'));
				return;
			} else if (this.formFields2.RequirementRevision == null) {
				this.$message.error(this.$ts('Please fill in Version!'));
				return;
			} else if (this.formFields2.MaintenanceReason == null) {
				this.$message.error(this.$ts('Please fill in Maintenance Reason!'));
				return;
			};

			let postdata;
			// 修改、更新
			if (this.formFields1.RequirementName) {
				postdata = {
					"RequirementType": this.formFields1.RequirementType,
					"RequirementName": this.formFields1.RequirementName,
					"NewRequirementName": this.formFields2.NewRequirementName,
					"RequirementRevision": this.formFields2.RequirementRevision,
					"RequirementDescription": this.formFields2.RequirementDescription,
					"MaintenanceReason": this.formFields2.MaintenanceReason,
					"PendingEmailTargetList": [],
					"PendingEmailText": this.formFields3.PendingEmailText,
					"DueEmailTargetList": [],
					"DueEmailText": this.formFields4.DueEmailText,
					"PastDueEmailTargetList": [],
					"PastDueEmailText": this.formFields5.PastDueEmailText,
					"CheckListList":[]
				};
			} else {
				// 新增
				postdata = {
					"RequirementType": this.formFields1.RequirementType,
					"RequirementName": this.formFields2.NewRequirementName,
					"NewRequirementName": this.formFields2.NewRequirementName,
					"RequirementRevision": this.formFields2.RequirementRevision,
					"RequirementDescription": this.formFields2.RequirementDescription,
					"MaintenanceReason": this.formFields2.MaintenanceReason,
					"PendingEmailTargetList": [],
					"PendingEmailText": this.formFields3.PendingEmailText,
					"DueEmailTargetList": [],
					"DueEmailText": this.formFields4.DueEmailText,
					"PastDueEmailTargetList": [],
					"PastDueEmailText": this.formFields5.PastDueEmailText,
					"CheckListList":[]
				};
			}
			let CheckListList = [];
			for (let i = 0; i < this.tableData.length; i++) {
				console.log(this.tableData[i].DataCollectionDefName==null||this.tableData[i].DataCollectionDefName=='');
				let CheckList = {
					"Action": this.tableData[i].Action,
					"ChecklistId": this.tableData[i].ChecklistId,
					"Description": this.tableData[i].Description,
					"Instruction":this.tableData[i].Instruction,
					"Notes":this.tableData[i].Notes,
					"SingleOnly":this.tableData[i].SingleOnly,
					"__index":this.tableData[i].ChecklistSequence==''||this.tableData[i].ChecklistSequence==null?this.tableData[i].ChecklistSequence:this.tableData[i].ChecklistSequence-1,
					"DataCollectionDef":null,
					"EmployeeGroup":null,
				}
				if(this.tableData[i].EmployeeGroup !=null&this.tableData[i].EmployeeGroup!=''){
					CheckList.EmployeeGroup ={
						Name:this.tableData[i].EmployeeGroupName
					}
				}
				if (this.tableData[i].DataCollectionDefName != null && this.tableData[i].DataCollectionDefName != '') {
				    CheckList.DataCollectionDef = {
						Name:this.tableData[i].DataCollectionDefName.split(':')[0],
						Version:this.tableData[i].DataCollectionDefName.split(':')[1],
					}
				}
				CheckListList.push(CheckList);
			}
			console.log( this.deleteData);
			for (let i = 0; i < this.deleteData.length; i++) {
				let CheckList = {
					"Action": this.deleteData[i].Action,
					"__index":this.deleteData[i].ChecklistSequence==''||this.deleteData[i].ChecklistSequence==null?this.deleteData[i].ChecklistSequence:this.deleteData[i].ChecklistSequence-1,
				}
				CheckListList.push(CheckList);
			}
			postdata.CheckListList = CheckListList;
			switch (this.formFields1.RequirementType) {
				case "重复周期":
					if (!this.formFields2.Frequency) {
						this.$message.error(this.$ts('Please fill in Frequency!'));
						return;
					} else if (!this.formFields2.RecurringDatePattern) {
						this.$message.error(this.$ts('Please fill in Repeating Cycle!'));
						return;
					}
					let RecurringDateReqMaint = {
						"RecurringDatePattern": this.formFields2.RecurringDatePattern,
						// "DayOfWeek": this.formFields2.DayOfWeek,
						// "DayOfMonth": this.formFields2.DayOfMonth,
						// "MonthOfYear": this.formFields2.MonthOfYear,
						"Frequency": this.formFields2.Frequency,
						"SeedDate": this.formFields2.SeedDate,
						"Occurrences": this.formFields2.Occurrences,
						"EndDate": this.formFields2.EndDate,
						"WarningPeriod": this.WarningPeriod,
						"TolerancePeriod": this.TolerancePeriod,
					}

					let pattern;
					// 判断重复方式
					switch (this.formFields2.RecurringDatePattern) {
						case 2:
							pattern = {
								"DayOfWeek": this.formFields2.DayOfWeek,
								"DayOfMonth": null,
								"MonthOfYear": null,
							}
							Object.assign(RecurringDateReqMaint, pattern);
							break;
						case 3:
							pattern = {
								"DayOfMonth": this.formFields2.DayOfMonth,
								"DayOfWeek": null,
								"MonthOfYear": null,
							}
							Object.assign(RecurringDateReqMaint, pattern);
							break;
						case 4:
							pattern = {
								"DayOfWeek": null,
								"DayOfMonth": this.formFields2.DayOfMonth,
								"MonthOfYear": this.formFields2.MonthOfYear,
							}
							Object.assign(RecurringDateReqMaint, pattern);
							break;
						default:
							pattern = {
								"DayOfMonth": null,
								"DayOfWeek": null,
								"MonthOfYear": null,
							}
							Object.assign(RecurringDateReqMaint, pattern);
							break;
					}

					Object.assign(postdata, RecurringDateReqMaint);
					break;
				case "使用寿命":
					let ThruputReqMaint = {
						"Qty": this.formFields2.Qty,
						"WarningQty": this.formFields2.WarningQty,
						"ToleranceQty": this.formFields2.ToleranceQty,
					}
					Object.assign(postdata, ThruputReqMaint);
					break;
				case "指定日期":
					let DateReqMaint = {
						"ScheduleDate": this.formFields2.ScheduleDate,
						"WarningPeriod": this.WarningPeriod,
						"TolerancePeriod": this.TolerancePeriod,
					}
					Object.assign(postdata, DateReqMaint);
					break;
				default:
					break;
			}


			console.log(this.tableData3);
			// this.tabledata3[0];
			postdata.PendingEmailTargetList.push(...this.tableData3);
			postdata.DueEmailTargetList.push(...this.tableData4);
			postdata.PastDueEmailTargetList.push(...this.tableData5);

			console.log(JSON.stringify(postdata));
			console.log(postdata);

			this.http.post("api/CDO/AddRequirement", JSON.stringify(postdata)).then(res => {
				console.log(JSON.stringify(res));
				if (res.status === '1') {
					this.$message.success(res.message);
					this.$message.success(this.$ts('Execution Success!'))
					this.reset_all();
					this.$refs.form1.reset(this.formFields1);
				} else {
					this.$message.error(this.$ts('Execution failed, please contact MES team.'))
					this.$message.error(res.message);
				}
			}).catch(
				error => {
					this.$message.error(this.$ts('Execution failed, please contact MES team.'))
					this.$message.error(error.message);
				}
			).finally(() => {
				this.MaintenanceReqSelect();
			});

		},
		setReadonlyStaus(status) {
			this.$refs.form.setReadonlyStaus(status);
		},

		editClick(row, column, index) {
			this.$refs.table.edit.rowIndex = index;
		},
		loadBefore(params, callBack) {//调用后台接口前处理
			//设置查询条件参数
			params.wheres.push({
				name: "OrderNo",
				value: this.OrderNo,
				displayType: "like"//模糊查询
			})

			//也可以给value设置值，后台自己解析
			// params.value=this.OrderNo

			//查询前方法也可以动态设置url参数
			//params.url='api/xxx/xx?参数1='+this.xx参数

			callBack(true)//false不会调用后台接口
		},
		//查询后方法
		loadAfter(rows, callBack, result) {
			//如果有合计：后台返回合计格式
			//返回的行数据
			//返回的总行数
			//合计
			// < !-- var data = new {rows=[],total=200,summary={ TotalPrice=100, TotalQty=200 }} -- >
			callBack(true)
		},
		copyClick() {
			if (this.formFields1.RequirementName == null) {
				this.$message.error(this.$ts('Requirement Name must have value!'));
				return;
			}
			this.formFields1.RequirementName = null;
			this.formFields2.NewRequirementName = null;

			this.$message.warning(this.$ts('Please rename the Requirement Name, then click the Save button.'));
		},
		addRow(tableRef) {
			// console.log(tableRef);
			if (tableRef === 'table3') {
				this.tableData3.push({});
			} else if (tableRef === 'table4') {
				this.tableData4.push({});
			} else if (tableRef === 'table5') {
				this.tableData5.push({});
			}else if (tableRef === 'table') {

			    this.tableData.push({
					Action: "add",
				});
			}
			 else {
				return;
			}
		},
		delete_select_rows(tableRef) {
			if (tableRef === 'table3') {
				this.$refs.table3.delRow();
			} else if (tableRef === 'table4') {
				this.$refs.table4.delRow();
			} else if (tableRef === 'table5') {
				this.$refs.table5.delRow();
			}
			else if (tableRef === 'table') {
				let row = this.$refs.table.getSelected();
				if (row.length == 0) {
					this.$message.error(this.$ts('Please select the data to be deleted!'));
					return;
				}
				
				else if(row.length>=1){
					debugger
					row.forEach(item => {
						if(item.__index!=null&&item.__index!=''){
				    		item.Action = "delete"
							this.deleteData.push(item);
						}
				}
				);
			    this.$refs.table.delRow();
				}
				
			}
		},
		selectResource(cdoName,typeName,perCdoStr){
      let param={
                // cdo:"ResourceFamily",
                cdo:cdoName,
                type:typeName,
				perCdo:perCdoStr
            }
            let dataArr=[]
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        
                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
               
            )
            return dataArr;
    },

	selectProdct(cdoName,typeName,perCdoStr){
      let param ={
        cdo:cdoName,
        type:typeName,
		perCdo:perCdoStr
      }
	  let dataArry=[]
      this.http.get(this.ApiUrl.GetRevisionObject,param).then(res=>{
        if(res.Result==1){
			res.Data.forEach(item => {
				dataArry.push({
                                key: item.Name+':'+item.Version,
                                label: item.Name+':'+item.Version,
                                value: item.Name+':'+item.Version
                            })
                        })
		  
        }
        else{
          this.$message.error(res.Message)
        }
      })

	  return dataArry;
    },
	}
};
</script>
<style lang="less" scoped>
.container {
  padding: 10px;
  background: #f3f7fc;

  .form-content {
    border-radius: 5px;
    padding: 10px 0;
    background: #fff;
  }

  .table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
      height: 15px;
      background: rgb(33, 150, 243);
      width: 5px;
      border-radius: 10px;
      position: relative;
      margin-right: 5px;
    }

    .table-item-text {
      font-weight: bolder;
    }

    .table-item-buttons {
      flex: 1;
      text-align: right;
    }

    .small-text {
      font-size: 12px;
      color: #2196f3;
      margin-left: 10px;
      position: relative;
      top: 2px;
    }
  }
}

.button-container {
  margin-left: 10px;
  margin-top: 10px;
}
</style>