<template>
  <div class="container">

    <div class="form-content">
      <VolHeader :title="this.$ts(title)" text="text" icon="el-icon-s-grid"></VolHeader>
      <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
        <div style="text-align: end; margin-top: 0px;width: 100%;">
          <el-button type="primary" plain @click="getForm">{{ this.$ts('Submit') }}</el-button>
          <el-button type="success" plain @click="reset">{{ this.$ts('Reset') }}</el-button>
        </div>
      </VolForm>
    </div>

  </div>

  <!-- <Opc_EquipRepairEdit ref="modalForm" @ok="modalFormOk"></Opc_EquipRepairEdit> -->

</template>
<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import { mapState } from 'vuex'
// import Opc_EquipRepairEdit from './Opc_EquipRepairEdit.vue'
import {
  computed,
} from 'vue';
export default {
  components: {
    ModelInfo,
    VolHeader,
    VolForm,
    'vol-table': VolTable,
    // Opc_EquipRepairEdit,
  },
  data() {
    return {
      title: this.$ts('Repair Apply'),
      tableTitleOne: this.$ts('Repair Apply'),
      formFields: {
        ResourceName: null,
        Description: null,
        Biz_RepairingAttach: null,
        Biz_EquipIMRSup: null,
        // 自定义字段
        Equipment_name: null,
        Equipment_description: null,
        Biz_PhysicalLocation: null,
        Biz_PhysicalPosition: null,
      },
      formRules: [
        [
          {
            dataKey: "RESOURCE",
            data: [],
            title: this.$ts('Equipment code'),
            placeholder: this.$ts('Equipment code'),
            required: true,
            field: "ResourceName",
            type: "select",
            colSize: 6,
            onChange: ((val, opt) => this.handleChange(val, opt))
          },
          {
            title: this.$ts("Equipment name"),
            placeholder: this.$ts("Equipment name"),
            required: false,
            readonly: true,
            hidden:true,
            field: "Equipment_name",
            type: "text",
            colSize: 6,
          },
        ],
        [
          {
            title: this.$ts("Malfunction Description"),
            placeholder: this.$ts("Malfunction Description"),
            required: true,
            field: "Description",
            type: "textarea",
            min: 3,
            max: 5,
            colSize: 6,
          },
          {
            title: this.$ts("Equipment Description"),
            placeholder: this.$ts("Equipment Description"),
            required: false,
            readonly: true,
            field: "Equipment_description",
            type: "textarea",
            colSize: 6,
          },
        ],
        [
          {
            dataKey: "JobOrderEmployeeGroup",
            data: [],
            title: this.$ts('Repair Owner'),
            placeholder: this.$ts('Repair Owner'),
            required: true,
            field: "Biz_EquipIMRSup",
            type: "select",
            colSize: 6,
          },
          {
            title: this.$ts("District"),
            placeholder: this.$ts("District"),
            required: false,
            readonly: true,
            field: "Biz_PhysicalLocation",
            type: "text",
            colSize: 6,
          },
        ],
        [
          // {
          //   title: this.$ts("Attachment"),
          //   required: false,
          //   field: "Biz_RepairingAttach",
          //   type: "img",
          //   multiple: true,
          //   // maxFile: 5,
          //   // maxSize: 7,
          //   // url: "api/Demo_Order/Upload",
          //   url: "/api/CDO/UploadFile",
          //   colSize: 5,
          // },
          {
            title: this.$ts("Location"),
            placeholder: this.$ts("Location"),
            required: false,
            readonly: true,
            field: "Biz_PhysicalPosition",
            type: "text",
            colSize: 6,
          },
        ],
      ],
      url: {
        add: 'api/CDO/AddJobOrder',
        search: 'api/Query/SearchJobOrderForResource',
      },
    }
  },
  computed: {
    ...mapState({ userInfo: (state) => state.userInfo })
  },
  mounted() {

  },
  methods: {

    // 下拉框触发
    handleChange(val, opt) {
      if (!val) {
        this.formFields.Equipment_name = null
        this.formFields.Equipment_description = null
        this.formFields.Biz_PhysicalLocation = null
        this.formFields.Biz_PhysicalPosition = null
      } else {
        this.hadnleSearchEquipInfo(val)
      }
    },

    // 查询设备信息
    hadnleSearchEquipInfo(val) {
      let params = { ResourceName: val };
      this.http.post(this.url.search, params).then(res => {
        if (res.status == 1) {
          let data = res.rows[0]
          if (res.rows !== null && res.rows.length > 0) {
            this.formFields.Equipment_name = data.ResourceName
            this.formFields.Equipment_description = data.Description
            this.formFields.Biz_PhysicalLocation = data.Biz_PhysicalLocation
            this.formFields.Biz_PhysicalPosition = data.Biz_PhysicalPosition
          }
          // this.$message.success(this.$ts('Execution Success!'))
        } else {
          this.$message.error(this.$ts('Execution failed, please contact MES team.'))
          this.$message.warning(res.message)
        }
      })
    },

    // 获取表单数据
    getForm() {
      this.handleSendRequest()
    },

    handleSendRequest() {
      this.$refs.form.validate((err) => {
        let fileData = []
        if (this.formFields.Biz_RepairingAttach !== null && this.formFields.Biz_RepairingAttach.length > 0) {
          fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
          this.formFields.Biz_RepairingAttach = fileData.join()
          //fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
        } else {
          //this.formFields.Biz_RepairingAttach = fileData.join()
          this.formFields.Biz_RepairingAttach = null;
        }
        let params = {};
        // if(this.formFields.Biz_RepairingAttach !== null)
        // {
        //     params = {
        //     ResourceName: this.formFields.ResourceName,
        //     Description: this.formFields.Description,
        //     Biz_EquipIMRSup: this.formFields.Biz_EquipIMRSup,
        //     Biz_RepairingAttach: this.formFields.Biz_RepairingAttach,
        //     Biz_RepairingApplicant:this.userInfo.userName,
        //   }
        // }
        // else
        // {

        // }
        params = {
            ResourceName: this.formFields.ResourceName,
            Description: this.formFields.Description,
            Biz_EquipIMRSup: this.formFields.Biz_EquipIMRSup,
            Biz_RepairingAttach: this.formFields.Biz_RepairingAttach,
            Biz_RepairingApplicant:this.userInfo.userName
          }
        this.http.post(this.url.add, params, true).then((res) => {
          if (res.status == 1) {
            this.reset()
            this.$message.success(this.$ts('Execution Success!'))
            this.$message.success(res.message)
          } else {
            this.$message.error(this.$ts('Execution failed, please contact MES team.'))
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.error(err.message)
        })
      })
    },

    reset() {
      this.$refs.form.reset(this.formFields);
    },


  }
};
</script>
<style lang="less" scoped>
.container {
  padding: 10px;
  background: #F3F7FC;

  .form-content {
    border-radius: 5px;
    padding: 10px 0;
    background: #fff;
  }

  .table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
      height: 15px;
      background: rgb(33, 150, 243);
      width: 5px;
      border-radius: 10px;
      position: relative;
      margin-right: 5px;
    }

    .table-item-text {
      font-weight: bolder;
    }

    .table-item-buttons {
      flex: 1;
      text-align: right;
    }

    .small-text {
      font-size: 12px;
      color: #2196F3;
      margin-left: 10px;
      position: relative;
      top: 2px;
    }
  }
}
</style>