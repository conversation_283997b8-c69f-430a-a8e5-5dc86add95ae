<template>
	<div class="form-content">
		<VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
			<VolHeader :title="this.$ts(title)" text="text" icon="el-icon-s-grid"></VolHeader>
		</VolForm>
	</div>
	<div class="table-container  clearfix">
		<div class="left-part">
			<div class="table-item">
				<div class="table-item-header">
					<div class="table-item-border"></div> <span class="table-item-text">抽检细项</span>
					<div class="table-item-buttons">
						<div>
							<el-button type="primary" icon="Plus" @click="addRow" plain>添加行</el-button>
							<el-button type="danger" icon="Delete" @click="delRow" plain>删除行</el-button>
						</div>
					</div>
				</div>
				<vol-table @loadBefore="loadBefore" @loadAfter="loadAfter" ref="table" @update:sort="sortChange" index
					:tableData="tableData" @rowClick="rowClick" :columns="columns" :height="400" :rowKey="rowKey"
					:span-method="cellSpanMethod" :pagination-hide="true" :load-key="true" :column-index="true"
					:single="true" @pageSizeChanged="pageSizeChanged" @pageSizesChanged_="pageSizeChanged"></vol-table>

			</div>
		</div>
		<div class="right-part">
			<div class="form-container">
				<VolForm :label-width="150" ref="form" label-position="left" :loadKey="true"
					:formFields="DialogFormFieldsTwo" :formRules="DialogFormRulesTwo">
				</VolForm>
			</div>
			<div class="button-container">
				<el-button type="primary" icon="Check" size="small" :disabled = "IsCilck" @click="Submit">提交</el-button>
			</div>
		</div>
	</div>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolForm from '@/components/basic/VolForm.vue';
import { mapState } from 'vuex';
import Common from "@/uitils/common.js";
import Excel from '@/uitils/xlsl.js';
import axios from 'axios';
import * as XLSX from 'xlsx';

export default {
	components: {
		VolHeader,
		VolForm,
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			IsCilck: false,
			YP_DefectRateTemp: null,
			testData: null,
			currentPage: 1,
			title: this.$ts('抽检检验'),
			DialogFormFieldsTwo: {
				JudgmentResults: null,
			},
			DialogFormRulesTwo: [
				[
					{
						dataKey: "判定结果", //后台下拉框对应的数据字典编号
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("抽检最终判定结果"),
						placeholder: this.$ts(""),
						filter: true,
						required: false, //设置为必选项
						field: "JudgmentResults",
						type: "select",
						colSize: 15,
					},
				],
			],
			formFields: {
				Container: null, CurrentSN: null, Employee: null, Shif: null, Fatory: null, Spec: null, Product: null, ProductDescription: null,
				MfgOrder: null, ContainerQty: null, AQLQty: null
			},
			formRules: [
				[
					{
						title: this.$ts('扫描批次码'),
						placeholder: this.$ts(''),
						required: false,
						field: "Container",
						type: "text",
						onKeyPress: ($event) => {
							this.onKeySearch($event, this.formFields)
						}
					},
					{
						title: this.$ts('当前批次码'),
						placeholder: this.$ts(''),
						required: false,
						field: "CurrentSN",
						type: "text",
						disabled: true,
						colSize: 2
					},
					{
						title: this.$ts('人员'),
						placeholder: this.$ts(''),
						required: false,
						field: "Employee",
						disabled: true,
						type: "text",
						hidden:true,
						colSize: 2
					},
					{
						title: this.$ts('班组'),
						placeholder: this.$ts(''),
						required: false,
						field: "Shif",
						disabled: true,
						type: "text",
						colSize: 2
					},
					{
						dataKey: "", //后台下拉框对应的数据字典编号
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("生产车间"),
						placeholder: this.$ts(""),
						filter: true,
						required: false, //设置为必选项
						field: "Fatory",
						type: "select",
						colSize: 2,
						disabled: true,
					},
					{
						title: this.$ts('工序'),
						placeholder: this.$ts(''),
						required: false,
						field: "Spec",
						disabled: true,
						type: "text",
						colSize: 2,

					},
				],
				[
					{
						dataKey: "", //后台下拉框对应的数据字典编号
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("产品编码"),
						placeholder: this.$ts(""),
						filter: true,
						required: true, //设置为必选项
						field: "Product",
						type: "select",
						colSize: 2,
						disabled: true,
					},

					{
						dataKey: "", //后台下拉框对应的数据字典编号
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("产品名称"),
						placeholder: this.$ts(""),
						filter: true,
						required: false, //设置为必选项
						field: "ProductDescription",
						type: "select",
						colSize: 2,
						disabled: true,
					},
					{
						dataKey: "", //后台下拉框对应的数据字典编号
						data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
						title: this.$ts("生产工单"),
						placeholder: this.$ts(""),
						filter: true,
						required: false, //设置为必选项
						field: "MfgOrder",
						type: "select",
						colSize: 2,
						disabled: true,
					},
					{
						title: this.$ts('批次数量'),
						placeholder: this.$ts(''),
						required: false,
						field: "ContainerQty",
						disabled: true,
						type: "text",
						colSize: 2
					},
					{
						title: this.$ts('抽检数量'),
						placeholder: this.$ts(''),
						required: false,
						field: "AQLQty",
						disabled: false,
						type: "text",
						colSize: 2,
						onKeyPress: () => {
							if (parseInt(this.formFields.ContainerQty, 10) < parseInt(this.formFields.AQLQty, 10)) {
								this.$message.error('抽检数量不能大于批次数量');
								this.formFields.AQLQty = null;
								return;
							}
						}
					},
				],
			],
			columns: [{ field: 'Data_Id', title: 'Data_Id', type: 'guid', width: 80, hidden: true },
			{
				field: 'Squence',
				title: '序号',
				type: 'string',
				width: 50,
				hidden: true,
				align: 'center'
			},
			{ field: 'YP_isDefectReason', title: '不良项', type: 'select', bind: { key: '', data: [] }, width: 100, edit: { type: "select" } },
			{ field: 'YP_DefectQty', title: '不良数量', type: 'string', width: 100, edit: { type: "input", keep: true } },
			{
				field: 'YP_SingleDefectRate',
				title: '单项不良率',
				type: 'string',
				width: 100,
				align: 'center'
			},
			{
				field: 'YP_DefectRate',
				title: '抽检数不良率',
				type: 'string',
				width: 100,
				align: 'center'
			}
			],
			tableData: [],
			//接口地址
			ApiUrl: {
				GetRevisionObject: '/api/Query/GetVolSelectRData', //获取带版本的建模
				GetAQLSpecInfo: '/api/Query/GetAQLSpecInfo',
				GetAQLContainerInfo: "/api/query/GetAQLContainerInfo",
				GetCurrentShif: "/api/query/GetCurrentShift",
				GetNameObject: "/api/query/GetNameObject",
				AQLInspectionCreateTxn: '/api/CDO/AQLInspectionCreateTxn',
			},
		}
	},
	created() {
		this.GetCurrentInfo();
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	mounted: function () {
		//this.GetSpecs(); //页面加载时获取工序
	},
	methods: {
		Submit() {
			this.IsCilck = true;
			if (!this.DialogFormFieldsTwo.JudgmentResults) {
				this.$message.error("判定结果为空，请检查");
				this.IsCilck = false;
				return;
			}
			//add by zt 2024-12-09
			if(parseInt(this.tableData[0].YP_DefectRate) > 100){
				this.$message.error('不良数总和不能大于抽检数量！')
				this.IsCilck = false;
				return;
			}
			let tempValue = "";
			let DataArr = [];
			if (this.tableData.length > 0) {
				for (let i = 0; i < this.tableData.length; i++) {
					DataArr.push({
						'YP_isDefectReason': this.tableData[i].YP_isDefectReason, 'YP_DefectQty': this.tableData[i].YP_DefectQty, 'YP_SingleDefectRate': this.tableData[i].YP_SingleDefectRate,
						'YP_DefectRate': this.tableData[i].YP_DefectRate
					},);
					tempValue = this.tableData[i].YP_DefectRate;
				}
			}
			let params = {
				Shift: this.formFields.Shif,
				Container: this.formFields.CurrentSN,
				Factory: this.formFields.Fatory,
				Spec: this.formFields.Spec,
				YP_AQLQty: this.formFields.AQLQty,
				Product: this.formFields.Product,
				ProductDescription: this.formFields.ProductDescription,
				YP_ContainerQty: this.formFields.ContainerQty,
				MfgOrder: this.formFields.MfgOrder,
				YP_InputType: "1",
				YP_Creator: this.formFields.Employee,
				YP_DefectRate: tempValue,
				YP_JudgmentResults: this.DialogFormFieldsTwo.JudgmentResults,
				YP_Spec: this.formFields.Spec,
				YP_AQLInspectionDetail: DataArr
			};
			DataArr = [];
			this.http.post(this.ApiUrl.AQLInspectionCreateTxn, params).then(res => {
				if (res.Result == 1) {
					this.$message.success("提交成功");
					if(this.DialogFormFieldsTwo.JudgmentResults == "2"){
					this.$tabs.open({
						text: '异常登记',
						path: '/Opc_LotException',
						query: { Search_Container: this.formFields.CurrentSN,
							Resion: "抽检验不合格",
					 		Comments: "抽检验不合格"
						 },
					});
					this.Clear();
				}
				} else {
					this.$message.error('提交失败');
					this.IsCilck = false;
					return;
				}
			}).catch(error => {
				this.IsCilck = false;
				this.$message.error(error);
			});
		},
		Clear() {
			this.formFields.CurrentSN = null,
			this.formFields.Fatory = null,
			this.formFields.Spec = null,
			this.formFields.Product = null,
			this.formFields.MfgOrder = null,
			this.formFields.Container = null,
			this.formFields.ContainerQty = null,
			this.formFields.AQLQty = null,
			this.tableData = [],
			this.DialogFormFieldsTwo.JudgmentResults = null,
			this.IsCilck = false;
		},
		cellSpanMethod({ row, column, rowIndex, columnIndex }) {
			if (column.property === 'YP_DefectRate') {
				if (row.YP_DefectQty != "" && row.YP_DefectQty != null && (row.YP_isDefectReason == "" || row.YP_isDefectReason == null)) {
					this.$message.error('填写不良数量前必须选择不良项');
					row.YP_DefectQty = null;
					return;
				}
				if (row.YP_DefectQty != "" && row.YP_DefectQty != null) {
					row.YP_SingleDefectRate = ((parseInt(row.YP_DefectQty, 10) / parseInt(this.formFields.AQLQty, 10)) * 100).toFixed(2) + "%";
				}
				let tempValue = 0;
				let Sum = 0;
				if (row.YP_SingleDefectRate != null && row.YP_SingleDefectRate != "") {
					for (let k = 0; k < this.tableData.length; k++) {
						// tempValue = tempValue + parseFloat(this.tableData[k].YP_SingleDefectRate.replace(/%/g, ''));
						Sum += parseInt(this.tableData[k].YP_DefectQty, 10);
					}
					tempValue = (Sum / parseInt(this.formFields.AQLQty, 10) * 100).toFixed(2);
					for (let j = 0; j < this.tableData.length; j++) {
						this.tableData[j].YP_DefectRate = tempValue.toString() + "%";
					}
					this.YP_DefectRateTemp = tempValue.toString() + "%";
				} else {
					for (let j = 0; j < this.tableData.length; j++) {
						this.tableData[j].YP_DefectRate = this.YP_DefectRateTemp;
					}
				}

				// 假设你想合并具有相同YP_DefectRate值的连续行  
				let spanCount = 1;
				for (let i = rowIndex + 1; i < this.tableData.length; i++) {
					if (this.tableData[i].YP_DefectRate === row.YP_DefectRate) {
						spanCount++;
					} else {
						break;
					}
				}
				if (spanCount > 1) {
					return [spanCount, 1];
				} else {
					return [1, 1];
				}
			}
			return [1, 1];
		},


		loadBefore(params, callBack) {
			callBack(true)
		},
		//查询后方法
		loadAfter(rows, callBack, result) {
			//如果有合计：后台返回合计格式
			//返回的行数据
			//返回的总行数
			//合计200 }} -- >
			callBack(true)
			// < !-- var data = new {rows=[],total=200,summary={ TotalPrice=100, TotalQty=
		},
		//
		// 切换页面或者设置每页数据行的回调方法
		pageSizeChanged(val) {
			this.currentPage = val;
		},
		rowKey(row) {
			// console.log(row.id, 'row.id');
			return row.id; // 使用唯一标识符作为 row-key
		},
		// 键盘事件
		onKeySearch($event, data) {
			if ($event.keyCode == 13 && $event.type == 'keyup') {
				this.GetAQLContainerInfo();
			}
		},
		async GetAQLContainerInfo() {
			let params = {
				name: this.formFields.Container
			};
			this.http.get(this.ApiUrl.GetAQLContainerInfo, params).then(res => {
				if (res.Result == 1) {
					if (res.Data.length < 1) {
						this.formFields.CurrentSN = null;
						this.formFields.Fatory = null;
						this.formFields.Spec = null;
						this.formFields.Product = null;
						this.formFields.ProductDescription = null;
						this.formFields.MfgOrder = null;
						this.formFields.ContainerQty = null;
						return;
					}
					if (res.Data[0].YP_IsSamplingInspection == "False") {
						this.$message.error("当前工序：" + res.Data[0].SpecName + "没有设置抽检");
					}
					else {
						this.formFields.CurrentSN = res.Data[0].ContainerName,
							this.formFields.Fatory = res.Data[0].FactoryName,
							this.formFields.Spec = res.Data[0].SpecName,
							this.formFields.Product = res.Data[0].ProductName,
							this.formFields.ProductDescription = res.Data[0].ProductDescription,
							this.formFields.MfgOrder = res.Data[0].MfgOrderName,
							this.formFields.ContainerQty = res.Data[0].ContainerQty
						this.GetAQLSpecInfo(this.formFields.Spec);
					}
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		GetAQLSpecInfo(name) {
			let params = {
				name: name
			};
			this.http.get(this.ApiUrl.GetAQLSpecInfo, params).then(res => {
				if (res.Result == 1) {
					this.testData = res.Data;
					console.log(this.testData);
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		GetCurrentInfo() {
			// 获取当前信息
			this.http.get(this.ApiUrl.GetCurrentShif).then(res => {
				if (res.Result == 1) {
					this.formFields.Shif = res.Data[0].Name;
				} else {
					this.$message.error(res.Message);
				}
			});
			//获取当前用户的信息
			this.formFields.Employee = this.userInfo.userName;
		},
		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行')
		},
		addRow() {
			if (this.formFields.AQLQty == "" || this.formFields.AQLQty == null) {
				this.$message.error('抽检数量不能为空')
				return;
			}
			if (this.formFields.Spec == "" || this.formFields.Spec == null) {
				this.$message.error('工序不能为空')
				return;
			}
			//add by zt 2024-12-09
			if(this.tableData.length >= parseInt(this.formFields.AQLQty)){
				this.$message.error('行数不能超过抽检数量！')
				return;
			}
			
			this.columns[2].bind.data = this.testData;
			this.tableData.push({ YP_isDefectReason: "", YP_DefectQty: "", YP_SingleDefectRate: "", YP_DefectRate: "" });
		},
		delRow() {
			this.$refs.table.delRow();
		},
		getCurrentDateTimeString() {
			const now = new Date();
			const year = now.getFullYear();
			const month = this.padNumber(now.getMonth() + 1);
			const day = this.padNumber(now.getDate());
			const hours = this.padNumber(now.getHours());
			const minutes = this.padNumber(now.getMinutes());
			const seconds = this.padNumber(now.getSeconds());

			return `${year}${month}${day}${hours}${minutes}${seconds}`;
		},
		padNumber(num) {
			return num < 10 ? '0' + num : num;
		}
	}
}
</script>
<style lang="less" scoped>
.table-container {
	display: flex;
	flex-direction: row;
	/* 设置主轴为水平方向 */
	justify-content: space-between;
	/* 子元素之间的间隔分布 */
}

.left-part {
	float: left;
	width: 58%;
	/* 或者其他你需要的宽度 */
}

.right-part {
	float: right;
	width: 38%;
	/* 或者其他你需要的宽度 */
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	/* 在顶部和底部之间分配空间 */
	align-items: center;
	/* 横向居中所有子元素 */
	padding: 10px;
	/* 可选，用于内部填充 */
	box-sizing: border-box;
	/* 包括填充和边框在内计算宽度和高度 */
}

.form-container {
	flex: 1;
	/* 让这个容器占据除了底部按钮外的所有空间 */
	display: flex;
	justify-content: center;
	/* 横向居中表单 */
	align-items: center;
	/* 纵向居中（如果需要） */
	/* 可以添加额外的样式来调整表单的上下边距 */
	width: 100%;
	/* 使其宽度充满父容器 */
}

.button-container {
	width: 100%;
	/* 确保按钮容器宽度与父容器一致 */
	display: flex;
	justify-content: center;
	/* 横向居中按钮 */
}

.clearfix::after {
	content: "";
	display: table;
	clear: both;
	/* 清除浮动 */
}

.form-content {
	border-radius: 5px;
	padding: 10px 0;
	background: #fff;
}

.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	.table-item-text {
		font-weight: bolder;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196F3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>