<template>
	<div class="page-header">
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="editMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder" @change="changeMfgOrder">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>组别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="editEmployeeGroup" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getEmployeeGroup">
						<el-option v-for="item in employeeGroups" :key="item.Name" :label="item.Name"
							:value="item.Name" />
					</el-select>
				</div>
			</div>
			<!-- <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>巡检时段</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="editIPQCDuration" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getIPQCDuration">
						<el-option v-for="item in IPQCDurations" :key="item.Name" :label="item.Name"
							:value="item.Name" />
					</el-select>
				</div>
			</div> -->
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>机台</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="editResource" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getResource" @change="changeResource">
						<el-option v-for="item in resources" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>数量/穴数</span>
				</label>
				<div style="margin-top: 5px">
					<el-input ref="inputCavityCount" style="width: 200px;" v-model="editCavityCount" clearable
						placeholder="请输入"></el-input>
				</div>
			</div>
		</div>
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>产品编码</span>
				</label>
				<div style="margin-top: 5px">
					<el-input style="width: 200px;" disabled v-model="infoProduct" clearable placeholder=" "></el-input>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>产品描述</span>
				</label>
				<div style="margin-top: 5px">
					<el-input style="width: 200px;" disabled v-model="infoDescription" clearable
						placeholder=" "></el-input>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>工单数量</span>
				</label>
				<div style="margin-top: 5px">
					<el-input style="width: 200px;" disabled v-model="infoQty" clearable placeholder=" "></el-input>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>模具编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-input style="width: 200px;" disabled v-model="infoTool" clearable placeholder=" "></el-input>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>客户编码</span>
				</label>
				<div style="margin-top: 5px">
					<el-input style="width: 200px;" disabled v-model="infoCustomerReference" clearable
						placeholder=" "></el-input>
				</div>
			</div>
			<div style="margin-top: 28px; margin-left: 20px;">
				<el-button type="success" icon="Plus" @click="addRow" plain>创建</el-button>
			</div>
		</div>
	</div>
	<el-divider />
	<div class="page-header">
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>组别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchEmployeeGroup" clearable filterable placeholder="键入搜索"
						style="width: 150px" remote-show-suffix :remote="true" :remote-method="getEmployeeGroup">
						<el-option v-for="item in employeeGroups" :key="item.Name" :label="item.Name"
							:value="item.Name" />
					</el-select>
				</div>
			</div>
			<!-- <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>巡检时段</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchIPQCDuration" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getIPQCDuration">
						<el-option v-for="item in IPQCDurations" :key="item.Name" :label="item.Name"
							:value="item.Name" />
					</el-select>
				</div>
			</div> -->
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>创建时间</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
						end-placeholder="结束" :size="size" style="width: 260px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss" />
				</div>
			</div>
			<div class="table-item-buttons" style="margin-top: 28px; margin-left: 20px;">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="success" icon="Check" @click="editRow" plain>执行</el-button>
					<el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">巡检单</span>
		</div>
		<vol-table ref="masterTable" index :tableData="masterTableData" @rowClick="rowClick" @rowDbClick="rowDbClick"
			:columns="masterColumns" :height="400" :pagination-hide="false" :load-key="true" :column-index="true"
			:single="true" :url="apiUrl.getIPQC" @loadBefore="masterLoadBefore" @loadAfter="masterLoadAfter"
			:defaultLoadPage="false" :ck="true"></vol-table>
	</div>
	<!-- 编辑弹出框 -->
	<vol-box :lazy="true" v-model="showExe" :title="className+'巡检' + '检验报表'" :width="1200" :padding="5">
		<!-- <vol-header :title="className+'巡检' + '检验报表'" style="text-align: center;"></vol-header> -->
		<div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>巡检检验单号</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxIPQCOrder"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxMfgOrder"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>组别</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxEmployeeGroup"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>巡检时间段</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxIPQCDuration"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>机台</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxResource"></el-input>
				</div>
			</div>

			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>白夜班</span>
				</label>
				<div style="margin-top: 5px">
				<!-- 班次选择 -->
				<el-radio-group v-model="shift" style="margin-right: 10px;" @change="changeShift">
					<el-radio :label="1">白班</el-radio>
					<el-radio :label="0">夜班</el-radio>
				</el-radio-group>
				</div>
			</div>

		</div>
		<div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>模具编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxTool"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxProduct"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品描述</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxDescrition"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单数量</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxQty"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>客户编码</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="boxCustomerReference"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<div style="margin-top: 27px">
					<el-button type="primary" @click="toQualityInspectionSheet" plain>检验表维护</el-button>
				</div>
			</div>
		</div>

		<div class="table-item-header">
			<span class="table-item-text">定性检验项目</span>
		</div>
		<vol-table ref="qualitativeTable" index :tableData="qualitativeTableData" :columns="qualitativeCols"
			:max-height="600" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
			:edit="true"></vol-table>

		<div class="table-item-header">
			<span class="table-item-text">定量检验项目</span>
		</div>
		<vol-table ref="quantitativeTable" index :tableData="quantitativeTableData" :columns="quantitativeCols"
			:max-height="600" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
			:edit="true"></vol-table>
		<div style="margin-left: 10px; ">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>综合判定</span>
			</label>
			<div style="margin-top: 5px">
				<el-select v-model="IAResult" style="width: 200px">
					<el-option v-for="item in InepectResults" :key="item.key" :label="item.value" :value="item.key" />
				</el-select>
				<!-- <el-input style="width: 200px;" disabled v-model="InepectResult" placeholder=" "></el-input> -->
			</div>
		</div>
		<template #footer>
			<div v-show="showExeBtn">
				<el-button type="success" icon="Submit" size="small" @click="exeSave(1)">结束单据</el-button>
				<el-button type="success" icon="Check" size="small" @click="exeSave(2)">保存</el-button> 
				<!-- <el-button icon="Close" size="small" @click="exeClose">关闭</el-button> -->
			</div>
		</template>
	</vol-box>

	<vol-box :lazy="true" v-model="showCollect" title="定量项目实测值" :width="500" :padding="5">
		<div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>检验项目</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="collectInspectionPoint"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 80px; margin-left: 5px; font-size: 16px">
					<span>下限</span>
				</label>
				<div style="margin-top: 5px;width: 100px;">
					<el-input disabled v-model="collectLowerLimit"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 80px; margin-left: 5px; font-size: 16px">
					<span>上限</span>
				</label>
				<div style="margin-top: 5px;width: 100px;">
					<el-input disabled v-model="collectUpperLimit"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 80px; margin-left: 5px; font-size: 16px">
					<span>检验内容</span>
				</label>
				<div style="margin-top: 5px;width: 100px;">
					<el-input disabled v-model="InspectionPointContent"></el-input>
				</div>
			</div>
		</div>
		<vol-table ref="collectTable" index :tableData="collectTableData" :columns="collectCols" :max-height="600"
			:pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
			:edit="true"></vol-table>
		<template #footer>
			<div v-show="showCollectBTN">
				<el-button v-show="showIOT" type="primary" :icon="Share" size="small"
					@click="collectIOT">获取数据</el-button>
				<el-button type="success" icon="Check" size="small" @click="collectSave">保存</el-button>
				<el-button type="info" icon="Refresh" @click="initCollectTableData" plain>重置</el-button>
				<!-- <el-button type="default" icon="Close" size="small" @click="collectClose">关闭</el-button> -->
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox,
		'vol-header': VolHeader,
		
	},
	data() {
		return {
			//班次
			shift:1,
			//主页面
			editMfgOrder: null,
			editEmployeeGroup: null,
			editIPQCDuration: null,
			editResource: null,
			editCavityCount: null,
			infoProduct: null,
			infoDescription: null,
			infoQty: null,
			infoTool: null,
			infoCustomerReference: null,
			searchMfgOrder: null,
			searchEmployeeGroup: null,
			searchIPQCDuration: null,
			searchTxnDate: null,
			mfgorders: [],
			employeeGroups: [],
			specs: [],
			IPQCDurations: [],
			resources: [],
			masterTableData: [],
			tempMasterTableData: [],
			className: null,


			//执行页面
			showExeBtn: true,
			showExe: false,
			boxIPQCOrder: null,
			boxMfgOrder: null,
			boxEmployeeGroup: null,
			boxIPQCDuration: null,
			boxResource: null,
			boxTool: null,
			boxProduct: null,
			boxDescrition: null,
			boxCustomerReference: null,
			boxQty: null,
			IAResults: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }],
			qualitativeTableData: [],
			quantitativeTableData: [],
			collectTableData: [],
			InspectionPointContent: null,

			//数采页面
			currentRowIndex: null,
			showIOT: false,
			showCollect: false,
			IAResult: null,
			showCollectBTN: true,
			InepectResult: null,
			InepectResults: [{ key: '1', value: '合格' }, { key: '0', value: '不合格' }],
			masterColumns: [
				{ field: 'CDOID', title: '建模ID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'CDOName', title: '检验单号', type: 'string', width: 100, align: 'center' },
				{ field: 'EmployeeGroup', title: '组别', type: 'string', width: 100, align: 'center' },
				{ field: 'IPQCDuration', title: '巡检时间段', type: 'string', width: 100, align: 'center' },
				{ field: 'ResourceName', title: '机台', type: 'string', width: 100, align: 'center' },
				{ field: 'MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
				{ field: 'Product', title: '产品编码', type: 'string', width: 100, align: 'center' },
				{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
				{ field: 'CustomerReference', title: '客户编码', type: 'string', width: 80, align: 'center' },
				{ field: 'Qty', title: '工单数量', type: 'string', width: 80, align: 'center' },
				{
					field: 'Status', title: '状态', type: 'string', width: 60, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						if (row.Status == '已检验') {
							return { background: "#2196F3", color: "#fff" };
						}
					},
				},
				{
					field: 'OutCome', title: '综合判定', type: 'string', width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.OutCome) {
							case '合格':
								return { background: "#82C256", color: "#fff" };
							case '不合格':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{ field: 'Inspector', title: '巡检人', type: 'string', width: 80, align: 'center' },
				{ field: 'InspectionDate', title: '巡检时间', type: 'datetime', width: 120, align: 'center' },
			],

			qualitativeCols: [
				{ field: 'InspectionPoint', title: '检验项目', edit: false, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionPointContent', title: '检验内容', edit: false, type: 'string', width: 130, align: 'center' },
				{
					field: 'ItemResult', title:this.shift==null?'检验结果(8:00~10:00)':'检验结果(20:00~22:00)' , bind: { key: null, data: [] }, edit: { type: "select" }, width: 300, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{
					field: 'ItemResult1', title:this.shift==null?'检验结果(10:00~12:00)':'检验结果(22:00~24:00)', bind: { key: null, data: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }] }, edit: { type: "select" }, width: 300, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult1) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},

				{
					field: 'ItemResult2', title:this.shift==null?'检验结果(12:00~14:00)':'检验结果(24:00~02:00)', bind: { key: null, data: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }] }, edit: { type: "select" }, width: 300, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult2) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{
					field: 'ItemResult3', title:this.shift==null?'检验结果(14:00~16:00)':'检验结果(02:00~04:00)', bind: { key: null, data: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }] }, edit: { type: "select" }, width: 300, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult3) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{
					field: 'ItemResult4', title:this.shift==null?'检验结果(16:00~18:00)':'检验结果(04:00~06:00)', bind: { key: null, data: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }] }, edit: { type: "select" }, width: 300, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult4) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{
					field: 'ItemResult5',title:this.shift==null?'检验结果(18:00~20:00)':'检验结果(06:00~08:00)', bind: { key: null, data: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }] }, edit: { type: "select" }, width: 300, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult5) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{ field: 'Notes', title: '备注', edit: true, type: 'string', width: 300, align: 'center' },
			],

			quantitativeCols: [
				{ field: 'InspectionPoint', title: '检验项目', edit: false, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionPointContent', title: '检验内容', edit: false, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionTool', title: '检验工具', edit: false, type: 'string', width: 100, align: 'center' },
				{ field: 'LowerLimit', title: '下限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
				{ field: 'UpperLimit', title: '上限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
				{ field: 'DefaultValue', title: '标准值', edit: false, type: 'string', width: 80, align: 'center' },
				{ 
					field: 'ActualValue', title: '实测值', align: 'center', width: 80, render: (h, { row, column, index }) => {
						return (
							<div>
								<el-button
									onClick={($e) => {
										this.collectInspectionPoint = row.InspectionPoint;
										this.collectUpperLimit = row.UpperLimit;
										this.collectLowerLimit = row.LowerLimit;
										this.currentRowIndex = index;
										this.InspectionPointContent = row.InspectionPointContent;
										this.initCollectTableData();
										console.log(row);
										if (row.ActualValue != null) {
											for (let i = 0; i < row.ActualValue.length; i++) {
												if (this.collectTableData[i]) {
													// 确保 collectTableData[i] 存在
													this.collectTableData[i].ActualValue = row.ActualValue[i].ActualValue;
													this.collectTableData[i].ActualValue1 = row.ActualValue[i].ActualValue1;
													this.collectTableData[i].ActualValue2 = row.ActualValue[i].ActualValue2;
													this.collectTableData[i].ActualValue3 = row.ActualValue[i].ActualValue3;
													this.collectTableData[i].ActualValue4 = row.ActualValue[i].ActualValue4;
													this.collectTableData[i].ActualValue5 = row.ActualValue[i].ActualValue5;
												}
												else {
													// 如果 collectTableData[i] 不存在，可以选择添加一个新的对象
													this.collectTableData.push({
														ActualValue: row.ActualValue[i].ActualValue,
														ActualValue1: row.ActualValue[i].ActualValue1,
														ActualValue2: row.ActualValue[i].ActualValue2,
														ActualValue3: row.ActualValue[i].ActualValue3,
														ActualValue4: row.ActualValue[i].ActualValue4,
														ActualValue5: row.ActualValue[i].ActualValue5
													});
												}
											}
											
										}


										this.showCollect = true;
										if (row.FromIOT == '1') {
											this.showIOT = true;
										} else {
											this.showIOT = false;
										}
									}}
									size="small"
									type="primary"
									icon="Edit"
									plain >
								</el-button>
								{/* 这里可以接着放按钮或者其他组件 */}
							</div>
						);
					}
				},
				{
					field: 'ItemResult', title: '检验结果', edit: false, type: 'string', width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult, rowIndex, columnIndex) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{ field: 'Notes', title: '备注', edit: true, type: 'string', width: 130, align: 'center' },

				{ field: 'FromIOT', title: '来源IOT', hidden: true, edit: false, type: 'string', width: 130, align: 'center' },

			],
			collectCols: [
				{ field: 'ActualValue', title: this.shift==null?'实测值(8:00~10:00)':'实测值(20:00~22:00)', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
				{ field: 'ActualValue1', title:this.shift==null?'实测值(10:00~12:00)':'实测值(22:00~24:00)', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
				{ field: 'ActualValue2', title:this.shift==null?'实测值(12:00~14:00)':'实测值(24:00~02:00)', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
				{ field: 'ActualValue3', title:this.shift==null?'实测值(14:00~16:00)':'实测值(02:00~04:00)', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
				{ field: 'ActualValue4', title:this.shift==null?'实测值(16:00~18:00)':'实测值(04:00~06:00)', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
				{ field: 'ActualValue5', title:this.shift==null?'实测值(18:00~20:00)':'实测值(06:00~08:00)', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
			],

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/GetRevisionObject",
				getNameObject: "/api/query/GetNameObject",
				getIPQC: '/api/query/getIPQC',
				getStartMfgOrder: "/api/query/getStartMfgOrder",
				IPQCOrderMaint: '/api/cdo/IPQCOrderMaint',
				getQualityInspectionSheet: '/api/query/getQualityInspectionSheet',
				getIPQCCollectDetails: '/api/query/getIPQCCollectDetails',
				getMenu: '/api/menu/getTreeMenu',
				GetClassNamebyEmployee: '/api/query/GetClassNamebyEmployee',
			}
		}
	},
	created() {
		this.getIAResults();
		this.initCollectTableData();
		this.getClassName();
		
	},

	computed: {
		
		//获取当前用户的信息
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		}),
		
	},
	methods: {

		changeShift(){
			// 切换班次时，更新标题
			if (this.shift == 1) {
				this.collectCols[0].title = '实测值(8:00~10:00)';
				this.collectCols[1].title = '实测值(10:00~12:00)';
				this.collectCols[2].title = '实测值(12:00~14:00)';
				this.collectCols[3].title = '实测值(14:00~16:00)';
				this.collectCols[4].title = '实测值(16:00~18:00)';
				this.collectCols[5].title = '实测值(18:00~20:00)';
				console.log(this.qualitativeCols[2]);
				this.qualitativeCols[2].title = '检验结果(8:00~10:00)';
				this.qualitativeCols[3].title = '检验结果(10:00~12:00)';
				this.qualitativeCols[4].title = '检验结果(12:00~14:00)';
				this.qualitativeCols[5].title = '检验结果(14:00~16:00)';
				this.qualitativeCols[6].title = '检验结果(16:00~18:00)';
				this.qualitativeCols[7].title = '检验结果(18:00~20:00)';
			} else {
				this.collectCols[0].title = '实测值(20:00~22:00)';
				this.collectCols[1].title = '实测值(22:00~24:00)';
				this.collectCols[2].title = '实测值(24:00~02:00)';
				this.collectCols[3].title = '实测值(02:00~04:00)';
				this.collectCols[4].title = '实测值(04:00~06:00)';
				this.collectCols[5].title = '实测值(06:00~08:00)';
				this.qualitativeCols[2].title = '检验结果(20:00~22:00)';
				this.qualitativeCols[3].title = '检验结果(22:00~24:00)';
				this.qualitativeCols[4].title = '检验结果(24:00~02:00)';
				this.qualitativeCols[5].title = '检验结果(02:00~04:00)';
				this.qualitativeCols[6].title = '检验结果(04:00~06:00)';
				this.qualitativeCols[7].title = '检验结果(06:00~08:00)';
			}
		},
		getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getEmployeeGroup(query) {
			if (query) {
				let params = {
					cdo: "EmployeeGroup",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.employeeGroups = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getIPQCDuration(query) {
			if (query) {
				let params = {
					cdo: "W_IPQCDuration",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.IPQCDurations = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getResource(query) {
			if (query) {
				let params = {
					objectCategory: "RESOURCE",
					cdo: "Resource",
					name: query
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.resources = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getIAResults() {
			this.qualitativeCols[2].bind.data = this.IAResults;
		},
		initCollectTableData() {
			this.collectTableData = [];
			for (let i = 0; i < 7; i++) {
				this.collectTableData.push({ ActualValue: null, ActualValue1: null, ActualValue2: null, ActualValue3: null, ActualValue4: null, ActualValue5: null });
			}
		},
		changeMfgOrder() {
			if (!this.editMfgOrder) {
				this.infoProduct = null;
				this.infoDescription = null;
				this.infoQty = null;
				// this.infoTool = null;
				this.infoCustomerReference = null;
				return;
			};
			let params = {
				mfgorder: this.editMfgOrder
			};
			this.http.post(this.apiUrl.getStartMfgOrder, params).then(res => {
				if (res.Result == 1) {
					this.infoProduct = res.Data.tableData[0].Product;
					this.infoDescription = res.Data.tableData[0].P_Description;
					this.infoQty = res.Data.tableData[0].Qty;
					// this.infoTool = res.Data.tableData[0].Tool;
					this.infoCustomerReference = res.Data.tableData[0].CustomerReference;
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		changeResource() {
			if (!this.editResource) {
				this.infoTool = null;
				return;
			};
			let params = {
				resourcename: this.editResource
			};
			this.http.post(this.apiUrl.getStartMfgOrder, params).then(res => {
				if (res.Result == 1) {
					this.infoTool = res.Data.tableData[0].Tool;
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		reset() {
			this.editMfgOrder = null;
			this.editEmployeeGroup = null;
			this.editIPQCDuration = null;
			this.editResource = null;
			this.editCavityCount = null;
			this.infoProduct = null;
			this.infoDescription = null;
			this.infoQty = null;
			this.infoTool = null;
			this.infoCustomerReference = null;
			this.searchMfgOrder = null;
			this.searchEmployeeGroup = null;
			this.searchIPQCDuration = null;
			this.searchTxnDate = null;
			this.mfgorders = [];
			this.employeeGroups = [];
			this.specs = [];
			this.IPQCDurations = [];
			this.resources = [];
			this.masterTableData = [];
			this.$refs.masterTable.rowData = [];
			this.$refs.masterTable.paginations.total = 0;
		},
		//清除数据
		resetMaster() {
			this.masterTableData = [];
			this.tempMasterTableData = [];
			this.$refs.masterTable.rowData = [];
		},
		resetEdit() {
			this.boxIPQCOrder = null;
			this.boxMfgOrder = null;
			this.boxEmployeeGroup = null;
			this.boxIPQCDuration = null;
			this.boxResource = null;
			this.boxTool = null;
			this.boxProduct = null;
			this.boxDescrition = null;
			this.boxCustomerReference = null;
			this.boxQty = null;
			this.qualitativeTableData = [];
			this.quantitativeTableData = [];
		},
		addRow() {
			if (!this.editMfgOrder) {
				this.$message.error('工单不能为空');
				return;
			}
			if (!this.editEmployeeGroup) {
				this.$message.error('组别不能为空');
				return;
			}

			let params = {
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				MfgOrder: this.editMfgOrder,
				EmployeeGroup: this.editEmployeeGroup,
				IPQCDuration: this.editIPQCDuration,
				Resource: this.editResource,
				CavityCount: this.editCavityCount,
				status: '0',//0:新增
				type: 'new'
			};
			this.http.post(this.apiUrl.IPQCOrderMaint, params, true).then(res => {
				if (res.Result == 1) {
					this.$message.success('操作成功');
					this.reset();
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			this.$refs.masterTable.$refs.table.toggleRowSelection(row);
		},
		rowDbClick({
			row,
			column,
			index
		}) {
			if (row.Status == '已检验') {
				this.boxIPQCOrder = row.CDOName;
				this.boxMfgOrder = row.MfgOrder;
				this.boxEmployeeGroup = row.EmployeeGroup;
				this.boxIPQCDuration = row.IPQCDuration;
				this.boxResource = row.Resource;
				this.boxTool = row.Tool;
				this.boxProduct = row.Product;
				this.boxDescrition = row.P_Description;
				this.boxCustomerReference = row.CustomerReference;
				this.boxQty = row.Qty;

				this.getQualityInspectionSheetV2(row);
				this.showCollectBTN = false;
				this.showExeBtn = false;
				this.showExe = true;
			}
			else {
				this.$message.error('此检验单未检验');
				return;
			}
		},
		setCollectDetails(row) {
			let params = {
				cdoname: row.CDOName
			};
			this.http.post(this.apiUrl.getIPQCCollectDetails, params, true).then(res => {
				if (res.Result == 1) {
					//给检验表赋值
					this.qualitativeTableData.forEach(item => {
						const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
						if (tempData != null) {
							item.ItemResult = tempData.ItemResult == null ? 'OK' : (tempData.ItemResult == "1" ? 'OK' : 'NG');
							item.Notes = tempData.Notes;
							item.ItemResult1 = tempData.ItemResult1 == null || tempData.ItemResult1 == "0" ? 'OK' : (tempData.ItemResult1 == "2" ? 'NG' : 'OK');
							item.Comments1 = tempData.Comments1;
							item.ItemResult2 = tempData.ItemResult2 == null || tempData.ItemResult2 == "0" ? 'OK' : (tempData.ItemResult2 == "2" ? 'NG' : 'OK');
							item.Comments2 = tempData.Comments2;
							item.ItemResult3 = tempData.ItemResult3 == null || tempData.ItemResult3 == "0" ? 'OK' : (tempData.ItemResult3 == "2" ? 'NG' : 'OK');
							item.Comments3 = tempData.Comments3;
							item.ItemResult4 = tempData.ItemResult4 == null || tempData.ItemResult4 == "0" ? 'OK' : (tempData.ItemResult4 == "2" ? 'NG' : 'OK');
							item.Comments4 = tempData.Comments4;
							item.ItemResult5 = tempData.ItemResult5 == null || tempData.ItemResult5 == "0" ? 'OK' : (tempData.ItemResult5 == "2" ? 'NG' : 'OK');
							item.Comments5 = tempData.Comments5;
						}
					});
					this.quantitativeTableData.forEach(item => {
						item.ActualValue = []
						const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
						if (tempData != null) {
							item.ItemResult = tempData.ItemResult == null? 'OK' : (tempData.ItemResult == "1" ? 'OK' : 'NG');
							item.Notes = tempData.Notes;
							// item.ItemResult1 = tempData.ItemResult1 == null || tempData.ItemResult1 == "0" ? null : (tempData.ItemResult1 == "2" ? 'NG' : 'OK');
							// item.Comments1 = tempData.Comments1;
							// item.ItemResult2 = tempData.ItemResult2 == null || tempData.ItemResult2 == "0" ? null : (tempData.ItemResult2 == "2" ? 'NG' : 'OK');
							// item.Comments2 = tempData.Comments2;
							// item.ItemResult3 = tempData.ItemResult3 == null || tempData.ItemResult3 == "0" ? null : (tempData.ItemResult3 == "2" ? 'NG' : 'OK');
							// item.Comments3 = tempData.Comments3;
							// item.ItemResult4 = tempData.ItemResult4 == null || tempData.ItemResult4 == "0" ? null : (tempData.ItemResult4 == "2" ? 'NG' : 'OK');
							// item.Comments4 = tempData.Comments4;
							// item.ItemResult5 = tempData.ItemResult5 == null || tempData.ItemResult5 == "0" ? null : (tempData.ItemResult5 == "2" ? 'NG' : 'OK');
							item.Comments5 = tempData.Comments5;
							if (tempData.Detail != null) {
						tempData.Detail.forEach(detailItem => {
								if (detailItem.ActualValue != null||detailItem.ActualValue1 != null
								||detailItem.ActualValue2 != null||detailItem.ActualValue3 != null
								||detailItem.ActualValue4 != null||detailItem.ActualValue5 != null) {
									item.ActualValue.push({ActualValue:detailItem.ActualValue,ActualValue1:detailItem.ActualValue1,
										ActualValue2:detailItem.ActualValue2,ActualValue3:detailItem.ActualValue3,
										ActualValue4:detailItem.ActualValue4,ActualValue5:detailItem.ActualValue5});
								}
								
							});
							}
							
						}
						
					});
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		queryRow() {
			if (this.searchMfgOrder == null
				&& this.searchEmployeeGroup == null
				&& this.searchIPQCDuration == null
				&& this.searchTxnDate == null) {
				this.$message.error('请输入查询条件');
				return;
			}
			this.resetMaster();
			this.$refs.masterTable.load(null, true);
		},
		masterLoadBefore(params, callBack) {
			params["MfgOrder"] = this.searchMfgOrder != null ? this.searchMfgOrder : null;
			params["IPQCDuration"] = this.searchIPQCDuration != null ? this.searchIPQCDuration : null;
			params["EmployeeGroup"] = this.searchEmployeeGroup != null ? this.searchEmployeeGroup : null;
			params["StartTime"] = this.searchTxnDate != null ? this.searchTxnDate[0] : null;
			params["EndTime"] = this.searchTxnDate != null ? this.searchTxnDate[1] : null;
			callBack(true)
		},
		masterLoadAfter(rows, callBack, result) {
			if (result.Result == 1) {
				//this.columns = result.Data.colums;
				this.masterTableData = result.Data.tableData;
				this.tempMasterTableData = result.Data.tableData;
				this.$refs.masterTable.rowData = result.Data.tableData;
				this.$refs.masterTable.paginations.total = result.Data.total;
			}
			else {
				this.$message.error(result.Message);
			}
			callBack(false);
		},
		editRow() {
			const rows = this.$refs.masterTable.getSelected();
			if (!rows.length) {
				this.$message.error('请选中即将执行的行')
				return;
			}
			if (rows[0].Status != '未检验') {
				this.$message.error('检验单已检验')
				return;
			}
			this.resetEdit();
			//查找检验表
			// this.getQualityInspectionSheet(rows[0]);
			this.getQualityInspectionSheetV2(rows[0]);
			this.boxIPQCOrder = rows[0].CDOName;
			this.boxMfgOrder = rows[0].MfgOrder;
			this.boxEmployeeGroup = rows[0].EmployeeGroup;
			this.boxIPQCDuration = rows[0].IPQCDuration;
			this.boxResource = rows[0].ResourceName;
			this.boxTool = rows[0].Tool;
			this.boxProduct = rows[0].Product;
			this.boxDescrition = rows[0].P_Description;
			this.boxCustomerReference = rows[0].CustomerReference;
			this.boxQty = rows[0].Qty;
			this.showCollectBTN = true;
			this.showExeBtn = true;
			this.showExe = true;
		},
		getQualityInspectionSheet(row) {
			//查找检验表
			let params = {
				product: row.Product,
				inspectionType: '3',
			};
			this.http.post(this.apiUrl.getQualityInspectionSheet, params, true).then(res => {
				if (res.Result == 1) {
					if (res.Data.tableData != null) {
						this.qualitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '1');
						this.quantitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '2');
					}
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		getQualityInspectionSheetV2(row) {
			//查找检验表
			let params = {
				product: row.Product,
				inspectionType: '3',
			};
			this.http.post(this.apiUrl.getQualityInspectionSheet, params, true).then(res => {
				if (res.Result == 1) {
					if (res.Data.tableData != null) {
						this.qualitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '1');
						this.quantitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '2');
						// 将 qualitativeTableData 中的 ItemResult 到 ItemResult5 都设为 'OK'
						this.qualitativeTableData.forEach(item => {
							item.ItemResult = 'OK';
							item.ItemResult1 = 'OK';
							item.ItemResult2 = 'OK';
							item.ItemResult3 = 'OK';
							item.ItemResult4 = 'OK';
							item.ItemResult5 = 'OK';
						});
						this.setCollectDetails(row);
					}
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		outputRow() {
			// this.tableData.splice(0);
			//导出
			let tableData = this.$refs.masterTable.tableData;
			let sortData = this.$refs.table.filterColumns;
			let exportData = this.handleTableSortData(tableData, sortData);
			Excel.exportExcel(exportData, "IPQC" + '-' + this.base.getDate());
		},
		handleTableSortData(tableData, sortData) {
			let newArray = [];
			tableData.forEach(data => {
				let newItem = {};
				sortData.forEach(field => {
					if (data.hasOwnProperty(field.field)) {
						newItem[field.title] = data[field.field];
					}
				});
				newArray.push(newItem);
			});
			return newArray
		},
		exeSave(typeInt) {
			// 检查 qualitativeTableData 是否都有检验结果
			for (let item of this.qualitativeTableData) {
				if (typeInt == 1 && !item.ItemResult && !item.ItemResult1 && !item.ItemResult2 && !item.ItemResult3 && !item.ItemResult4 && !item.ItemResult5) {
					this.$message.error('定性检验项目中有未填写的检验结果');
					return;
				}
				// if (item.ItemResult == 'NG'|| item.ItemResult1 == 'NG' || item.ItemResult2 == 'NG' || item.ItemResult3 == 'NG' || item.ItemResult4 == 'NG' || item.ItemResult5 == 'NG') {
				// 	this.IAResult = 0;
				// }
			}
			// 检查 quantitativeTableData 是否都有检验结果
			for (let item of this.quantitativeTableData) {
				if (typeInt == 1 && !item.ItemResult) {
					this.$message.error('定量检验项目中有未填写的检验结果');
					return;
				}
			}
			if (typeInt == 1 && !this.IAResult) {
				this.$message.error('请选择综合判定结果');
				return;

			}

			let params = {
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				CDOName: this.boxIPQCOrder,
				OutCome: this.IAResult,
				status: typeInt == 1 ? 1 : 0,//2: 已检验
				type: 'inspection',
				CollectDetails: this.quantitativeTableData.concat(this.qualitativeTableData)
			};
			this.http.post(this.apiUrl.IPQCOrderMaint, params, true).then(res => {
				if (res.Result == 1) {
					this.showExe = false;
					this.queryRow();
					this.$message.success(res.Message);
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		exeClose() {
			this.showExe = false;
		},
		collectIOT() {

		},
		collectSave() {
			// 赋值回 quantitativeTableData
			if (this.currentRowIndex != null) {
				let arryData = this.collectTableData.map(item => {
					return {
						ActualValue: item.ActualValue,
						ActualValue1: item.ActualValue1,
						ActualValue2: item.ActualValue2,
						ActualValue3: item.ActualValue3,
						ActualValue4: item.ActualValue4,
						ActualValue5: item.ActualValue5
					};
				});
				this.quantitativeTableData[this.currentRowIndex].ActualValue = arryData.filter(item => item.ActualValue !== null && item.ActualValue !== undefined
					|| item.ActualValue1 !== null && item.ActualValue1 !== undefined
					|| item.ActualValue2 !== null && item.ActualValue2 !== undefined
					|| item.ActualValue3 !== null && item.ActualValue3 !== undefined
					|| item.ActualValue4 !== null && item.ActualValue4 !== undefined
					|| item.ActualValue5 !== null && item.ActualValue5 !== undefined
				);
				let result = false;
				for (let item of this.collectTableData) {
					
					if (parseFloat(item.ActualValue) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
						|| parseFloat(item.ActualValue) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)
					|| parseFloat(item.ActualValue1) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
						|| parseFloat(item.ActualValue1) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)
						|| parseFloat(item.ActualValue2) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
						|| parseFloat(item.ActualValue2) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)
						|| parseFloat(item.ActualValue3) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
						|| parseFloat(item.ActualValue3) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)
						|| parseFloat(item.ActualValue4) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
						|| parseFloat(item.ActualValue4) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)
						|| parseFloat(item.ActualValue5) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
						|| parseFloat(item.ActualValue5) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)
					) {
						this.quantitativeTableData[this.currentRowIndex].ItemResult = "NG";
						this.showCollect = false;
						return;
					} else {
						result = true;
					}
				}
				if (this.quantitativeTableData[this.currentRowIndex].ActualValue.length == 0) {
					this.quantitativeTableData[this.currentRowIndex].ItemResult = null;
				}
				if (result) {
					this.quantitativeTableData[this.currentRowIndex].ItemResult = "OK";
				}
			}
			this.showCollect = false;
		},
		collectClose() {
			this.showCollect = false;
		},
		getCurrentDateTimeString() {
			const now = new Date();
			const year = now.getFullYear();
			const month = this.padNumber(now.getMonth() + 1);
			const day = this.padNumber(now.getDate());
			const hours = this.padNumber(now.getHours());
			const minutes = this.padNumber(now.getMinutes());
			const seconds = this.padNumber(now.getSeconds());

			return `${year}${month}${day}${hours}${minutes}${seconds}`;
		},
		padNumber(num) {
			return num < 10 ? '0' + num : num;
		},
		toQualityInspectionSheet() {
			this.http.get(this.apiUrl.getMenu, {}, false).then(res => {
				let found = false;
				res.menu.forEach(item => {
					if (item.name && item.name.includes("品质检验表")) {
						found = true;
						return this.$tabs.open({
							text: '品质检验表',
							path: item.url,
							query: {
								product: this.boxProduct
                        	}
						});
					}
				});
				if (!found) {
					this.$message.error('没有访问品质检验表的权限');
				}
			}).catch(err => {
				this.$message.error('获取菜单失败' + err);
			});
		},
		getClassName() {
			this.http.get(this.apiUrl.GetClassNamebyEmployee, {employName:this.userInfo.userName}, false).then(res => {
				if (res.Result == 1) {
					this.className = res.Data.Name;
				} else {
					this.$message.error(res.Message);
				}
			}).catch(err => {
				this.$message.error('获取班组名称失败' + err);
			});
		},
	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	// .table-item-text {
	// 	font-weight: bolder;
	// 	border-bottom: 1px solid #0c0c0c;
	// }
	.table-item-text {
		margin-top: 3px;
		padding-bottom: 6px;
		font-weight: bold;
		font-size: 15px;
		color: #484848;
		white-space: nowrap;
		border-bottom: 2px solid #676767;
		margin-bottom: -1px;
		letter-spacing: 1px;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196f3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>