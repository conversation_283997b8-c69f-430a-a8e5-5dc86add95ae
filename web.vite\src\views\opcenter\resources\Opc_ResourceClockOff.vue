<template>
    <div style="display: flex;margin-top: 5px;">
            <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>设备课别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="classname" clearable filterable placeholder="键入搜索"
                     style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getclassname" :loading="loading">
						<el-option v-for="item in classnames" 
						:key="item.classname" 
						:label="item.classname" 
						:value="item.classname" />
					</el-select>
				</div>
			</div>
        <div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>设备/模具编码</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="Resource" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getResource">
                    <el-option v-for="item in Resources" 
					:key="item.Name" 
					:label="item.Name" 
					:value="item.Name" />
                </el-select>
            </div>
        </div>
		<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>日期筛选</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
			</div>
		</div>
    </div>
    <!-- 数据执行 -->
    <div class="table-item">
        <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">设备/模具维修单待关闭计时列表</span>
            <div class="table-item-buttons">
                <div>
                    <el-button icon="Search" @click="getRow" plain>查询</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="400"
            :pagination-hide="false" :load-key="true" :defaultLoadPage="false" :single="true"
            :url="apiUrl.GetEquipmentClockOff"
            @loadBefore="loadBefore" @loadAfter="loadAfter" :column-index="true" :ck="true">
        </vol-table>

        <div style="display: flex;margin-top: 5px;">
            <div class="table-item-buttons" style="margin-top: 28px; margin-left: 10px;">
                <div>
                    <el-button type="success" icon="Check" @click="submitRow" plain>确认关闭</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            //searchMfgOrder: null,
            searchPlanDate:null,
            Workcentername:'',
            Workcenternames:[],
            mfgorders: [],
            infoPrinter: null,
            printers: [],
			Resource: null,
            Name: null,
            classname   : null,
            classnames: [],

            columns: [
				{ field: 'OrderName', title: '维修单', type: 'string', width: 130, align: 'center' },
				{ field: 'Description', title: '设备/模具名称', type: 'string', width: 130, align: 'center' },					
				{ field: 'ResourceName', title: '设备/模具编码', type: 'string', width: 110, align: 'center' },
                { field: 'classnamename', title: '设备课别', type: 'string', width: 130, align: 'center' },
				{ field: 'Jobmodelname', title: '维修模式', type: 'string', width: 150, align: 'center' },
				{ field: 'CREATENAME', title: '维修单创建人', type: 'string', width: 120, align: 'center' },
				{ field: 'PROGRESSNAME', title: '维修处理人', type: 'datetime', width: 120, align: 'center' },
				{ field: 'COMMENTS', title: '维修处理备注', type: 'datetime', width: 120, align: 'center' },
				{ field: 'InprogressDate', title: '维修处理时间', type: 'datetime', width: 120, align: 'center' },
				//{ field: 'RepairAssignTime', title: '维修分配时间', type: 'datetime', width: 120, align: 'center' },
            ],
            tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },
			Resource: null,
            Name: null,
            Names: [],
			Resources:[],
            
            

            //接口地址
            apiUrl: {
                GetNameObject: "/api/query/GetNameObject",
                GetEquipmentClockOff: "/api/query/GetEquipmentClockOff",
                GetEmployee: "/api/query/GetEmployee",
                Getclassname: "/api/query/Getclassname",
                EquipmentRepairClockOffMaint: "/api/cdo/EquipmentRepairClockOffMaint",
            },
        }
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
		getResource(query) {
				if (query) {
					let params = {
						objectCategory: "RESOURCE",
						cdo: "RESOURCE",
						name: query
					};
					this.http.get(this.apiUrl.GetNameObject, params).then(res => {
						if (res.Result == 1) {
							this.Resources = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
        GetEmployee(query) {
            if (query) {
                let params = {
                    objectCategory: "name",
                    //cdo: "PrintQueue",
                    name: query
                };
                this.http.get(this.apiUrl.GetEmployee, params).then(res => {
                    if (res.Result == 1) {
                        this.Names = res.Data;

                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
		 getclassname(query) {
				if (query) {
					let params = {
						//objectCategory: "RESOURCE",
						//cdo: "RESOURCE",
						classname: query
					};
					this.http.get(this.apiUrl.Getclassname, params).then(res => {
						if (res.Result == 1) {
							this.classnames = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
        reset() {

            this.classname = '',
            this.tableData = [];
            this.$refs.table.rowData = [];
            this.$refs.table.paginations.total = 0;
			this.Resource = '';
            this.searchPlanDate = null;
        },
        rowClick({
            row,
            column,
            index
        }) {
           //this.$refs.table.$refs.table.clearSelection();
            this.$refs.table.$refs.table.toggleRowSelection(row);
            // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
            // this.$refs.table.$refs.table.toggleRowSelection(row);
            //this.changeInventoryQty();
        },
        getRow() {
      
            this.$refs.table.load(null, true);
        },
        loadBefore(params, callBack) {
            params["classnamename"] = this.classname;
            params["Resource"] = this.Resource;
            //params["Name"] = this.Name;
            params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
            params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
            callBack(true)
        },
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                this.tableData = result.Data.tableData;
                this.$refs.table.rowData = result.Data.tableData;
                this.$refs.table.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },
        submitRow() {
            const rows = this.$refs.table.getSelected();
            if (rows.length == 0) {
                this.$message.error('请选需要关闭计时的维修单')
                return;
            }
            const validRows = rows.filter(row => row.OrderName && row.ResourceName);

            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                OrderName: rows[0].OrderName,
				Resource: rows[0].ResourceName,
                JobModel:rows[0].Jobmodelname,
				stagename: rows[0].stagename,
                //requestData: validRows
            };
            this.http.post(this.apiUrl.EquipmentRepairClockOffMaint, params, true).then(res => {
                if (res.Result == 1) {
                    this.reset();
                    this.$message.success("关闭计时成功");
                } else {
                    this.$message.error(res.Message);
                }
            });
        }
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>