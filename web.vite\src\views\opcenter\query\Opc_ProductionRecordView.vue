<template>
    <div class="page-header">
        <!-- 搜索条件 -->
        <div style="display: flex; margin-top: 5px">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工单</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getMfgOrder">
                        <el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工序</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchSpec" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getSpec">
                        <el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div>
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>课别</span>
                </label>
                <div style="margin-top: 5px;">
                    <el-select v-model="searchClassName" clearable placeholder="请选择课别" style="width: 200px">
                        <el-option v-for="item in classNames" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div>
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>组别</span>
                </label>
                <div style="margin-top: 5px;">
                    <el-select v-model="searchWorkcenter" clearable placeholder="请选择组别" style="width: 200px">
                        <el-option v-for="item in workcenters" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px; ">
                <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                    <span>生产时间</span>
                </label>
                <div style="margin-top: 5px;">
                    <el-date-picker v-model="searchDate" type="daterange" range-separator="To" start-placeholder="开始"
                        end-placeholder="结束" :size="size" style="width: 240px" format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD hh:mm:ss" />
                </div>
            </div>
        </div>
    </div>
    <!-- 数据执行 -->
    <div class="table-item">
        <div class="table-item-header">
            <!-- <div class="table-item-border"></div>-->
            <span class="table-item-text">生产记录汇总</span>
            <div class="table-item-buttons">
                <div>
                    <el-button icon="Search" @click="queryRow" plain>查询</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="masterTable" index :tableData="masterTableData" @rowClick="rowClick" :columns="masterTableCols"
            :height="258" :pagination-hide="true" :load-key="true" :column-index="true" :single="true"
            :url="apiUrl.getProductionRecord" @loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false"
            :ck="false"></vol-table>
    </div>
    <div class="table-item">
        <div class="table-item-header">
            <!-- <div class="table-item-border"></div>-->
            <span class="table-item-text">生产明细</span>
        </div>
        <vol-table ref="subTable" index :tableData="subTableData" :columns="subTableCols"
            :height="200" :load-key="false" :defaultLoadPage="true" :pagination-hide="true" 
            :column-index="true" :single="true" :ck="false"></vol-table>
    </div>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            //搜索框字段
            searchWorkcenter: null,
            searchClassName: null,
            classNames: [],
            searchSpec: null,
            searchMfgOrder: null,
            searchEmployeeGroup: null,
            searchDate: null,
            mfgorders: [],
            specs: [],
            workcenters: [],
            // employeeGroups: [],
            masterTableData: [],
            subTableData: [],

            masterTableCols: [
                { field: 'Spec', title: '工序', type: 'string', width: 120, align: 'center' },
                { field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },
                { field: 'ResourceName', title: '设备编号', type: 'string', width: 120, align: 'center' },
                { field: 'Product', title: '产品编号', type: 'string', width: 120, align: 'center' },
                { field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
                { field: 'ResourceName', hidden: true, title: '机台编号', type: 'string', width: 130, align: 'center' },
                { field: 'Qty', title: '生产数量', type: 'string', width: 80, align: 'center' },
                { field: 'ResourceTime', title: '标准机台工时', type: 'string', width: 100, align: 'center' },
                { field: 'StandardTime', title: '标准人工工时', type: 'string', width: 80, align: 'center' },
                { field: 'ActualResourceTime', title: '实际机台工时', type: 'string', width: 80, align: 'center' },
                { field: 'ActualWorkTime', title: '实际人工工时', type: 'string', width: 120, align: 'center' },
                { field: 'Detail', hidden: true, title: '', type: 'string', width: 120, align: 'center' },
            ],
            subTableCols: [
                { field: 'Spec', title: '工序', type: 'string', width: 120, align: 'center' },
                { field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },
                { field: 'Container', title: '批次', type: 'string', width: 120, align: 'center' },
                { field: 'TxnDate', title: '生产时间', type: 'string', width: 150, align: 'center' },
                { field: 'Product', title: '产品编号', type: 'string', width: 120, align: 'center' },
                { field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
                { field: 'ResourceName', hidden: true, title: '机台编号', type: 'string', width: 130, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
                { field: 'ResourceTime', title: '标准机台工时', type: 'string', width: 100, align: 'center' },
                { field: 'StandardTime', title: '标准人工工时', type: 'string', width: 80, align: 'center' },
                { field: 'ActualResourceTime', title: '实际机台工时', type: 'string', width: 80, align: 'center' },
                { field: 'ActualWorkTime', title: '实际人工工时', type: 'string', width: 80, align: 'center' },
            ],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getProductionRecord: '/api/query/getProductionRecord',
            }

        }
    },
    created() {
        this.getWorkCenter();
        this.getClassName();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        getMfgOrder(query) {
            if (query) {
                let params = {
                    cdo: "mfgorder",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.mfgorders = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getSpec(query) {
            if (query) {
                let params = {
                    cdo: "spec",
                    name: query,
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.specs = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        async getWorkCenter() {
            let params = {
                cdo: "WorkCenter"
            };
            this.http.get(this.apiUrl.getNameObject, params).then(res => {
                if (res.Result == 1) {
                    this.workcenters = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        async getClassName() {
            let params = {
                cdo: "W_ClassName"
            };
            this.http.get(this.apiUrl.getNameObject, params).then(res => {
                if (res.Result == 1) {
                    this.classNames = res.Data;
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        reset() {
            this.searchMfgOrder = null;
            this.searchSpec = null;
            this.searchDate = null;
            this.masterTableData = [];
            this.$refs.masterTable.rowData = [];
            this.$refs.masterTable.paginations.total = 0;
            this.subTableData = [];
            this.$refs.subTable.rowData = [];
            this.$refs.subTable.paginations.total = 0;
        },
        rowClick({
            row,
            column,
            index
        }) {
            // this.$refs.masterTable.$refs.masterTable.toggleRowSelection(row);
            this.subTableData = (JSON.parse(JSON.stringify(row.Detail)));
            this.$refs.subTable.rowData = JSON.parse(JSON.stringify(row.Detail));
            this.$refs.subTable.paginations.total = row.Detail.length;
        },
        queryRow() {
            this.masterTableData = [];
            this.$refs.masterTable.rowData = [];
            this.$refs.masterTable.paginations.total = 0;
            this.subTableData = [];
            this.$refs.subTable.rowData = [];
            this.$refs.subTable.paginations.total = 0;
            if (!this.searchMfgOrder) {
                this.$message.error("请选择工单");
                return;
            }
            this.$refs.masterTable.load(null, true);
        },
        loadBefore(params, callBack) {
            params["spec"] = this.searchSpec;
            params["mfgorder"] = this.searchMfgOrder;
            params["ClassName"] = this.searchClassName;
            params["workcenter"] = this.searchWorkcenter;
            params["starttime"] = this.searchDate != null ? this.searchDate[0] : null
            params["endtime"] = this.searchDate != null ? this.searchDate[1] : null
            callBack(true)
        },
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                this.masterTableData = result.Data.tableData;
                this.$refs.masterTable.rowData = result.Data.tableData;
                this.$refs.masterTable.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    // .table-item-text {
    // 	font-weight: bolder;
    // 	border-bottom: 1px solid #0c0c0c;
    // }
    .table-item-text {
        margin-top: 3px;
        padding-bottom: 6px;
        font-weight: bold;
        font-size: 15px;
        color: #484848;
        white-space: nowrap;
        border-bottom: 2px solid #676767;
        margin-bottom: -1px;
        letter-spacing: 1px;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196f3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>