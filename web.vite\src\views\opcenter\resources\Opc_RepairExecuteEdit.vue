<template>
  <vol-box :lazy="true" v-model="visible" :title="title" :width="width" :padding="5" :onModelClose="onModelClose">
    <div class="form-content" :style="style">
      <div class="form-content">
        <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules"></VolForm>
      </div>

      <div class="table-item">
        <div class="table-item-header">
          <div class="table-item-buttons" style="display:flex;justify-content: end;padding: 0px 0px 1% 0px">
            <!-- <el-button type="primary" @click="addRow" plain>{{ this.$ts('Add') }}</el-button> -->
            <!-- <el-button type="primary" @click="delRow" color="#f89898" plain>{{ this.$ts('Delete') }}</el-button> -->
          </div>
        </div>

        <!-- <vol-table ref="table" index :tableData="tableData" :columns="columns" :height="300" :pagination-hide="true" :ck="false"
          :clickEdit='true' :load-key="true" :column-index="true"></vol-table> -->
          
      </div>
    </div>
    <template #footer>
      <div>
        <el-button type="primary" size="small" @click="handleModelOk">{{ this.$ts('Confirm') }}</el-button>
        <el-button type="default" size="small" @click="closeModel">{{ this.$ts('Close') }}</el-button>
      </div>
    </template>
  </vol-box>
</template>
<script lang="jsx">
import VolBox from '@/components/basic/VolBox.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import {GlobalElMessageBox} from '@/mixins/commonMixin.js'
export default {
  components: {
    'vol-box': VolBox,
    'vol-table': VolTable,
    VolHeader,
    VolForm
  },
  mixins: [GlobalElMessageBox],
  data() {
    return {
      visible: false,
      model: [],
      title: this.$ts('Edit'),
      text: this.$ts('Default'),
      width: 1000,
      style: { 'max-height': '500px', 'padding': '1% 1%' },
      formFields: {
        JobSymptomCode: null,
        JobCauseCode: null,
        JobRepairCode: null,
        YP_ActuralWorkingHour: null,
        YP_RepairingRemark: null,
        YP_RepairingAttach: '',
      },
      formRules: [
        [
          {
            dataKey: "JobSymptomCode",
            data: [],
            title: this.$ts('JobSymptomCode'),
            placeholder: this.$ts('JobSymptomCode'),
            filter: true,
            required: false,
            field: "JobSymptomCode",
            type: "select",
            colSize: 6,
          },
          {
            dataKey: "JobCauseCode",
            data: [],
            title: this.$ts('JobCauseCode'),
            placeholder: this.$ts('JobCauseCode'),
            filter: true,
            required: false,
            field: "JobCauseCode",
            type: "select",
            colSize: 6,
          },
        ],
        [
          {
            dataKey: "JobRepairCode",
            data: [],
            title: this.$ts('Repair Reason'),
            placeholder: this.$ts('Repair Reason'),
            filter: true,
            required: false,
            field: "JobRepairCode",
            type: "select",
            colSize: 6,
          },
          {
            title: this.$ts('Actual working hours'),
            placeholder: this.$ts('Actual working hours'),
            required: true,
            field: "YP_ActuralWorkingHour",
            type: "number",
            colSize: 6,
          },
        ],
        [
          {
            title: this.$ts('Remark'),
            placeholder: this.$ts('Remark'),
            required: false,
            field: "YP_RepairingRemark",
            type: "textarea",
            min: 3,
            max: 5,
            colSize: 6,
          },
        ],
        // [
        //   {
        //     title: this.$ts('Attachment'),
        //     required: false,
        //     field: "YP_RepairingAttach",
        //     type: "img",
        //     multiple: true,
        //     maxFile: 5,
        //     maxSize: 5,
        //     url: "api/Demo_Order/Upload",
        //     colSize: 12,
        //   }
        // ],
      ],
      paginationHide: true,
      columns: [
        { field: 'RepairingDetailName', title: this.$ts('Item Name'), type: 'string', width: 80, align: 'center', edit: { type: 'text' } },
        { field: 'Content', title: this.$ts('Content'), type: 'string', width: 80, align: 'center', edit: { type: 'text' } },
        {
          title: this.$ts('Operation'),
          field: '操作',
          width: 20,
          align: 'center',
          render: (h, { row, column, index }) => {
            return (
              <div>
                <el-button
                  onClick={($e) => {
                    $e.stopPropagation();
                    this.deleteClick(row, column, index);
                  }}
                  type="primary"
                  plain
                  style="height:26px; padding: 10px !important;"
                > {this.$ts('Delete')} </el-button>
                {/* <span
                  style="font-size: 13px;color: #409eff;margin: 5px 0 0 10px;cursor: pointer;"
                  class="el-dropdown-link" onClick={($e) => {
                    $e.stopPropagation();
                    this.deleteClick(row, column, index);
                  }}
                >{this.$ts('Delete')}
                </span> */}
              </div>
            );
          }
        }
      ],
      tableData: [],
      url: {
        add: '/api/CDO/CarryOutJobOrder',
      }
    };
  },
  methods: {

    add() {
      this.edit({});
    },
    edit(record) {
      this.model = Object.assign([], record);
      console.log(this.model, 'record')

      // if (!this.model.id) {
      // this.model = {};
      // } else {
      // this.formFields = Object.assign([], this.model)
      // }
      this.visible = true;
    },


    addRow() {
      this.$refs.table.addRow({ RepairingDetailName: "", Content: "" })
    },

    deleteClick(row, column, index) {
      // console.log(row, column, index,'row, column, index');
      // this.$refs.table.edit.rowIndex = index;
      // this.$refs.table.delRow();
      this.tableData.splice(index, 1);
    },

    handleModelOk() {
      this.$refs.form.validate(() => {
        let fileData = []
        let tableDataVal = []
        if (this.formFields.YP_RepairingAttach !== null && this.formFields.YP_RepairingAttach.length == 0) {
          this.formFields.YP_RepairingAttach = ''
        } else {
          fileData = this.formFields.YP_RepairingAttach.map(item => item.path)
          this.formFields.YP_RepairingAttach = fileData.join()
        }
        this.tableData.forEach(item => {
          tableDataVal.push({
            Content: item.Content,
            RepairingDetailName: item.RepairingDetailName,
          })
        })
        // this.formFields.YP_RepairingDetailList = tableDataVal
        this.formFields.JobOrderName = this.model[0].JobOrderName
        this.formFields.YP_TaskStartTime = this.model[0].YP_TaskStartTime
        let params = this.formFields
        console.log(params, 'params');
        this.http.post(this.url.add, params, true).then((res) => {
          if (res.status == 1) {
            this.$emit('handleModelOk',true);
            this.onModelClose('success');
            this.$message.success(res.message);
            this.$message.success(this.$ts('Execution Success!'));
          } else {
            this.$message.error(this.$ts('Execution failed, please contact MES team.'))
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.error(err.message)
        })
      })
    },

    reset() {
      this.$refs.form.reset(this.formFields);
    },

    onModelClose(status) {
      if(status == 'success'){
        this.closeModel()
      }else{
        this.handleGlobalMessageBox()
      }
    },
    closeModel() {
      this.reset()
      this.model = [];
      this.tableData = [];
      this.visible = false;
    }
  }
};
</script>