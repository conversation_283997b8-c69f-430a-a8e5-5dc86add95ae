<template>
	<!-- 退料区域 -->
	<div class="table-item-header">
		<div class="table-item-border"></div> <span class="table-item-text">退料</span>
	</div>
	<div style="display: flex;margin-top: 5px;">
		<div style="margin-left: 10px; ">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>物料批次</span>
			</label>
			<div style="margin-top: 5px;">
				<el-input style="width: 200px;" @change="changeContainer" v-model="container" clearable placeholder="请输入"></el-input>
			</div>
		</div>
        <div style="margin-left: 10px; ">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>报废数量</span>
			</label>
			<div style="margin-top: 5px;">
				<el-input-number style="width: 100px;" v-model="adjustQty" :min="0" :precision="0" :step="1" controls-position="right" placeholder="请输入"></el-input-number>
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>报废原因</span>
			</label>
			<div style="margin-top: 5px;">
				<!-- <el-input v-model="Spec"></el-input> -->
				<el-select v-model="reason" clearable filterable placeholder="请选择" style="width: 200px">
					<el-option v-for="item in reasons" :key="item.Name" :label="item.Name" :value="item.Name" />
				</el-select>
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span> </span>
			</label>
			<div style="margin-top: 7px;">
				<el-button type="success" icon="Check" @click="submitRow" plain>提交</el-button>
			</div>
		</div>
	</div>
    <el-text tag="ins">注：选择报废原因前请输入批次，将根据批次当前工序带出原因组。</el-text>

	<VolForm :labelWidth="90" ref="form" :loadKey="false" :formFields="formFields" :formRules="formRules">

	</VolForm>
	<el-divider />
	<div class="table-item-header">
		<div class="table-item-border"></div> <span class="table-item-text">报废明细查询</span>
	</div>

	<!-- 搜索条件 -->
	<div style="display: flex;margin-top: 5px;">
		<div style="margin-left: 10px;">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>物料批次</span></label>
			<div style=" margin-top: 5px;">
				<el-input style="width: 200px;" v-model="searchContainer" clearable placeholder="请输入"></el-input>
			</div>
		</div>
		<!-- <div style="margin-left: 10px; ">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>生产部门</span>
			</label>
			<div style="margin-top: 5px;">
				<el-select v-model="searchDepartment" clearable filterable placeholder="请选择" style="width: 200px">
					<el-option v-for="item in departments" :key="item.Name" :label="item.Name" :value="item.Name" />
				</el-select>
			</div>
		</div> -->
		<div style="margin-left: 10px;">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>生产车间</span>
			</label>
			<div style="margin-top: 5px;">
				<!-- <el-input v-model="Spec"></el-input> -->
				<el-select v-model="searchWorkcenter" clearable filterable placeholder="请选择" style="width: 200px">
					<el-option v-for="item in workcenters" :key="item.Name" :label="item.Name" :value="item.Name" />
				</el-select>
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>线边仓</span></label>
			<div style=" margin-top: 5px;">
				<!-- <el-input v-model="ResGroup"></el-input> -->
				<el-select v-model="searchLocation" clearable filterable placeholder="请选择" style="width: 200px">
					<el-option v-for="item in localtions" :key="item.Name" :label="item.Name" :value="item.Name" />
				</el-select>
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>物料编号</span></label>
			<div style=" margin-top: 5px;">
				<!-- <el-input v-model="ResGroup"></el-input> -->
				<el-select v-model="searchProduct" clearable filterable placeholder="请选择" remote-show-suffix :remote="true"
				:remote-method="remoteMethod" :loading="loading" style="width: 200px">
					<el-option v-for="item in products" :key="item.Name" :label="item.Name" :value="item.Name" />
				</el-select>
			</div>
		</div>
		<div style="margin-left: 10px; ">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span>报废时间</span>
			</label>
			<div style="margin-top: 5px;">
				<el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
					end-placeholder="结束" :size="size" style="width: 240px" />
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 200px; margin-left: 5px; font-size: 16px;">
				<span> </span>
			</label>
			<div style="margin-top: 7px;">
				<el-button icon="Search" @click="queryRow" plain>查询</el-button>
			</div>
		</div>
	</div>
	<div class="table-item">
		<div class="table-item-header">
			<div class="table-item-buttons">
				<div>
					<!-- <el-button icon="Refresh" type="info" plain @click="reset">重置</el-button> -->
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="220"
			:pagination-hide="false" :load-key="true" :column-index="true" :ck="false"></vol-table>
	</div>
</template>
<script lang="jsx">
	import VolTable from "@/components/basic/VolTable.vue";
    import VolForm from "@/components/basic/VolForm.vue";
	import { mapState } from 'vuex';

    export default {
		components: {
			'vol-table': VolTable,
			VolForm,
		},
        data() {
			return {
				container: '',
                adjustQty: '',
				reason: '',
				searchContainer: '',
				searchDepartment: '',
				searchWorkcenter: '',
				searchLocation: '',
				searchProduct: '',
				searchTxnDate:null,

				reasons: [],
				departments:[],
				workcenters:[],
				localtions:[],
				products:[],

                formFields:{
                    department: null,				
                    workcenter: null,				
                    inventory: null,	
                    container: null,		
                    product: null,	
					productType: null,			
                    modelNumber: null,				
                    uom: null,				
                    vendor: null,				
                    description: null,		
                    qty: null,		
                    mfgorder: null,
					comments: null
				},
				formRules: [
					// [
					// 	{ type: "text", title: "物料批次", readonly: false, required: true, placeholder: "请输入", field: "container", colSize: 2 },
					// 	{ type: "select", title: "退料原因", data: [], readonly: false, required: true, placeholder: "请选择", field: "container", colSize: 2 },
					// ],
					[
						{ type: "textarea", title: "备注", readonly: false, required: false, placeholder: " ", field: "comments", colSize: 11 },
					],
					[
						{ type: "text", title: "物料编号", readonly: true, required: false, placeholder: " ", field: "product", colSize: 2 },
						{ type: "text", title: "物料描述", readonly: true, required: false, placeholder: " ", field: "description", colSize: 3 },
						{ type: "text", title: "规格型号", readonly: true, required: false, placeholder: " ", field: "modelNumber", colSize: 3 },
						{ type: "text", title: "物料类型", readonly: true, required: false, placeholder: " ", field: "productType", colSize: 1 },
						{ type: "text", title: "批次数量", readonly: true, required: false, placeholder: " ", field: "qty", colSize: 1 },
						{ type: "text", title: "单位", readonly: true, required: false, placeholder: " ", field: "uom", colSize: 1 },
					],

					[
						// { type: "select", title: "领用工单", readonly: false, required: true,data:[], placeholder: "请选择", field: "mfgorder", colSize: 2 },
						{ type: "text", title: "生产部门", readonly: true, required: false, placeholder: " ", field: "department", colSize: 2 },
						{ type: "text", title: "生产车间", readonly: true, required: false, placeholder: " ", field: "workcenter", colSize: 2 },
						{ type: "text", title: "线边仓", readonly: true, required: false, placeholder: " ", field: "location", colSize: 2 },
						{ type: "text", title: "供应商", readonly: true, required: false, placeholder: " ", field: "vendor", colSize: 2 },
					],
                ],

				columns: [
					{ field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
					{ field: 'Container', title: '物料批次', type: 'string', width: 130, align: 'center' },
					{ field: 'Qty', title: '当前数量', type: 'string', width: 80, align: 'center' },
                    // { field: 'LastQty', title: '调整前数量', type: 'string', width: 130, align: 'center' },
                    { field: 'EnteredQty', title: '报废数量', type: 'string', width: 130, align: 'center' },
					// { field: 'OriginalQty', title: '原始数量', type: 'string', width: 80, align: 'center' },
					{ field: 'Reason', title: '报废原因', type: 'string', width: 130, align: 'center' },
                    { field: 'Employee', title: '操作人', type: 'string', width: 130, align: 'center' },
					{ field: 'TxnDate', title: '操作时间', type: 'datetime', width: 150, align: 'center' },
					{ field: 'Comments', title: '备注', type: 'string', width: 130, align: 'center' },
					{ field: 'Status', title: '批次状态', type: 'string', width: 130, align: 'center' },
					{ field: 'Department', title: '生产部门', type: 'string', width: 80, align: 'center' },
					{ field: 'WorkCenter', title: '生产车间', type: 'string', width: 80, align: 'center' },
					{ field: 'Location', title: '线边仓', type: 'string', width: 80, align: 'center' },
					{ field: 'Vendor', title: '供应商', type: 'string', width: 80, align: 'center' },
					{ field: 'Product', title: '物料编号', type: 'string', width: 100, align: 'center' },
					{ field: 'Description', title: '物料描述', type: 'string', width: 100, align: 'center' },
					{ field: 'ProductType', title: '物料类型', type: 'string', width: 100, align: 'center' },
					{ field: 'ModelNumber', title: '规格型号', type: 'string', width: 100, align: 'center' },
					{ field: 'Uom', title: '单位', type: 'string', width: 100, align: 'center' },
					// { field: 'MfgOrder', title: '领用工单', type: 'string', width: 130, align: 'center' },
				],
				tableData: [],

				//接口地址
				apiUrl: {
					getRevisionObject: "/api/query/GetRevisionObject",
					getNameObject: "/api/query/GetNameObject",
					getAdjustReason: "/api/query/getReason",
					getLocation: "/api/query/getLocation",
                    getMaterialInventoryInfo: "/api/query/getMaterialInventoryInfo",
					changeQty: '/api/CDO/ChangeQty',
				},
			}
		},
		created() {
			// this.getProducts();
			// this.getVendors();
			// this.getMfgorder();
			// this.getQtyAdjustReason();
			this.getDepartment();
			this.getWorkCenter();
			this.getLocation();
		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
        methods: {
			remoteMethod(query) {
				if (query) {
					let params = {
						cdo: "Product",
						name: query
					};
					this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
						if (res.Result == 1) {
							this.products = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			// async getProducts(){
			// 	let params = {
			// 		cdo: "product"
			// 	};
			// 	this.http.get(this.apiUrl.getRevisionObject,params).then(res => {
			// 		if (res.Result == 1) {
			// 			this.products = res.Data;
			// 		} else {
			// 			this.$message.error(res.Message);
			// 		}
			// 	});
			// },
			async getVendors(){
				let params = {
					cdo: "vendor"
				};
				this.http.get(this.apiUrl.getNameObject,params).then(res => {
					if (res.Result == 1) {
						this.formRules[1][5].data = res.Data.map(s=> ({ key: s.Name, value: s.Name }));
					} else {
						this.$message.error(res.Message);
					}
				});
			},
			async getAdjustReason(){
				let params = {
					container: this.container
				};
				this.http.get(this.apiUrl.getAdjustReason,params).then(res => {
					if (res.Result == 1) {
						this.reasons = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			},
			async getDepartment(){
				let params = {
					cdo: "Department"
				};
				this.http.get(this.apiUrl.getNameObject,params).then(res => {
					if (res.Result == 1) {
						this.departments = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			},
            async getWorkCenter(){
				let params = {
					cdo: "WorkCenter"
				};
				this.http.get(this.apiUrl.getNameObject,params).then(res => {
					if (res.Result == 1) {
						this.workcenters = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			},
			async getLocation(){
				let params = {
					cdoName: decodeURIComponent('誉品')
				};
				this.http.get(this.apiUrl.getLocation,params).then(res => {
					if (res.Result == 1) {
						this.localtions = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			},
			async getProductInfo(value){
				let params = {
					name: value
				};
				this.http.get(this.apiUrl.getProductInfo,params).then(res => {
					if (res.Result == 1) {
						this.formFields.description = res.Data[0].Description;
						this.formFields.productType = res.Data[0].ProductType;
						this.formFields.modelNumber = res.Data[0].ModelNumber;
						this.formFields.uom = res.Data[0].Uom; 
					} else {
						this.$message.error(res.Message);
					}
				});
			},
			changeContainer() {
				this.$refs.form.reset();
				if (this.container != '') {
					let params = {
						type: 'change',
						container: this.container
					};
					this.http.get(this.apiUrl.getMaterialInventoryInfo, params).then(res => {
						if (res.Result == 1) {
							if(res.Data.length > 0){
								if(res.Data[0].Status == '2'){//关闭
									this.$message.error('当前批次'+this.container+'已关闭，不允许退料。');
									this.container = '';
									return;
								}
								if(res.Data[0].Status == '4'){//消耗
									this.$message.error('当前批次'+this.container+'在使用中(机台消耗)，不允许退料。');
									this.container = '';
									return;
								}
								this.formFields.product = res.Data[0].Product;
								this.formFields.description = res.Data[0].Description;
								this.formFields.productType = res.Data[0].ProductType;
								this.formFields.modelNumber = res.Data[0].ModelNumber;
								this.formFields.uom = res.Data[0].Uom;
								this.formFields.qty = res.Data[0].Qty;
								this.formFields.department = res.Data[0].Department;
								this.formFields.workcenter = res.Data[0].WorkCenter;
								this.formFields.location = res.Data[0].Location;
								this.formFields.vendor = res.Data[0].Vendor;
                                this.getAdjustReason();
							}
							else{
								this.$message.error('未找到当前'+this.container+'批次信息。');
							}
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
            submitRow() {
				if (this.container == '' || this.adjustQty == '' || this.reason == '') {
				    this.$message.error('物料批次/调整原因不能为空，请检查。')
				    return;
				}
				
                let params = {
                    user: this.userInfo.userName,
                    password: this.userInfo.userPwd,
                    container: this.container,
                    adjustQty: this.adjustQty - this.formFields.qty,
                    reason: this.reason,
                    comments: this.formFields.comments,
                };
                this.http.post(this.apiUrl.changeQty, params, true).then(res => {
                    if (res.Result == 1) {
                        this.$message.success(res.Message);
                        this.container = '';
                        this.adjustQty = '';
                        this.reason = '';
                        this.$refs.form.reset();
                    } else {
                        this.$message.error(res.Message);
                    }
                });	
				
			},
            queryRow() {
				if (this.searchContainer == '' && this.searchDepartment == '' && 
					this.searchWorkcenter == '' && this.searchLocation == '' &&
					this.searchProduct == '' && this.searchTxnDate == null
				) {
				    this.$message.error('请输入查询条件。')
				    return;
				}
				let params = {
					type: 'adjust_query',
					container: this.searchContainer,
					department: this.searchDepartment,
					workcenter: this.searchWorkcenter,
					location: this.searchLocation,
					product: this.searchProduct,
					startDate: this.searchTxnDate != null?this.searchTxnDate[0] : null,
					endDate: this.searchTxnDate != null?this.searchTxnDate[1] : null,
				};
				this.http.get(this.apiUrl.getMaterialInventoryInfo, params, true).then(res => {
					if (res.Result == 1) {
						this.tableData = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});	
			},           

        }
    }
</script>
<style lang="less" scoped>
	.table-item-header {
		display: flex;
		align-items: center;
		padding: 6px;

		.table-item-border {
			height: 15px;
			background: rgb(33, 150, 243);
			width: 5px;
			border-radius: 10px;
			position: relative;
			margin-right: 5px;
		}

		.table-item-text {
			font-weight: bolder;
		}

		.table-item-buttons {
			flex: 1;
			text-align: right;
		}

		.small-text {
			font-size: 12px;
			color: #2196F3;
			margin-left: 10px;
			position: relative;
			top: 2px;
		}
	}
</style>