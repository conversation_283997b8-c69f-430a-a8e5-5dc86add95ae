<template>
    <div style="display: flex;margin-top: 5px;">
            <div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>课别</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="classname" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getclassname">
                    <el-option v-for="item in classnames" 
					:key="item.classname" 
					:label="item.classname" 
					:value="item.classname" />
                </el-select>
            </div>
        </div>
		<div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>组别</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="Workcentername" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="GetWorkcenter">
                    <el-option v-for="item in Workcenternames" 
					:key="item.Workcentername" 
					:label="item.Workcentername" 
					:value="item.Workcentername" />
                </el-select>
            </div>
        </div>
		<div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>产品编码</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchProduct" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getProduct">
                        <el-option v-for="item in products" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
                    </el-select>
                </div>
            </div>
         	<div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工单</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getMfgOrder">
                        <el-option v-for="item in mfgorders" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
                    </el-select>
                </div>
            </div>   
		<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>日期筛选</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
			</div>
		</div>
    </div>
    <!-- 数据执行 -->
    <div class="table-item" style="width: 100%; overflow-x: auto;">
        <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">工单生产进度查询</span>
            <div class="table-item-buttons">
                <div>
                    <el-button icon="Search" @click="getRow" plain>查询</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="518"
            :pagination-hide="false" :load-key="true" :defaultLoadPage="false" :single="true"
            :url="apiUrl.GetWorkorderProductionProgressReport"
            @loadBefore="loadBefore" @loadAfter="loadAfter" :column-index="true" :ck="true">
        </vol-table>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            //searchMfgOrder: null,
            searchPlanDate:null,
			Resource: null,
            Name: null,

            columns: [
				{ field: 'classnamename', title: '课别', type: 'string', width: 130, align: 'center' },
				{ field: 'workcentername', title: '组别', type: 'string', width: 130, align: 'center' },					
				{ field: 'mfgordername', title: '工单号', type: 'string', width: 110, align: 'center' },
                { field: 'containername', title: '生产批次', type: 'string', width: 120, align: 'center' },
				{ field: 'productfamily', title: '产品系列', type: 'string', width: 150, align: 'center' },
                { field: 'product', title: '产品编码', type: 'string', width: 120, align: 'center' },
				{ field: 'description', title: '产品名称', type: 'string', width: 120, align: 'center' },
				{ field: 'ccode', title: '客户编码', type: 'string', width: 120, align: 'center' },
				{ field: 'qty', title: '计划生产数量', type: 'string', width: 120, align: 'center'},
				{ field: 'plannedstartdate', title: '计划开始时间', type: 'datetime', width: 120, align: 'center' },
				{ field: 'plannedcompletiondate', title: '计划完成时间', type: 'datetime', width: 120, align: 'center' },
            { field: 'ispaigong', title: '是否派工', type: 'string', width: 120, align: 'center' },
            { field: 'orderstatusname', title: '工单状态', type: 'string', width: 120, align: 'center' },
            { field: 'paistardate', title: '工单开始时间', type: 'datetime', width: 120, align: 'center' },
            { field: 'Rationalityrate', title: '产出合理性预警', type: 'string', width: 120, align: 'center'},
            { field: 'orderendtime', title: '工单结束时间', type: 'datetime', width: 120, align: 'center' },
            { field: 'operationname', title: '当前工序', type: 'string', width: 120, align: 'center' },
            { field: 'resourcename', title: '生产机台', type: 'string', width: 120, align: 'center' },
            { field: 'tool', title: '绑定的模具', type: 'string', width: 120, align: 'center' },
            { field: 'isstart', title: '是否创批', type: 'string', width: 120, align: 'center' },
            { field: 'containerqty', title: '已创批数量', type: 'string', width: 120, align: 'center' },
            { field: 'passtime', title: '已生产时长', type: 'string', width: 120, align: 'center' },
            { field: 'CAPACITY', title: '标准产能', type: 'string', width: 120, align: 'center' },
            { field: 'Theoryqty', title: '理论生产数量', type: 'string', width: 120, align: 'center' },
            { field: 'WAREHOUSINGQTY', title: '实际生产数量', type: 'string', width: 120, align: 'center' },
            { field: 'ProgressWarning', title: '进度预警', type: 'string', width: 120, align: 'center'},
            { field: 'ScrapQty', title: '报废数量', type: 'string', width: 120, align: 'center' },
            { field: 'REPORTQTY', title: '已报工数量', type: 'string', width: 120, align: 'center' },
            { field: 'InnerQty', title: '已入库数量', type: 'string', width: 120, align: 'center' },
            { field: 'Planrate', title: '计划达成率', type: 'string', width: 120, align: 'center' },
           // { field: 'tool', title: '理论生产数量', type: 'string', width: 120, align: 'center' },
            ],
            tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },
			Resource: null,
			Resources:[],
            searchWorkcenter: null,
            mfgorders: [],
            searchMfgOrder: null,
            Workcentername: null,
            Workcenternames: [],
            classname: null,
			classnames:[],
            product: null,
			products:[],
			searchProduct:'',

            //接口地址
            apiUrl: {
                GetNameObject: "/api/query/GetNameObject",
                Getclassname: "/api/query/Getclassname",
				GetWorkcenter: "/api/query/GetWorkcenter",
                //GetNameObject: "/api/query/getNameObject",
                GetWorkorderProductionProgressReport: "/api/query/GetWorkorderProductionProgressReport",
                getRevisionObject: "/api/query/GetRevisionObject",
            },
        }
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        }),
    },
    methods: {
		getResource(query) {
				if (query) {
					let params = {
						objectCategory: "RESOURCE",
						cdo: "RESOURCE",
						name: query
					};
					this.http.get(this.apiUrl.GetNameObject, params).then(res => {
						if (res.Result == 1) {
							this.Resources = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
        getclassname(query) {
				if (query) {
					let params = {
						//objectCategory: "RESOURCE",
						//cdo: "RESOURCE",
						classname: query
					};
					this.http.get(this.apiUrl.Getclassname, params).then(res => {
						if (res.Result == 1) {
							this.classnames = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
        GetWorkcenter(query) {
            if (query) {
                let params = {
                    //cdo: "mfgorder",
                    Workcentername: query,
                };
                this.http.get(this.apiUrl.GetWorkcenter, params).then(res => {
                    if (res.Result == 1) {
                        this.Workcenternames = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query
				};
				this.http.get(this.apiUrl.GetNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
        getProduct(query) {
                if (query) {
                    let params = {
                        cdo: "product",
                        name: query
                    };
                    this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                        if (res.Result == 1) {
                            this.products = res.Data;
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },       
        reset() {
            //this.searchContainer = null;
            //this.searchInventoryQty = null;
            this.searchMfgOrder = null;
            this.tableData = [];
            this.$refs.table.rowData = [];
            this.$refs.table.paginations.total = 0;
			this.classname = '';
			this.Workcentername = '';
            this.searchMfgOrder = null;
        },
        rowClick({
            row,
            column,
            index
        }) {
           //this.$refs.table.$refs.table.clearSelection();
            this.$refs.table.$refs.table.toggleRowSelection(row);
            // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
            // this.$refs.table.$refs.table.toggleRowSelection(row);
            //this.changeInventoryQty();
        },
        getRow() {
      
            this.$refs.table.load(null, true);
        },
        loadBefore(params, callBack) {
           //params["container"] = this.searchContainer;
            params["mfgordername"] = this.searchMfgOrder;
            params["product"] = this.searchProduct;
            params["classnamename"] = this.classname;
            params["Workcentername"] = this.Workcentername;
            params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
            params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
            callBack(true)
        },
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                this.tableData = result.Data.tableData;
                this.$refs.table.rowData = result.Data.tableData;
                this.$refs.table.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        }
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 25px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>