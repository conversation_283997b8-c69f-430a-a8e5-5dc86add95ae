<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <link rel="stylesheet" type="text/css" media="print" href="/print-lock.css">
  <link rel="stylesheet" type="text/css" media="print" href="/print-lock.css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>MES</title>
  <script src="/src/api/config.js"></script>
  <script>
    var lang_storage_key = 'vol_lang';

    window._CONFIG = {};
    //文件查看路径，服务器IP
    window._CONFIG['UPLOAD_URL'] = 'http://*********:80/'
  </script>
</head>

<body>
  <noscript>
    <strong>We're sorry but vol doesn't work properly without JavaScript enabled. Please enable
      it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>

<!DOCTYPE html>

<style>
  html,
  body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
  }

  * {
    box-sizing: border-box;
  }

  .el-loading {
    z-index: 999999;
  }

  .el-table th {
    display: table-cell !important;
  }

  .el-loading .el-loading-spinner {
    padding: 7px;
    background: #ececec;
    width: 200px;
    color: red;
    left: 0;
    right: 0;
    margin: 0 auto;
    border-radius: 5px;
    border: 1px solid #a0a0a0;
  }

  h1,
  h2,
  h3,
  h4 {
    margin: 0;
  }

  .v-dialog {
    border-radius: 5px;
    top: 50%;
    /* margin-top: -220px !important; */
  }

  .v-dialog .el-dialog__header {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 0px 13px;
    line-height: 53px;
    border-bottom: 1px solid #e2e2e2;
    height: 50px;
    color: white;
    font-weight: bold;
    font-size: 14px;
    background-image: linear-gradient(135deg, #0cd7bd 10%, #50c3f7);
  }

  .v-dialog .el-dialog__header .el-dialog__headerbtn {
    top: 3px;
    right: 0px;
  }

  .v-dialog .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
    font-size: 19px;
    color: white;
  }

  .v-dialog .el-dialog__body {
    padding: 0;
  }

  .el-message {
    z-index: 3500 !important;
  }

  .v-date-range .el-input__inner {
    padding: 0 15px 0 8px;
  }

  .v-date-range .el-input__suffix .el-input__icon {
    display: table-caption;
    background: white;
    margin: 1px;
    height: auto;
    margin-right: -4px;
    height: 33px;
    width: 19px;
    font-size: 13px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
  }

  .v-date-range .el-icon-circle-check {
    display: none !important;
  }

  .v-dialog .el-dialog__header {
    margin-right: 0;
  }

  .el-button {
    font-size: 12px !important;
  }

  .el-button--small {
    padding: 15px 15px !important;
  }
</style>