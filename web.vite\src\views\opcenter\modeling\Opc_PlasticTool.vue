<template>
    <div class="page-Container">
      <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules">
        <div style="text-align: end; margin-top: 0px; width: 100%">
          <div style="margin-right: 20px; margin-top: -39px">
            <el-button type="primary" plain @click="search">查询</el-button>
            <el-button type="success" icon="Plus" @click="addRow" plain>新建</el-button>
            <el-button type="primary" icon="Edit" @click="editRow" plain>编辑</el-button>
            <el-button type="danger" icon="Delete" @click="delRow" plain>删除</el-button>
            <el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
          </div>
        </div>
      </VolForm>
      <vol-table ref="table"   index :column-index="true" :loadKey="true"
        :columns="columns" :tableData="tableData" :ck="true" :pagination-hide="false" :max-height="380"
        :url="ApiUrl.QueryResourceInfo" :defaultLoadPage="false" @loadBefore="loadBefore" :single="true"
        @loadAfter="loadAfter"></vol-table>
    </div>
    <vol-box ref="box" title="设备维护" :lazy="true" v-model="showEdit" :width="1500" :height="800">
      <VolForm ref="boxform" :loadKey="true" :formFields="boxFields" :formRules="boxRules">
      </VolForm>
      <div style="text-align: center; margin-top: 100px">
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </vol-box>
  </template>
  
  <script lang="jsx">
  import VolTable from '@/components/basic/VolTable.vue'
  import VolForm from '@/components/basic/VolForm.vue'
  import VolHeader from '@/components/basic/VolHeader.vue'
  import VolBox from '@/components/basic/VolBox.vue'
  import { mapState } from 'vuex'
  export default {
    components: {
      VolHeader,
      VolForm,
      'vol-table': VolTable,
      'vol-box': VolBox
    },
    computed: {
    ...mapState({
      //获取当前用户的信息
      userInfo: (state) => state.userInfo,
      //获取当前用户的权限
      permission: (state) => state.permission
    })
  },
    data() {
      return {
        ApiUrl: {
          QueryResourceInfo: '/api/Query/QueryResourceInfoDetails', //查询设备台账信息
          MesResourceMaintenance: '/api/CDO/MaintenanceMesTool', //tool维护
          GetNameObject: "/api/query/getNameObject", //获取下拉框
        },
        showEdit: false, //编辑框是否显示
        headerFields: {
            cdoName: 'Tools', //cdo名称
            cdoType: '塑模', //cdo类型
            ResourceName: null, //设备编号
            CreateUser: null, //创建人
            ProductNo:null,//料号
            StarchTime: null,
        },
        headerRules: [
          [
            {
              title: this.$ts('模具编号'),
              placeholder: this.$ts(''),
              required: false,
              field: 'ResourceName',
              type: 'string',
              colSize: 2
            },
            {
              title: this.$ts('创建人'),
              placeholder: this.$ts(''),
              required: false,
              field: 'CreateUser',
              type:'string',
              colSize: 2
            },
            {
              title: this.$ts('料号'),
              placeholder: this.$ts(''),
              required: false, 
              field: 'ProductNo',
              type:'string',
              colSize: 2
            }
          ],
          [
            {
              title: this.$ts('选择时间'),
              placeholder: this.$ts(''),
              required: false,
              field: 'StarchTime',
              type: 'datetime',
              range: true,
              colSize: 4
            }
          ]
        ],
        columns: [
          { title: this.$ts('ID'), field: 'ResourceID', width: 100, align: 'center', hidden: true, type: 'string' },
          { field: 'ResourceName', title: '模具编号', type: 'string', width: 130, align: 'center', hidden: false },
          //产品名称
          { field: 'ProductDesc', title: '产品名称', type:'string', width: 130, align: 'center', hidden: false },
          //料号
          { field: 'ProductNo', title: '料号', type:'string', width: 130, align: 'center', hidden: false },
          { field: 'ModelDate', title: '开模时间', type:'string', width: 130, align: 'center', hidden: false },
          //设计次数
          { field: 'Frequency', title: '设计次数', type:'string', width: 130, align: 'center', hidden: false },
          //塑模材料
          { field: 'PlasticMaterial', title: '塑模材料', type:'string', width: 130, align: 'center', hidden: false },
          //配比原料
          { field: 'IngredientRatio', title: '配比原料', type:'string', width: 130, align: 'center', hidden: false },
          //产品单重
          { field: 'SingleWeight', title: '产品单重', type:'string', width: 130, align: 'center', hidden: false },
          //水口单重
          { field: 'WaterWeight', title: '水口单重', type:'string', width: 130, align: 'center', hidden: false },
          //适用机台吨位
          { field: 'MachineTonnage', title: '适用机台吨位', type:'string', width: 130, align: 'center', hidden: false },
          //生产周期
          { field: 'ProductionCycle', title: '生产周期', type:'string', width: 130, align: 'center', hidden: false },
          //创建人
          { field: 'CreateUser', title: '创建人', type:'string', width: 130, align: 'center', hidden: false },
          //创建时间
          { field: 'CreateTime', title: '创建时间', type:'string', width: 130, align: 'center', hidden: false },
          //模具类型
          { field: 'ModelType', title: '模具类型', type:'string', width: 130, align: 'center', hidden: true },
          { field: 'Indexs', title: 'Indexs', type:'string', width: 130, align: 'center', hidden: true },
          
        ],
        tableData: [],
  
        boxFields: {
            User: null,
            Password:null,
          EventName: null, //事件名称
          ResourceID: null, //ID
          ResourceName: null, //设备编号
          NewResourceName: null, //新设备编号
          ModelType: "塑模", //型号类别：塑模和冲模
          ModelDate: null, //开模时间
          Indexs: null, //指标
          Frequency: null, //设计次数
          ProductDesc: null, //产品名称
          ProductNo: null, //料号
          MachineTonnage: null, //适用机台吨位
          MaterialQuality: null, //材料材质
          ProductionCycle: null, //生产周期
          MaterialQuantity: null, //料带数量
          MaterialThickness: null, //材料厚度
          MaterialWidth: null, //材料宽度
          MaterialType: null, //材料类型
          OutQty: null, //出几支
          PlasticMaterial: null, //材料材质
          RushNumber: null, // 冲数/分钟
          SingleWeight: null, //产品单重
          WaterWeight: null, //水口单重
          IngredientRatio: null, //配比原料
          MachineParts: null, //机器互换件
        },
        boxRules: [
          [
            {
              title: this.$ts('设备编号'),
              placeholder: this.$ts(''),
              field: 'ResourceName',
              type: 'string',
              colSize: 2,
              readonly: true,
              hidden: true,
            },
            {
              title: this.$ts('模具编号'),
              placeholder: this.$ts(''),
              field: 'NewResourceName',
              type:'string',
              colSize: 2, 
            },
            {
              //开模时间
              title: this.$ts('开模时间'),
              placeholder: this.$ts(''),
              field: 'ModelDate',
              type: 'datetime',
              colSize: 2,
            },
            {
                //产品名称
                title: this.$ts('产品名称'),
                placeholder: this.$ts(''),
                field: 'ProductDesc',
                type: 'string',
                colSize: 2,
            },
            {
                //料号
                title: this.$ts('料号'),
                placeholder: this.$ts(''),
                field: 'ProductNo',
                type:'string',
                colSize: 2,
            },
            {
                //设计次数
                title: this.$ts('设计次数'),
                placeholder: this.$ts(''),
                field: 'Frequency',
                type:'string',
                colSize: 2,

            },
            {
                //塑胶材质
                title: this.$ts('塑胶材质'),
                placeholder: this.$ts(''),
                field: 'PlasticMaterial',
                type:'string',
                colSize: 2,
            },
          ],
          [
            {
                //配比原料
                title: this.$ts('配比原料'),
                placeholder: this.$ts(''),
                field: 'IngredientRatio',
                type:'string',
                colSize: 2,
            },
            {
                //产品单重
                title: this.$ts('产品单重'),
                placeholder: this.$ts(''),
                field: 'SingleWeight',
                type:'string',
                colSize: 2,
            },
            {
                //水口单重
                title: this.$ts('水口单重'),
                placeholder: this.$ts(''),
                field: 'WaterWeight',
                type:'string',
                colSize: 2,
            },
            {
             //适用机台吨位
             title: this.$ts('适用机台吨位'),
             placeholder: this.$ts(''),
             field: 'MachineTonnage', 
             type:'string',
             colSize: 2,
            },
            {
                //生产周期
                title: this.$ts('生产周期'),
                placeholder: this.$ts(''),
                field: 'ProductionCycle',
                type:'string',
                colSize: 2,
            }
          ]
        ]
      }
    },
  //   修改后（使用 async/await 等待数据）
    async mounted() {
  
    },
    methods: {
      search() {
        this.$refs.table.load(null, true)
      },
      loadBefore(params, callBack) {
        let param = {
          startDate: this.headerFields.StarchTime && this.headerFields.StarchTime[0] ? this.headerFields.StarchTime[0] : null,
          endDate: this.headerFields.StarchTime && this.headerFields.StarchTime[1] ? this.headerFields.StarchTime[1] : null,
          ResourceName: this.headerFields.ResourceName,
          cdoName: 'Tool',
          cdoType: '塑模',
          CreateUser: this.headerFields.CreateUser,
          ProductNo:this.headerFields.ProductNo,
          rows: this.$refs.table.paginations.size,
          page: this.$refs.table.paginations.page
        }
        params = Object.assign(params, param)
        callBack(true)
      },
      loadAfter(rows, callBack, result) {
        if (result.Result == 1) {
          this.$refs.table.rowData = result.Data.tableData
          this.$refs.table.paginations.total = result.Data.total
        } else {
          this.$message.error(result.Message)
        }
        callBack(false)
      },
      addRow() {
        this.boxReset();
        this.boxFields.EventName = 'add';
        this.boxFields.User = this.userInfo.userName;
        this.boxFields.Password = this.userInfo.userPwd;
        this.showEdit = true;
      },
      editRow() {
        this.boxReset();
        let rows = this.$refs.table.getSelected();
        if (rows.length == 0) {
          this.$message.error('请选择一条数据进行编辑！');
          return;
        } 
        if (rows.length > 1) {
          this.$message.error('只能选择一条数据进行编辑！');
          return;
        }
        this.boxFields.User = this.userInfo.userName;
        this.boxFields.Password = this.userInfo.userPwd;
        this.boxFields.EventName = 'load';
        this.boxFields.ResourceID = rows[0].Id;
        this.boxFields.ResourceName = rows[0].ResourceName;
        this.boxFields.NewResourceName = rows[0].ResourceName;
        this.boxFields.ModelDate = rows[0].ModelDate;
        this.boxFields.Frequency = rows[0].Frequency;
        this.boxFields.ProductDesc = rows[0].ProductDesc;
        this.boxFields.ProductNo = rows[0].ProductNo;
        this.boxFields.MachineTonnage = rows[0].MachineTonnage;
        this.boxFields.PlasticMaterial = rows[0].PlasticMaterial;
        this.boxFields.IngredientRatio = rows[0].IngredientRatio;
        this.boxFields.SingleWeight = rows[0].SingleWeight;
        this.boxFields.WaterWeight = rows[0].WaterWeight;
        this.boxFields.ProductionCycle = rows[0].ProductionCycle;
        this.boxFields.ModelType ="塑模"
        this.boxFields.Indexs = rows[0].Indexs;
        this.showEdit = true;
      },
      boxReset(){
      for (let key in this.boxFields) {
        if (this.boxFields.hasOwnProperty(key)) {
          this.boxFields[key] = null;
        }
      }
      },
      submit() {
  
        if (this.boxFields.NewResourceName == null || this.boxFields.NewResourceName == '') {
          return this.$message.error('模具名称不能为空');
        }
        //料号必填
        if (this.boxFields.ProductNo == null || this.boxFields.ProductNo == '') {
          return this.$message.error('料号不能为空');
        }
        this.boxFields.ModelType = "塑模"
        this.boxFields.ToolProductDetails = {
          Frequency: this.boxFields.Frequency, //设计次数
          ProductDesc: this.boxFields.ProductDesc, //产品名称
          ProductNo: this.boxFields.ProductNo, //料号 
          MachineTonnage: this.boxFields.MachineTonnage, //适用机台吨位
          MaterialQuality: this.boxFields.MaterialQuality, //材料材质
          ProductionCycle: this.boxFields.ProductionCycle, //生产周期
          MaterialQuantity: this.boxFields.MaterialQuantity, //料带数量
          MaterialThickness: this.boxFields.MaterialThickness, //材料厚度
          MaterialWidth: this.boxFields.MaterialWidth, //材料宽度
          ModelType: this.boxFields.ModelType, //模具类型
          OutQty: this.boxFields.OutQty, //出几支
          PlasticMaterial: this.boxFields.PlasticMaterial, //材料材质
          RushNumber: this.boxFields.RushNumber, // 冲数/分钟
          SingleWeight: this.boxFields.SingleWeight, //产品单重
          WaterWeight: this.boxFields.WaterWeight, //水口单重
          IngredientRatio: this.boxFields.IngredientRatio, //配比原料
          Indexs: this.boxFields.Indexs, //指标
        }
        this.http.post(this.ApiUrl.MesResourceMaintenance, this.boxFields,true).then((res) => {
              if (res.status == 1) {
                this.$message.success(res.message);
                this.showEdit = false;
                this.boxReset();
                this.$refs.table.load(null, true); 
              }
              else {
                this.$message.error(res.message); 
              } 
            })
      },
      delRow() {
        let rows = this.$refs.table.getSelected();
        if (rows.length == 0) {
          this.$message.error('请选择一条数据进行删除！');
          return;
        }
        this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning' 
        }) 
        .then(() => {
          let params=rows[0];
          params.EventName = 'delete';
          params.User = this.userInfo.userName;
          params.Password = this.userInfo.userPwd;
          params.NewResourceName = rows[0].ResourceName; //新设备编号
          this.http.post(this.ApiUrl.MesResourceMaintenance, params,true).then((res) => {
            if (res.status == 1) {
              this.$message.success(res.message);
              this.$refs.table.load(null, true);
            }
            else {
              this.$message.error(res.message);
            }
          }) 
        })
      },
      async getNameObject(cdoName) {
        const params = { cdo: cdoName };
        try {
          const res = await this.http.get(this.ApiUrl.GetNameObject, params);
          if (res.Result === 1) {
            return res.Data.map(item => ({
              label: item.Name,
              value: item.Name,
              key: item.Name
            }));
          } else {
            this.$message.error(res.Message);
            console.log(res.Message);
            return [];
          }
        } catch (error) {
          this.$message.error('接口请求失败');
          console.error(error);
          return [];
        }
      },
    }
  }
  </script>