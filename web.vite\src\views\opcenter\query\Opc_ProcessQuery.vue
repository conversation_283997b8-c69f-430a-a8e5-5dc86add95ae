<template>
    <VolHeader title="车间在制品查询" />
    <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules">  

      </VolForm>

      <div class="table-item">
		<div class="table-item-header">
			<div class="table-item-border"></div> <span class="table-item-text">在制明细</span>
			<div class="table-item-buttons">
          <div >
            <el-button icon="Search" @click="onQuery" plain>查询</el-button>
            <el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
          </div>
			</div>
		</div>
    <vol-table ref="table" row-key="Id" index :column-index="true" :reserveSelection="true" :loadKey="true"
        :columns="columns" :tableData="tableData" @rowClick="rowClick" :single="false" :pagination-hide="false"
        :height="528"  :url="url" @loadBefore="loadBefore" @loadAfter="loadAfter"
        :defaultLoadPage="false"></vol-table>
	</div>

</template>

<script lang="jsx">
  import VolTable from "@/components/basic/VolTable.vue";
  import VolForm from '@/components/basic/VolForm.vue';
  import VolHeader from '@/components/basic/VolHeader.vue';
  import Excel from '@/uitils/xlsl.js';
  import {
    mapState
  } from 'vuex';
  export default {
    components: {
      VolHeader,
      VolForm,
      'vol-table': VolTable
    },
    computed: {
      ...mapState({
        //获取当前用户的信息
        userInfo: state => state.userInfo,
        //获取当前用户的权限
        permission: state => state.permission,
      })
    },
    //初始化页面
    created() {
      this.GetWorkcenter();
    },
    data() {
      return {
        ApiUrl: {
          GetRevisionObject: "/api/query/GetRevisionObject",
          GetNameObject: "/api/query/GetNameObject",
          GetScrapReason: '/api/Query/GetScrapReason', //获取容器信息
          GetContainerInfo: '/api/Query/GetContainerInfo', //获取批次信息
          QueryLotProcess: '/api/Query/QueryLotProcess', //获取批次信息
          GetSpecByWorkcenter: 'api/Query/GetSpecByWorkcenter', //获取规格
          GetEuipmentBySpecOrWorkcenter: 'api/Query/GetEuipmentBySpecOrWorkcenter', //报废和取消报废
        },
        url:'/api/Query/QueryLotProcess',
        columns:[
              { field: 'mfgOrderName', title: '生产工单', type: 'string', width: 120, align: 'center'},
              { field: 'productName', title: '产品编号', type: 'string', width: 120, align: 'center'},
              { field: 'productDesc', title: '产品描述', type: 'string', width: 180, align: 'center'},
              { field: 'containerName', title: '批次码', type: 'string', width: 150, align: 'center'},
              { field: 'qty', title: '批次数量', type: 'string', width: 80, align: 'center'},
              { field: 'uom', title: '单位', type: 'string', width: 90, align: 'center' },
              { field: 'currentStep', title: '当前工序', type: 'string', width: 150, align: 'center' },
              { field: 'status', title: '批次状态', type: 'select', width: 80, align: 'center'},
              { field: 'moveStatus', title: '移进状态', type: 'select', width: 80, align: 'center',
                bind:{data:[{key: 0, value: "Deleted"},
                  {key: 1, value: "Active"},
                  {key: 2, value: "Closed"},
                  {key: 3, value: "In Transit (shipped)"},
                  {key: 4, value: "Issued (component)"}]}
                },
              { field: 'isHold', title: '是否Hold', type: 'string', width: 80, align: 'center' },
              { field: 'resourceName', title: '设备/Line', type: 'string', width: 130, align: 'center' },
              { field: 'nextStep', title: '下工序', type: 'string', width: 150, align: 'center'},
              // { field: 'employeeName', title: '操作人', type: 'string', width: 150, align: 'center' },
              // { field: 'txnDatetime', title: '操作时间', type: 'string', width: 150, align: 'center'},
              { field: 'workflowName', title: '工作流程', type: 'string', width: 130, align: 'center'},
              // { field: 'IsPassed', title: '执行是否成功', type: 'string', width: 130, align: 'center' },
              // { field: 'StatusMsg', title: '执行消息！', type: 'string', width: 180, align: 'center'},
        ],
        headerFields: {
          workCenter: '',
          mfgOrder: '',
          productNo: '',
          currentSpec: '',
          resource: '',
          container: '',
          isStatus: [],
          StartTime: '',
          EndTime: '',
        },
        headerRules: [
          [{
              title: this.$ts('生产车间'),
              placeholder: this.$ts(''),
              field: "workCenter",
              type: "select",
              colSize: 2,
              data: [],
              onChange: (val) => {
                if (val) {
                  this.GetSpecs(val);
                  this.GetEquipments(val,null);
                }
              }
            },
            {
              title: this.$ts('生产订单'),
              placeholder: this.$ts(''),
              field: "mfgOrder",
              type: "text",
              colSize: 2,
            },
            {
              title: this.$ts('产品编号'),
              placeholder: this.$ts(''),
              field: "productNo",
              type: "text",
              colSize: 2,
            },
            {
              title: this.$ts('工序'),
              placeholder: this.$ts(''),
              field: "currentSpec",
              type: "select",
              data: [],
              colSize: 2,
              onChange: (val) => {
                if  (val) {
                  this.GetEquipments(null,val);
                }
                
              }
            },
            {
              title: this.$ts('设备/Line'),
              placeholder: this.$ts(''),
              field: "resource",
              type: "select",
              data: [],
              colSize: 2,
            },
            {
              title: this.$ts('批次'),
              placeholder: this.$ts(''),
              field: "container",
              type: "text",
              colSize: 2,
            },  
          ]
        ]
      }
    },
    methods: {
      //查询
      onQuery() {
        if(this.headerFields.workCenter == ''&&this.headerFields.mfgOrder == ''
          &&this.headerFields.productNo == ''&&this.headerFields.currentSpec == ''
          &&this.headerFields.resource == ''&&this.headerFields.container == ''
          &&this.headerFields.isStatus == []&&this.headerFields.StartTime == ''
          &&this.headerFields.EndTime == ''
         ){
          this.$message.error('请选择查询条件');
          return;
        };
        this.$refs.table.load(null, true);
      },
      outputRow() {
        // this.tableData.splice(0);
        //导出
        // let tableData = this.$refs.table.tableData
        let tableData = this.$refs.table.rowData
        let sortData = this.columns
        // let sortData = this.$refs.table.filterColumns
        let exportData = this.handleTableSortData(tableData, sortData)
        Excel.exportExcel(exportData, "在制品查询" + '-' + this.base.getDate());
      },
      handleTableSortData(tableData, sortData) {
        let newArray = [];
        tableData.forEach(data => {
          let newItem = {};
          sortData.forEach(field => {
            if (data.hasOwnProperty(field.field)) {
              newItem[field.title] = data[field.field];
            }
          });
          newArray.push(newItem);
        });
        return newArray;
      },
      //获取工序
      GetSpecs(val) {
        let params = {
          workcenter: val
        }
        let dataArry = [];
        // 获取规格
        this.http.get(this.ApiUrl.GetSpecByWorkcenter, params).then(res => {
          if (res.Result == 1) {
            res.Data.forEach(item => {
              dataArry.push({
                label: item.Name + ':' + item.Version,
                value: item.Name+':'+ item.Version,
                key: item.Name + ':' + item.Version,
              })
            })
            this.headerRules[0][3].data = dataArry;
          } else {
            this.$message.error(res.Message);
          }
        })
      },

      //获取设备
      GetEquipments(workcenter,spec) {
        let param = {
          spec: spec==null?null: spec.split(':')[0],
          specRevision: spec==null?null: spec.split(':')[1],
          workcenter: workcenter==null?this.headerFields.workcenter: workcenter,
        }
        let dataArry = [];
        this.http.get(this.ApiUrl.GetEuipmentBySpecOrWorkcenter, param).then(res => {
          if (res.Result == 1) {
            res.Data.forEach(item => {
              dataArry.push({
                label: item.Name,
                value: item.Name,
                key: item.Name
              })
            })
            this.headerRules[0][4].data = dataArry;
          } else {
            this.$message.error(res.Message);
          }
        })
      },

      GetWorkcenter() {
        let params = {
          cdo: "WorkCenter"
        }
        let dataArry = [];
        // 获取规格
        this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
          if (res.Result == 1) {
            res.Data.forEach(item => {
              dataArry.push({
                label: item.Name,
                value: item.Name,
                key: item.Name
              })
            })
            this.headerRules[0][0].data = dataArry;
          } else {
            this.$message.error(res.Message);
          }

        })
      },
      loadBefore(params,callBack) {
          let Container= this.headerFields.container;
          let MfgOrder = {
            Name: this.headerFields.mfgOrder
          };
          let Product= {
            Name: this.headerFields.productNo.trim==null?null: this.headerFields.productNo.split(':')[0],
            Version:  this.headerFields.productNo.trim==null?null: this.headerFields.productNo.split(':')[1]
          };
          let Spec= {
            Name: this.headerFields.currentSpec.trim==null?null: this.headerFields.currentSpec.split(':')[0],
            Version: this.headerFields.currentSpec.split(':')[1]
          };
          let Workcenter= {
            Name: this.headerFields.workCenter
          };
          let Resource= {
            Name: this.headerFields.resource
          };
          let Status= this.headerFields.isStatus;
          let StarTime= this.headerFields.StartTime;
          let EndTime= this.headerFields.EndTime;
          let PageSize= this.$refs.table.paginations.size;
          let PageCount= this.$refs.table.paginations.page;
          // params.push(Container, MfgOrder, Product, Spec, Workcenter, Resource, Status, StarTime, EndTime, PageSize, PageCount),
          params["Container"] = Container;
          params["MfgOrder"] = MfgOrder;
          params["Product"] = Product;
          params["Spec"] = Spec;
          params["Workcenter"] = Workcenter;
          params["Status"] = Status;
          params["StarTime"] = StarTime;
          params["EndTime"] = EndTime;
          params["PageSize"] = PageSize;
          params["PageCount"] = PageCount;

      
        callBack(true)
      },
      loadAfter(rows, callBack, result) {
           if(result.Result == 1) {
            //this.columns = result.Data.colums;
            this.tableData = result.Data.tableData;
            this.$refs.table.rowData = result.Data.tableData;
            this.$refs.table.paginations.total = result.Data.total;

           }
           else{
            this.$message.error(result.Message);
           }
            callBack(false);
        },

    }
  }
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	.table-item-text {
		font-weight: bolder;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196F3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>