<template>
    <!-- <ModelInfo></ModelInfo> -->

    <el-tabs v-model="tabsModel" @click="changeClick" >
        

        <el-tab-pane :label="this.$ts('Maintain Equipment')" name="Equipment">
            <div class="container">
                <div class="form-content">
                    <VolForm ref="form1" :loadKey="true" :formFields="formFields_EquipAccount"
                        :formRules="formRules_EquipAccount">
                    </VolForm>
                </div>
            </div>
            <div style="text-align: center; width: 100%;margin-top:10px">
                <el-button type="primary" plain @click="formSubmit_Account">{{ this.$ts('Submit') }}</el-button>
            </div>
        </el-tab-pane>
        <el-tab-pane :label="this.$ts('Maintain Tool')" name = "Tool">
            <div class="container">
                <div class="form-content">
                    <VolForm ref="form" :loadKey="true" :formFields="formFields_Tool" :formRules="formRules_Tool">
                        <VolHeader :title="this.$ts(title)" text="text" icon="el-icon-s-grid"></VolHeader>
                    </VolForm>
                </div>

                <div class="table-item">
                    <div class="table-item-header">
                        <div class="table-item-border"></div> <span class="table-item-text">
                            {{ this.$ts('Applicable Product') }}
                        </span>
                        <div class="table-item-buttons">
                            <div>
                                <el-button type="primary" @click="addRow('table2')" plain>{{ this.$ts('Add')
                                    }}</el-button>
                                <el-button type="primary" @click="delRow('table2')" color="#f89898" plain>{{
                                    this.$ts('Delete') }}</el-button>

                            </div>
                        </div>
                    </div>

                    <vol-table @row-click="event => rowClick(event, 'table2')" ref="table2" index
                        :tableData="tableData2" :columns="Biz_PNList" :max-height="500" :pagination-hide="true"
                        :load-key="true" :column-index="true"></vol-table>
                </div>

            </div>
            <div style="text-align: center; width: 100%;margin-top:10px">
                <el-button type="primary" plain @click="formSubmit_Tool">{{ this.$ts('Submit') }}</el-button>
            </div>
        </el-tab-pane>
        <el-tab-pane :label="this.$ts('Maintain Accessory')" name = "Acessory">
            <div class="container">
                <div class="form-content">
                    <VolForm ref="form2" :loadKey="true" :formFields="formFields_EquipPartAccount"
                        :formRules="formRules_EquipPartAccount">
                    </VolForm>
                </div>
            </div>
            <div style="text-align: center; width: 100%;margin-top:10px">
                <el-button type="primary" plain @click="formSubmit_Part">{{ this.$ts('Submit') }}</el-button>
            </div>
        </el-tab-pane>


    </el-tabs>

</template>



<script lang="jsx">
//import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'


export default {
    components: {
        // ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable
    },
    mixins: [GlobalElMessageBox],
     //初始化页面
     created() {
            
        },
    data() {
        return {

            tabsModel:"Equipment",
            title: this.$ts('Maintain Tool'),
            tableTitleOne: this.$ts('Email notification address'),
            tableTitleTwo: this.$ts('Applicable material number'),
            //治工具台账维护tab页表单字段
            formFields_Tool: {
                ToolName: null,//治具编码
                NewToolName: null,//新治具编码
                Description: null,//描述
                Biz_MESCode: null,//MES资产编号
                VendorModel: null,//厂商型号
                ToolFamily: null,//分类代码
                ToolFamilyDescription: null,//分类描述
                Biz_PhysicalLocation: null,//区域/库房
                Biz_PhysicalPosition: null,//位置/储位
                Biz_AssetPic: null,//治具图片
                Biz_EquipStatus: null,//资产管理状态

                // Biz_AssetSpec: "SB123",//规格型号

                Biz_LifetimeLimit: null,//最大寿命数
                Biz_LifetimeWarning: null,//寿命提醒数
                Biz_Usage: null,//已使用寿命
                // Biz_PNList: [],
            },
            //设备台账维护tab页表单字段
            formFields_EquipAccount: {
                ResourceName: null,//设备编码
                NewResourceName: null,//
                Description: null,//描述
                Biz_MESCode: null,//MES资产编号
                VendorModel: null,//规格型号
                ResourceFamily: null,//分类代码
                ResourceFamilyDescription: null,//分类描述
                Biz_PhysicalLocation: null,//区域/库房
                Biz_PhysicalPosition: null,//位置/储位

                ResourceBom: null,//设备BOM
                //ResourceBomRevision: "1",//设备BOM版本
                Biz_EquipStatus: null,//管理状态
                Biz_AssetPic: null,//设备图片
                Factory: null,//工厂
            },
            //配件台账维护tab页表单字段
            formFields_EquipPartAccount: {
                PartName: null,//
                NewPartName: null,//
                Description: null,//描述
                ResourceMaterialPart: null,//配件料号

                PartQty: null,//数量
                PartFamily: null,//分类代码Maybe
                PartFamilyDescription: null,//分类描述Maybe
                Biz_PhysicalLocation: null,//区域/库房
                Biz_PhysicalPosition: null,//储位
                Biz_AssetPic: null,//配件图片
                Biz_EquipStatus: null,//状态

            },
            checked: false,

            //治工具台账维护tab页表单规则
            formRules_Tool:
                [
                    [
                        {
                            // dataKey: "TOOL_NUMBER", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Tool Code", //后台下拉框对应的数据字典编号
                            placeholder: "Tool Code",
                            filter: true,
                            type: "select",
                            //  readonly: true,
                            required: true, //设置为必选项
                            field: "ToolName",
                            colSize: 3,
                            onChange: async (value) => {
                                await this.formGetPageData_Tool(value);
                            },
                        },
                        // {
                        //     dataKey: "Biz_EmailGroup", //后台下拉框对应的数据字典编号
                        //     data: [],
                        //     title: this.$ts("Email Address"), //后台下拉框对应的数据字典编号
                        //     placeholder: this.$ts("Email Address"),
                        //     filter: true,
                        //     hidden: false, //设置为必选项
                        //     type: "select",
                        //     field: "EmailGroup",
                        //     colSize: 3,

                        // }
                    ],
                    [
                        {
                            title: "MES Asset No.",
                            placeholder: "MES Asset No.",
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_MESCode",
                            //colSize: 3
                        },
                        {
                            title: "Vendor Model",
                            placeholder: "Vendor Model",
                            filter: true,
                            required: false, //设置为必选项
                            field: "VendorModel",
                            //colSize: 3
                        },
                    ],
                    [
                        {
                            // dataKey: "resourceFamilyName", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ToolFamily",
                            type: "select",
                            onChange: (value) => {
                                let postData_Condition = {
                                    resourceFamilyName: value, // 表单
                                };
                                this.http.post('api/DropdownList/SearchResourceFamily', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {
                                        //console.log(this.curFormFields, 'rows');
                                        console.log(res.rows, 'rows');
                                        this.formFields_Tool.ToolFamilyDescription = res.rows[0].ResourceFamilyDescription;


                                    }

                                }).catch(error => {
                                }).finally(() => {

                                })

                            },
                        },
                        {
                            title: "Classification Description",
                            placeholder: "Classification Description",
                            filter: true,
                            readonly: true,
                            //required: true, //设置为必选项
                            field: "ToolFamilyDescription",
                            //colSize: 3
                        },
                    ],
                    [
                        {
                            // dataKey: "Biz_PhysicalLocation", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Physical Location",
                            placeholder: "Physical Location",
                            filter: true,
                            required: true, //设置为必选项
                            field: "Biz_PhysicalLocation",
                            type: "select",
                            onChange: async (value) => {

                                try {
                                    // 等待异步方法完成并获取结果
                                    const positionData = await this.getPositionByLocation(value);
                                    if (positionData && positionData[0].key !== null || positionData[0].value !== '') {
                                        // 如果返回的是数组，则更新数据
                                        this.formRules_Tool[3][1].data = positionData;
                                    }
                                    if (positionData[0].key === null || positionData[0].value === null) {
                                        this.formRules_Tool[3][1].required = false;
                                        this.formFields_Tool.Biz_PhysicalPosition = null;
                                    } else {
                                        this.formRules_Tool[3][1].required = true;
                                    }
                                } catch (error) {
                                    console.error('请求失败或其他错误:', error);
                                }
                            }
                            //colSize: 3
                        },
                        {
                            // dataKey: "", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Physical Position",
                            placeholder: "Physical Position",
                            filter: true,
                            required: true, //设置为必选项
                            field: "Biz_PhysicalPosition",
                            type: "select"
                            //colSize: 3
                        },
                    ],
                    [
                        {
                            title: "Tool Image",
                            placeholder: "Tool Image",
                            filter: true,
                            type: "img",
                            multiple: true,
                            maxFile: 5,
                            maxSize: 5,
                            url: "api/Demo_Order/Upload",
                            //required: true, //设置为必选项
                            field: "Biz_AssetPic",

                            //colSize: 3
                        },
                        {
                            dataKey: "Biz_EQU_Status", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: this.$ts('Manage Status'),
                            placeholder: this.$ts('Manage Status'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "Biz_EquipStatus",
                            type: "select",
                            //colSize: 3
                        },
                    ],
                    [
                        {
                            title: this.$ts("Lifetime Limit"),
                            placeholder: "Lifetime Limit",
                            //filter: true,
                            //required: true, //设置为必选项
                            field: "Biz_LifetimeLimit",
                            type: "number",
                            colSize: 2.5,
                            onKeyPress: () => {
                                this.formRules_Tool[0][1].required = true;
                                let limit = this.formFields_Tool.Biz_LifetimeLimit;
                                let usage = this.formFields_Tool.Biz_Usage;
                                let warning = this.formFields_Tool.Biz_LifetimeWarning;
                                if (limit < 0) limit = 0;
                                const limitTitle = this.formRules_Tool[5] && this.formRules_Tool[5][0] ? this.formRules_Tool[5][0].title : '';
                                const usangeTitle = this.formRules_Tool[5] && this.formRules_Tool[5][2] ? this.formRules_Tool[5][2].title : '';
                                const warningTitle = this.formRules_Tool[5] && this.formRules_Tool[5][1] ? this.formRules_Tool[5][1].title : '';

                                if (limit && limit < usage && limit < warning) {
                                    this.$message.error(limitTitle + this.$ts('cannot be less than') + usangeTitle + ', ' + warningTitle);
                                }
                                console.log(limit, 'onKeyPress');



                            },
                            validator: (rule, val) => {
                                if (val && val === '' || val === null) {
                                    this.formRules_Tool[0][1].required = false;
                                    return ''
                                }
                                if (val && val !== null && val !== '' && val <= 0) {
                                    //this.formFields.Biz_LifetimeLimit=0;
                                    return '不能是小于0'
                                }

                                if (val < this.formFields_Tool.Biz_Usage || val <= 0) {
                                    return this.formRules_Tool[5][0].title + '不能小于等于' + this.formRules_Tool[5][2].title;
                                }
                                return "";
                            },

                        },
                        {
                            title: this.$ts("Lifetime Warning"),
                            placeholder: "Lifetime Warning",
                            filter: true,
                            type: "number",
                            // required: true, //设置为必选项
                            field: "Biz_LifetimeWarning",
                            colSize: 2.5,
                            onKeyPress: () => {
                                this.formRules_Tool[0][1].required = true;
                                let limit = this.formFields_Tool.Biz_LifetimeLimit;
                                let usage = this.formFields_Tool.Biz_Usage;
                                let warning = this.formFields_Tool.Biz_LifetimeWarning;
                                if (limit < 0) limit = 0;
                                const limitTitle = this.formRules_Tool[5] && this.formRules_Tool[5][0] ? this.formRules_Tool[5][0].title : '';
                                const usangeTitle = this.formRules_Tool[5] && this.formRules_Tool[5][2] ? this.formRules_Tool[5][2].title : '';
                                const warningTitle = this.formRules_Tool[5] && this.formRules_Tool[5][1] ? this.formRules_Tool[5][1].title : '';

                                if (limit && limit < usage && limit < warning) {
                                    this.$message.error(limitTitle + this.$ts('cannot be less than') + usangeTitle + ', ' + warningTitle);
                                }
                                console.log(limit, 'onKeyPress');
                            },

                            validator: (rule, val) => {
                                let limit = this.formFields_Tool.Biz_LifetimeLimit;
                                const warningTitle = this.formRules_Tool[5] && this.formRules_Tool[5][1] ? this.formRules_Tool[5][1].title : '';
                                const limitTitle = this.formRules_Tool[5] && this.formRules_Tool[5][0] ? this.formRules_Tool[5][0].title : '';

                                if (val && val === '' || val === null) {
                                    this.formRules_Tool[0][1].required = false;
                                    return ''
                                }
                                if (val && val !== null && val !== '' && val <= 0) {
                                    //this.formFields.Biz_LifetimeLimit=0;
                                    return '不能是小于0'
                                }

                                if (val > limit || val <= 0) {
                                    return warningTitle + '不能大于' + limitTitle;
                                }
                                return "";
                            },
                        },
                        {
                            title: this.$ts("Lifespan used"),
                            placeholder: "Lifespan used",
                            filter: true,
                            type: "number",
                            // required: true, //设置为必选项
                            field: "Biz_Usage",
                            colSize: 2.5,
                            // onKeyPress: () => {
                            //     this.formRules_Tool[0][1].required = true;
                            //     let limit = this.formFields_Tool.Biz_LifetimeLimit;
                            //     let usage = this.formFields_Tool.Biz_Usage;
                            //     let warning = this.formFields_Tool.Biz_LifetimeWarning;
                            //     if (limit < 0) limit = 0;
                            //     const limitTitle = this.formRules_Tool[5] && this.formRules_Tool[5][0] ? this.formRules_Tool[5][0].title : '';
                            //     const usangeTitle = this.formRules_Tool[5] && this.formRules_Tool[5][2] ? this.formRules_Tool[5][2].title : '';
                            //     const warningTitle = this.formRules_Tool[5] && this.formRules_Tool[5][1] ? this.formRules_Tool[5][1].title : '';

                            //     if (limit && limit < usage && limit < warning) {
                            //         this.$message.error(`${limitTitle}不能小于${usangeTitle}和${warningTitle}`);
                            //     }
                            //     console.log(limit, 'onKeyPress');
                            // },

                            // validator: (rule, val) => {
                            //     let limit = this.formFields_Tool.Biz_LifetimeLimit;
                            //     const usangeTitle = this.formRules_Tool[5] && this.formRules_Tool[5][2] ? this.formRules_Tool[5][2].title : '';
                            //     const warningTitle = this.formRules_Tool[5] && this.formRules_Tool[5][1] ? this.formRules_Tool[5][1].title : '';
                            //     const limitTitle = this.formRules_Tool[5] && this.formRules_Tool[5][0] ? this.formRules_Tool[5][0].title : '';

                            //     if (val && val === '' || val === null ) {
                            //         this.formRules_Tool[0][1].required = false;
                            //         return ''
                            //     }
                            //     if (val && val !== null && val !== '' && val <= 0) {
                            //         //this.formFields.Biz_LifetimeLimit=0;
                            //         return '不能是小于0'
                            //     }

                            //     if (val > limit || val <= 0) {
                            //         return usangeTitle + '不能大于' + limitTitle;
                            //     }
                            //     return "";
                            // },
                        },


                    ]
                ],

            //设备台账tab页表单规则
            formRules_EquipAccount:
                [
                    [
                        {
                            //dataKey: "RESOURCE_NUMBER", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Equipment code",
                            placeholder: "Equipment code",
                            filter: true,
                            //readonly: true,
                            required: true, //设置为必选项
                            field: "ResourceName",
                            colSize: 3,
                            type: "select",
                            onChange: (value) => {
                                this.formGetPageData_Account(value);
                            },

                        },
                        /* {
                            title: "New Equipment code",
                            placeholder: "New Equipment code",
                            filter: true,
                            //readonly: true,
                            required: true, //设置为必选项
                            field: "NewResourceName",
                            colSize: 3,

                        }, */
                        {

                            title: "MES Asset No.",
                            placeholder: "MES Asset No.",
                            filter: true,
                            required: false, //设置为必选项
                            field: "Biz_MESCode",
                            colSize: 3,
                        },
                        {
                            title: "Vendor Model",
                            placeholder: "Vendor Model",
                            filter: true,
                            required: false, //设置为必选项
                            field: "VendorModel",
                            colSize: 3,
                        },

                    ],
                    [
                        {
                            // dataKey: "resourceFamilyName", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: true, //设置为必选项
                            type: "select",
                            field: "ResourceFamily",
                            colSize: 3,
                            onChange: (value) => {
                                let postData_Condition = {
                                    resourceFamilyName: value, // 表单
                                };
                                this.http.post('api/DropdownList/SearchResourceFamily', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {
                                        //console.log(this.curFormFields, 'rows');
                                        console.log(res.rows, 'rows');
                                        this.formFields_EquipAccount.ResourceFamilyDescription = res.rows[0].ResourceFamilyDescription;

                                    }
                                }).catch(error => {
                                });
                            },
                        },
                        {
                            title: "Classification Description",
                            placeholder: "Classification Description",
                            filter: true,
                            readonly: true,
                            required: false, //设置为必选项
                            field: "ResourceFamilyDescription",
                            colSize: 3,
                        },
                        {
                            // dataKey: "Biz_PhysicalLocation", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Physical Location",
                            placeholder: "Physical Location",
                            filter: true,
                            required: true, //设置为必选项
                            field: "Biz_PhysicalLocation",
                            type: "select",
                            colSize: 3,
                            onChange: async (value) => {

                                try {
                                    // 等待异步方法完成并获取结果
                                    const positionData = await this.getPositionByLocation(value);
                                    if (positionData && positionData[0].key !== null || positionData[0].value !== '') {
                                        // 如果返回的是数组，则更新数据
                                        this.formRules_EquipAccount[2][0].data = positionData;
                                    console.log(positionData, 'positionData');
                                    console.log(this.formRules_EquipAccount[2][0].data, 'this.formRules_Tool[2][0].data');

                                    }
                                    if (positionData[0].key === null || positionData[0].value === null) {
                                        this.formRules_EquipAccount[2][0].required = false;
                                        this.formFields_EquipAccount.Biz_PhysicalPosition = null;
                                    } else {
                                        this.formRules_EquipAccount[2][0].required = true;
                                    }
                                } catch (error) {
                                    console.error('请求失败或其他错误:', error);
                                }
                            }
                        },
                    ],
                    [
                        {
                            dataKey: "", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Physical Position",
                            placeholder: "Physical Position",
                            filter: true,
                            required: true, //设置为必选项
                            field: "Biz_PhysicalPosition",
                            type: "select",
                            colSize: 3,
                        },
                        {
                            dataKey: "ResourceBom", //后台下拉框对应的数据字典编号
                            data: [],
                            title: "Equipment BOM",
                            // required: true,
                            required: false, //设置为必选项
                            placeholder: "Equipment BOM",
                            field: "ResourceBom",
                            type: "select",
                            colSize: 3,
                        },
                        {
                            dataKey: "Biz_EQU_Status", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Manage Status",
                            required: true, //设置为必选项
                            placeholder: "Manage Status",
                            field: "Biz_EquipStatus",
                            type: "select",
                            colSize: 3,
                        },
                    ],

                    [
                        {
                            title: "Equipment Image",
                            placeholder: "Equipment Image",
                            filter: true,
                            type: "img",
                            multiple: true,
                            maxFile: 5,
                            maxSize: 5,
                            url: "api/Demo_Order/Upload",
                            //required: true, //设置为必选项
                            field: "Biz_AssetPic",
                            colSize: 3,
                        },
                    ]
                ],


            formRules_EquipPartAccount:
                [
                    [
                        {
                            dataKey: 'PART_NUMBER',
                            data: [],
                            title: "Accessory Code",//配件编码
                            placeholder: "Accessory Code",
                            filter: true,
                            required: true, //设置为必选项
                            field: "PartName",
                            type: "select",
                            onChange: (value) => {
                                this.formGetPageData_Part(value);
                            },
                            colSize: 3
                        },
                        {

                            title: "New Part Name",
                            placeholder: "New Part Name",
                            filter: true,
                            //required: false, //设置为必选项
                            hidden: true,
                            field: "NewPartName",

                            colSize: 3
                        },
                        {
                            dataKey: "resourceMateRialPart", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Accessory Part Number",
                            placeholder: "Accessory Part Number",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterialPart",
                            type: "select",
                            colSize: 3
                        },

                    ],

                    [
                        {
                            title: "Quantity",
                            placeholder: "Quantity",
                            filter: true,
                            required: true, //设置为必选项
                            type: "number",
                            field: "PartQty",
                            colSize: 3
                        },
                        {
                            dataKey: "resourceFamilyName", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Classification Code",
                            placeholder: "Classification Code",
                            filter: true,
                            required: true, //设置为必选项
                            field: "PartFamily",
                            type: "select",
                            colSize: 3,
                            onChange: (value) => {
                                let postData_Condition = {
                                    resourceFamilyName: value, // 表单
                                };
                                this.http.post('api/DropdownList/SearchResourceFamily', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {
                                        //console.log(this.curFormFields, 'rows');
                                        console.log(res.rows, 'rows');
                                        this.formFields_EquipPartAccount.PartFamilyDescription = res.rows[0].ResourceFamilyDescription;

                                    }
                                }).catch(error => {
                                });
                            },
                        },
                        {
                            title: "Classification Description",
                            placeholder: "Classification Description",
                            filter: true,
                            readonly: true,
                            required: false, //设置为必选项
                            field: "PartFamilyDescription",
                            colSize: 3
                        },
                    ],
                    [
                        {
                            dataKey: "Biz_PhysicalLocation", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Physical Location",
                            placeholder: "Physical Location",
                            filter: true,
                            required: true, //设置为必选项
                            type: "select",
                            field: "Biz_PhysicalLocation",
                            colSize: 3,
                            onChange: async (value) => {

                                try {
                                    // 等待异步方法完成并获取结果
                                    const positionData = await this.getPositionByLocation(value);
                                    if (positionData && positionData[0].key !== null || positionData[0].value !== '') {
                                        // 如果返回的是数组，则更新数据
                                        this.formRules_EquipPartAccount[2][1].data = positionData;
                                    }
                                    if (positionData[0].key === null && positionData[0].value === null) {
                                        this.formRules_EquipPartAccount[2][1].required = false;
                                        this.formFields_EquipPartAccount.Biz_PhysicalPosition = null;
                                    } else {
                                        this.formRules_EquipPartAccount[2][1].required = true;

                                    }
                                } catch (error) {
                                    console.error('请求失败或其他错误:', error);
                                }
                            }
                        },
                        {
                            dataKey: "", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Physical Position",
                            placeholder: "Physical Position",
                            filter: true,
                            type: "select",
                            required: true, //设置为必选项
                            field: "Biz_PhysicalPosition",
                            colSize: 3
                        },
                        {
                            title: "Accessory Image",
                            placeholder: "Accessory Image",
                            filter: true,
                            type: "img",
                            multiple: true,
                            maxFile: 5,
                            maxSize: 5,
                            url: "api/Demo_Order/Upload",
                            //required: true, //设置为必选项
                            field: "Biz_AssetPic",
                            colSize: 3
                        },
                    ],
                    [
                        {
                            dataKey: "Biz_EQU_Status", //后台下拉框对应的数据字典编号
                            data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            title: "Manage Status",
                            placeholder: "Manage Status",
                            filter: true,
                            required: true, //设置为必选项
                            type: "select",
                            field: "Biz_EquipStatus",
                            colSize: 3
                        },
                    ]
                ],
            //隐藏分页
            paginationHide: true,


            /* tableData: [
                {

                }
            ], */
            Biz_PNList: [
                {
                    field: 'ProductName', title: this.$ts('Part Number'), type: 'string', bind: { key: 'BIZ_PRODUCT', data: [] }, width: 90, edit: { type: "select", keep: true },
                    onChange: (editRow) => {
                        let cacheTable = [];

                        this.http.post('/api/DropdownList/SearchProductDes', { ProductName: editRow.ProductName }).then(res => {
                            editRow.ProductDes = res.rows[0].ProductDes;

                        }).catch(error => {
                            // 错误处理
                            console.error('请求失败或其他错误:', error);
                        });
                        const originalLength = this.tableData2.length;
                        let uniqueRows = new Map();
                        this.tableData2.forEach(row => uniqueRows.set(row.ProductName, row));

                        let filteredTableData2 = Array.from(uniqueRows.values());
                        this.tableData2 = filteredTableData2;
                        console.log(filteredTableData2);
                        if (originalLength > filteredTableData2.length) {
                            this.$message({
                                message: this.$ts('Duplicate rows have been removed'),
                                type: 'error'
                            });
                        }
                        // console.log(editRow, 'editRow');
                    }
                },
                {
                    field: 'ProductRevision', title: this.$ts('ProductRevision'), hidden: true, type: 'string', width: 180, align: 'center', edit: { type: "input", keep: true },

                },
                { field: 'ProductDes', title: this.$ts('Part Number Description'), type: 'string', width: 180, align: 'center' }
            ],
            tableData2: [
                /* { ProductName: null, ProductRevision: "1", ProductDes: null } */
            ],
            isShow: false,
            model: false,
            ApiUrl: {
                GetResourceObject: "/api/Query/GetResourceByType",//获取NameObject
                GetNameObject: "/api/Query/GetNameObject",//获取NameObject

                },
        }
    },
    created() {
        this.$nextTick(() => {
            if (this.formRules_Tool[3][1].data.length === 0) {
                this.formRules_Tool[3][1].required = false;
            }
        });
    },
    mounted() {
        /* console.log(this.http.get(), 'get');
        this.http.get(this.url, {}).then(res => {
            console.log(res, 'res');
        }) */
    },
    methods: {
        checkAndSubmit(tableData, formFields) {
            // 遍历tableData检查空行或必填列是否为空
            for (let row of tableData) {
                // 动态地检查必填列是否为空
                if (!row[formFields] || row[formFields].trim() === '') {
                    // 如果必填列为空，弹出提示消息并返回，不执行提交
                    this.$message({
                        message: this.$ts('There is an empty row or the required column is empty, and it cannot be submitted.'),
                        type: 'error'
                    });
                    return false; // 返回false，指示调用者不应继续执行
                }
            }
            return true; // 所有行都检查通过，返回true
        },
        
        //配件台账维护查询
        formGetPageData_Part(item) {
            console.log(this.formFields_EquipPartAccount, 'formFields_EquipPartAccount');
            let postData = {
                PartName: this.formFields_EquipPartAccount.PartName
            }
            console.log(postData, 'postData');
            //console.log(this.formFields_EquipPartAccount.Biz_AssetPic, 'this.formFields_EquipPartAccount');
            this.http.post('api/Query/SearchMaintenanceMesPart', postData)
                .then(res => {
                    console.log(res, 'res');
                    this.formFields_EquipPartAccount = res.rows[0];
                    //this.formFields_EquipPartAccount.Biz_AssetPic = [{name:'屏幕截图 2024-04-29 164122.png',path:res.rows[0].Biz_AssetPic}];
                    // this.formFields_EquipPartAccount[0].Biz_AssetPic = res.rows[0].Biz_AssetPic;



                })
                .catch(error => {
                    // 错误处理
                    console.error('请求失败或其他错误:', error);
                });
        },

        //治工具台账维护查询
        async formGetPageData_Tool(item) {
            let postData = {
                ToolName: this.formFields_Tool.ToolName
            }
            try {
                const res = await this.http.post('api/Query/SearchMaintenanceMesTool', postData);
                if (res.rows && res.rows.length > 0) {
                    this.formFields_Tool = res.rows[0];
                    console.log(res.rows[0], 'res.rows[0]');
                    this.tableData2 = res.rows[0].Biz_PNList.map(item => {
                        return {
                            ProductDes: item.ProductDescription,
                            ProductName: item.ProductName,
                            ProductRevision: "1"

                        };
                    });
                    // console.log(res.rows[0].Biz_LifetimeLimit, 'res.rows.Biz_LifetimeLimit');
                    //加载数据后如果寿命限制、使用寿命、寿命提醒数有值则设置邮件组为必填项
                    if (res.rows[0].Biz_LifetimeLimit > 0 || res.rows[0].Biz_Usage > 0 || res.rows[0].Biz_LifetimeWarning > 0) {
                        this.formRules_Tool[0][1].required = true;
                    } else {
                        this.formRules_Tool[0][1].required = false;
                    }
                    // this.tableData2 = res.rows[0].Biz_PNList;
                } else {
                    // this.$message.error(res.error);
                    console.log(res.error, 'res.error');
                    // this.resultMessageStay(res.status, res.message);
                }
            } catch (error) {
                this.$message.error(error.message);
            }

        },
        //设备台账维护查询
        formGetPageData_Account(item) {
            console.log(this.formFields_EquipAccount, 'formFields_EquipAccount');
            let postData = {
                ResourceName: this.formFields_EquipAccount.ResourceName
            }
            this.http.post('api/Query/SearchMaintenanceMesResource', postData)
                .then(res => {
                    this.formFields_EquipAccount = res.rows[0];
                    // console.log(res, 'res');

                })
                .catch(error => {
                    // 错误处理
                    console.error('请求失败或其他错误:', error);
                });
        },
        //治工具台账维护提交
        async formSubmit_Tool() {

            const valid = await this.$refs.form.validate();
            const canSubmit = this.checkAndSubmit(this.tableData2, "ProductName");

            if (!valid || !canSubmit) {
                // this.$message.error('The maximum life limit cannot be less than the life used');
                return;
            }
 
            let proxyArray = this.formRules_Tool[0][0].data;
            let fileData = []
            if (this.formFields_Tool.Biz_AssetPic !== null && this.formFields_Tool.Biz_AssetPic.length > 0) {
                fileData = this.formFields_Tool.Biz_AssetPic.map(item => item.path)
                this.formFields_Tool.Biz_AssetPic = fileData.join()
                //fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
            }
            this.formFields_Tool.Biz_AssetPic = fileData.join();
            let postData = {

                ...this.formFields_Tool, // 表单
                NewToolName: this.formFields_Tool.ToolName,
                Biz_PNList: this.tableData2, // table
                ProductRevision: "1",
                Biz_AssetPic: this.formFields_Tool.Biz_AssetPic //&& this.formFields_Tool.Biz_AssetPic.length > 0 ? this.formFields_Tool.Biz_AssetPic[0].path : null


                //EmailGroup: this.tableData, // table
            };//
            console.log(postData, 'postData');
            this.http.post('/api/CDO/MaintenanceMesTool', postData).then(res => {
                this.resultMessageStay(res.status, res.message);

            }).finally(() => {
                this.resetItemSource(proxyArray, 'TOOL_NUMBER');
                this.$refs.form.reset(this.formFields_Tool);

                this.$refs.table2.reset();
                this.formFields_Tool.Biz_LifetimeLimit = '';
                this.formFields_Tool.Biz_Usage = '';
                this.formFields_Tool.Biz_LifetimeWarning = '';
                this.formRules_Tool[0][1].required = false;
                //this.formRules_Tool[0][1].hidden = true;
            })
        },
        //Get Resource
        getResource(){
            let param =  {
                cdo:"Resource",
                type:"Resource"
            }
            
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        let dataArr=[]
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        this.formRules_EquipAccount[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
            )
        },
        getTool(){
            let param =  {
                cdo:"Resource",
                type:"Tool"
            }
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        let dataArr=[]
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
            )
        },
        getLocalTion(){
            let param={
                cdo:"Location"
            }
            this.http.get(this.ApiUrl.GetNameObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        let dataArr=[]
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        this.formRules_Tool[3][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
            )
        },
        getResourceSelectValue(typeName,cdoname){
            let param={
                // cdo:"ResourceFamily",
                cdo:cdoname,
                type:typeName
            }
            let dataArr=[]
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        
                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
               
            )
            return dataArr;
        },
        reset() {
            this.$refs.form.reset();
            this.$refs.table.reset();
            this.$refs.table2.reset();
        },


        //设备台账维护提交
        async formSubmit_Part() {
            let proxyArray = this.formRules_EquipPartAccount[0][0].data;
            const valid = await this.$refs.form2.validate();
            if (!valid) return;
            let fileData = []
            if (this.formFields_EquipPartAccount.Biz_AssetPic !== null && this.formFields_EquipPartAccount.Biz_AssetPic.length > 0) {
                fileData = this.formFields_EquipPartAccount.Biz_AssetPic.map(item => item.path)
                // this.formFields_EquipPartAccount.Biz_AssetPic = fileData.join()
                //fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
            }
            this.formFields_EquipPartAccount.Biz_AssetPic = fileData.join();

            let postData = {
                ...this.formFields_EquipPartAccount, // 表单
                NewPartName: this.formFields_EquipPartAccount.PartName,
                Biz_AssetPic: this.formFields_EquipPartAccount.Biz_AssetPic// && this.formFields_EquipPartAccount.Biz_AssetPic.length > 0 ? this.formFields_EquipPartAccount.Biz_AssetPic[0].path : null
                ,
                resourceMaterialPartRevision: "1",//配件料号版本
            };
            console.log(postData, 'postData');
            this.http.post('api/CDO/MaintenanceMesPart', JSON.stringify(postData)).then(res => {
                this.resultMessageStay(res.status, res.message);
            }).finally(() => {
                this.resetItemSource(proxyArray, 'PART_NUMBER');
                this.$refs.form2.reset();
            })
        },
        //设备台账维护提交
        async formSubmit_Account() {
            let proxyArray = this.formRules_EquipAccount[0][0].data;
            const valid = await this.$refs.form1.validate();
            if (!valid) return;
            console.log(this.formFields_Account, 'formFields_Tool');
            let fileData = []
            if (this.formFields_EquipAccount.Biz_AssetPic !== null && this.formFields_EquipAccount.Biz_AssetPic.length > 0) {
                fileData = this.formFields_EquipAccount.Biz_AssetPic.map(item => item.path)
                this.formFields_EquipAccount.Biz_AssetPic = fileData.join()
                //fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
            }
            this.formFields_EquipAccount.Biz_AssetPic = fileData.join();
            let postData = {
                ...this.formFields_EquipAccount, // 表单
                NewResourceName: this.formFields_EquipAccount.ResourceName,
                Biz_AssetPic: this.formFields_EquipAccount.Biz_AssetPic// && this.formFields_EquipAccount.Biz_AssetPic.length > 0 ? this.formFields_EquipAccount.Biz_AssetPic[0].path : null
                ,
                ResourceBomRevision: "1",//设备编码版本

            };
            console.log(postData, 'postData');
            this.http.post('api/CDO/MesResourceMaintenance', JSON.stringify(postData)).then(res => {
                console.log(res, 'res');
                this.resultMessageStay(res.status, res.message);
            }).finally(() => {
                this.resetItemSource(proxyArray, 'RESOURCE_NUMBER');
                this.$refs.form1.reset();
            });
        },
        addRow(tableRef) {
            let newRow = { EmailGroup: null, Biz_Remark: null };
            if (tableRef === 'table1') {

                this.$refs.table.addRow(newRow)
            } else if (tableRef === 'table2') {
                this.$refs.table2.addRow({ ProductRevision: "1", ProductName: null, ProductDescription: null })
            }
        },
        delRow(tableRef) {
            if (tableRef === 'table1') {
                this.$refs.table.delRow();
            } else if (tableRef === 'table2') {
                this.$refs.table2.delRow();
            }

        },
        rowClick(event, tableRef) {
            const { row, column } = event;
            if (tableRef === 'table1') {
                console.log(event, 'row');
                this.$refs.table.$refs.table.toggleRowSelection(row);
            } else if (tableRef === 'table2') {
                console.log(event, 'row');
                this.$refs.table2.$refs.table.toggleRowSelection(row);
            }
        },
        async resetItemSource(proxyArray, dicNo) {
            try {
                let newData = await this.SysApi_GetDicData(dicNo);
                if (!Array.isArray(newData)) {
                    throw new Error("返回的数据不是数组类型");
                }
                while (proxyArray.length > 0) {
                    proxyArray.pop();
                }
                newData.forEach(item => {
                    proxyArray.push(item);
                });
            } catch (error) {
                console.error("重置项目源时出错:", error);//
            }
        },
        changeClick(){
            switch (this.tabsModel) {
                    case 'Equipment': {
                        let resourceArry = this.getResourceSelectValue("Resource","Resource");
                        this.formRules_EquipAccount[0][0].data= resourceArry;
                        let familyArry = this.getResourceSelectValue("ResourceFamily","ResourceFamily")
                        this.formRules_EquipAccount[1][0].data= familyArry;
                        let locationArry = this.getResourceSelectValue("Location","Location")
                        this.formRules_EquipAccount[1][2].data= locationArry;
                        this.formRules_EquipAccount[2][0].data = locationArry;
                    }
                    break;
                    case 'Tool': this.initialTool();
                    break;
                    case 'Acessory': this.initialAcessory();
                    break;
                    default:this.$Message.error(this.activeTab+' is not found'); break;
                    
                }

        }
    }
};
</script>
<style lang="less" scoped>
.container {
    padding: 10px;
    background: #F3F7FC;

    .form-content {
        border-radius: 5px;
        padding: 10px 0;
        background: #fff;
    }

    .table-item-header {
        display: flex;
        align-items: center;
        padding: 6px;

        .table-item-border {
            height: 15px;
            background: rgb(33, 150, 243);
            width: 5px;
            border-radius: 10px;
            position: relative;
            margin-right: 5px;
        }

        .table-item-text {
            font-weight: bolder;
        }

        .table-item-buttons {
            flex: 1;
            text-align: right;
        }

        .small-text {
            font-size: 12px;
            color: #2196F3;
            margin-left: 10px;
            position: relative;
            top: 2px;
        }
    }
}
</style>