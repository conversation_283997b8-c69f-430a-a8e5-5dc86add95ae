<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder" :loading="loading">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>机台编号</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="searchResource" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getResource" :loading="loading">
						<el-option v-for="item in resources" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>模具编号</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="searchTool" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getTool" :loading="loading">
						<el-option v-for="item in tools" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>日期</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchDate" type="daterange" range-separator="To" start-placeholder="开始"
						end-placeholder="结束" :size="size" style="width: 240px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">换线调机记录</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="success" icon="Plus" @click="addRow" plain>新增</el-button>
					<el-button type="danger" icon="Delete" @click="completeRow" plain>结束</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="518"
			:pagination-hide="false" :load-key="true" :column-index="true" :single="true"
			:url="apiUrl.getLineChangeAndSetup" @loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false"
			:ck="false"></vol-table>
	</div>
	<vol-box :lazy="true" v-model="showAdd" title="新增换线调机" :width="600" :padding="5" :onModelClose="onModelClose">
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="addMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder" :loading="loading">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>机台编号</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="addResource" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getResource" :loading="loading">
						<el-option v-for="item in resources" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>换线类型</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="addChangeType" clearable filterable placeholder="选择" style="width: 100px">
						<el-option v-for="item in changeTypes" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
		</div>
		<template #footer>
			<div>
				<el-button type="success" icon="Check" size="small" @click="save">保存</el-button>
				<el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
			</div>
		</template>
	</vol-box>

	<vol-box :lazy="true" v-model="showComplete" title="结束换线调机" :width="600" :padding="5" :onModelClose="onModelClose">
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px; width: 160px;">
					<el-input v-model="infoMfgOrder" disabled></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>机台编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-input v-model="infoResource" disabled></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>模具编号</span></label>
				<div style="margin-top: 5px">
					<el-input v-model="infoTool" disabled></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>开始时间</span></label>
				<div style="margin-top: 5px; width: 160px;">
					<el-input v-model="infoStartTime" disabled></el-input>
				</div>
			</div>
		</div>
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>生产穴数</span>
				</label>
				<div style="margin-top: 5px">
					<el-input v-model="completeCavityCount"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>部门</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="completeDepartment" @change="selectDepartment" clearable filterable placeholder="选择" style="width: 100px">
						<el-option v-for="item in departments" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>生产速度</span></label>
				<div style="margin-top: 5px">
					<el-input v-model="completeSpeed"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>单位</span></label>
				<div style="margin-top: 5px">
					<el-input v-model="completeUom" disabled></el-input>
				</div>
			</div>
		</div>
		<div style="margin-top: 10px;width: 300px;margin-left: 5px; ">
			<vol-table ref="reasonTable" index :tableData="reasonTableData" :columns="reasonTableCol"
				:height="200" :width="280" :pagination-hide="true" :load-key="false" :column-index="true" 
				:single="true" :ck="false">
			</vol-table>
		</div>
		<div>
			<label style="width: 200px; margin-left: 5px; font-size: 16px"> <span>备注</span></label>
			<div style="margin-top: 5px">
				<el-input style="width: 280;height: 280; margin-left: 5px" v-model="completeNotes" type="textarea"></el-input>
			</div>
		</div>
		<template #footer>
			<div>
				<el-button type="danger" icon="Delete" size="small" @click="complete">结束</el-button>
				<el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			show: false,
			columns: [
				{ field: 'CDOName', hidden: true, title: 'CDOName', type: 'string', width: 120, align: 'center' },
				{ field: 'ResourceName', title: '机台编号', type: 'string', width: 120, align: 'center' },
				{ field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },
				{ field: 'Product', title: '产品编号', type: 'string', width: 120, align: 'center' },
				{ field: 'P_Description', hidden: true, title: '产品描述', type: 'string', width: 130, align: 'center' },
				{ field: 'P_Revision', hidden: true, title: '产品版本', type: 'string', width: 130, align: 'center' },
				{ field: 'StandardTime', title: '标准时间', type: 'string', width: 80, align: 'center' },
				{ field: 'Duration', title: '换线时长', type: 'string', width: 80, align: 'center' },
				{ field: 'StartTime', title: '开始时间', type: 'datetime', width: 150, align: 'center' },
				{ field: 'EndTime', title: '结束时间', type: 'datetime', width: 150, align: 'center' },
				{ field: 'Creator', title: '换线人员', type: 'string', width: 80, align: 'center' },
				{ field: 'Notes', title: '备注', type: 'string', width: 120, align: 'center' },
				{ field: 'Tool', title: '模具编号', type: 'string', width: 120, align: 'center' },
				{ field: 'Speed', title: '生产速度', type: 'string', width: 80, align: 'center' },
				{ field: 'CavityCount', title: '生产穴数', type: 'string', width: 80, align: 'center' },
				{ field: 'EmployeeGroup', title: '组别', type: 'string', width: 80, align: 'center' },
				{ field: 'Department', title: '部门', type: 'string', width: 80, align: 'center' },
				{ field: 'Status',hidden:true, title: '状态', type: 'string', width: 80, align: 'center' },

			],
			reasonTableCol:[
				{ field: 'Reason', title: '超时原因', type: 'string', width: 100, align: 'center',bind: {key:null, data: []},edit: { type: "select" }  },
			],

			tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },

			//搜索框字段
			searchMfgOrder: '',
			searchResource: '',
			searchTool: '',
			searchDate: null,
			mfgorders: [],
			resources: [],
			tools: [],

			showAdd: false,
			addMfgOrder: '',
			addResource: '',
			addChangeType: '',
			changeTypes: [
				{ Name: '换PIN', Value: '换PIN' },
				{ Name: '换形态', Value: '换形态' },
			],

			showComplete: false,
			infoMfgOrder: '',
			infoResource: '',
			infoTool: '',
			infoStartTime: '',
			infoCDOName: '',
			completeCavityCount: '',
			completeDepartment: '',
			completeSpeed: '',
			completeUom: '',
			completeNotes: '',
			departments:[],
			reasonTableData:[],

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/GetRevisionObject",
				getNameObject: "/api/query/GetNameObject",
				getDepartmentInfo: '/api/query/GetDepartmentInfo',
				getLineChangeAndSetup: '/api/query/getLineChangeAndSetup',
				lineChangeAndSetup: '/api/CDO/lineChangeAndSetup',
			}

		}
	},
	created() {
		this.getDepartment();
		// this.getReason();
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	methods: {
		getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getResource(query) {
			if (query) {
				let params = {
					cdo: "resource",
					name: query,
					objectCategory: "RESOURCE"
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.resources = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getTool(query) {
			if (query) {
				let params = {
					cdo: "resource",
					name: query,
					objectCategory: "TOOL"
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.tools = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		async getDepartment() {
			let params = {
				cdo: "department"
			};
			this.http.get(this.apiUrl.getNameObject, params).then(res => {
				if (res.Result == 1) {
					this.departments = res.Data;
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		async getReason(){
			let params = {
				cdo: "W_OutTimeReason"
			};
			this.http.get(this.apiUrl.getNameObject,params).then(res => {
				if (res.Result == 1) {
					this.reasonTableData = Array.from({ length: res.Data.length }, () => ({ "Reason": '' }));
					this.reasonTableCol[0].bind.data = res.Data.map(s => ({ key: s.Name, value: s.Name }));
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		selectDepartment(){
			let params = {
				cdoname: this.completeDepartment
			};
			this.http.post(this.apiUrl.getDepartmentInfo,params).then(res => {
				if (res.Result == 1) {
					this.completeUom = res.Data.tableData[0].Uom;
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		queryRow() {
			if (!this.searchMfgOrder 
				&& !this.searchResource 
				&& !this.searchTool 
				&& !this.searchDate
			) {
				this.$message.error('请选择查询条件。')
				return;
			}
			this.$refs.table.load(null, true);
		},
		loadBefore(params, callBack) {
			params["mfgorder"] = this.searchMfgOrder;
			params["resource"] = this.searchResource;
			params["tool"] = this.searchTool;
			params["startTime"] = this.searchDate != null ? this.searchDate[0] : '';
			params["endTime"] = this.searchDate != null ? this.searchDate[1] : '';
			callBack(true)
		},
		loadAfter(rows, callBack, result) {
			if (result.Result == 1) {
				//this.columns = result.Data.colums;
				result.Data.tableData.forEach(item => {
					item.RequestQty = this.searchRequestQty;
				});
				this.tableData = result.Data.tableData;
				this.$refs.table.rowData = result.Data.tableData;
				this.$refs.table.paginations.total = result.Data.total;
			}
			else {
				this.$message.error(result.Message);
			}
			callBack(false);
		},
		//清除数据
		reset() {
			this.searchMfgOrder = '';
			this.searchResource = '';
			this.searchTool = '';
			this.searchDate = null;
			this.tableData = [];
			this.$refs.table.rowData = [];
			this.$refs.table.paginations.total = 0;
		},
		addRow() {
			this.addMfgOrder = '';
			this.addResource = '';
			this.addChangeType = '';
			this.showAdd = true;
		},
		save() {
			if (!this.addMfgOrder) {
				this.$message.error('请选择工单号');
				return;
			}
			if (!this.addResource) {
				this.$message.error('请选择机台编号');
				return;
			}
			if (!this.addChangeType) {
				this.$message.error('请选择换线类型');
				return;
			}
			let params = {
				action: 'add',
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				resource: this.addResource,
				mfgorder: this.addMfgOrder,
				changeType: this.addChangeType,
			};
			this.http.post(this.apiUrl.lineChangeAndSetup, params, true).then(res => {
				if (res.Result == 1) {
					this.reset();
					this.showAdd = false;
					this.$message.success(res.Message);
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		close() {
			this.showAdd = false;
			this.showComplete = false;
		},
		completeRow(){
			const rows = this.$refs.table.getSelected();
			if (!rows.length) {
				this.$message.error('请选中表格中将要结束换线调机的记录。')
				return;
			}
			if (rows[0].Status == '1') {
				this.$message.error('当前换线调机记录已结束。')
				return;
			} 
			this.infoCDOName = rows[0].CDOName;
			this.infoMfgOrder = rows[0].MfgOrder;
			this.infoResource = rows[0].Resource;
			this.infoStartTime = rows[0].StartTime;
			this.infoTool = rows[0].Tool;
			this.completeCavityCount='';
			this.completeDepartment='';
			this.completeSpeed='';
			this.reasonTableData=[];
			this.completeNotes='';
			this.completeUom='';
			this.getReason();
			this.showComplete = true;
		},
		complete() {
			// if (!this.completeCavityCount) {
			// 	this.$message.error('请输入生产穴数。');
			// 	return;
			// }
			if (!this.completeDepartment) {
				this.$message.error('请选择部门。');
				return;
			}
			// if (!this.completeSpeed) {
			// 	this.$message.error('请输入生产速度。');
			// 	return;
			// }
			let params = {
				action: 'complete',
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				cdoName: this.infoCDOName,
				reason: [...new Set(this.reasonTableData
						.filter(s => s.Reason) // 过滤掉空值或未定义的值
						.map(s => s.Reason)    // 提取 Reason
					)],//this.reasonTableData.map(s => s.Reason),
				speed: this.completeSpeed,
				cavityCount: this.completeCavityCount,
				department: this.completeDepartment,
				notes: this.completeNotes,
			};
			this.http.post(this.apiUrl.lineChangeAndSetup, params, true).then(res => {
				if (res.Result == 1) {
					this.reset();
					this.showComplete = false;
					this.$message.success(res.Message);
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			this.$refs.table.$refs.table.toggleRowSelection(row);
		},
	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	// .table-item-text {
	// 	font-weight: bolder;
	// 	border-bottom: 1px solid #0c0c0c;
	// }
	.table-item-text {
		margin-top: 3px;
		padding-bottom: 6px;
		font-weight: bold;
		font-size: 15px;
		color: #484848;
		white-space: nowrap;
		border-bottom: 2px solid #676767;
		margin-bottom: -1px;
		letter-spacing: 1px;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196f3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>