<template>
  <VolHeader :title="'工单物料需求明细查询报表'"></VolHeader>
  <!-- 查询条件 -->
  <div >
    <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">

		<div style="text-align: end; margin-top: 0px; width: 80%">
			<div style="margin-right: 5px; margin-top: -39px">
      <el-button icon="Search" @click="queryRow" plain>查询</el-button>
      <el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
    </div>
	</div>
</VolForm>
  </div>
  <!-- 数据执行 -->
  <div class="table-item">
    <vol-table
      ref="table"
      index
      :tableData="tableData"
      @rowClick="rowClick"
      :columns="mfg_columns"
      :load-key="true"
      :column-index="true"
      :ck="false"
      :url="ApiUrl.getMaterialRequest"
      @loadBefore="loadBefore"
      @loadAfter="loadAfter"
      :defaultLoadPage="false"
      :height="500"
      :pagination-hide="false"
    ></vol-table>
  </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolForm from '@/components/basic/VolForm.vue';
export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox,
		VolHeader,
		VolForm
	},
	data() {
		return {
			modify_show: false,
			// Search_PlanDate: '',
			// Search_Department: '',
			// Search_MfgOrder: '',
			// Search_WorkCenter: '',
			// Search_Product: '',
			// WorkCenters:[],
			// Products:[],
			// Departments:[],
			//查询条件
			formFields: {
				StartTime: null,
				EndTime: null,
				Search_Department: null,
				Search_MfgOrder: null,
				Search_WorkCenter: null,
				Search_Product: null,
			},
			formRules: [
			    [
				    {
                            title: this.$ts('计划开始日期'),
                            placeholder: this.$ts(''),
                            required: false,
                            field: "StartTime",
                            type: "date",
							colSize: 2,
                            
                        },
                        {
                            title: this.$ts('计划结束日期'),
                            placeholder: this.$ts(''),
                            required: false,
                            field: "EndTime",
                            type: "date",
							colSize: 2,
                        },
						{
							title: this.$ts('生产工单'),
							type: 'text',
							field: 'Search_MfgOrder',
							placeholder: this.$ts('请输入'),
							colSize: 2,
						},],
						[
						{
							title: this.$ts('产品编码'),
							type: 'select',
							field: 'Search_Product',
							placeholder: this.$ts('请选择'),
							colSize: 2,
							data: [],
						},
						{
							title: this.$ts('生产车间'),
							type: 'select',
							field: 'Search_WorkCenter',
							placeholder: this.$ts('请选择'),
							colSize: 2,
							data: [],
						}
						
				]
			],

			//生产工单表格
			mfg_columns: [
				{ field: 'GUID', title: 'GUID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'CDOName', title: '生产工单', type: 'string', width: 130, align: 'center' },
				{ field: 'WorkCenter', title: '生产车间', type: 'string', width: 80, align: 'center' },
				{ field: 'OrderType', title: '工单类型', type: 'string', width: 80, align: 'center' },
				{ field: 'OrderStatus', title: '工单状态', type: 'string', width: 80, align: 'center' },
				{ field: 'Qty', title: '工单数量', type: 'string', width: 80, align: 'center' },
				{ field: 'Uom', title: '单位', type: 'string', width: 80, align: 'center' },
				{ field: 'Priority', title: '生产优先级', type: 'string', width: 100, align: 'center' },
				{ field: 'Product', title: '物料编码', type: 'string', width: 130, align: 'center' },
				{ field: 'P_Description', title: '物料描述', type: 'string', width: 130, align: 'center' },
				{ field: 'QtyRequired', title: '标准用量', type: 'string', width: 130, align: 'center' },
				{ field: 'RequestQty', title: '需求数量', type: 'string', width: 130, align: 'center' },
				{ field: 'ProductType', title: '产品类型', type: 'string', width: 80, align: 'center' },
				{ field: 'ModelNumber', title: '规格型号', type: 'string', width: 130, align: 'center' },
				{ field: 'Feature', title: '材质', type: 'string', width: 130, align: 'center' },
				// { field: 'Uom', title: '单位', type: 'string', width: 60, align: 'center' },
				{ field: 'PrintingSpecifications', title: '印刷规格', type: 'string', width: 130, align: 'center' },
				{ field: 'Thickness', title: '厚度', type: 'string', width: 80, align: 'center' },
				{ field: 'PlannedStartDate', title: '计划开始时间', type: 'string', width: 150, align: 'center' },
				{ field: 'PlannedCompletionDate', title: '计划完成时间', type: 'string', width: 150, align: 'center' },
			],
			tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },

			//接口地址
			ApiUrl: {
				GetRevisionObject: "/api/query/GetRevisionObject",
				GetNameObject: "/api/query/GetNameObject",
				getMaterialRequest: '/api/query/GetReportMaterialRequest',
                getProductByType: "/api/query/GetProductByType",
			},
		}
	},
	created() {
		this.getWorkCenter();
        this.getProductByType();
        this.getDepartment();
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	methods: {
        async getProductByType(){
            var list = ["成品", "半成品"];
            var queryParams = list.map(encodeURIComponent).join('&productType=');
            this.http.get(this.ApiUrl.getProductByType + '?&productType=' + queryParams).then(res => {
                if (res.Result == 1) {
                    this.formRules[1][0].data = res.Data.map(item => {
						return {
							value: item.Name,
							label: item.Name,
							key: item.Name
						}
					});
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
		async getWorkCenter() {
			// this.formRules[3][0].data = [];
			// let dataArr = [];
			let params = {
				cdo: "WorkCenter"
			};
			this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
				if (res.Result == 1) {
					this.formRules[1][1].data = res.Data.map(item => {
						return {
							value: item.Name,
							label: item.Name,
							key: item.Name
						}
					});
				} else {
					this.$message.error(res.Message);
				}
			});
		},
        async getDepartment() {
            let params = {
                cdo: "Department"
            };
            this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
                if (res.Result == 1) {
                    this.formRules[0][6].Departments = res.Data.map(item => {
						return {
							value: item.Name,
							label: item.Name,
							key: item.Name
						}
					});
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
		outputRow() {
			// this.tableData.splice(0);
			//导出
			let tableData = this.$refs.table.tableData
			let sortData = this.$refs.table.filterColumns
			let exportData = this.handleTableSortData(tableData, sortData)
			Excel.exportExcel(exportData, "物料需求" + '-' + this.base.getDate());
		},
		handleTableSortData(tableData, sortData) {
			let newArray = [];
			tableData.forEach(data => {
				let newItem = {};
				sortData.forEach(field => {
					if (data.hasOwnProperty(field.field)) {
						newItem[field.title] = data[field.field];
					}
				});
				newArray.push(newItem);
			});
			return newArray
		},
		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			// this.$refs.table.$refs.table.toggleRowSelection(row);
		},
		queryRow() {
			this.tableData = [];
			this.$refs.table.load(null, true);
		},
		loadBefore(params,callBack) {
			let param= {
				mfgorder: this.formFields.Search_MfgOrder,
                product: this.formFields.Search_Product,
                // workcenter: decodeURIComponent(this.Search_WorkCenter),
				// department: decodeURIComponent(this.Search_Department),
				workcenter: this.formFields.Search_WorkCenter,
				department: this.formFields.Search_Department,
				// PlanStart: this.Search_PlanDate != null ? this.Search_PlanDate[0] : null,
				// PlanEnd: this.Search_PlanDate != null ? this.Search_PlanDate[1] : null,
				PlanStart: this.formFields.StartTime,
				PlanEnd: this.formFields.EndTime,
				pageInfo:{
					PageSize: this.$refs.table.paginations.size,
					PageCount: this.$refs.table.paginations.page
				}
			};
		params = Object.assign(params, param)
        callBack(true)
      },
      loadAfter(rows, callBack, result) {
           if(result.Result == 1) {
            this.tableData = result.Data.tableData;
            this.$refs.table.rowData = result.Data.tableData;
            this.$refs.table.paginations.total = result.Data.total;
           }
           else{
            this.$message.error(result.Message);
           }
            callBack(false);
        },


	}
}
</script>
<style lang="less" scoped>
</style>