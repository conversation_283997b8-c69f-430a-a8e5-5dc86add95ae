<template>
    <div class="lot-exception">
      <VolHeader title="模具与设备绑定/解绑" />
      <el-form-item label="设备/模具扫描" required style="width:10; margin-top: 10px;"> 
        <el-input type="text" style="width: auto;" v-model="formFields.selectLot" placeholder="设备/模具" required=true @change="onselectLot(formFields.selectLot,'CODE')" />  
      </el-form-item>
      <VolForm ref="form" :label-width="150" :loadKey="true" :formFields="formFields" :formRules="formRules">
      </VolForm>
      <div style="text-align: center; margin-top: 20px; width: 100%;margin-right: 20%;">
        <div style="margin-right: 10%; ">
            <el-button type="primary" @click="submit(1)">绑定</el-button>
            <el-button type="primary" @click="submit(2)">解绑</el-button>
        </div>
      </div>
    </div>
  </template>
  <script lang="jsx">
      import VolTable from "@/components/basic/VolTable.vue";
      import VolForm from '@/components/basic/VolForm.vue';
      import VolHeader from '@/components/basic/VolHeader.vue';
      import VolBox from '@/components/basic/VolBox.vue';
      import { mapState } from 'vuex';
      import Common from "@/uitils/common.js";
      import axios from 'axios'; 
      import Excel from '@/uitils/xlsl.js';
      export default {
          components: {
              VolHeader,
              VolForm,
              'vol-table': VolTable,
              'vol-box': VolBox
          },
          computed: {
              ...mapState({
                  //获取当前用户的信息
                  userInfo: state => state.userInfo,
                  //获取当前用户的权限
                  permission: state => state.permission,
              })
          },
          //初始化页面
          created() {
               this.GetCurrentShift();
               this.GetPrinterQueue(); //页面加载时获取打印机队列
               this.GetResource();
               this.GetTool();
               this.formFields.employee = this.userInfo.userName;
          },
  
          data() {
              return {
                  ApiUrl: {
                      GetRevisionObject: "/api/query/GetRevisionObject",
                      GetNameObject: "/api/query/GetNameObject",
                      GetCurrentShift: '/api/Query/GetCurrentShift', //获取当前班次
                      GetPrinters: '/api/Query/GetPrinter', //获取打印机
                      GetResourceByCategory: 'api/Query/GetResourceByCategory', //根据类型获取设备/模具/载具下拉
                      GetResourceToolInfo: 'api/Query/GetResourceToolInfo', //获取设备/模具信息绑定
                      ResourceToolTxn: '/api/CDO/ResourceToolTxn', //绑定/解绑
                  },
                  formFields: {
                      code: '',
                      shift: '',
                      employee: '',
                      printer:'',
                      resource: '',                
                      resourceDesc: '',                      
                      tool: '',
                      toolDesc:'',
                      productName: '',
                      pressure: '',
                      oilDrip: '',
                      height:'',
                      speed: '',
                      isTest:[],                      
                      testResult: '',
                      remark: '',
                      historyId:'',
                      status:''
                  },
                  formRules: [
                    [{
                            title: this.$ts('班次'),
                            placeholder: this.$ts(''),
                            required: false,
                            field: "shif",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },
                        {
                            title: this.$ts('人员'),
                            placeholder: this.$ts(''),
                            required: false,
                            field: "employee",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },
                        {
                            title: this.$ts('打印机'),
                            placeholder: this.$ts(''),
                            required: false,
                            field: "printer",
                            type: "text",
                            readonly: true,
                            colSize: 2,
                            hidden:true
                        },
                    ],
                      [
                          {
                              title: this.$ts('设备编号'),
                              placeholder: this.$ts(''),
                              field: "resource",
                              type: "select",
                              readonly: false,
                              required:true,
                              colSize: 2,
                              dataKey: "",
                              data: [],
                              onChange: (val, item) => {
                                //this.formFields.resourceDesc=item.find(a=>a.key==val).label;
                                this.onselectLot(val,"RESOURCE");
                              }
                          },
                          {
                              title: this.$ts('设备名称'),
                              placeholder: this.$ts(''),
                              field: "resourceDesc",
                              type: "text",
                              readonly: true,
                              colSize: 2,
                          }                          
                      ],
                      [{
                              title: this.$ts('模具编码'),
                              placeholder: this.$ts(''),
                              field: "tool",
                              type: "select",
                              readonly: false,
                              required:true,
                              colSize: 2,
                              dataKey: "",
                              data: [],
                              onChange: (val) => {
                                this.onselectLot(val,"TOOL");
                              }
                          },
                          {
                              title: this.$ts('模具名称'),
                              placeholder: this.$ts(''),
                              field: "toolDesc",
                              type: "text",
                              readonly: true,
                              colSize: 2,
                          },
                          {
                              title: this.$ts('产品编码'),
                              placeholder: this.$ts(''),
                              field: "productName",
                              type: "text",
                              readonly: true,
                              colSize: 2
                          }
                      ],
                      [  
                          {
                              title: this.$ts('机台气压'),
                              placeholder: this.$ts(''),
                              field: "pressure",
                              type: "text",
                              readonly: false,
                              colSize: 2,
                          },
                          {
                              title: this.$ts('用油点滴/分钟'),
                              placeholder: this.$ts(''),
                              field: "oilDrip",
                              type: "text",
                              readonly: false,
                              colSize: 2
                          },
                          {
                              title: this.$ts('模具闭合高度'),
                              placeholder: this.$ts(''),
                              field: "height",
                              type: "text",
                              readonly: false,
                              colSize: 2
                          },                          
                          {
                              title: this.$ts('冲压速度'),
                              placeholder: this.$ts(''),
                              field: "speed",
                              type: "text",
                              readonly: false,
                              colSize: 2,
                          }
                      ],
                      [
                        {
                            title: this.$ts(' '),
                            placeholder: this.$ts(''),
                            field: "isTest",
                            type: "checkbox",
                            colSize: 2,
                            data: [{key: "True",value: "是否为试模"},],
                        },                      
                        {
                            data: [{key: "OK",value: "合格"},{key: "NG",value: "不合格"},], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                            dataKey: '', //后台下拉框对应的数据字典编号
                            title: this.$ts('试模结果'),
                            placeholder: this.$ts(''),
                            field: "testResult",
                            type: "select",
                            readonly: false,
                            colSize: 2,
                        },
                      ],
                      [{
                              title: this.$ts('试模情况记录'),
                              placeholder: this.$ts(''),
                              field: "remark",
                              type: "textarea",
                              readonly: false,
                              colSize: 10,
                              minRows: 5,
                          }
                      ],
                      [
                          {
                              title: this.$ts('设备/模具绑定历史ID'),
                              placeholder: this.$ts(''),
                              field: "historyId",
                              type: "text",
                              hidden: true,
                              colSize: 2,
                          } ,
                          {
                            title: this.$ts('状态'),
                            placeholder: this.$ts(''),
                            field: "status",
                            type: "select",
                            hidden: true,
                            colSize: 2,
                            dataKey: "",
                            data: [
                                {key: "0",value: "未绑定"},
                                {key: "1",value: "已绑定"},
                                {key: "2",value: "已解绑"},
                            ]
                          } 
                      ]
                  ],
              }
          },
          methods: {
              //获取班次
              async GetCurrentShift() {
                this.http.get(this.ApiUrl.GetCurrentShift).then(res => {
                    if (res.Result == 1) {
                        this.formFields.shift = res.Data[0].Name;
                    } else {
                        this.$message.error(res.Message);
                    }
                }).catch(err => {
                    this.$message.error(err);
                })
              },
              //打印机
              async GetPrinterQueue() {
                    let params = {
                        cdo: "PrintQueue"
                    }
                    let dataArry=[]
                this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
                        if (res.Result == 1) {
                            res.Data.map(item => {
                                if (dataArry.find((a) => a.key === item.mfgLineName) == null) {
                                dataArry.push({ key: item.value, value: item.value, label: item.label});
                            }
                            }
                            )
                            this.formRules[0][2].data = dataArry;
                        } else {
                            this.$message.error(res.Message);
                        }
                })
              },
              //获取设备
              async GetResource(){
                let params = {
                    category: "RESOURCE"
                  };
                  this.http.get(this.ApiUrl.GetResourceByCategory,params).then(res => {
                      if (res.Result == 1) {
                          this.formRules[1][0].data =res.Data.map((item)=> {return {key:item.value,value:item.value}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
             //获取模具
              async GetTool(){
                  let params = {
                    category: "TOOL"
                  };
                  this.http.get(this.ApiUrl.GetResourceByCategory,params).then(res => {
                      if (res.Result == 1) {
                          this.formRules[2][0].data = res.Data.map((item)=> {return {key:item.value,value:item.value}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //扫描批次
              onselectLot(val,type) {
                  if(this.formFields.historyId !=null) this.$refs.form.reset(this.formFields);
                  this.setReadOnly(false);
                  if (val) {
                    let params = {
                        resource: val,
                        type:type
                    }
                    this.http.get(this.ApiUrl.GetResourceToolInfo, params).then((res) => {
                        if (res.Result == 1) {
                            if (res.Data !=null){
                                if(res.Data.HistoryId ==null)
                                {
                                    if (res.Data.ObjectCategory=="RESOURCE"){
                                        this.formFields.resource = res.Data.Resource;
                                        this.formFields.resourceDesc = res.Data.ResourceDesc;   
                                    }else if(res.Data.ObjectCategory=="TOOL"){
                                        this.formFields.tool = res.Data.Tool;
                                        this.formFields.toolDesc = res.Data.ToolDesc;
                                        this.formFields.productName = res.Data.Product;
                                    }
                                } else{
                                    this.formFields.resource = res.Data.Resource;
                                    this.formFields.resourceDesc = res.Data.ResourceDesc;
                                    this.formFields.tool = res.Data.Tool;
                                    this.formFields.toolDesc = res.Data.ToolDesc;
                                    this.formFields.productName = res.Data.Product;
                                    this.formFields.isTest = res.Data.IsTest=="True"?[res.Data.IsTest]:[];
                                    this.setReadOnly(true);
                                }
                                this.formFields.pressure = res.Data.Pressure;
                                this.formFields.oilDrip = res.Data.OilDrip;
                                this.formFields.height = res.Data.Height;
                                this.formFields.speed = res.Data.Speed;
                                this.formFields.testResult = res.Data.TestResult;
                                this.formFields.remark = res.Data.Remark;
                                this.formFields.historyId = res.Data.HistoryId;
                                this.formFields.status = res.Data.Status; 
                            }
                        } else {
                            this.$message.error(res.Message);
                        }
                    })
                  }
              },
              //提交
              submit(val) {
                  if (this.formFields.resource == null || this.formFields.resource == '') {
                      this.$message.warning('设备编号不能为空');
                      return;
                  }
                  if (this.formFields.tool == null || this.formFields.tool == '') {
                      this.$message.warning('模具编码不能为空');
                      return;
                  }
                  if (val == 1 && (this.formFields.historyId != null && this.formFields.historyId != '')) {
                      this.$message.warning('设备/模具已存在绑定，请先解绑');
                      return;
                  }
                  if (val == 2 && (this.formFields.historyId == null || this.formFields.historyId == '')) {
                      this.$message.warning('设备/模具未查找到绑定关系，不能解绑');
                      return;
                  }
                  let param = {
                      Action:val,
                      HistoryId:this.formFields.historyId,
                      User:this.userInfo.userName,
                      Password:this.userInfo.userPwd,
                      EmployeeName: this.formFields.employee,
                      Shift: this.formFields.shift,
                      Resource: this.formFields.resource,
                      Tool: this.formFields.tool,
                      Product: this.formFields.productName,
                      Pressure: this.formFields.pressure,
                      OilDrip: this.formFields.oilDrip,
                      Height: this.formFields.height,
                      Speed: this.formFields.speed,
                      IsTest: this.formFields.isTest,
                      TestResult: this.formFields.testResult,
                      Remark: this.formFields.remark
                  }
                  this.http.post(this.ApiUrl.ResourceToolTxn, param).then((res) => {
                      if (res.Result == 1) {
                          this.setReadOnly(false);
                          this.$refs.form.reset(this.formFields);
                          this.$Message.success('操作成功');
                      } else {
                          this.$Message.error(res.Message);
                      }
                  })
              },
              setReadOnly(val){
                this.formRules[3][0].readonly=val;
                this.formRules[3][1].readonly=val;
                this.formRules[3][2].readonly=val;
                this.formRules[3][3].readonly=val;
                this.formRules[4][0].readonly=val;
                this.formRules[4][1].readonly=val;
                this.formRules[5][0].readonly=val;
              }
          },
      }
  </script>