<template>
    <ModelInfo></ModelInfo>
    <VolHeader :title="this.$ts(title)" text="text"></VolHeader>
    <div class="container">
        <div class="form-content">
            <VolForm ref="form1" :formFields="BOMinfo" :formRules="formRulestop">
                <div style="text-align: end; margin-top: 0px;width:100%;">
                    <el-button type="primary" @click="setCurrentAction('add')" class="el-icon-circle-plus-outline">{{
                        this.$ts('Add') }}</el-button>
                    <el-button type="primary" @click="setCurrentAction('edit')" class="el-icon-edit">{{ this.$ts('Edit')
                        }}</el-button>
                    <el-button type="primary" @click="setCurrentAction('copy')" class="el-icon-document-copy">{{
                        this.$ts('Copy')
                        }}</el-button>
                    <el-button type="primary" color="pink" @click="setCurrentAction('delete')" class="el-icon-delete">{{
                        this.$ts('Delete')
                        }}</el-button>
                </div>
            </VolForm>
        </div>
        <div class="table-item">
            <div class="table-item-header">
                <div class="table-item-border"></div> <span class="table-item-text">{{ this.$ts(TitleName)
                    }}</span>

            </div>


        </div>
        <div class="form-content" v-show="true">
            <VolForm ref="form" :loadKey="true" :formFields="curFormFields" :formRules="curFormRules">
                <div>
                    <div style="margin-left: 12px;margin-top: 10px;margin-bottom: 10px;">{{
                        this.$ts('Accessory Part Number')
                        }}</div>
                    <div style="margin-left: 12px;margin-bottom: 10px;">
                        <el-button type="primary" ref="btntd" plain color="red" @click="delete_select_rows()"
                            class="el-icon-delete" round></el-button>
                        <el-button type="primary" ref="btnta" plain @click="add_row()" class="el-icon-plus"
                            round></el-button>

                    </div>
                    <div style="margin-left: 12px;width: 900px;">
                        <!-- SN表 -->
                        <vol-table ref="table" index :tableData="tableData" :columns="columns2" :max-height="260"
                            :pagination-hide="true" :load-key="true" :column-index="true"></vol-table>
                    </div>
                </div>

            </VolForm>
        </div>


    </div>
    <div style="text-align: center; width: 100%;margin-top:10px">

        <el-button type="primary" class="el-icon-check" @click="formSubmit">{{ this.$ts('Submit') }}</el-button>
    </div>
    <!-- <Opc_Machine_BOM_Management ref="modalForm2" @ok="modalFormOk" @confirm="handleConfirm"></Opc_Machine_BOM_Management> -->
</template>



<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import { mapState } from 'vuex';
export default {
    components: {
        ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable,
    },
    data() {
        return {
            currentAction: '', // 当前操作
            title: this.$ts('Maintain Equipment BOM'),
            tempedit: null,
            modelcontrol: '',
            curFormRules: [],//底层表单具体项（不含表格）
            curFormFields: {},//底层表单具体项值（不含表格）
            tableData: [],
            BOMinfo: {
                BOM_NO: null,//设备BOM
            },
            //设备BOM拉取页
            formRulestop: [
                [
                    {
                        //dataKey: "ResourceBom", //后台下拉框对应的数据字典编号
                        data: [],
                        title: this.$ts('Equipment BOM'),
                        placeholder: this.$ts('Equipment BOM'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_NO",
                        type: "select",

                        colSize: 3,

                    }
                ],
            ],
            //底部表单基础
            Fields_bottom_base: {
                BOM_NO: null,
                BOM_Des: null,
                // BOM_Rev:null
            },
            formRules_bottom_base: [
                [
                    {
                        title: this.$ts('Equipment BOM'),
                        placeholder: this.$ts('Equipment BOM'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_NO",
                        type: "text",
                        colSize: 3,
                    }, {
                        title: this.$ts('Description'),
                        placeholder: this.$ts('Description'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_Des",
                        type: "text",
                        colSize: 3,
                    },
                ],
                //  [
                //     {
                //         title: this.$ts('Version'),
                //         placeholder: this.$ts('Version'),
                //         filter: true,
                //         readonly: false,
                //         required: true, //设置为必选项
                //         field: "BOM_Rev",
                //         type: "text",
                //         colSize: 3,
                //     },
                // ],
            ],
            //底部表单新增
            Fields_bottom_add: {
                BOM_NO: null,
                BOM_Des: null,
                // BOM_Rev: null
            },
            formRules_bottom_add: [
                [
                    {
                        title: this.$ts('Equipment BOM'),
                        placeholder: this.$ts('Equipment BOM'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_NO",
                        type: "text",
                        colSize: 3,
                    }, {
                        title: this.$ts('Description'),
                        placeholder: this.$ts('Description'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_Des",
                        type: "text",
                        colSize: 3,
                    },
                ],
                //  [
                //     {
                //         title: this.$ts('Version'),
                //         placeholder: this.$ts('Version'),
                //         filter: true,
                //         readonly: false,
                //         required: true, //设置为必选项
                //         field: "BOM_Rev",
                //         type: "text",
                //         colSize: 3,
                //     },
                // ],
            ],
            //底部表单编辑
            Fields_bottom_edit: {
                BOM_NO: null,
                BOM_Des: null,
                // BOM_Rev: null
            },
            formRules_bottom_edit: [
                [
                    {
                        title: this.$ts('Equipment BOM'),
                        placeholder: this.$ts('Equipment BOM'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_NO",
                        type: "text",
                        colSize: 3,
                    }, {
                        title: this.$ts('Description'),
                        placeholder: this.$ts('Description'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_Des",
                        type: "text",
                        colSize: 3,
                    },
                ],
                // [
                //     {
                //         title: this.$ts('Version'),
                //         placeholder: this.$ts('Version'),
                //         filter: true,
                //         readonly: false,
                //         required: true, //设置为必选项
                //         field: "BOM_Rev",
                //         type: "text",
                //         colSize: 3,
                //     },
                // ],
            ],
            //底部表单复制
            Fields_bottom_copy: {
                BOM_NO: null,
                BOM_Des: null,
                // BOM_Rev: null
            },
            formRules_bottom_copy: [
                [
                    {
                        title: this.$ts('Equipment BOM'),
                        placeholder: this.$ts('Equipment BOM'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_NO",
                        type: "text",
                        colSize: 3,
                    }, {
                        title: this.$ts('Description'),
                        placeholder: this.$ts('Description'),
                        filter: true,
                        readonly: false,
                        required: true, //设置为必选项
                        field: "BOM_Des",
                        type: "text",
                        colSize: 3,
                    },
                ],
                // [
                //     {
                //         title: this.$ts('Version'),
                //         placeholder: this.$ts('Version'),
                //         filter: true,
                //         readonly: false,
                //         required: true, //设置为必选项
                //         field: "BOM_Rev",
                //         type: "text",
                //         colSize: 3,
                //     },
                // ],
            ],
            //底部表单删除
            Fields_bottom_delete: {
                BOM_NO: null,
                BOM_Des: null,
                // BOM_Rev: null
            },
            formRules_bottom_delete: [
                [
                    {
                        title: this.$ts('Equipment BOM'),
                        placeholder: this.$ts('Equipment BOM'),
                        filter: true,
                        readonly: true,
                        field: "BOM_NO",
                        type: "text",
                        colSize: 3,
                    }, {
                        title: this.$ts('Description'),
                        placeholder: this.$ts('Description'),
                        filter: true,
                        readonly: true,
                        field: "BOM_Des",
                        type: "text",
                        colSize: 3,
                    },
                ],
                // [
                //     {
                //         title: this.$ts('Version'),
                //         placeholder: this.$ts('Version'),
                //         filter: true,
                //         readonly: true,
                //         field: "BOM_Rev",
                //         type: "text",
                //         colSize: 3,
                //     },
                // ],
            ],
            //数据表维护
            columns2: [
                {
                    field: 'M_Code', title: this.$ts('Accessory Part Number'), type: 'string', width: 300, align: 'center', readonly: false, required: true,
                    edit: { type: "select" },
                    bind: { key: "resourceMateRialPart", },
                    // render: (h, { row, column, index }) => {
                    //     return (
                    //     <div>
                    //             {row.M_Code}
                    //             <i onClick={() => { this.handleEditRows(row, column, index) }} style="color: #2196F3;cursor: pointer;margin-left:50px;" class="el-icon-search"></i>
                    //     </div>)

                    // }
                },
                { field: 'Description', title: this.$ts('Description'), type: 'string', width: 300, align: 'center', readonly: true },
                { field: 'quantity', title: this.$ts('Quantity'), type: 'float', width: 100, align: 'center', readonly: false, required: true, edit: { type: "input", style: "keep" } },
            ],


        }
    },
    computed: {
        ...mapState({
            userInfo: state => state.userInfo
        })
    },

    mounted() {
        // console.log(this.http.get(), 'get');
        // this.http.get(this.url, {}).then(res => {
        //     console.log(res, 'res');
        // })
        this.getCodeData();
        //默认为ADD
        this.setCurrentAction('add');
        //绑定校验
        this.columns2.forEach((x) => {
            //删除操作下，用户不允许调整数据
            if (x.field == 'M_Code') {
                x.onChange = (ow, event) => {
                    if (this.currentAction == 'delete') {
                        ow.M_Code = "";
                    } else {
                        let tempname = 0;
                        this.tableData.forEach(item => {
                            if (item.M_Code === ow.M_Code) {
                                tempname++;
                            }
                        });
                        if (tempname > 1) {
                            ow.M_Code = "";
                            this.$message.error(this.$ts('No Duplicates Allowed'));
                            return;
                        } else {
                            let postData_Condition = {
                                "productName": ow.M_Code
                            };

                            //携带BOM向服务器获取数据
                            this.http.post('api/DropdownList/SearchProductDes', postData_Condition).then(res => {
                                //此处处理数据
                                if (res.rows.length > 0) {
                                    ow.Description = res.rows[0].ProductDes;

                                }

                                //载入数据
                            }).catch(error => {
                                this.$message.error(error);
                            });
                        }

                    }
                };
            }
            if (x.field == 'quantity') {
                x.onKeyPress = (row, column, $event) => {
                    if (this.currentAction == 'delete') {
                        row.quantity = '';
                    }
                }
            }
        });
    },
    methods: {

        //弹出窗口以填写数据
        // handleEditRows(row){
        //     console.log(row,'AAA');
        //     row.M_Code = 'AAA';
        // },
        async getCodeData() {
            let keys = ['ResourceBom'];
            let res = await this.http.post("/api/Sys_Dictionary/GetVueDictionary", keys);
            //console.log(res, 'res')
            if (res[0].data !== null && res[0].data.length > 0) {
                this.formRulestop[0][0].data = res[0].data;
            } else {
                this.formRulestop[0][0].data = [];
            }
        },
        handleClick(rowIndex) {
            alert(rowIndex);
        },
        /* handleConfirm(callback) {
            // 检查是否接收到回调函数
            console.log(callback, "AAA");
            this.curFormFields = Object.assign({}, callback);
            // resourceFamilyName
            // description
            // 执行回调函数
            // callback(this);
        }, */

        //表格中增加一列
        add_row() {
            if (this.currentAction == 'delete') {
                return;
            }
            this.tableData.push({ "M_Code": null, "Description": null, "quantity": null });

        },
        //删除全部选中列
        delete_select_rows() {
            if (this.currentAction == 'delete') {
                return;
            }
            const rows = this.$refs.table.getSelected();
            //console.log(rows, 'rows');
            if (!rows.length) {
                //请选中行
                this.$message.error(this.$ts('Please select row.'))
                return;
            }
            this.$refs.table.delRow();
            //删除成功
            this.$message.success(this.$ts('Successfully Deleted'))
        },
        setCurrentAction(action) {

            this.currentAction = action;
            switch (action) {
                case 'add':
                    //新增BOM
                    this.TitleName = this.$ts('Add BOM');
                    this.clearfrom(this.Fields_bottom_add);
                    this.curFormRules = this.formRules_bottom_add;
                    this.curFormFields = this.Fields_bottom_add;
                    this.columns2[0].readonly = false;
                    this.columns2[0].required = true;
                    this.columns2[2].readonly = false;
                    this.columns2[2].required = true;
                    break;
                case 'edit':
                    //编辑现有BOM
                    if (this.BOMinfo.BOM_NO == null || this.BOMinfo.BOM_NO == "") {
                        this.$message.error(this.$ts('Please select the equipment BOM first.'));
                        return;
                    }
                    this.TitleName = this.$ts('Edit the Existing BOM');
                    this.columns2[0].readonly = false;
                    this.columns2[0].required = true;
                    this.columns2[2].readonly = false;
                    this.columns2[2].required = true;
                    this.tempedit = this.BOMinfo.BOM_NO;

                    //构建json
                    let postData_Condition = {
                        "resourceBom_Name": this.BOMinfo.BOM_NO,
                        "resourceBom_Revision": "1"
                    };

                    //携带BOM向服务器获取数据
                    this.http.post('api/ResourceBom/SearchResourceBom', postData_Condition).then(res => {
                        this.clearfrom(this.Fields_bottom_edit);
                        if (res.rows.length > 0) {
                            this.Fields_bottom_edit.BOM_NO = res.rows[0].ResourceBom_Name;
                            this.Fields_bottom_edit.BOM_Des = res.rows[0].ResourceBom_Description;
                            for (let i = 0; i < res.rows[0].RresourceBOMMaterialListItemList.length; i++) {
                                this.tableData.push({
                                    "M_Code": res.rows[0].RresourceBOMMaterialListItemList[i].ProductName,
                                    "Description": res.rows[0].RresourceBOMMaterialListItemList[i].ProductDescription,
                                    "quantity": res.rows[0].RresourceBOMMaterialListItemList[i].Qty
                                });
                            }

                            this.curFormRules = this.formRules_bottom_edit;
                            this.curFormFields = this.Fields_bottom_edit;
                        } else {
                            this.$message.error('No matching data found');
                        }
                    }).catch(error => {
                        this.$message.error(error);
                    });

                    break;
                case 'copy':
                    //从现有的BOM复制
                    if (this.BOMinfo.BOM_NO == null || this.BOMinfo.BOM_NO == "") {
                        this.$message.error(this.$ts('Please select the equipment BOM first.'));
                        return;
                    }
                    this.TitleName = this.$ts('Copy from an existing BOM');
                    this.columns2[0].readonly = false;
                    this.columns2[0].required = true;
                    this.columns2[2].readonly = false;
                    this.columns2[2].required = true;

                    //构建json
                    let postData_Condition2 = {
                        "resourceBom_Name": this.BOMinfo.BOM_NO,
                        "resourceBom_Revision": "1"
                    };

                    //携带BOM向服务器获取数据
                    this.http.post('api/ResourceBom/SearchResourceBom', postData_Condition2).then(res => {
                        this.clearfrom(this.Fields_bottom_copy);
                        if (res.rows.length > 0) {
                            //this.Fields_bottom_copy.BOM_NO = res.rows[0].ResourceBom_Name;
                            this.Fields_bottom_copy.BOM_Des = res.rows[0].ResourceBom_Description;
                            for (let i = 0; i < res.rows[0].RresourceBOMMaterialListItemList.length; i++) {
                                this.tableData.push({
                                    "M_Code": res.rows[0].RresourceBOMMaterialListItemList[i].ProductName,
                                    "Description": res.rows[0].RresourceBOMMaterialListItemList[i].ProductDes,
                                    "quantity": res.rows[0].RresourceBOMMaterialListItemList[i].Qty
                                });
                            }

                            this.curFormRules = this.formRules_bottom_copy;
                            this.curFormFields = this.Fields_bottom_copy;
                        } else {
                            this.$message.error('No matching data found');
                        }
                    }).catch(error => {
                        this.$message.error(error);
                    });

                    break;
                case 'delete':
                    //删除已有BOM
                    if (this.BOMinfo.BOM_NO == null || this.BOMinfo.BOM_NO == "") {
                        this.$message.error(this.$ts('Please select the equipment BOM first.'));
                        return;
                    }
                    this.TitleName = this.$ts('Delete an existing BOM');
                    this.columns2[0].readonly = true;
                    this.columns2[0].required = false;
                    this.columns2[2].readonly = true;
                    this.columns2[2].required = false;

                    //构建json
                    let postData_Condition3 = {
                        "resourceBom_Name": this.BOMinfo.BOM_NO,
                        "resourceBom_Revision": "1"
                    };

                    //携带BOM向服务器获取数据
                    this.http.post('api/ResourceBom/SearchResourceBom', postData_Condition3).then(res => {
                        this.clearfrom(this.Fields_bottom_delete);
                        if (res.rows.length > 0) {
                            this.Fields_bottom_delete.BOM_NO = res.rows[0].ResourceBom_Name;
                            this.Fields_bottom_delete.BOM_Des = res.rows[0].ResourceBom_Description;
                            for (let i = 0; i < res.rows[0].RresourceBOMMaterialListItemList.length; i++) {
                                this.tableData.push({
                                    "M_Code": res.rows[0].RresourceBOMMaterialListItemList[i].ProductName,
                                    "Description": res.rows[0].RresourceBOMMaterialListItemList[i].ProductDes,
                                    "quantity": res.rows[0].RresourceBOMMaterialListItemList[i].Qty
                                });
                            }

                            this.curFormRules = this.formRules_bottom_delete;
                            this.curFormFields = this.Fields_bottom_delete;
                        } else {
                            this.$message.error('No matching data found');
                        }
                    }).catch(error => {
                        this.$message.error(error);
                    });

                    break;
            }
        },
        clearfrom(jsonObj) {
            for (var key in jsonObj) {
                // 检查属性是否是对象自身的属性，而不是继承自原型链的属性
                if (jsonObj.hasOwnProperty(key)) {
                    // 将每个属性的值设置为null
                    jsonObj[key] = null;
                }
            }
            this.tableData = [];
        },
        checktable(columns, tableData) {
            for (let i = 0; i < columns.length; i++) {
                if (columns[i].required) {
                    let tempid = columns[i].field;
                    let typecorol = columns[i].type;
                    for (let n = 0; n < tableData.length; n++) {
                        // 使用hasOwnProperty方法检查对象是否包含指定的key  
                        if (tableData[n].hasOwnProperty(tempid)) {
                            if (tableData[n][tempid] == null || tableData[n][tempid] == '' || tableData[n][tempid] == "") {
                                this.$message.error(columns[i].title + " " + this.$ts('Required'));
                                return false;
                            } else if (typecorol === 'float') {
                                const regex = /^[+]?((\d+(\.\d*)?)|(\.\d+))$/;
                                if (regex.test(tableData[n][tempid])) {
                                    if (Number(tableData[n][tempid]) > 0) {

                                    } else {
                                        this.$message.error(this.$ts("Value entered doesn't match format.") + '(' + columns[i].title + ')');
                                        return false;
                                    }
                                } else {
                                    this.$message.error(this.$ts("Value entered doesn't match format.") + '(' + columns[i].title + ')');
                                    return false;
                                }
                            }
                        } else {
                            this.$message.error(columns[i].title + " " + this.$ts('Required'));
                            return false;
                        }
                    }
                }
            }
            return true;
        },

        formSubmit() {
            //console.log(this.curFormFields, 'curFormFields');
            //console.log(this.currentAction);
            this.$refs.form.validate(() => {
                if (!this.checktable(this.columns2, this.tableData)) {
                    return;
                }
                // let postData = {
                //     "BOM_NO2":this.BOMinfo.BOM_NO, // 表单
                //     ...this.curFormFields, // 表单
                //     ...this.tableData,//表格
                //     "employee": this.userInfo.userName,
                // };
                //console.log(JSON.stringify(postData));
                let tempadd = [];

                switch (this.currentAction) {
                    case 'add':
                        let postData_Condition0 = {
                            "resourceBom_Name": this.Fields_bottom_add.BOM_NO,
                            "resourceBom_Revision": "1"
                        };
                        for (let i = 0; i < this.tableData.length; i++) {
                            //开发顾问说默认1
                            tempadd.push({ "qty": this.tableData[i].quantity, "productName": this.tableData[i].M_Code, "productRevision": "1" });
                        }
                        let postData = {
                            "ResourceBom_Name": this.Fields_bottom_add.BOM_NO,
                            "newResourceBom_Name": this.Fields_bottom_add.BOM_NO,
                            "resourceBom_Revision": "1",
                            "resourceBom_Description": this.Fields_bottom_add.BOM_Des,
                            "rresourceBOMMaterialListItemList": tempadd
                        };

                        this.http.post('api/ResourceBom/SearchResourceBom', postData_Condition0).then(res2 => {
                            // 检查返回的rows是否存在，如果是，则提示用户并在确认后执行备用API调用
                            if (res2.rows.length > 0) {
                                this.$confirm(this.$ts('The record already exists, do you want to update it?'), this.$ts('Tips'), {
                                    confirmButtonText: this.$ts('Yes'),
                                    cancelButtonText: this.$ts('Cancel'),
                                    type: 'warning'
                                }).then(() => {
                                    // 用户确认后执行备用API调用
                                    this.http.post('api/ResourceBom/AddResourceBom', postData).then(res => {
                                        //console.log(JSON.stringify(res));
                                        if (res.status === '1') {
                                            this.$message.success(this.$ts('Success'));
                                            this.clearfrom(this.Fields_bottom_add);
                                            this.getCodeData();
                                        } else {
                                            this.$message.error(this.$ts(res.message));
                                        }
                                    }).catch(error => {
                                        this.$message.error(error);
                                    });
                                }).catch(() => {
                                    // 用户取消或关闭提示框
                                    this.$message.info(this.$ts('Cancelled'));
                                });
                            } else {
                                this.http.post('api/ResourceBom/AddResourceBom', postData).then(res => {
                                    //console.log(JSON.stringify(res));
                                    if (res.status === '1') {
                                        this.$message.success(this.$ts('Success'));
                                        this.clearfrom(this.Fields_bottom_add);
                                        this.getCodeData();
                                    } else {
                                        this.$message.error(this.$ts(res.message));
                                    }
                                }).catch(error => {
                                    this.$message.error(error);
                                });
                            }
                        }).catch(error => {
                            // 处理第一个API调用的错误
                            this.$message.error(error);
                        });

                        break;


                    case 'edit':

                        if (this.BOMinfo.BOM_NO == null || this.BOMinfo.BOM_NO == "") {
                            this.$message.error(this.$ts('Please select the equipment BOM first.'));
                            return;
                        }

                        if (this.tempedit !== this.BOMinfo.BOM_NO) {
                            this.$message.error(this.$ts('The edited object is inconsistent with the queried object. Please retrieve it again.'));
                            return;
                        }

                        for (let i = 0; i < this.tableData.length; i++) {
                            //开发顾问说默认1
                            tempadd.push({ "qty": this.tableData[i].quantity, "productName": this.tableData[i].M_Code, "productRevision": "1" });
                        }
                        let postData2 = {
                            "ResourceBom_Name": this.BOMinfo.BOM_NO,
                            "newResourceBom_Name": this.Fields_bottom_edit.BOM_NO,
                            "resourceBom_Revision": "1",
                            "resourceBom_Description": this.Fields_bottom_edit.BOM_Des,
                            "rresourceBOMMaterialListItemList": tempadd
                        };

                        this.http.post('api/ResourceBom/AddResourceBom', postData2).then(res => {
                            if (res.status === '1') {
                                this.$message.success(this.$ts('Success'));
                                this.clearfrom(this.Fields_bottom_edit);
                                this.getCodeData();
                            } else {
                                this.$message.error(this.$ts(res.message));
                            }
                        }).catch(error => {
                            this.$message.error(error);
                        });

                        break;
                    case 'copy':
                        if (this.BOMinfo.BOM_NO == null || this.BOMinfo.BOM_NO == "") {
                            this.$message.error(this.$ts('Please select the equipment BOM first.'));
                            return;
                        }

                        for (let i = 0; i < this.tableData.length; i++) {
                            //开发顾问说默认1
                            tempadd.push({ "qty": this.tableData[i].quantity, "productName": this.tableData[i].M_Code, "productRevision": "1" });
                        }

                        let postData3 = {
                            "ResourceBom_Name": this.Fields_bottom_copy.BOM_NO,
                            "newResourceBom_Name": this.Fields_bottom_copy.BOM_NO,
                            "resourceBom_Revision": "1",
                            "resourceBom_Description": this.Fields_bottom_copy.BOM_Des,
                            "rresourceBOMMaterialListItemList": tempadd
                        };

                        this.http.post('api/ResourceBom/AddResourceBom', postData3).then(res => {
                            if (res.status === '1') {
                                this.$message.success(this.$ts('Success'));
                                this.clearfrom(this.Fields_bottom_copy);
                                this.getCodeData();
                            } else {
                                this.$message.error(this.$ts(res.message));
                            }
                        }).catch(error => {
                            this.$message.error(error);
                        });
                        break;
                    case 'delete':
                        if (this.BOMinfo.BOM_NO == null || this.BOMinfo.BOM_NO == "") {
                            this.$message.error(this.$ts('Please select the equipment BOM first.'));
                            return;
                        }

                        let postData4 = {
                            "ResourceBom_Name": this.Fields_bottom_delete.BOM_NO,
                            "newResourceBom_Name": this.Fields_bottom_delete.BOM_NO,
                            "resourceBom_Revision": "1",
                            "resourceBom_Description": this.Fields_bottom_delete.BOM_Des,
                            "rresourceBOMMaterialListItemList": tempadd
                        };

                        this.$confirm(this.$ts('Delete') + '?', this.$ts('Tips'), {
                            confirmButtonText: this.$ts('Yes'),
                            cancelButtonText: this.$ts('Cancel'),
                            type: 'warning'
                        }).then(() => {
                            // 用户确认后执行删除API调用
                            this.http.post('api/ResourceBom/DeleteResourceBom', postData4).then(res => {
                                if (res.status === '1') {
                                    this.$message.success(this.$ts('Success'));
                                    this.clearfrom(this.Fields_bottom_delete);
                                    this.getCodeData();
                                } else {
                                    this.$message.error(this.$ts(res.message));
                                }
                            }).catch(error => {
                                this.$message.error(error);
                            });
                        }).catch(() => {
                            // 用户取消或关闭提示框
                            this.$message.info(this.$ts('Cancelled'));
                        });

                        //console.log(JSON.stringify(postData4));

                        break;
                    default:
                        this.$message.error(this.$ts('Please click the corresponding function button to operate.'));
                        break;

                }
            });



        },




    }
};
</script>
<style lang="less" scoped>
.container {
    padding: 10px;
    background: #F3F7FC;

    .form-content {
        border-radius: 5px;
        padding: 10px 0;
        background: #fff;
    }

    .table-item-header {
        display: flex;
        align-items: center;
        padding: 6px;

        .table-item-border {
            height: 15px;
            background: rgb(33, 150, 243);
            width: 5px;
            border-radius: 10px;
            position: relative;
            margin-right: 5px;
        }

        .table-item-text {
            font-weight: bolder;
        }

        .table-item-buttons {
            flex: 1;
            text-align: right;
        }

        .small-text {
            font-size: 12px;
            color: #2196F3;
            margin-left: 10px;
            position: relative;
            top: 2px;
        }
    }
}
</style>