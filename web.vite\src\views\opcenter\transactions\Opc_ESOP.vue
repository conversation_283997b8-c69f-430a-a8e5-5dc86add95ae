<template>
    <VolHeader :title="'查询条件'"></VolHeader>
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>批次</span>
            </label>
            <div style="margin-top: 5px;">
                <el-input style="width: 200px;" @change="onChangeContainer" v-model="container"
                    clearable placeholder="请输入"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px">
            <label style="width: 200px; margin-left: 5px; font-size: 16px">
                <span>产品编码</span></label>
            <div style="margin-top: 5px">
                <el-select v-model="product" @change="onSelectProduct" clearable filterable placeholder="键入搜索"
                    style="width: 200px" remote-show-suffix :remote="true" :remote-method="getProducts">
                    <el-option v-for="item in products" :key="item.Name" :label="item.Name" :value="item.ObjectID" />
                </el-select>
            </div>
        </div>
    </div>
    <div class="opc-ESOP-container">
        <VolHeader :title="'批次产品ESOP'"></VolHeader>
        <VolForm ref="infoForm" :formFields="infoFormFields" :colspan="12" :formRules="infoFormRules" :inline="true"
            style="border-bottom: 1px solid #ccc;">
        </VolForm>
        <vol-table ref="sopTable" index :loadKey="true" :ck="false" :tableData="sopTableData" :columns="sopColumns"
            :height="400"></vol-table>
    </div>
</template>

<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from '@/components/basic/VolForm.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolBox from '@/components/basic/VolBox.vue';
import {
    mapState
} from 'vuex';
export default {
    components: {
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    //初始化页面
    created() {
        this.routename = this.$route.query.product;
    },
    watch: {
        routename: function (product) {
            if (product) {
                this.formdataHeader.selectProduct = product + ":1";
                this.$nextTick(() => {
                    this.onSelectLot();
                });
            }
        },
    },
    data() {
        return {
            routename: null,
            apiUrl: {
                getDocumentSet: '/api/Query/getDocumentSet',//获取产品文件集
                getContainerInfo: '/api/Query/getContainerInfo',
                getRevisionObject: "/api/query/getRevisionObject",
            },
            formdataHeader: {
                selectLot: '',
                selectProduct: '',

            },
            infoFormFields: {
                productNo: '',
                productDes: '',
                workflow: '',
                currentSpec: '',
            },
            infoFormRules: [
                [{ title: this.$ts('产品编码'), placeholder: this.$ts(' '), field: "productNo", type: "text", readonly: true, colSize: 2, },
                { title: this.$ts('产品描述'), placeholder: this.$ts(' '), field: "productDes", type: "text", readonly: true, colSize: 2, },
                { title: this.$ts('工艺流程'), placeholder: this.$ts(' '), field: "workflow", type: "text", readonly: true, colSize: 2, },
                { title: this.$ts('当前工序'), placeholder: this.$ts(' '), field: "currentSpec", type: "text", readonly: true, colSize: 2, },
                ]
            ],
            sopColumns: [
                { field: 'DocumentName', title: '文档名', type: 'string', width: 180, align: 'center' },
                {
                    field: 'Url', title: '操作', type: 'string', width: 150, align: 'center',
                    render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <el-button
                                    onClick={($e) => {
                                        $e.stopPropagation();
                                        this.editClick(row, column, index);
                                    }}
                                    type="primary"
                                    plain
                                    style="height:26px; padding: 10px !important;"
                                >
                                    查看
                                </el-button>
                            </div>
                        );
                    }
                },
            ],
            sopTableData: [],
            container: '',
            product: '',
            products: [],
        }
    },
    methods: {
        getProducts(query) {
            if (query) {
                let params = {
                    cdo: "product",
                    name: query
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.products = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        preReset() {
            this.$refs.sopTable.reset();
            this.$refs.infoForm.reset();
        },
        onChangeContainer() {
            this.product = '';
            this.preReset();
            if (!this.container || this.container != '') {
                let param = {
                    container: this.container
                }
                this.http.get(this.apiUrl.getContainerInfo, param, true).then((res) => {
                    if (res.Result == 1) {
                        this.infoFormFields.productNo = res.Data.productName;
                        this.infoFormFields.productDes = res.Data.productDesc;
                        this.infoFormFields.workflow = res.Data.workflowName;
                        this.infoFormFields.currentSpec = res.Data.currentStep;
                        this.getDocumentSet(res.Data.ProductId, res.Data.SpecId);
                    } else {
                        this.$Message.error(res.Message);
                    }
                });
            }
        },
        onSelectProduct() {
            this.container = '';
            this.preReset();
            if (!this.product || this.product != '') {
                this.getDocumentSet(this.product, '');
            }
        },
        getDocumentSet(productId, specId) {
            let param = {
                ProductId: productId,
                SpecId: specId
            }
            this.http.get(this.apiUrl.getDocumentSet, param, true).then((res) => {
                if (res.Result == 1) {
                    this.sopTableData = res.Data.tableData;
                }
                else {
                    this.$Message.error(res.Message)
                }
            })
        },
        editClick(row, column, index) {
            // window.location.href = row.url;
            if (row.Url == null) {
                this.$message.warning('ESOP不存在');
                return;
            }
            window.open(row.Url, 'Doucment');
        }


    }
}

</script>