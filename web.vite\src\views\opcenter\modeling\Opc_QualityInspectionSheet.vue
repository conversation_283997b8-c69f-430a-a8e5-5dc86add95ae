<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>检验类型</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchInspectionType" clearable filterable placeholder="请选择"
						style="width: 100px" remote-show-suffix :remote="true" :remote-method="getProduct"
						:loading="loading">
						<el-option v-for="item in inspectionTypes" :key="item.Name" :label="item.label"
							:value="item.value" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchProduct" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getProduct" :loading="loading">
						<el-option v-for="item in products" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工序</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchSpec" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getSpec">
						<el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>操作时间</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
						end-placeholder="结束" :size="size" style="width: 260px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss" />
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">检验表维护</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="success" icon="Plus" @click="addRow" plain>新建</el-button>
					<el-button type="primary" icon="Edit" @click="copyRow" plain>复制</el-button>
					<el-button type="primary" icon="Edit" @click="editRow" plain>编辑</el-button>
					<el-button type="danger" icon="Delete" @click="delRow" plain>删除</el-button>
					<el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="masterTable" index :tableData="masterTableData" @rowClick="rowClick" :columns="masterColumns"
			:height="518" :pagination-hide="false" :load-key="true" :column-index="true" :single="true"
			:url="apiUrl.getQualityInspectionSheet" @loadBefore="masterLoadBefore" @loadAfter="masterLoadAfter"
			:defaultLoadPage="false"></vol-table>
	</div>
	<!-- 编辑弹出框 -->
	<vol-box :lazy="true" v-model="showEdit" title="检验表维护" :width="1000" :padding="5" :onModelClose="onModelClose">
		<div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>检验表名称</span>
				</label>
				<div style="margin-top: 5px">
					<el-input ref="editInspectionName" :readonly="isReadOnly" v-model="editName"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>检验类型</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="editInspectionType" clearable filterable placeholder="请选择" style="width: 100px"
						remote-show-suffix :remote="true" :remote-method="getProduct" :loading="loading"  @change="handleSelectChange">
						<el-option v-for="item in inspectionTypes" :key="item.Name" :label="item.label"
							:value="item.value" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="editProduct" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getProduct" :loading="loading"  @change="handleSelectChange">
						<el-option v-for="item in products" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工序</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="editSpec" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getSpec">
						<el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
		</div>
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">检验表维护</span>
			<div class="table-item-buttons">
				<div>
					<el-button type="success" icon="Plus" @click="addItem" plain>添加</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="editTable" index :tableData="editTableData" :columns="editColumns" :max-height="600"
			:pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
			:edit="true"></vol-table>
		<template #footer>
			<div>
				<el-button type="success" icon="Check" size="small" @click="save">保存</el-button>
				<el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			//搜索
			searchInspectionType: null,
			searchProduct: null,
			searchSpec: null,
			searchTxnDate: null,
			masterColumns: [
				{ field: 'CDOID', title: '建模ID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'CDOName', title: '检验表名称', type: 'string', width: 0, align: 'center' },
				{ field: 'InspectionType', title: '检验类型', type: 'string', width: 130, align: 'center' },
				{ field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center' },
				{ field: 'Spec', title: '工序', type: 'string', width: 110, align: 'center' },
				{ field: 'Operator', title: '操作人', type: 'string', width: 80, align: 'center' },
				{ field: 'CreateDate', title: '操作时间', type: 'datetime', width: 120, align: 'center' },
			],
			masterTableData: [],
			tempMasterTableData: [],
			pagination: { total: 0, size: 30, sortName: "" },

			showEdit: false,
			isReadOnly: false,
			//编辑框字段
			editName: null,
			editInspectionType: null,
			editProduct: null,
			editSpec: null,
			inspectionTypes: [],
			products: [],
			specs: [],
			inspectionPointType: [{ key: '1', value: '定性' }, { key: '2', value: '定量' }],

			editColumns: [
				{ field: 'InspectionPoint', title: '检验项目', edit: true, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionPointContent', title: '检验内容', edit: true, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionTool', title: '检验工具', edit: true, type: 'string', width: 100, align: 'center' },
				{
					field: 'InspectionPointType', title: '检验点类型', bind: { key: null, data: [] }, edit: { type: "select" }, width: 80, align: 'center',
					onChange: (val) => {
						if (val == '1') {
							this.editColumns[4].edit = false;
							this.editColumns[5].edit = false;
						}
						else {
							this.editColumns[4].edit = true;
							this.editColumns[5].edit = true;
						}
					}
				},
				{ field: 'DefaultValue', title: '标准值', edit: true, type: 'string', width: 80, align: 'center' },
				{ field: 'LowerLimit', title: '下限', edit: false, edit: { type: "decimal" }, type: "decimal", precision: 2, width: 80, align: 'center' },
				{ field: 'UpperLimit', title: '上限', edit: false, edit: { type: "decimal" }, type: "decimal", precision: 2, width: 80, align: 'center' },
				{
					field: "FromIOT", title: "源于IOT", type: "int", width: 80, align: "center", render: (h, { row, column, index }) => {
						return (
							<el-switch
								active-value={1}
								inactive-value={0}
								v-model={row.Enable}
								inline-prompt
								active-text="是"
								inactive-text="否"
							></el-switch>
						);
					},
				},
				{
					title: '', field: 'Action', align: 'center', width: 50, fixed: 'right', render: (h, { row, column, index }) => {
						return (
							<div>
								<el-button
									onClick={($e) => {
										this.editTableData.splice(index, 1);
									}}
									size="small"
									type="danger"
									icon="Delete"
									circle
									plain>
								</el-button>
								{/* 这里可以接着放按钮或者其他组件 */}
							</div>
						);
					}
				}
			],
			editTableData: [],

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/GetRevisionObject",
				getNameObject: "/api/query/GetNameObject",
				getQualityInspectionSheet: '/api/query/getQualityInspectionSheet',
				qualityInspectionSheetMaint: '/api/cdo/QualityInspectionSheetMaint',
			}

		}
	},
	created() {
		this.getInspectionTypes();
		this.getInspectionPointTypes();
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	mounted() {
    const {
        product,
       } =  this.$route.query;
        if (product) {
            this.searchProduct = product; // 赋值给搜索框
			this.$refs.masterTable.load(null, true);
        }
       },
	methods: {
    
		getProduct(query) {
			if (query) {
				let params = {
					cdo: "Product",
					name: query
				};
				this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
					if (res.Result == 1) {
						this.products = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getSpec(query) {
			if (query) {
				let params = {
					cdo: "spec",
					name: query
				};
				this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
					if (res.Result == 1) {
						this.specs = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getInspectionTypes() {
			this.inspectionTypes = [
				{
					value: '1',
					label: 'SI'
				},
				{
					value: '2',
					label: 'FAI'
				},
				{
					value: '3',
					label: 'IPQC'
				},
				{
					value: '4',
					label: 'FQC'
				}
			]
		},
		getInspectionPointTypes() {
			this.editColumns[3].bind.data = this.inspectionPointType;
		},
		//清除数据
		resetMaster() {
			this.masterTableData = [];
			this.tempMasterTableData = [];
			this.$refs.masterTable.rowData = [];
		},
		resetInfo() {
			this.editInspectionType = '';
			this.editName = '';
			this.editProduct = '';
			this.editSpec = '';
			this.editTableData = [];
		},

		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			this.$refs.masterTable.$refs.table.toggleRowSelection(row);
		},
		queryRow() {
			if (!this.searchTxnDate
				&& !this.searchInspectionType 
				&& !this.searchProduct 
				&& !this.searchSpec
			) {
				this.$message.error('请选择查询条件');
				return;
			} else {
				this.resetMaster();
				this.$refs.masterTable.load(null, true);
			}
		},
		masterLoadBefore(params, callBack) {
			params["InspectionType"] = this.searchInspectionType;
			params["Product"] = this.searchProduct;
			params["Spec"] = this.searchSpec;
			params["StartTime"] = this.searchTxnDate != null ? this.searchTxnDate[0] : null;
			params["EndTime"] = this.searchTxnDate != null ? this.searchTxnDate[1] : null;
			callBack(true)
		},
		masterLoadAfter(rows, callBack, result) {
			if (result.Result == 1) {
				//this.columns = result.Data.colums;
				this.masterTableData = result.Data.tableData;
				this.tempMasterTableData = result.Data.tableData;
				this.$refs.masterTable.rowData = result.Data.tableData;
				this.$refs.masterTable.paginations.total = result.Data.total;
			}
			else {
				this.$message.error(result.Message);
			}
			callBack(false);
		},
		editRow() {
			const rows = this.$refs.masterTable.getSelected();
			if (!rows.length) {
				this.$message.error('请选中即将编辑的行')
				return;
			}
			// 查找this.tempMasterTableData中CDOName与rows[0].CDOName相同的数据
			this.isReadOnly = true;
			this.editTableData = [];
			const matchedData = this.tempMasterTableData.find(item => item.CDOName == rows[0].CDOName);
			if (matchedData && matchedData.Detail) {
				this.editTableData = Array.from(matchedData.Detail);
			} else {
				this.editTableData = [];
			}
			this.editName = rows[0].CDOName,
				this.editInspectionType = rows[0].InspectionType,
				this.editProduct = rows[0].Product,
				this.editSpec = rows[0].Spec,
				this.showEdit = true;
		},
		copyRow(){
			const rows = this.$refs.masterTable.getSelected();
			if (!rows.length) {
				this.$message.error('请选中即将复制的行')
				return;
			}
			// 查找this.tempMasterTableData中CDOName与rows[0].CDOName相同的数据
			this.isReadOnly = false;
			this.editTableData = [];
			const matchedData = this.tempMasterTableData.find(item => item.CDOName == rows[0].CDOName);
			if (matchedData && matchedData.Detail) {
				this.editTableData = Array.from(matchedData.Detail);
			} else {
				this.editTableData = [];
			}
				this.editInspectionType = rows[0].InspectionType,
				this.editProduct = rows[0].Product,
				this.editSpec = rows[0].Spec,
				this.editName =`${this.editProduct}${this.editInspectionType}检验表`,
				this.showEdit = true;

		},
		addRow() {
			// this.tableData.push({ OrderNo: "D2022040600009" })
			this.resetInfo();
			this.isReadOnly = false;
			this.showEdit = true;
		},
		addItem() {
			this.editTableData.push({
				InspectionItem: '',
				InspectionContent: '',
				InspectionTool: '',
				InspectionPointType: null,
				LowerLimit: null,
				UpperLimit: null,
				FromIOT: 0,
			});
		},
		delRow() {
			// this.$message.success('删除成功')
			const rows = this.$refs.masterTable.getSelected();
			if (!rows.length) {
				this.$message.error('请选中即将删除的行')
				return;
			}
			if (rows.length > 0) {
				let params = {
					User: this.userInfo.userName,
					Password: this.userInfo.userPwd,
					EventName: 'delete',
					CDOName: rows[0].CDOName,
					CDOID: rows[0].CDOID,
				};
				this.http.post(this.apiUrl.qualityInspectionSheetMaint, params, true).then(res => {
					if (res.Result == 1) {
						this.$refs.masterTable.delRow();
						this.$message.success('删除成功');
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		outputRow() {
			// this.tableData.splice(0);
			//导出
			let tableData = this.$refs.masterTable.tableData;
			let sortData = this.$refs.table.filterColumns;
			let exportData = this.handleTableSortData(tableData, sortData);
			Excel.exportExcel(exportData, "检验表" + '-' + this.base.getDate());
		},
		handleTableSortData(tableData, sortData) {
			let newArray = [];
			tableData.forEach(data => {
				let newItem = {};
				sortData.forEach(field => {
					if (data.hasOwnProperty(field.field)) {
						newItem[field.title] = data[field.field];
					}
				});
				newArray.push(newItem);
			});
			return newArray
		},
		save() {
			if (!this.editName) {
				this.$message.error('请输入名称');
				return;
			}
			if (!this.editInspectionType) {
				this.$message.error('请选择检验类型');
				return;
			}
			if (!this.editProduct) {
				this.$message.error('请选择产品');
				return;
			}
			if (!this.editTableData.length) {
				this.$message.error('请添加检验项目');
				return;
			}
			// 检查InspectionPoint是否有重复项
			const inspectionPointMap = new Map();
			for (let i = 0; i < this.editTableData.length; i++) {
				const inspectionPoint = this.editTableData[i].InspectionPoint;
				if (!inspectionPoint) {
					this.$message.error('第' + (i + 1) + '行，请输入检验项目');
					return;
				}
				if (inspectionPointMap.has(inspectionPoint)) {
					this.$message.error(`第${i + 1}行与第${inspectionPointMap.get(inspectionPoint) + 1}行的检验项目重复`);
					return;
				}
				if (!this.editTableData[i].InspectionPointType) {
					this.$message.error('第' + (i + 1) + '行，请选择检验点类型');
					return;
				}
				// if (this.editTableData[i].InspectionPointType == 1 && !this.editTableData[i].DefaultValue) {
				// 	this.$message.error('第' + (i + 1) + '行，请输入标准值');
				// 	this.editTableData[i].LowerLimit = null;
				// 	this.editTableData[i].UpperLimit = null;
				// 	return;
				// }
				inspectionPointMap.set(inspectionPoint, i);
			}

			let params = {
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				CDOName: this.editName,
				product: this.editProduct,
				spec: this.editSpec,
				inspectionType: this.editInspectionType,
				requestData: this.editTableData
			};
			this.http.post(this.apiUrl.qualityInspectionSheetMaint, params, true).then(res => {
				if (res.Result == 1) {
					this.showEdit = false;
					this.resetInfo();
					if (this.searchTxnDate) {
						this.queryRow();
					}
					this.$message.success('提交成功');
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		close() {
			this.showEdit = false;
		},
		getCurrentDateTimeString() {
			const now = new Date();
			const year = now.getFullYear();
			const month = this.padNumber(now.getMonth() + 1);
			const day = this.padNumber(now.getDate());
			const hours = this.padNumber(now.getHours());
			const minutes = this.padNumber(now.getMinutes());
			const seconds = this.padNumber(now.getSeconds());

			return `${year}${month}${day}${hours}${minutes}${seconds}`;
		},
		padNumber(num) {
			return num < 10 ? '0' + num : num;
		},
		handleSelectChange() {
			//如果检验类型和产品编号都不为空，则将editName 的值为 editProduct+editInspectionType+‘检验表’
			if (this.editInspectionType && this.editProduct) {
				this.editName = `${this.editProduct}${this.editInspectionType}检验表`;
			} else {
				this.editName = '';
			}
			
		}
	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	// .table-item-text {
	// 	font-weight: bolder;
	// 	border-bottom: 1px solid #0c0c0c;
	// }
	.table-item-text {
		margin-top: 3px;
		padding-bottom: 6px;
		font-weight: bold;
		font-size: 15px;
		color: #484848;
		white-space: nowrap;
		border-bottom: 2px solid #676767;
		margin-bottom: -1px;
		letter-spacing: 1px;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196f3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>