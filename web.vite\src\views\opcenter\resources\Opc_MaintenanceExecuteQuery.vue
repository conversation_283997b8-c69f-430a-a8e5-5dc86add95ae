<template>
    <!-- <ModelInfo></ModelInfo> -->
    <div style="padding: 15px 20px 15px 5px">
        <div class="pre-text">{{ text }}</div>

        <VolForm ref="form1" :loadKey="true" :formFields="curFormFields" :formRules="curFormRules">
        </VolForm>
        <div style="text-align: center;">
            <el-button type="primary" @click="Search('form1')">{{ this.$ts('查询') }}</el-button>
        </div>
        <div class="form-content" v-show="true">

            <div>
                <div style="margin-left: 12px;margin-top: 10px;margin-bottom: 10px;"></div>
                <div style="margin-left: 12px;margin-bottom: 10px;">
                    <!-- <el-button type="primary" plain color="red" @click="delRow('table1')" class="el-icon-delete"
                        round></el-button> -->
                </div>
                <div style="margin-left: 12px;">

                    <vol-table ref="table1" index :tableData="tableData" :columns="tableColumns" :max-width="560"
                        :max-height="460" :ck="false" :pagination-hide="false" :load-key="true"
                        :column-index="true"></vol-table>
                </div>
            </div>
            <div style="text-align: center; width: 100%;margin-top:20px">

            </div>
        </div>
        <div class="form-btns">

            <el-button type="primary" @click="reset" size="mini">{{ this.$ts('重置')
            }}</el-button>
        </div>
    </div>
    <BarScan ref="modalScan" 
      v-model="scancode"
      @decoded="getResult"/>
    <Opc_MaintenanceExecuteEdit ref="modalForm" @ok="modalFormOk" :draggable="true"></Opc_MaintenanceExecuteEdit>
</template>

<script lang="jsx">
// import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolForm from '@/components/basic/VolForm.vue';
import VolTable from "@/components/basic/VolTableEx.vue";
import Opc_MaintenanceExecuteEdit from './Opc_MaintenanceExecuteEdit.vue'
import BarScan from '@/components/basic/Scan.vue';
import {
    mapState
} from 'vuex';
export default {
    components: {
        // ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        Opc_MaintenanceExecuteEdit,
        BarScan,
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    data() {
        return {

            scancode:false,//控制是否使用扫一扫功能
            result: '',//扫码结果信息
            checkboxGroupStatuses: [],
            isActive: false,
            text: "",
            //tabsModel: "0",
            curFormFields: { resourceGroupName: null, resourceName: null, state: ['No Status','Past Due','Due','Pending'],noState:"true",pastDue:"true",due:"true",pending:"true" },
            ApiUrl: {
                GetResourceObject: "/api/Query/GetResourceByType",//获取NameObject
                GetNameObject: "/api/Query/GetNameObject",//获取NameObject
                GetRevisionObject: "/api/query/GetRevisionObject",
            },
            curFormRules:
                [
                    [
                        {
                            field: "resourceGroupName", "title": this.$ts('设备组'), "type": "select", "required": false, "readonly": false,
                            "data": [],
                            // "dataKey": "BIZ_RESOURCE_GROUP",
                            placeholder: this.$ts('设备组'),

                            onChange: (val) => {
                                this.http.post('api/Query/SearchResourceGroupToResource', { resourceGroupName: val })
                                    .then(res => {
                                        this.curFormRules[0][1].data = res.rows;
                                    })
                                    .catch(error => {
                                        // 错误处理
                                        console.error('请求失败或其他错误:', error);
                                    });
                            },
                            colSize: 2,
                        },
                        {
                            field: "resourceName",
                            "title": this.$ts('设备代码'),
                            "type": "string",
                            "required": false,
                            "readonly": false,
                            // "data": [], 
                            // "dataKey": "RESOURCE_NUMBER",
                            placeholder: this.$ts('设备代码'),
                            colSize: 2,
                            extra: {
                                style: "color:#2196F3;cursor: pointer;font-size:12px",
                                icon: "el-icon-search", //显示图标
                                click: () => {
                                    this.scancode = true; // Trigger the scan component
                                },
                            },
                        },


                    ],
                    [
                        {
                            dataKey: "",
                            title: "Task Status",
                            data: [
                                { key: this.$ts('No Status'), value: this.$ts('No Status') },
                                { key: this.$ts('Past Due'), value: this.$ts('Past Due') },
                                { key: this.$ts('Due'), value: this.$ts('Due') },
                                { key: this.$ts('Pending'), value: this.$ts('Pending') }
                            ],
                            min: 0,
                            max: 4,
                            field: "state",
                            type: "checkbox",
                            colSize: 2,
                            required: false,
                            onChange: (selectedValues) => {
                                // console.log(selectedValues, 'selectedValues');
                                // 初始化所有状态为未选中（空字符串）
                                this.curFormFields.noState = "";
                                this.curFormFields.pastDue = "";
                                this.curFormFields.due = "";
                                this.curFormFields.pending = "";
                                selectedValues.forEach(value => {
                                    switch (value) {
                                        case this.$ts('No Status'):
                                            this.curFormFields.noState = "true";
                                            break;
                                        case this.$ts('Past Due'):
                                            this.curFormFields.pastDue = "true";
                                            break;
                                        case this.$ts('Due'):
                                            this.curFormFields.due = "true";
                                            break;
                                        case this.$ts('Pending'):
                                            this.curFormFields.pending = "true";
                                            break;
                                        default:
                                            break;
                                    }
                                });

                            }

                        }
                    ]


                ],
            tableColumns: [
                {
                    field: 'ResourceName', title: this.$ts('设备名称'), type: 'string',
                    render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <i onClick={() => { this.handleEditRow(row, column, index) }} style="color: #2196F3;cursor: pointer;">{row.ResourceName}</i>
                            </div>)
                    }
                },
                {
                    field: 'MaintenanceType', title: this.$ts('保养计划类型'), type: 'string'
                },
                {
                    field: 'MaintenanceReqName', title: this.$ts('保养计划名称'), type: 'string', sort: false, align: 'center'
                },
                {
                    field: 'MaintenanceRevision', title: this.$ts('Requirement Revision'), hidden: true, type: 'string', sort: false, align: 'center'
                },
                {
                    field: 'MaintenanceState', title: this.$ts('保养状态'), type: 'string', bind: { key: 'BIZ_TASK_STATUS' }
                },
                {
                    field: 'NextDateDue', title: this.$ts('下次保养时间'), type: 'string', sort: false, align: 'center',
                },
                {
                    field: 'NextThruputQtyDue', title: this.$ts('下次保养数量'), type: 'string', sort: false, align: 'center',
                }/* ,
                {
                    field: 'MaintenanceStatus', title: this.$ts('维护状态'), type: 'string', sort: true, align: 'center',
                } */
            ],
            tableData: [],
        };
    },
    created() {
        let resourceArry = this.getResourceSelectValue(null, "Resource");
        this.curFormRules[0][1].data = resourceArry;
        let resourceGroupArry = this.getResourceSelectValue(null, "ResourceGroup");
        this.curFormRules[0][0].data = resourceGroupArry;
    },

    methods: {
         //返回扫描结果并关闭摄像头
        getResult(result){
            if(result!==""){
                    this.scancode=false;
                    this.curFormFields.resourceName=result;
                    }
        },
        modalFormOk() {
            this.reset();
        },

        handleEditRow(row, column, index) {
            console.log(row, column, index, '点击行数据');
            if (this.$refs.modalForm.edit(row) === true) {
            }
        },

        async Search(Ref) {
            this.tableData = [];
            if (Ref === 'form1') {
                const validate1 = await this.$refs.form1.validate();
                if (!validate1) {
                    return;
                }
                let postdata = {
                    "User": this.userInfo.userName,
                    "Password": this.userInfo.userPwd,
                    "resourceGroupName": this.curFormFields.resourceGroupName,
                    "resourceName": this.curFormFields.resourceName,
                    "noState": this.curFormFields.noState,
                    "pastDue": this.curFormFields.pastDue,
                    "due": this.curFormFields.due,
                    "pending": this.curFormFields.pending
                };
                console.log(postdata, 'postdata');
                await this.http.post("api/CDO/SearchMaintenanceManagement", postdata).then(res => {
                    if (res.status == 1) {
                        this.tableData = res.rows;

                    } else {
                        this.$message.error(res.message);
                        return;
                    }

                });

            }

        },
        reset() {
            this.$refs.form1.reset();
            this.$refs.table1.reset();
        },

        getRevisionObject(cdoname) {
            let param = {
                // cdo:"ResourceFamily",
                cdo: cdoname,
            }
            let dataArr = []
            this.http.get(this.ApiUrl.GetRevisionObject, param).then(
                res => {
                    if (res.Result == 1) {

                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })

                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }

            )
            return dataArr;
        },
        getResourceSelectValue(typeName, cdoname) {
            let param = {
                // cdo:"ResourceFamily",
                cdo: cdoname,
                type: typeName
            }
            let dataArr = []
            this.http.get(this.ApiUrl.GetResourceObject, param).then(
                res => {
                    if (res.Result == 1) {

                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })

                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }

            )
            return dataArr;
        },

    }
};

</script>



<style lang="less" scoped>
.scan-icon {
    color: #2196F3;
    cursor: pointer;
    font-size: 14px;
    margin-left: 5px;
}
.form-btns {
    text-align: center;
    margin-top: 20px;
}

.tables {
    padding-left: 15px;

    .table-item {
        padding: 10px;
    }

    .table-header {
        display: flex;
        margin-bottom: 8px;
    }

    .header-text {
        position: relative;
        bottom: -9px;
        flex: 1;
        font-weight: bold;
    }

    .header-btns {
        text-align: right;
    }

    .action {
        width: 100%;
        display: flex;

        margin-bottom: 15px;

        .ivu-checkbox-wrapper {
            margin-right: 20px;
        }

        .ck {
            line-height: 33px;
            display: inline-block;
            display: flex;

            label:first-child {
                min-width: 58px;
                float: left;
                margin-top: 1px;
            }

            >div {
                float: left;
            }
        }
    }
}
</style>






