<template>
    <div class="page-header">
        <!-- 选择条件 -->
        <div style="display: flex; margin-top: 5px">
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>组别</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchGroup" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getGroup" @change="selectCondition">
                        <el-option v-for="item in employeeGroups" :key="item.Name" :label="item.Name"
                            :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>模具编号</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="searchTool" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getTool" @change="selectCondition">
                        <el-option v-for="item in tools" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 200px; margin-left: 5px; font-size: 16px">
                    <span>工单</span>
                </label>
                <div style="margin-top: 5px">
                    <el-select v-model="mfgorder" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getMfgOrder" @change="selectMfgOrder">
                        <el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px">
                <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>打印机</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="printer" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="getPrinter">
                        <el-option v-for="item in printers" :key="item.Name" :label="item.Name" :value="item.Description" />
                    </el-select>
                </div>
            </div>
            <div style="margin-left: 10px;">
                <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                    <span>外箱标签不打印</span>
                </label>
                <div style="margin-top: 5px;">
                    <el-switch v-model="printOutBox" size="small" active-text="是" inactive-text="否"></el-switch>
                </div>
            </div>
        </div>
        <div style="display: flex; margin-top: 5px">
            <div style="display: flex; margin-top: 5px">
                <div style="margin-left: 10px">
                    <label style="width: 100px; margin-left: 5px; font-size: 16px">
                        <span>工单数量</span></label>
                    <div style="margin-top: 5px">
                        <el-input v-model="mfgOrderQty" disabled></el-input>
                    </div>
                </div>
            </div>
            <div style="display: flex; margin-top: 5px">
                <div style="margin-left: 10px">
                    <label style="width: 100px; margin-left: 5px; font-size: 16px">
                        <span>产品编码</span></label>
                    <div style="margin-top: 5px">
                        <el-input v-model="product" disabled></el-input>
                    </div>
                </div>
            </div>
            <div style="display: flex; margin-top: 5px">
                <div style="margin-left: 10px">
                    <label style="width: 100px; margin-left: 5px; font-size: 16px">
                        <span>产品描述</span></label>
                    <div style="margin-top: 5px">
                        <el-input v-model="description" disabled></el-input>
                    </div>
                </div>
            </div>
            <div style="display: flex; margin-top: 5px">
                <div style="margin-left: 10px">
                    <label style="width: 100px; margin-left: 5px; font-size: 16px">
                        <span>物料数量</span></label>
                    <div style="margin-top: 5px">
                        <el-input v-model="qty" style="width: 100px" type="number" oninput="form.ekHour = form.ekHour
                .replace(/[^\d|\.]/g, '')
                .replace(/^00/g, '0')
                .replace(/^\./g, '0.')
						"></el-input>
                    </div>
                </div>
            </div>
            <div style="display: flex; margin-top: 5px">
                <div style="margin-left: 10px">
                    <label style="width: 100px; margin-left: 5px; font-size: 16px">
                        <span>最小包装数量</span></label>
                    <div style="margin-top: 5px">
                        <el-input v-model="minPackageQty" @change="changeMinPackageQty" style="width: 100px"
                            type="number" oninput="form.ekHour = form.ekHour
                .replace(/[^\d|\.]/g, '')
                .replace(/^00/g, '0')
                .replace(/^\./g, '0.')
						"></el-input>
                    </div>
                </div>
            </div>
            <div style="display: flex; margin-top: 5px">
                <div style="margin-left: 10px">
                    <label style="width: 100px; margin-left: 5px; font-size: 16px">
                        <span>大箱可装小箱数</span></label>
                    <div style="margin-top: 5px">
                        <el-input v-model="maxInnerQty" style="width: 100px" type="number" oninput="form.ekHour = form.ekHour
                .replace(/[^\d|\.]/g, '')
                .replace(/^00/g, '0')
                .replace(/^\./g, '0.')
						"></el-input>
                    </div>
                </div>
            </div>
        </div>
        <!-- 数据执行 -->
        <div class="table-item">
            <div class="table-item-header">
                <div class="table-item-buttons">
                    <div>
                        <el-button type="primary" icon="printer" @click="submitRow" plain>打印申请</el-button>
                        <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            printOutBox: false,
            searchGroup: null,
            searchTool: null,
            mfgorder: null,
            printer: null,
            mfgOrderQty: null,
            product: null,
            description: null,
            qty: null,
            minPackageQty: null,
            maxInnerQty: null,

            printers: [],
            mfgorders: [],
            tools: [],
            employeeGroups: [],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getMaterialRequestInfo: '/api/query/GetMaterialRequestInfoV2',
                getMfgOrder: '/api/query/GetMfgOrder',
                packageLabelPrintTxn: '/api/CDO/PackageLabelPrintTxn',
                getDefaultPrinter: '/api/query/getDefaultPrinter',
            }

        }
    },
    created() {

    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        getMfgOrder(query) {
            if (query) {
                let params = {
                    mfgorder: query,
                    employeeGroup: this.searchGroup,
                    tool: this.searchTool
                };
                this.http.post(this.apiUrl.getMfgOrder, params).then(res => {
                    if (res.Result == 1) {
                        this.mfgorders = res.Data.map(item => ({ Name: item.MfgOrder }));
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getTool(query) {
            if (query) {
                let params = {
                    cdo: "resource",
                    name: query,
                    objectCategory: "TOOL"
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.tools = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getGroup(query) {
            if (query) {
                let params = {
                    cdo: "employeegroup",
                    name: query,
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.employeeGroups = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getPrinter(query) {
            if (query) {
                let params = {
                    cdo: "PrintQueue",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.printers = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        selectCondition() {
            this.mfgorders = [];
            if (this.searchGroup || this.searchTool) {
                let params = {
                    employeeGroup: this.searchGroup,
                    tool: this.searchTool
                };
                this.http.post(this.apiUrl.getMfgOrder, params, true).then(res => {
                    if (res.Result == 1) {
                        if (res.Data.length == 0) {
                            this.$message.error('未找到工单信息');
                            return;
                        } else {
                            this.mfgorders = res.Data.map(item => ({ Name: item.MfgOrder }));
                        }
                    } else {
                        this.$message.error(res.Message);
                    }
                })
            }
        },
        selectMfgOrder() {
            this.mfgOrderQty = null;
            this.product = null;
            this.description = null;
            this.qty = null;
            this.minPackageQty = null;
            this.maxInnerQty = null;
            if (this.mfgorder) {
                let params = {
                    mfgorder: this.mfgorder
                };
                this.http.post(this.apiUrl.getMfgOrder, params, true).then(res => {
                    if (res.Result == 1) {
                        if (res.Data.length == 0) {
                            this.$message.error('未找到工单信息');
                            return;
                        }
                        this.mfgOrderQty = res.Data[0].MfgOrderQty;
                        this.qty = res.Data[0].MfgOrderQty;
                        this.product = res.Data[0].Product;
                        this.description = res.Data[0].P_Description;
                    } else {
                        this.$message.error(res.Message);
                    }
                })
            }
        },
        getDefaultPrinter() {
            if (this.userInfo.userName) {
                let params = {
                    username: this.userInfo.userName
                };
                this.http.post(this.apiUrl.getDefaultPrinter, params).then(res => {
                    if (res.Result == 1) {
                        this.printers = [{ Name: res.Data.Printer, Description: res.Data.Description }];
                        this.printer = res.Data.Description; // 设置默认选中第一个打印机
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        changeMinPackageQty() {
            if (Number(this.minPackageQty) > Number(this.qty)) {
                this.$message.error('最小包装数量不能大于物料数量');
                this.minPackageQty = null;
            }
        },
        //清除数据
        reset() {
            this.searchGroup = null;
            this.searchTool = null;
            this.mfgorder = null;
            this.printer = null;
            this.mfgOrderQty = null;
            this.product = null;
            this.description = null;
            this.qty = null;
            this.minPackageQty = null;
            this.maxInnerQty = null;
        },
        submitRow() {
            if (!this.mfgorder) {
                this.$message.error('请选择工单');
                return;
            }
            if (!this.printer) {
                this.$message.error('请选择打印机');
                return;
            }
            if (!this.qty) {
                this.$message.error('请输入物料数量');
                return;
            }
            if (!this.minPackageQty) {
                this.$message.error('请输入最小包装数量');
                return;
            }
            if (!this.printOutBox && !this.maxInnerQty) {
                this.$message.error('请输入大箱可装小箱数');
                return;
            }
            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                mfgorder: this.mfgorder,
                qty: this.qty,
                minPackageQty: this.minPackageQty,
                maxInnerQty: this.maxInnerQty,
                printer: this.printer,
                printOutBox: this.printOutBox
            };
            this.http.post(this.apiUrl.packageLabelPrintTxn, params, true).then(res => {
                if (res.Result == 1) {
                    this.reset();
                    this.$message.success(res.Message);
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    // .table-item-text {
    // 	font-weight: bolder;
    // 	border-bottom: 1px solid #0c0c0c;
    // }
    .table-item-text {
        margin-top: 3px;
        padding-bottom: 6px;
        font-weight: bold;
        font-size: 15px;
        color: #484848;
        white-space: nowrap;
        border-bottom: 2px solid #676767;
        margin-bottom: -1px;
        letter-spacing: 1px;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196f3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>
