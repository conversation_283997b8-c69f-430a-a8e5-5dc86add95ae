<template>
    <ModelInfo></ModelInfo>
    <el-tabs v-model="tabsModel">
        <el-tab-pane :label="this.$ts('Tool Register')">
            <div class="container">
                <div class="form-content">
                    <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
                    </VolForm>

                </div>

                <div class="table-item">
                    <div class="table-item-header">
                        <div class="table-item-border"></div>
                        <span class="table-item-text">
                            {{ this.$ts('Applicable Product') }}
                        </span>
                        <div class="table-item-buttons">
                            <div>
                                <el-button type="primary" @click="addRow('table2')" plain>{{ this.$ts('Add')
                                    }}</el-button>
                                <el-button type="primary" @click="delRow('table2')" color="#f89898" plain>{{
                                    this.$ts('Delete') }}</el-button>
                            </div>
                        </div>
                    </div>
                    <!-- <el-alert type="success" title="" style="line-height: 12px;">
                    </el-alert> -->
                    <vol-table @row-click="event => rowClick(event, 'table2')" ref="table2" index
                        :tableData="tableData2" :columns="Biz_PNList" :max-height="500" :pagination-hide="true"
                        :load-key="true" :column-index="true"></vol-table>
                </div>
            </div>
            <div style="text-align: center; width: 100%;margin-top:10px">
                <el-button type="primary" plain @click="formSubmit_Tool">{{ this.$ts('Submit') }}</el-button>
                <!-- <el-button type="success" plain @click="reset">{{ this.$ts('Reset') }}</el-button> -->
            </div>
        </el-tab-pane>

        <el-tab-pane :label="this.$ts('Register Accessory')">
            <div class="container">
                <div class="form-content">
                    <VolForm ref="form1" :loadKey="true" :formFields="formFields_EquipParts"
                        :formRules="formRules_EquipParts">
                    </VolForm>
                </div>
            </div>
            <div style="text-align: center; width: 100%;margin-top:10px">
                <el-button type="primary" plain @click="formSubmit_Part">{{ this.$ts('Submit') }}</el-button>
                <!-- <el-button type="success" plain @click="reset">{{ this.$ts('Reset') }}</el-button> -->
            </div>
        </el-tab-pane>

    </el-tabs>

</template>



<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
//import Opc_EquipRegEdit from './Opc_EquipRegEdit.vue'
//import Opc_EquipRegEdit_Email from './Opc_EquipRegEdit_Email.vue'
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'
//import { readonly } from 'vue';
export default {
    components: {
        ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        // Opc_EquipRegEdit,
        // Opc_EquipRegEdit_Email
    },
    mixins: [GlobalElMessageBox],
    data() {
        return {
            //title: this.$ts('Tool register'),
            tableTitleOne: this.$ts('Email'),
            tableTitleTwo: this.$ts('Applicable material number'),
            tableData2Copy: [],
            //治具注册tab页表单字段
            formFields: {
                ToolFamily: null,//分类代码
                Description: null,//分类描述
                Biz_PhysicalLocation: null,//区域/库房
                Biz_PhysicalPosition: null,//位置/储位
                Biz_AssetDept: null,//保管部门
                Biz_AssetOwner1: null,//保管员

                Biz_AssetVendor: null,//供应商
                VendorModel: null,//型号
                VendorSerialNumber: null,//序列号
                Biz_LifetimeLimit: null,//最大寿命数
                Biz_LifetimeWarning: null,//寿命提醒数

                Biz_Usage: null,//使用次数

                Biz_EquipStatus: null,//状态
                // Biz_PNList: null,
                Biz_MESCode: null,//MES编码

                Name: null,//名称
                Biz_AssetPic: null,//图片
                EmailGroup: null
            },
            //注册tab页表单字段
            formFields_EquipParts: {
                Name: null,
                Description: null,
                PartFamily: null,//分类代码
                PartFamilyDescription: null,//分类描述
                ResourceMaterialPart: null,//配件料号
                ResourceMaterialPartRevision: "1",//版本
                Biz_PhysicalLocation: null,//区域/库房
                Biz_PhysicalPosition: null,//位置/储位
                Factory: null,//工厂
                Biz_AssetVendor: null,//供应商
                VendorModel: null,//型号
                VendorSerialNumber: null,//序列号
                Biz_EquipStatus: null,//设备管理状态
                Employee: null,//领用人、归还人
                Biz_AssetDept: null,//保管部门
                Biz_AssetOwner1: null,//保管员
                Biz_AssetPic: null,//设备图片
                Biz_OldReturn: true,
                PartQty: null//数量

            },
            checked: false,
            formRules: [
                [
                    {

                        title: this.$ts('Tool Name'),
                        placeholder: this.$ts('Tool Name'),
                        filter: true,
                        //required: true, //设置为必选项
                        field: "Name",
                        hidden: true,
                    },
                    {
                        dataKey: "familyType_Tool", //后台下拉框对应的数据字典编号
                        data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                        title: this.$ts('Classification Code'),
                        placeholder: this.$ts('Classification Code'),
                        filter: true,
                        required: true, //设置为必选项
                        field: "ToolFamily",
                        type: "select",
                        onChange: (value) => {
                            let postData_Condition = {
                                resourceFamilyName: value, // 表单
                            };
                            this.http.post('api/Query/SearchResourceFamily', postData_Condition).then(res => {
                                if (res.rows && res.rows.length > 0) {
                                    //console.log(this.curFormFields, 'rows');
                                    console.log(res.rows, 'rows');
                                    this.formFields.Description = res.rows[0].ResourceFamilyDescription;

                                }
                            }).catch(error => {

                                //this.$message.error(error);

                            });
                        },
                    },
                    {
                        title: this.$ts('Classification Description'),
                        placeholder: this.$ts('Classification Description'),
                        filter: true,
                        required: false, //设置为必选项
                        field: "Description",
                        readonly: true,
                        colSize: 3,
                    },
                ],
                [

                    {
                        dataKey: "Biz_PhysicalLocation", //后台下拉框对应的数据字典编号
                        data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                        title: this.$ts('Physical Location'),
                        // required: true,
                        placeholder: this.$ts('Physical Location'),
                        field: "Biz_PhysicalLocation",
                        type: "select",
                        required: true,
                        colSize: 3,
                        onChange: async (value) => {

                            try {
                                // 等待异步方法完成并获取结果
                                const positionData = await this.getPositionByLocation(value);
                                if (positionData && positionData[0].key !== null || positionData[0].value !== '') {
                                    // 如果返回的是数组，则更新数据
                                    this.formRules[1][1].data = positionData;
                                }
                                if (positionData[0].key === null || positionData[0].value === null) {
                                    this.formRules[1][1].required = false;
                                    this.formFields.Biz_PhysicalPosition = null;
                                } else {
                                    this.formRules[1][1].required = true;
                                }
                            } catch (error) {
                                console.error('请求失败或其他错误:', error);
                            }
                        }
                    },
                    {
                        dataKey: "", //后台下拉框对应的数据字典编号
                        data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                        title: this.$ts('Physical Position'),
                        placeholder: "Physical Position",
                        type: "select",
                        required: true,
                        field: "Biz_PhysicalPosition",
                        colSize: 2,
                    },
                ],
                [
                    {
                        title: this.$ts('Custodial department'),
                        // required: true,
                        placeholder: this.$ts('Custodial department'),
                        field: "Biz_AssetDept",
                        required: true,
                    },
                    {
                        title: this.$ts('Custodian'),
                        // required: true,
                        placeholder: this.$ts('Custodian'),
                        field: "Biz_AssetOwner1",
                        required: true,
                    },
                ],
                [
                    {
                        title: this.$ts('Vendor'),
                        // required: true,
                        placeholder: this.$ts('Vendor'),
                        field: "Biz_AssetVendor",
                        colSize: 3,
                        required: true,
                    },
                    {
                        title: this.$ts('Vendor Model'),
                        // required: true,
                        placeholder: this.$ts('Vendor Model'),
                        field: "VendorModel",
                        colSize: 3,
                        required: false,
                    },
                    {
                        title: this.$ts('Vendor Serial Number'),
                        // required: true,
                        placeholder: this.$ts('Vendor Serial Number'),
                        field: "VendorSerialNumber",
                        colSize: 2,
                        required: false,
                    },
                    {
                        title: this.$ts('Image'),
                        // required: true,
                        placeholder: this.$ts('Image'),
                        field: "Biz_AssetPic",
                        type: "img",
                        multiple: true,
                        maxFile: 5,
                        maxSize: 5,
                        url: "api/Demo_Order/Upload",
                        colSize: 2,

                    },
                ],
                [
                    {
                        title: this.$ts('Lifetime Limit'),
                        placeholder: this.$ts('Lifetime Limit'),
                        field: "Biz_LifetimeLimit",
                        type: "number",
                        required: false,
                        onKeyPress: () => {
                            this.formRules[4][2].required = true;
                            if (this.formFields.Biz_LifetimeLimit < 0) this.formFields.Biz_LifetimeLimit = 0;
                            const warningTitle = this.formRules[4] && this.formRules[4][1] ? this.formRules[4][1].title : '';
                            const limitTitle = this.formRules[4] && this.formRules[4][0] ? this.formRules[4][0].title : '';
                            if (this.formFields.Biz_LifetimeLimit < this.formFields.Biz_LifetimeWarning) {
                                this.$message.error(warningTitle + ' ' + this.$ts('cannot be greater than') + ' ' + limitTitle);
                            }
                        },
                        validator: (rule, val) => {
                            if (val === '' || val === null) {
                                this.formRules[4][2].required = false;
                                return ''
                            }
                            if (val && val !== null && val !== '' && val <= 0) {
                                //this.formFields.Biz_LifetimeLimit=0;
                                return '不能是小于0'
                            }
                            if (val <= this.formFields.Biz_LifetimeWarning) {
                                return this.formRules[4][0].title + '不能小于等于' + this.formRules[4][1].title;
                            }
                            return "";
                        },
                    },
                    {
                        filter: true,
                        title: this.$ts('Lifetime Warning'),
                        placeholder: this.$ts('Lifetime Warning'),
                        field: "Biz_LifetimeWarning",
                        // required: true,
                        type: "number",
                        onKeyPress: () => {
                            this.formRules[4][2].required = true;
                            if (this.formFields.Biz_LifetimeWarning < 0) this.formFields.Biz_LifetimeWarning = 0;


                            const warningTitle = this.formRules[4] && this.formRules[4][1] ? this.formRules[4][1].title : '';
                            const limitTitle = this.formRules[4] && this.formRules[4][0] ? this.formRules[4][0].title : '';
                            if (this.formFields.Biz_LifetimeWarning >= this.formFields.Biz_LifetimeLimit) {
                                this.$message.error(warningTitle + ' ' + this.$ts('cannot be greater than or equal to') + ' ' + limitTitle);
                            }
                        },
                        validator: (rule, val) => {

                            // 检查val是否为空或null
                            if (val === '' || val === null) {
                                this.formRules[4][2].required = false;
                                return '';
                            }

                            // 检查val是否小于等于0
                            if (val && val <= 0) {
                                this.formFields.Biz_LifetimeWarning = 0;
                                return '不能是小于0';
                            }

                            // 检查val是否大于等于Biz_LifetimeLimit
                            if (val >= this.formFields.Biz_LifetimeLimit) {

                                const warningTitle = this.formRules[4] && this.formRules[4][1] ? this.formRules[4][1].title : '';
                                const limitTitle = this.formRules[4] && this.formRules[4][0] ? this.formRules[4][0].title : '';
                                return `${warningTitle}不能大于等于${limitTitle}`;
                            }

                            return "";
                        },
                    },
                    {
                        dataKey: "Biz_EmailGroup", //后台下拉框对应的数据字典编号
                        data: [],
                        title: this.$ts("Email Address"), //后台下拉框对应的数据字典编号
                        placeholder: this.$ts("Email Address"),
                        filter: true,
                        hidden: false,
                        //required: true, //设置为必选项
                        type: "select",
                        field: "EmailGroup",
                        colSize: 3,

                    }

                ]
            ],
            //配件注册tab页表单规则
            formRules_EquipParts: [
                [

                    {
                        dataKey: "resourceMateRialPart", //后台下拉框对应的数据字典编号
                        data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                        title: this.$ts('Accessory Part Number'),
                        placeholder: this.$ts('Accessory Part Number'),
                        filter: true,
                        required: true, //设置为必选项
                        field: "ResourceMaterialPart",
                        type: "select",
                        colSize: 3,
                    },
                    {

                        title: this.$ts('Quantity'),
                        placeholder: this.$ts('Quantity'),
                        filter: true,
                        required: true, //设置为必选项
                        field: "PartQty",
                        type: "number",
                        colSize: 3,
                    },

                ],
                [
                    {

                        title: this.$ts('Accessory Name'),
                        placeholder: this.$ts('Accessory Name'),
                        filter: true,
                        required: false, //设置为必选项
                        field: "Name",
                        colSize: 3,
                        hidden: true,
                    },
                    {

                        title: this.$ts('Accessory Image'),
                        placeholder: this.$ts('Accessory Image'),

                        required: false, //设置为必选项
                        field: "Biz_AssetPic",
                        type: "img",
                        multiple: true,
                        maxFile: 5,
                        maxSize: 5,
                        url: "api/Demo_Order/Upload",
                        colSize: 3,
                    },
                    {
                        dataKey: "familyType_Part", //后台下拉框对应的数据字典编号
                        data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                        title: this.$ts('Classification Code'),
                        placeholder: this.$ts('Classification Code'),
                        filter: true,
                        required: true, //设置为必选项
                        field: "PartFamily",
                        type: "select",
                        colSize: 3,
                        onChange: (value) => {
                            let postData_Condition = {
                                resourceFamilyName: value, // 表单
                            };
                            this.http.post('api/Query/SearchResourceFamily', postData_Condition).then(res => {
                                if (res.rows && res.rows.length > 0) {
                                    //console.log(this.curFormFields, 'rows');
                                    console.log(res.rows, 'rows');
                                    this.formFields_EquipParts.Description = res.rows[0].ResourceFamilyDescription;

                                }
                            }).catch(error => {

                                //this.$message.error(error);

                            });
                        },
                    },
                ],
                [
                    {

                        title: this.$ts('Accessory Description'),
                        placeholder: this.$ts('Accessory Description'),
                        filter: true,
                        readonly: true,
                        required: false, //设置为必选项
                        field: "Description", //"PartFamilyDescription",
                        colSize: 3,
                    },
                    {
                        dataKey: "Biz_PhysicalLocation", //后台下拉框对应的数据字典编号
                        data: [],
                        title: this.$ts('Physical Location'),
                        required: true,
                        placeholder: this.$ts('Physical Location'),
                        field: "Biz_PhysicalLocation",
                        type: "select",
                        colSize: 3,
                        onChange: async (value) => {

                            try {
                                // 等待异步方法完成并获取结果
                                const positionData = await this.getPositionByLocation(value);
                                if (positionData && positionData[0].key !== null || positionData[0].value !== '') {
                                    // 如果返回的是数组，则更新数据
                                    this.formRules_EquipParts[2][2].data = positionData;
                                }
                                if (positionData[0].key === null || positionData[0].value === null) {
                                    this.formRules_EquipParts[2][2].required = false;
                                    this.formFields_EquipParts.Biz_PhysicalPosition = null;
                                } else {
                                    this.formRules_EquipParts[2][2].required = true;
                                }
                            } catch (error) {
                                console.error('请求失败或其他错误:', error);
                            }
                        }
                    },
                    {
                        dataKey: "Biz_PhysicalPosition", //后台下拉框对应的数据字典编号
                        data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                        title: this.$ts('Physical Position'),
                        placeholder: this.$ts('Physical Position'),
                        field: "Biz_PhysicalPosition",
                        type: "select",
                        required: true,
                        colSize: 3,
                    },
                ],
                [
                    {
                        title: this.$ts('Custodial department'),
                        required: true,
                        placeholder: this.$ts('Custodial department'),
                        field: "Biz_AssetDept",
                        colSize: 3,
                    },
                    {
                        title: this.$ts('Custodian'),
                        required: true,
                        placeholder: this.$ts('Custodian'),
                        field: "Biz_AssetOwner1",
                        colSize: 3,
                    },
                ],
                [
                    {
                        title: this.$ts('Vendor'),
                        required: true,
                        placeholder: this.$ts('Vendor'),
                        field: "Biz_AssetVendor",
                        colSize: 3,
                    },
                    {
                        title: this.$ts('Vendor Model'),
                        required: true,
                        placeholder: this.$ts('Vendor Model'),
                        field: "VendorModel",
                        colSize: 3,
                    },
                    {
                        title: this.$ts('Vendor Serial Number'),
                        required: true,
                        placeholder: this.$ts('Vendor Serial Number'),
                        field: "VendorSerialNumber",
                        colSize: 3,
                    },
                ]
            ],

            Biz_PNList: [

                {
                    field: 'ProductName', title: this.$ts('Part Number'), type: 'string', bind: { key: 'BIZ_PRODUCT', data: [] }, width: 90, edit: { type: "select", keep: true },
                    onChange: (editRow) => {
                        //let tableData2Copy = JSON.parse(JSON.stringify(this.tableData2));
                        //console.log(editRow, 'onSelectrows');
                        this.http.post('/api/Query/SearchProductDes', { ProductName: editRow.ProductName }).then(res => {

                            editRow.ProductDes = res.rows[0].ProductDes;
                            //console.log(editRow, 'editRow');

                        }).catch(error => {
                            // 错误处理
                            console.error('请求失败或其他错误:', error);
                        });
                        const originalLength = this.tableData2.length;
                        let uniqueRows = new Map();
                        this.tableData2.forEach(row => uniqueRows.set(row.ProductName, row));

                        // 从Map对象中提取数据，生成没有重复行的新数组
                        let filteredTableData2 = Array.from(uniqueRows.values());
                        this.tableData2 = filteredTableData2;
                        console.log(filteredTableData2);
                        if (originalLength > filteredTableData2.length) {
                            // 如果长度有变化，说明有重复行被去除，弹出消息框提示用户
                            this.$message({
                                message: this.$ts('Duplicate rows have been removed'),
                                type: 'error'
                            });
                        }

                    }
                },

                {
                    field: 'ProductDes', title: this.$ts('Part Number Description'), type: 'string', sort: true, width: 180, align: 'center'/* , edit: { type: "select", rule: "phone", keep: true }, */
                    /* blur: (editRow) => {//编辑结束事件
                        console.log(editRow, 'blur');
                    } */
                },
                {
                    field: 'ProductRevision', title: this.$ts('ProductRevision'), hidden: true, sort: true, width: 180, align: 'center',

                },
                {
                    field: 'ProductId', title: this.$ts('ProductId'), hidden: true, sort: true, width: 180, align: 'center',

                }

            ],

            tableData2: [
                /*  {
                     ProductRevision: "1", ProductName: null, ProductDes: null, ProductId: null
                 } */
            ],
            isShow: false,
            model: false,
        }
    },
    
    mounted() {

    },

    methods: {
        previewImg(url) {
            this.base.previewImg(url);
        },
        checkAndSubmit(tableData, formFields) {
            // 遍历tableData检查空行或必填列是否为空
            for (let row of tableData) {
                // 动态地检查必填列是否为空
                if (!row[formFields] || row[formFields].trim() === '') {
                    // 如果必填列为空，弹出提示消息并返回，不执行提交
                    this.$message({
                        message: this.$ts('There is an empty row or the required column is empty, and it cannot be submitted.'),
                        type: 'error'
                    });
                    return false; // 返回false，指示调用者不应继续执行
                }
            }
            return true; // 所有行都检查通过，返回true
        },
        // 治具注册提交表单
        formSubmit_Tool() {
            let fileData = []
            if (this.formFields.Biz_AssetPic !== null && this.formFields.Biz_AssetPic.length > 0) {
                fileData = this.formFields.Biz_AssetPic.map(item => item.path)
                this.formFields.Biz_AssetPic = fileData.join()
                //fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
            }
            // this.$refs.form.validate(()=>{ if(!valid){return;}})
            let postData = {
                ...this.formFields, // 表单
                Biz_PNList: this.tableData2, // table
                Biz_AssetPic: this.formFields.Biz_AssetPic && this.formFields.Biz_AssetPic.length > 0 ? this.formFields.Biz_AssetPic[0].path : null,

                //Biz_EmailList: this.tableData, // table
            };

            /* let fileData = [];
            if (this.formFields.Biz_AssetPic !== null && this.formFields.Biz_AssetPic.length > 0) {
                fileData = this.formFields.Biz_AssetPic.map(item => item.path)
            } else {
                this.formFields.Biz_AssetPic = ''
            }
            this.formFields.Biz_AssetPic = fileData.join(); */

            // 检查 Biz_EmailUrl 列是否有内容
            /* const isEmailFilled = this.checkBizEmailUrlContent();
            if (!isEmailFilled) {
                this.$message.error('Please fill in the email address');

                return;
            } */

            const canSubmit = this.checkAndSubmit(this.tableData2, "ProductName");

            this.$refs.form.validate((valid) => {

                if (!valid || !canSubmit) {

                    return;

                }

                this.http.post('api/CDO/RegistMesTool', JSON.stringify(postData)).then(res => {
                    //console.log(res, 'res');
                    this.resultMessageStay(res.status, res.message);
                    /* console.log(res);
                    if (this.resultMessage(res.status, res.message, "提交成功!", "提交失败!") === true) {
                      //  alert(res.message);

                    } else {
                        console.log('失败!!!');
                    } */


                }).finally(() => {
                    console.log(postData, 'postData');
                    this.$refs.form.reset(this.formFields);
                    // this.$refs.table.reset();
                    this.$refs.table2.reset();
                    this.formFields.Biz_LifetimeLimit = '';
                    this.formFields.Biz_LifetimeWarning = '';
                    this.formRules[4][2].required = false;
                    //this.formRules[4][2].hidden = true;
                });
            });

        },

        // 遍历表格数据，检查 Biz_EmailUrl 列是否有内容
        /* checkBizEmailUrlContent() {
            let isAllFilled = true;
            if (this.formFields.Biz_LifetimeLimit
                && this.formFields.Biz_LifetimeWarning) {
                for (const row of this.tableData) {
                    console.log(row, 'row.Biz_EmailUrl');
                    if (!row.Biz_EmailUrl || row.Biz_EmailUrl.trim() === '' || row.Biz_EmailUrl === null) {
                        isAllFilled = false;
                        break;
                    }
                }
            }
            return isAllFilled;
        }, */
        // 配件注册提交
        formSubmit_Part() {
            if (this.formFields_EquipParts.Biz_AssetPic !== null && this.formFields_EquipParts.Biz_AssetPic.length > 0) {
                fileData = this.formFields_EquipParts.Biz_AssetPic.map(item => item.path)
                this.formFields_EquipParts.Biz_AssetPic = fileData.join()
                //fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
            }

            this.$refs.form1.validate((valid) => {
                if (!valid) {
                    return;
                }
                let postData = {
                    ...this.formFields_EquipParts, // 表单
                    Biz_AssetPic: this.formFields_EquipParts.Biz_AssetPic && this.formFields_EquipParts.Biz_AssetPic.length > 0 ? this.formFields_EquipParts.Biz_AssetPic[0].path : null

                };
                console.log(postData, 'postDataReg');
                this.http.post('api/CDO/RegistMesPart', postData).then(res => {

                    this.resultMessageStay(res.status, res.message);

                }).finally(() => {
                    this.$refs.form1.reset(this.formFields_EquipParts);
                    this.formFields_EquipParts.PartQty = "";

                });
            });

            // 

        },
        addRow(tableRef) {
            const newRow = { /* 新行的数据 */ };
            if (tableRef === 'table1') {

                this.$refs.table.addRow(newRow)
            } else if (tableRef === 'table2') {
                this.$refs.table2.addRow({ ProductRevision: "1", ProductName: null, ProductDescription: null });
                // this.removeDuplicateRowsAndUpdateView();
            }
        },
        delRow(tableRef) {
            if (tableRef === 'table1') {
                this.$refs.table.delRow();
            } else if (tableRef === 'table2') {
                this.$refs.table2.delRow();
            }
            //this.$refs.table.delRow();
            // this.$message.success('success')
        },



        rowClick(event, tableRef) {
            // 从event参数中解构出row, column
            const { row, column } = event;
            // 现在可以根据tableRef执行特定操作
            if (tableRef === 'table1') {
                console.log(event, 'row');
                this.$refs.table.$refs.table.toggleRowSelection(row);
            } else if (tableRef === 'table2') {
                console.log(event, 'row');
                this.$refs.table2.$refs.table.toggleRowSelection(row);
            }
        },

    }
};
</script>
<style lang="less">
.container {
    padding: 10px;
    background: #F3F7FC;

    .form-content {
        border-radius: 5px;
        padding: 10px 0;
        background: #fff;
    }

    .table-item-header {
        display: flex;
        align-items: center;
        padding: 6px;

        .table-item-border {
            height: 15px;
            background: rgb(33, 150, 243);
            width: 5px;
            border-radius: 10px;
            position: relative;
            margin-right: 5px;
        }

        .table-item-text {
            font-weight: bolder;
        }

        .table-item-buttons {
            flex: 1;
            text-align: right;
        }

        .small-text {
            font-size: 12px;
            color: #2196F3;
            margin-left: 10px;
            position: relative;
            top: 2px;
        }
    }
}
</style>