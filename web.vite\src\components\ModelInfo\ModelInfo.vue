<template>
  <div class="ModelInfo-Container">
    <el-form ref="ruleFormRef" style="max-width: 100%" :model="ruleForm" :rules="rules" label-width="auto"
      :inline="true" class="demo-ruleForm" size="small" status-icon>
      <el-row>
        <el-col :xl="5" :lg="5" :md="5" :sm="5">
          <el-form-item :label="this.$ts('Time')" prop="Time" style="width: 100%;">
            <el-input v-model="ruleForm.time" disabled />
          </el-form-item>
        </el-col>
        <el-col :xl="5" :lg="5" :md="5" :sm="5">
          <el-form-item :label="this.$ts('Shift')" prop="Shift" style="width: 100%;">
            <el-select v-model="ruleForm.shift" filterable :placeholder="this.$ts('Shift')" clearable @change="ShiftChange"  >
              <el-option v-for="(item, index) in shiftArr" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="5" :lg="5" :md="5" :sm="5">
          <el-form-item :label="this.$ts('User')" prop="User" style="width: 100%;">
            <el-input v-model="ruleForm.user" disabled />
          </el-form-item>
        </el-col>
        <el-col :xl="5" :lg="5" :md="5" :sm="5">
          <el-form-item :label="this.$ts('Mfg Line')" prop="MfgLine" style="width: 100%;">
            <el-select v-model="ruleForm.line" :placeholder="this.$ts('Mfg Line')" clearable filterable @change="LineChange">
              <el-option v-for="(item, index) in lineArr" :label="item.value" :value="item.key"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="4" :lg="4" :md="4" :sm="4">
          <el-form-item :label="this.$ts('Spec')" prop="Spec" style="width: 100%;">
            <el-select v-model="ruleForm.spec" :placeholder="this.$ts('Spec')" clearable filterable @change="SpecChange">
              <el-option v-for="(item, index) in specArr" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xl="6" :lg="6" :md="6" :sm="6">
          <el-form-item :label="this.$ts('PrintQueue')" prop="PrintQueue" style="width: 100%;">
            <el-select v-model="ruleForm.print" :placeholder="this.$ts('PrintQueue')" clearable filterable @change="PrintQueueChange">
              <el-option v-for="(item, index) in printArr" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="6" :md="6" :sm="6">
          <el-form-item :label="this.$ts('Equipment code')" prop="EquipmentCode" style="width: 100%;">
            <el-select v-model="ruleForm.resource" :placeholder="this.$ts('Equipment code')" clearable filterable @change="ResourceChange">
              <el-option v-for="(item, index) in resourceArr" :label="item.value" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="6" :md="6" :sm="6" style="text-align: center">
          <el-form-item>
            <el-link v-if="ruleForm.sop" :href="ruleForm.sop" target="_blank">{{this.$ts('SOP')}}</el-link>
          </el-form-item>
        </el-col>

        <el-col :xl="6" :lg="6" :md="6" :sm="6" style="text-align: right">
          <el-form-item>
            <el-button type="primary" @click="handleFormSave()" size="mini" plain>{{this.$ts('Save')}}</el-button>
            <el-button type="primary" @click="handleFormReset()" size="mini" plain>{{this.$ts('Reset')}}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import {
  defineComponent,
  ref,
  onMounted,
  computed,
  reactive,
  toRefs,
  onBeforeUnmount,
  getCurrentInstance
} from 'vue';
// import common from '@/uitils/common.js';
import { formateDate } from '@/uitils/common.js';
import store from '@/store/index';
import { mapState } from 'vuex'
import { useStore } from 'vuex'
export default {
  data() {
    return {
      timer: 0,
      Time: this.$ts('Time'),
      Shift: this.$ts('Shift'),
      User: this.$ts('User'),
      MfgLine: this.$ts('Mfg Line'),
      Spec: this.$ts('Spec'),
      PrintQueue: this.$ts('PrintQueue'),
      EquipmentCode: this.$ts('Equipment code'),
      ruleForm: {
        time: null,
        shift: null,
        user: null,
        line: null,
        spec: null,
        print: null,
        resource:null,
        sop:"",
      },
      rules: {
        Time: [
          { required: false, message: 'time', trigger: 'blur' },
        ],
      },
      url: {
        shift: '',
        spec: '',
      },
      shiftArr: [],
      lineArr: [],
      specArr: [],
      printArr: [],
      resourceArr: []
    }
  },
  computed: {
    ...mapState({ userInfo: (state) => state.userInfo })
  },
  mounted() {
    this.timer = setInterval(() => {
      this.ruleForm.time = formateDate()
    }, 1000)
    this.ruleForm.user = this.userInfo.userName
    this.getCodeData()
    this.getModelInfo()
    this.getPrintQueueData();
  },
  methods: {


    // 保存数据
    handleFormSave() {

      localStorage.setItem('modelInfo', JSON.stringify(this.ruleForm));
      store.dispatch('handleModelInfo', this.ruleForm);
      this.$message.success(this.$ts('Execution Success!'));
      this.$emit('ModleSave', this.ruleForm);

    },
    //重置
    handleFormReset() {
      this.ruleForm.shift = null;
        this.ruleForm.line = null;
        this.ruleForm.spec =null;
        this.ruleForm.print = null;
        this.ruleForm.resource = null;
        localStorage.setItem('modelInfo', JSON.stringify(this.ruleForm));
        // store.dispatch('handleModelInfo', this.ruleForm);
        this.$message.success(this.$ts('Execution Success!'));
    },
    async getModelInfo() {
      let modelInfo = await localStorage.getItem('modelInfo')
      console.log(JSON.parse(modelInfo), 'modelInfo');
      let data = JSON.parse(modelInfo)
      if (data !== null) {
        this.ruleForm.shift = data.shift
        this.ruleForm.line = data.line
        this.ruleForm.spec = data.spec
        this.ruleForm.print = data.print
        this.ruleForm.resource = data.resource
      }
      store.dispatch('handleModelInfo', data);
    },
    async getCodeData() {
      let keys = ['shift','headerMfgLine','hearderSpec']
      let res = await this.http.post('/api/Sys_Dictionary/GetVueDictionary', keys)
      console.log(res, 'res');
      if (res !== null && res.length > 0) {
        if (res[0].dicNo == keys[0]) {
          this.shiftArr = res[0].data
        } else {
          this.shiftArr = []
        }
        if (res[1].dicNo == keys[1]) {
          this.lineArr = res[1].data
        } else {
          this.lineArr = []
        }
        if (res[2].dicNo == keys[2]) {
          this.specArr = res[2].data
        } else {
          this.specArr = []
        }
      }
    },
    async getPrintQueueData() {
      let modelInfo = await localStorage.getItem('modelInfo');
      let data = JSON.parse(modelInfo);
      let selectLine = { MfgLine:null };
      let selectSpec = { Spec:null };
      if (data !== null) {
        selectLine ={ MfgLine:data.line };
        selectSpec ={ Spec:data.spec }
      }
      console.log(selectLine, 'modelInfoLine');
      console.log(selectSpec, 'modelInfoSpec');
      let LineResult = await this.http.post('/api/DropdownList/PrintQueueSelect', selectLine);
      if (LineResult !== null && LineResult.rows.length > 0) {
        console.log(LineResult.rows,"LineResult");
        this.printArr = LineResult.rows;
      }
      else
      {
        this.printArr = [];
      }
      let ResourceResult = await this.http.post('/api/DropdownList/ResourceForSpecSelect', selectSpec);
      if (ResourceResult !== null && ResourceResult.rows.length > 0) {
        console.log(ResourceResult.rows,"ResourceResult");
        this.resourceArr = ResourceResult.rows;
      }
      else
      {
        this.resourceArr = [];
      }
    },
    async LineChange(selVal)
    {
      let selectLine = { MfgLine:selVal };
      this.handleFormSave();
      this.$emit('LineChange', selectLine.MfgLine);
      let LineResult = await this.http.post('/api/DropdownList/PrintQueueSelect', selectLine);
      if (LineResult !== null && LineResult.rows.length > 0) {
        // console.log(LineResult.rows,"LineResult");
        this.printArr = LineResult.rows;
      }
      else
      {
        this.printArr = [];
      }
    },
    async SpecChange(selVal)
    {
      let selectSpec = { Spec:selVal };
      console.log(selectSpec, 'selectSpec');
      this.handleFormSave();
      this.$emit('SpecChange', selectSpec.Spec);
      let ResourceResult = await this.http.post('/api/DropdownList/ResourceForSpecSelect', selectSpec);
      if (ResourceResult !== null && ResourceResult.rows.length > 0) {
        console.log(ResourceResult.rows,"ResourceResult");
        this.resourceArr = ResourceResult.rows;
      }
      else
      {
        this.resourceArr = [];
      }
      this.ruleForm.resource ='';

      //加载SOP
      this.ruleForm.sop=await this.http.post('/api/DropdownList/GetSopBySpec', selectSpec);
    },
    PrintQueueChange(selVal){
      this.handleFormSave();
  },
  ResourceChange(selVal){
    this.handleFormSave();
  },
  ShiftChange(selVal){
    this.handleFormSave();
  }
  },
  beforeUnmount() {
    console.log("销毁前")
    clearInterval(this.timer) //清除定时器
    this.timer = 0
  },
  unmounted() {
    // console.log("销毁后")
  },
}

</script>
<style lang="less" scoped>
.ModelInfo-Container {
  width: 100%;
  display: flex;
  // justify-content: space-between;
  align-items: end;
  padding: 10px 1%;
  //height: 100%;
  font-size: 10px;
  color: #333;
  background-color: #ffff;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  .item {
    min-width: 10%;
    margin: 0 1% 0 1%;
  }
}
</style>