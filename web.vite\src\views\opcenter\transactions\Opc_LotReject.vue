<template>
  <div class="lot-reject">
    <VolHeader title="品质不合格品管理" />
    <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules">
      <div style="text-align: end; margin-top: 0px; width: 100%">
        <div style="margin-right: 20px; margin-top: -39px">
          <el-button type="primary" plain @click="search">查询</el-button>
          <el-button type="success" plain @click="outputRow">导出</el-button>
        </div>
      </div>
    </VolForm>
    <vol-table
      ref="table"
      index
      :loadKey="true"
      :ck="false"
      :columns="columns"
      :tableData="tableData"
      :pagination-hide="false"
      :max-height="160"
      @rowClick="rowClick"
      @loadBefore="loadBefore"
      @loadAfter="loadAfter"
      :defaultLoadPage="false"
      :url="ApiUrl.GetLotReject"
    ></vol-table>
    <div style="display: flex; margin-top: 5px; margin-bottom: 10px">
      <VolForm ref="form1" :loadKey="true" :formFields="formFields" :formRules="formRules">
        <div style="text-align: end; margin-top: 0px; width: 100%">
          <div style="margin-right: 30px; margin-top: -39px">
            <el-button type="primary" @click="submit">提交处理</el-button>
          </div>
        </div>
      </VolForm>
    </div>
  </div>
</template>
  <script lang="jsx">
      import VolTable from "@/components/basic/VolTable.vue";
      import VolForm from '@/components/basic/VolForm.vue';
      import VolHeader from '@/components/basic/VolHeader.vue';
      import VolBox from '@/components/basic/VolBox.vue';
      import Excel from '@/uitils/xlsl.js';
      import {
          mapState
      } from 'vuex';
      export default {
          components: {
              VolHeader,
              VolForm,
              'vol-table': VolTable,
              'vol-box': VolBox
          },
          computed: {
              ...mapState({
                  //获取当前用户的信息
                  userInfo: state => state.userInfo,
                  //获取当前用户的权限
                  permission: state => state.permission,
              })
          },
          //初始化页面
          created() {
              this.GetWorkCenter();
              this.GetDept();
              this.GetReworkWorkflow();
              this.GetDefectReason();
              this.GetLossReason();
              this.GetEmployee();
          },
  
          data() {
              return {
                fullNames:[],
                wfDes:[],
                  ApiUrl: {
                      GetRevisionObject: "/api/query/GetRevisionObject",
                      GetNameObject: "/api/query/GetNameObject",
                      GetReworkWorkflow: '/api/Query/GetReworkWorkflow', //获取返工工作流程
                      GetLotReject: '/api/Query/GetLotReject', //获取品质不合格品记录
                      LotRejectTxn:'/api/CDO/LotRejectTxn',
                      GetResourceBySpec:'/api/Query/GetResourceBySpec', //根据工序获取设备
                      GetMoveContainerSpec:'/api/Query/GetMoveContainerSpec', //根据批次获取工序
                      GetEmployee:'/api/Query/GetEmployee' //获取责任人
                  },
                  headerFields: {
                      selectLot: '',
                      workCenter: '',
                      product: '',
                      handlerStatus: '',
                      createTime: ''
                  },
                  headerRules: [
                      [{
                              title: this.$ts('扫描批次码'),
                              placeholder: this.$ts(''),
                              field: "selectLot",
                              type: "text",
                              readonly: false,
                              colSize: 2,
                              onKeyPress: ($event) => {
                                if ($event.keyCode == 13 && $event.type == 'keyup') {
                                    this.search();
				                }
                              },
                          },
                          {
                              data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                              dataKey: '', //后台下拉框对应的数据字典编号
                              title: this.$ts('生产车间'),
                              placeholder: this.$ts(''),
                              field: "workCenter",
                              type: "select",
                              colSize: 2,
                          },
                          {
                              data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                              dataKey: '', //后台下拉框对应的数据字典编号
                              title: this.$ts('产品编码'),
                              placeholder: this.$ts(''),
                              field: "product",
                              type: "select",
                              colSize: 2,
                              url: '/api/query/GetRevisionObject',
                              params: {cdo: 'Product'},
                          },
                          {
                            data: [
                                    {key: "待判断",value: "待判断"},
                                    {key: "关闭",value: "关闭"}
                                ], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                              dataKey: '', //后台下拉框对应的数据字典编号
                              title: this.$ts('处理状态'),
                              placeholder: this.$ts(''),
                              field: "handlerStatus",
                              type: "select",
                              colSize: 2,
                          },
                          {
                              title: this.$ts('登记时间范围'),
                              placeholder: this.$ts(''),
                              field: "planDate",
                              type: "datetime",
                              range: true,
                              colSize: 2,
                          },  
                      ],
                  ],
                  columns: [
                      { field: 'HistoryId', title: '异常登记历史记录ID', type: 'string', width: 130, align: 'center',hidden:true},
                      { field: 'Container', title: '批次码', type: 'string', width: 150, align: 'center'},
                      { field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center'},
                      { field: 'ProductDesc', title: '产品名称描述', type: 'string', width: 100, align: 'center'},
                      { field: 'Qty', title: '批次数量', type: 'string', width: 130, align: 'center'},
                      { field: 'Uom', title: '单位（UOM）', type: 'string', width: 105, align: 'center'},
                      { field: 'CurrentStep', title: '当前工序', type: 'string', width: 130, align: 'center' },
                      { field: 'ExceptionReason', title: '异常原因', type: 'string', width: 130, align: 'center' },
                      { field: 'ExceptionHandlerStatus', title: '异常处理状态', type: 'string', width: 130, align: 'center'},
                      { field: 'Resource', title: '设备/line', type: 'string', width: 80, align: 'center' },
                      { field: 'MfgOrder', title: '生产工单', type: 'string', width: 130, align: 'center' },
                      { field: 'MfgQty', title: '工单数量', type: 'string', width: 130, align: 'center' },
                      { field: 'Workflow', title: '工作流程', type: 'string', width: 130, align: 'center' },
                      { field: 'Employee', title: '异常登记人', type: 'string', width: 130, align: 'center'},
                      { field: 'CreateTime', title: '异常登记时间', type: 'datetime', width: 150, align: 'center'},
                      { field: 'ExceptionRemark', title: '异常备注', type: 'string', width: 130, align: 'center' },
                      { field: 'Shift', title: '班次', type: 'string', width: 130, align: 'center'},

                      { field: 'JudgmentResult', title: '判断结果', type: 'string', width: 130, align: 'center' },
                      { field: 'ReworkWorkflow', title: '到返工工作流程', type: 'string', width: 130, align: 'center' },
                      { field: 'Remark', title: '处理备注', type: 'string', width: 130, align: 'center' },
                      { field: 'EmployeeName', title: '异常责任人', type: 'string', width: 130, align: 'center'},
                      { field: 'DeptName', title: '异常责任单位', type: 'string', width: 130, align: 'center'},
                      { field: 'CommitUser', title: '提交人', type: 'string', width: 130, align: 'center'},
                      { field: 'CommitTime', title: '提交时间', type: 'datetime', width: 150, align: 'center'},
                  ],
                  formFields:{
                      historyId:'',
                      containerName:'',
                      exceptionReason: '',
                      exceptionRemark: '',
                      result: '',
                      reworkWorkflow:'',
                      defectReason:'',
                      lossReason:'',
                      scrapQty:'',
                      employee: '',
                      dept: '',
                      remark:'',
                      resource:'',
                      spec:''
                  },
                  formRules:[
                        [{
                                title: this.$ts('异常登记历史记录ID'),
                                placeholder: this.$ts(''),
                                field: "historyId",
                                type: "text",
                                hidden: true,
                                readonly: true,
                                colSize: 4,
                            },  
                            {
                                title: this.$ts('批次码'),
                                placeholder: this.$ts(''),
                                field: "containerName",
                                type: "text",
                                hidden: false,
                                readonly: true,
                                colSize: 2,
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('异常原因'),
                                placeholder: this.$ts(''),
                                field: "exceptionReason",
                                type: "select",
                                readonly: true,
                                colSize: 2,
                            },                          
                        ],
                        [
                            {
                                title: this.$ts('异常备注'),
                                placeholder: this.$ts(''),
                                field: "exceptionRemark",
                                type: "textarea",
                                readonly: true,
                                colSize: 10,
                                minRows: 2,
                            }
                        ],
                        [
                            {
                                title: this.$ts('判断结果'),
                                placeholder: this.$ts(''),
                                field: "result",
                                type: "radio",
                                hidden: false,
                                colSize: 10,
                                dataKey: "",
                                data: [
                                    {key: 1,value: "让步接收"},
                                    {key: 2,value: "返工处理"},
                                    {key: 3,value: "报废处理"},
                                    {key: 4,value: "误判"}
                                ],
                                onChange:(val)=>{
                                    this.formFields.reworkWorkflow = null;
                                    this.formFields.defectReason = null;
                                    this.formFields.scrapQty = null;
                                    //this.formFields.employee = this.userInfo.userName;
                                    this.formFields.dept = null;
                                   switch(val){
                                        case 1:
                                            this.formRules[3][0].hidden=true;
                                            this.formRules[3][1].hidden=false; 
                                            this.formRules[3][2].hidden=true; 
                                            this.formRules[3][3].hidden=true; 
                                            this.formRules[3][4].hidden=false; 
                                            this.formRules[3][5].hidden=false;  
                                            this.formRules[3][6].hidden=false;  
                                            this.formRules[3][7].hidden=false;  
                                            break;
                                        case 2:
                                            this.formRules[3][0].hidden=false;
                                            this.formRules[3][1].hidden=false; 
                                            this.formRules[3][2].hidden=true; 
                                            this.formRules[3][3].hidden=true; 
                                            this.formRules[3][4].hidden=false; 
                                            this.formRules[3][6].hidden=false; 
                                            this.formRules[3][7].hidden=false; 
                                            this.formRules[3][5].hidden=false;  
                                            break;
                                        case 3: 
                                            this.formRules[3][0].hidden=true;
                                            this.formRules[3][1].hidden=true; 
                                            this.formRules[3][2].hidden=false; 
                                            this.formRules[3][3].hidden=false; 
                                            this.formRules[3][4].hidden=false; 
                                            this.formRules[3][5].hidden=false; 
                                            this.formRules[3][6].hidden=false; 
                                            this.formRules[3][7].hidden=false; 
                                            break;
                                        case 4:
                                            this.formRules[3][0].hidden=true;
                                            this.formRules[3][1].hidden=true; 
                                            this.formRules[3][2].hidden=true; 
                                            this.formRules[3][3].hidden=true; 
                                            this.formRules[3][4].hidden=true; 
                                            this.formRules[3][5].hidden=true;  
                                            this.formRules[3][6].hidden=true; 
                                            this.formRules[3][7].hidden=true;
                                            break;             
                                   }                                   
                                }
                            },
                        ],
                        [{
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('到返工流程'),
                                placeholder: this.$ts(''),
                                field: "reworkWorkflow",
                                type: "select",
                                hidden: true,
                                colSize: 2,
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('不良项'),
                                placeholder: this.$ts(''),
                                field: "defectReason",
                                type: "select",
                                hidden: true,
                                colSize: 2,
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('报废原因'),
                                placeholder: this.$ts(''),
                                field: "lossReason",
                                type: "select",
                                hidden: true,
                                colSize: 2,
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('报废数量'),
                                placeholder: this.$ts(''),
                                field: "scrapQty",
                                type: "number",
                                hidden: true,
                                colSize: 2,
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('异常责任人'),
                                placeholder: this.$ts(''),
                                field: "employee",
                                type: "select",
                                hidden: true,
                                colSize: 2,
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('异常责任部门'),
                                placeholder: this.$ts(''),
                                field: "dept",
                                type: "select",
                                hidden: true,
                                colSize: 2,
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('异常处理工序'),
                                placeholder: this.$ts(''),
                                field: "spec",
                                type: "select",
                                hidden: true,
                                colSize: 2,
                                onChange: (val) => {
                                    this.GetResource(val);
                                }, 
                            },
                            {
                                data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                                dataKey: '', //后台下拉框对应的数据字典编号
                                title: this.$ts('异常处理设备'),
                                placeholder: this.$ts(''),
                                field: "resource",
                                type: "select",
                                hidden: true,
                                colSize: 2,
                            }
                        ],
                        [
                            {
                                title: this.$ts('处理备注'),
                                placeholder: this.$ts(''),
                                field: "remark",
                                type: "textarea",
                                readonly: false,
                                colSize: 10,
                                minRows: 3,
                            }
                        ],
                    ],
                  tableData: []
  
              }
          },
          methods: {
             //获取生产车间
             async GetWorkCenter(){
                  let params = {
                      cdo: "WorkCenter"
                  };
                  this.http.get(this.ApiUrl.GetNameObject,params).then(res => {
                      if (res.Result == 1) {
                        this.headerRules[0][1].data = res.Data.map((item)=> {return {key:item.Name,value:item.Name}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //获取产品
              async GetProduct(){
                  let params = {
                      cdo: "Product"
                  };
                  this.http.get(this.ApiUrl.GetRevisionObject,params).then(res => {
                      if (res.Result == 1) {
                        this.headerRules[0][2].data = res.Data.map((item)=> {return {key:item.Name,value:item.Name}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //获取工序
              async GetSpec(container){
                  let params = {
                    container: container
                  };
                  this.http.get(this.ApiUrl.GetMoveContainerSpec,params).then(res => {
                      if (res.Result == 1) {
                        this.formRules[3][6].data = res.Data.map((item)=> {return {key:item.Name+':'+item.Version,value:item.Name+':'+item.Version}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //获取设备
              async GetResource(val){
                  let params = {
                    spec: val.split(":")[0]
                  }
                  this.http.get(this.ApiUrl.GetResourceBySpec,params).then(res => {
                      if (res.Result == 1) {
                        this.formRules[3][7].data = res.Data.map((item)=> {return {key:item.Name,value:item.Name}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //获取责任人
              async GetEmployee() {
                  let params = {
                      name: ""
                  };
                  this.http.get(this.ApiUrl.GetEmployee, params).then(res => {
                      if (res.Result == 1) {
                        this.fullNames = res.Data.map((item) => { return { key: item.value, value: item.label } });
                        this.formRules[3][4].data = this.fullNames;
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //获取部门
              async GetDept(){
                  let params = {
                      cdo: "Department"
                  };
                  this.http.get(this.ApiUrl.GetNameObject,params).then(res => {
                      if (res.Result == 1) {
                        this.formRules[3][5].data = res.Data.map((item)=> {return {key:item.Name,value:item.Name}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //获取返工工作流程
              async GetReworkWorkflow(){
                this.http.get(this.ApiUrl.GetReworkWorkflow).then(res => {
                    if (res.Result == 1) {
                        this.wfDes = res.Data.map((item)=> {return {key:item.Name,value:item.Description}});
                        this.formRules[3][0].data = this.wfDes;
                    } else {
                        this.$message.error(res.Message);
                    }
                }).catch(err => {
                    this.$message.error(err);
                })
              },
              //获取不良项
              async GetDefectReason(){
                  let params = {
                      cdo: "isDefectReason"
                  };
                  this.http.get(this.ApiUrl.GetNameObject,params).then(res => {
                      if (res.Result == 1) {
                        this.formRules[3][1].data = res.Data.map((item)=> {return {key:item.Name,value:item.Name}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //报废原因
              async GetLossReason(){
                  let params = {
                      cdo: "LossReason"
                  };
                  this.http.get(this.ApiUrl.GetNameObject,params).then(res => {
                      if (res.Result == 1) {
                        this.formRules[3][2].data = res.Data.map((item)=> {return {key:item.Name,value:item.Name}});
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //提交
              submit() {
                  if (this.formFields.historyId == null || this.formFields.historyId == '') {
                      this.$message.warning('请选择行');
                      return;
                  }
                  if (this.formFields.result == null || this.formFields.result == '') {
                      this.$message.warning('请选择判定结果');
                      return;
                  }
                  if(this.formFields.result !=4&&(this.formFields.employee == null || this.formFields.employee == '')){
                      this.$message.warning('请输入异常责任人');
                      return;
                  }

                  let fullName = this.fullNames.find(item => item.key == this.formFields.employee);
                  let decription = this.wfDes.find(item => item.key == this.formFields.reworkWorkflow);

                  let param = {
                      User:this.userInfo.userName,
                      Password:this.userInfo.userPwd,
                      HistoryId: this.formFields.historyId,
                      ContainerName:this.formFields.containerName,
                      HandlerStatus:"关闭",
                      CreateUser: this.userInfo.userName,
                      DeptName: this.formFields.dept,
                      EmployeeName:this.formFields.employee,
                      JudgmentResult: this.formFields.result,
                      ReworkWorkflow: this.formFields.reworkWorkflow,
                      DefectReason:  this.formFields.result=="3"?this.formFields.lossReason:this.formFields.defectReason,
                      ScrapQty:this.formFields.scrapQty,
                      Remark: this.formFields.remark,
                      Spec:null,
                      Resource: null,
                      FullName:fullName != null ? fullName.value : "",
                      Decription:decription != null ? decription.value : ""
                  }
                  if(this.formFields.spec!=null || this.formFields.spec!=''){
                      param.Spec = {Name:this.formFields.spec.split(":")[0],Version:this.formFields.spec.split(":")[1]}
                    }
                    if(this.formFields.resource!=null || this.formFields.resource!=''){
                      param.Resource = {Name:this.formFields.resource}
                    }
                  this.http.post(this.ApiUrl.LotRejectTxn, param).then((res) => {
                      if (res.Result == 1) {
                          this.$Message.success('提交处理成功');
                          this.reset();
                          this.search();
                      } else {
                          this.$Message.error(res.Message);
                      }
                  })
              },
              reset(){
                this.$refs.form1.reset(this.formFields);
              },
              //查询
              search(){
                //   this.reset();

                  this.$refs.table.load(null, true);
              },
              //查询界面table点击行选中当前行
              rowClick({row,column,event}) { 
                if(row.ExceptionHandlerStatus == "待判断"){
                    this.formFields.historyId=row.HistoryId;
                    this.formFields.containerName=row.Container;
                    this.formFields.exceptionReason=row.ExceptionReason;
                    this.formFields.exceptionRemark=row.ExceptionRemark;  
                    this.GetSpec(row.Container);
                }

              },
              //导出
              outputRow(){
                  let tableData = this.$refs.table.tableData
                  let sortData = this.$refs.table.filterColumns
                  let exportData = this.handleTableSortData(tableData, sortData)
                  Excel.exportExcel(exportData, "批次异常登记详情" + '-' + this.base.getDate());
              },
              handleTableSortData(tableData, sortData) {
                  let newArray = [];
                  tableData.forEach(data => {
                      let newItem = {};
                      sortData.forEach(field => {
                          if (data.hasOwnProperty(field.field)) {
                              newItem[field.title] = data[field.field];
                          }
                      });
                      newArray.push(newItem);
                  });
                  return newArray
              },
              loadBefore(params,callBack) {
            let param = {
                container: this.headerFields.selectLot,
                      workcenter: this.headerFields.workCenter,
                      product: this.headerFields.product,
                      handlerStatus: this.headerFields.handlerStatus,
                      startTime: this.headerFields.createTime != null?this.headerFields.createTime[0] : null,
                      endTime: this.headerFields.createTime != null?this.headerFields.createTime[1] : null,
                PageSize: this.$refs.table.paginations.size,
                PageCount: this.$refs.table.paginations.page,
            };
            params = Object.assign(params, param);
            callBack(true);
        },
        loadAfter(rows, callBack, result) {
           if(result.Result == 1) {
            //this.columns = result.Data.colums;
            this.tableData = result.Data.tableData;
            this.$refs.table.rowData = result.Data.tableData;
            this.$refs.table.paginations.total = result.Data.total;
           }
           else{
            this.$message.error(result.Message);
           }
            callBack(false);
        },
          },
  
  
      }
  </script>