<template>
    <el-tabs v-model="tabsModel">
        <el-tab-pane :label="this.$ts('Request Accessories')">
            <div class="container">
                <div class="form-content">

                    <VolForm ref="form1" :loadKey="true" :formFields="formFields2" :formRules="formRules2">
                        <VolForm ref="form2" :loadKey="true" :formFields="formFields" :formRules="formRules"></VolForm>
                    </VolForm>
                </div>
            </div>
            <div style="text-align: center; width: 100%;margin-top:10px">
                <el-button type="primary" plain @click="formSubmit_Tool">{{ this.$ts('Submit') }}</el-button>
                <el-button type="success" plain @click="resetd">{{ this.$ts('Reset') }}</el-button>
            </div>
        </el-tab-pane>

        <el-tab-pane :label="this.$ts('Request Accessory Cancellation')">
            <div class="container">
                <!-- <div class="form-content" @keyup.enter.native="onKeyCancel"> -->
                    <div class="form-content">
                    <VolForm ref="form3" :loadKey="true" :formFields="formFields_EquipParts"
                        :formRules="formRules_EquipParts">
                        <div style="margin-top: 20px; width:1000px; height:200px; margin-left:10px;">
                            <vol-table ref="table" index :tableData="tableData" :columns="columnst" :max-height="220"
                                :pagination-hide="true" :load-key="true" :column-index="true"></vol-table>
                        </div>
                    </VolForm>
                </div>
            </div>
            <div style="text-align: center; width: 100%;margin-top:10px">
                <el-button type="primary" plain @click="formSubmit_Part">{{ this.$ts('Submit') }}</el-button>
            </div>
        </el-tab-pane>

    </el-tabs>

</template>



<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import { mapState } from 'vuex'
export default {
    components: {
        ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable,
    },
    data() {
        return {
            tableTitleOne: this.$ts('Request Accessories'),
            tableTitleTwo: this.$ts('Cancel Request Accessories'),
            //配件领用tab页表单字段
            formFields2: {
                Accessory_code: null,
            },
            formFields: {
                Accessory_description: null,
                Equipment_code: null,
                Accessory_number: null,
                Inventory_quantity: null,
                Receiver: null,
                Received_quantity: null,
                Trade_in: null,
                Remarks: null,
            },
            //配件领用取消tab页表单字段
            formFields_EquipParts: {
                Equipment_code: null,
                Accessory_code: null,

            },
            checked: false,
            formRules2: [
                [{
                    dataKey: "Accessorycode",
                    title: this.$ts('Accessory Code'),//配件编码
                    placeholder: this.$ts('Accessory Code'),
                    filter: true,
                    required: true, //设置为必选项
                    type: "select",
                    field: "Accessory_code",
                    data:[], //数据源
                    colSize: 5,
                    onChange: (rows, options) => {
                        let postData = {
                            partName: this.formFields2.Accessory_code
                        };
                        //领用
                        this.http.post('api/Query/SearchPart', postData).then(res => {
                            //console.log(res, 'res');
                            if (res.status !== '0') {
                                this.formFields.Accessory_description = res.rows[0].Description;
                                this.formFields.Accessory_number = res.rows[0].ResourceMaterialPart;
                                this.formFields.version = res.rows[0].ResourceMaterialPartRevision;
                                this.formFields.Inventory_quantity = res.rows[0].PartQty;
                            } else {
                                this.formFields.Accessory_description = '';
                                this.formFields.Accessory_number = '';
                                this.formFields.version = '';
                                this.formFields.Inventory_quantity = '';
                            }
                        });
                    },
                },],
            ],
            formRules: [

                [{
                    title: this.$ts('Accessory Description'),//配件描述
                    filter: true,
                    readonly: true,
                    required: false, //设置为必选项
                    type: "text",
                    field: "Accessory_description",
                    colSize: 5,

                },
                {
                    title: this.$ts('Remaining Quantity'),//库存数
                    readonly: true,
                    filter: true,
                    required: false, //设置为必选项
                    type: "text",
                    field: "Inventory_quantity",
                    colSize: 5,

                },],
                [
                    {
                        title: this.$ts('Accessory Part Number'),//配件料号
                        filter: true,
                        readonly: true,
                        required: false, //设置为必选项
                        type: "text",
                        field: "Accessory_number",
                        colSize: 5,
                    },
                    {
                        title: this.$ts('Version'),//版本
                        filter: true,
                        readonly: true,
                        required: false, //设置为必选项
                        type: "text",
                        field: "version",
                        colSize: 5,
                    },
                ],
                [
                    {
                        title: this.$ts('Equipment code'),//设备编码
                        placeholder: this.$ts('Equipment code'),
                        filter: true,
                        required: true, //设置为必选项
                        type: "select",
                        field: "Equipment_code",
                        colSize: 5,
                        data:[],
                    },
                ],
                [
                    {
                        dataKey: 'EquipIMRTech',
                        title: this.$ts('Receiver'),
                        placeholder: this.$ts('Receiver'),
                        filter: true,
                        required: true, //设置为必选项
                        type: "select",
                        field: "Receiver",
                        colSize: 5,

                    },
                    {
                        title: this.$ts('Quantity Received'),//领用数量
                        filter: true,
                        required: true, //设置为必选项
                        type: "text",
                        field: "Received_quantity",
                        colSize: 5,

                    },],
                [{
                    dataKey: '是否',
                    title: this.$ts('Trade-in'),//以旧换新
                    placeholder: this.$ts('Trade-in'),
                    filter: true,
                    required: true, //设置为必选项
                    type: "select",
                    field: "Trade_in",
                    data:[{
                      key:1,value:"是"  
                    },{
                      key:0,value:"否"
                    }
                    ],
                    colSize: 5,

                },],
                [{
                    title: this.$ts('Remark'),//备注
                    filter: true,
                    required: false, //设置为必选项
                    type: "textarea",
                    field: "Remarks",
                    colSize: 5,

                },],
            ],
            //配件领用取消
            formRules_EquipParts: [[{
                dataKey: "Accessorycode",
                title: this.$ts('Accessory Code'),//配件编码
                placeholder: this.$ts('Accessory Code'),
                filter: true,
                required: true, //设置为必选项
                type: "select",
                field: "Accessory_code",
                colSize: 5,
                data: [],
                onChange: (rows, options) => {
                    let postData = {
                        partName: this.formFields_EquipParts.Accessory_code
                    };
                    //归还
                    this.http.post('api/Query/SearchPartBorrowTask', postData).then(res => {
                        if (res.rows.length > 0) {
                            this.tableData = [];
                            for (let i = 0; i < res.rows.length; i++) {
                                this.tableData.push({
                                    "TaskName": res.rows[i].TaskName,
                                    "PartName": res.rows[i].PartName,
                                    "EquipBorrower": res.rows[i].EquipBorrower,
                                    "EquipBorrowTime": res.rows[i].EquipBorrowTime,
                                    "EquipBorrowQty": res.rows[i].EquipBorrowQty,
                                    "EquipRemark": res.rows[i].EquipRemark,
                                    "ResourceMaterialPart": res.rows[i].ResourceMaterialPart,
                                    "ResourceMaterialPartRevision": res.rows[i].ResourceMaterialPartRevision,
                                    "PartBorrowActualQty": res.rows[i].PartBorrowActualQty,
                                    "ActualQty": res.rows[i].PartBorrowActualQty,
                                    "PartQty": res.rows[i].YP_PartQty
                                });
                            }
                        } else {
                            this.tableData = [];
                        }
                    });
                },
            }],],
            columnst: [
                { field: 'ResourceMaterialPart', title: this.$ts('Task No.'), type: 'string', width: 120, align: 'center', readonly: true, hidden: true },
                { field: 'ResourceMaterialPartRevision', title: this.$ts('ResourceMaterialPartRevision'), type: 'string', width: 120, align: 'center', readonly: true, hidden: true },
                { field: 'TaskName', title: this.$ts('Task No.'), type: 'string', width: 120, align: 'center', readonly: true, },
                { field: 'PartName', title: this.$ts('Accessory Code'), type: 'string', width: 100, align: 'center', readonly: true, },
                { field: 'EquipBorrower', title: this.$ts('Receiver'), type: 'string', width: 100, align: 'center', readonly: true, },
                { field: 'EquipBorrowTime', title: this.$ts('Received Time'), type: 'string', width: 150, align: 'center', readonly: true, },
                { field: 'EquipBorrowQty', title: this.$ts('Quantity Received'), type: 'string', width: 100, align: 'center', readonly: true, },
                { field: 'EquipRemark', title: this.$ts('Remark'), type: 'string', width: 100, align: 'center', readonly: true, },
                {
                    field: 'ReturnQty', title: this.$ts('Return Quantity'), type: 'number', width: 100, align: 'center', required: true, edit: { type: "text" },
                },
                { field: 'PartBorrowActualQty', title: this.$ts('Actual quantity received'), type: 'string', width: 100, align: 'center', readonly: true, },
                { field: 'ActualQty', title: this.$ts('Actual quantity received'), type: 'string', width: 100, align: 'center', hidden: true, },
                { field: 'PartQty', title: this.$ts('PartQty'), type: 'string', width: 100, align: 'center', hidden: true, },
            ],
            // columnst2: [
            //     { field: 'PartName', title: this.$ts('Accessory code'), type: 'string', width: 100, align: 'center', readonly: true, },
            //     { field: 'Receiver', title: this.$ts('Receiver'), type: 'string', width: 100, align: 'center', readonly: true, },
            //     { field: 'Received_time', title: this.$ts('Received time'), type: 'string', width: 150, align: 'center', readonly: true, },
            //     { field: 'Received_quantity', title: this.$ts('Received quantity'), type: 'string', width: 100, align: 'center', readonly: true, },
            //     { field: 'Received_quantity', title: this.$ts('Return quantity'), type: 'string', width: 100, align: 'center', readonly: true, },
            //     { field: 'Actual_quantity_received', title: this.$ts('Actual quantity received'), type: 'string', width: 100, align: 'center', readonly: true, },
            // ],
            tableData: [],
            tableData2: [],
            //隐藏分页
            paginationHide: true,
            isShow: false,
            model: false,
            ApiUrl: {
                GetResourceObject: "/api/Query/GetResourceByType",//获取NameObject
            }
        }
    },
    computed: {
        ...mapState({
            userInfo: state => state.userInfo
        })
    },
    mounted() {
        this.onInit();
        this.formRules_EquipParts[0][0].data = this.formRules2[0][0].data =  this.selectResource('RESOURCE','part');
        this.formRules[2][0].data =  this.selectResource('RESOURCE','RESOURCE')
    },
    methods: {
        clearfrom(jsonObj) {
            for (var key in jsonObj) {
                // 检查属性是否是对象自身的属性，而不是继承自原型链的属性
                if (jsonObj.hasOwnProperty(key)) {
                    // 将每个属性的值设置为null
                    jsonObj[key] = null;
                }
            }
        },
        selectResource(cdoName,typeName){
      let param={
                // cdo:"ResourceFamily",
                cdo:cdoName,
                type:typeName
            }
            let dataArr=[]
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        
                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
               
            )
            return dataArr;
    },
        // 查询
        // onKeySearch($event, data) {
        //     let postData = {
        //         partName: this.formFields2.Accessory_code
        //     };
        //     //领用
        //     this.http.post('api/YP_EquipBorrowTask/SearchPart', postData).then(res => {
        //         //console.log(res, 'res');
        //         if (res.status !== '0') {
        //             this.formFields.Accessory_description = res.rows[0].Description;
        //             this.formFields.Accessory_number = res.rows[0].ResourceMaterialPart;
        //             this.formFields.version = res.rows[0].ResourceMaterialPartRevision;
        //             this.formFields.Inventory_quantity = res.rows[0].PartQty;
        //         } else {
        //             this.formFields.Accessory_description = '';
        //             this.formFields.Accessory_number = '';
        //             this.formFields.version = '';
        //             this.formFields.Inventory_quantity = '';
        //         }
        //     });
        // },
        //取消
        // onKeyCancel($event, data) {
        //     let postData = {
        //         partName: this.formFields_EquipParts.Accessory_code
        //     };
        //     //归还
        //     this.http.post('api/YP_EquipBorrowTask/SearchPartBorrowTask', postData).then(res => {
        //         if (res.rows.length > 0) {
        //             this.tableData = [];
        //             for (let i = 0; i < res.rows.length; i++) {
        //                 this.tableData.push({
        //                     "TaskName": res.rows[i].TaskName,
        //                     "PartName": res.rows[i].PartName,
        //                     "EquipBorrower": res.rows[i].EquipBorrower,
        //                     "EquipBorrowTime": res.rows[i].EquipBorrowTime,
        //                     "EquipBorrowQty": res.rows[i].EquipBorrowQty,
        //                     "EquipRemark": res.rows[i].EquipRemark,
        //                     "ResourceMaterialPart": res.rows[i].ResourceMaterialPart,
        //                     "ResourceMaterialPartRevision": res.rows[i].ResourceMaterialPartRevision,
        //                     "PartBorrowActualQty": res.rows[i].PartBorrowActualQty,
        //                     "ActualQty": res.rows[i].PartBorrowActualQty
        //                 });
        //             }
        //         } else {
        //             this.tableData = [];
        //         }
        //     });
        // },
        // 领用提交
        formSubmit_Tool() {
            this.$refs.form1.validate(() => {
                this.$refs.form2.validate(() => {
                    const postData = {
                        "partName": this.formFields2.Accessory_code,
                        "partQty": this.formFields.Inventory_quantity,
                        "resourceMaterialPart": this.formFields.Accessory_number,
                        "resourceMaterialPartRevision": this.formFields.version,
                        "YP_MatchingEquip": this.formFields.Equipment_code,
                        "YP_EquipStatus": "领用",
                        "YP_EquipBorrower": this.formFields.Receiver,
                        "YP_EquipBorrowQty": this.formFields.Received_quantity,
                        "YP_OldPartReturn": this.formFields.Trade_in == 1 ? true : false,
                        "YP_EquipRemark": this.formFields.Remarks,
                        "YP_EquipOperator": this.userInfo.userName
                    };
                    this.http.post('api/CDO/AddPartBorrowTask', postData).then(res => {
                        if (res.status !== '0') {
                            this.$message.success(this.$ts('Success'));
                            this.$refs.form1.reset();
                            this.$refs.form2.reset();
                        } else {
                            this.$message.error(res.message);
                        }

                    });
                });
            });
        },
        resetd() {
            this.clearfrom(this.formFields);
            this.clearfrom(this.formFields2);
        },
        onInit() {
            this.columnst.forEach((x) => {
                if (x.field == 'ReturnQty') {
                    x.onKeyPress = (row, event) => {
                        const rows = this.$refs.table.getSelected();
                        if (!rows.length) {
                            //请选中行
                            this.$message.error(this.$ts('Please select the row'));
                            row.ReturnQty = '';
                            return;
                        }
                        //归还数量+实际领用数量<=领用数量
                        if (Number(row.ReturnQty) > Number(row.ActualQty)) {
                            this.$message.error(this.$ts('Cannot be greater than the available quantity'));
                            row.ReturnQty = '';
                        } else {
                            row.PartBorrowActualQty = Number(row.ActualQty) - Number(row.ReturnQty);
                        }
                    };
                }
            });
        },
        // 领用归还
        formSubmit_Part() {
            this.$refs.form3.validate(() => {
                const rows = this.$refs.table.getSelected();
                if (!rows.length || rows.length > 1) {
                    //请选中行
                    this.$message.error(this.$ts('Please select the row'))
                    return;
                }
                if (rows[0].ReturnQty == '' || rows[0].ReturnQty == null) {
                    this.$message.error(this.$ts('Please enter a quantity.'))
                    return;
                }
                if (Number.isInteger(parseInt(rows[0].ReturnQty, 10)) && parseInt(rows[0].ReturnQty, 10) < 0) {
                    this.$message.error(this.$ts('Its msut be Integer.'))
                    return;
                }
                const postData = {
                    "TaskName": rows[0].TaskName,
                    "PartName": rows[0].PartName,
                    "YP_PartQty": rows[0].PartQty,
                    "ResourceMaterialPart": rows[0].ResourceMaterialPart,
                    "ResourceMaterialPartRevision": rows[0].ResourceMaterialPartRevision,
                    "EquipBorrower": rows[0].EquipBorrower,
                    "EquipBorrowTime": rows[0].EquipBorrowTime,
                    "EquipBorrowQty": rows[0].EquipBorrowQty,
                    "EquipRemark": rows[0].EquipRemark,
                    "PartBorrowActualQty": rows[0].PartBorrowActualQty,
                    "ReturnQty": rows[0].ReturnQty
                };
                this.http.post('api/CDO/AddPartReturnBorrowTask', postData).then(res => {
                    if (res.status !== '0') {
                        this.$message.success(this.$ts("Submitted Successfully") + res.message);
                    } else {
                        this.$message.error(res.message);
                    }

                });
            });
        },
    }
};
</script>
<style lang="less" scoped>
.container {
    padding: 10px;
    background: #F3F7FC;

    .form-content {
        border-radius: 5px;
        padding: 10px 0;
        background: #fff;
    }

    .table-item-header {
        display: flex;
        align-items: center;
        padding: 6px;

        .table-item-border {
            height: 15px;
            background: rgb(33, 150, 243);
            width: 5px;
            border-radius: 10px;
            position: relative;
            margin-right: 5px;
        }

        .table-item-text {
            font-weight: bolder;
        }

        .table-item-buttons {
            flex: 1;
            text-align: right;
        }

        .small-text {
            font-size: 12px;
            color: #2196F3;
            margin-left: 10px;
            position: relative;
            top: 2px;
        }
    }
}
</style>