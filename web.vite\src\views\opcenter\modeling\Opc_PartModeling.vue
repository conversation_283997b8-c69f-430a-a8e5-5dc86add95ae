<template>
  <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules"></VolForm>
  <div style="text-align: end; margin-top: 10px; width: 100%">
    <div style="margin-right: 20px">
      <el-button type="primary" icon="Search" plain @click="search">查询</el-button>
      <el-button type="success" icon="Plus" @click="addRow" plain>新建</el-button>
      <el-button type="primary" icon="Edit" @click="getRow" plain>编辑</el-button>
      <!-- <el-button type="danger" icon="Delete" @click="delRow" plain>删除</el-button> -->
      <el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
    </div>
  </div>
  <vol-table
    ref="table"
    index
    :column-index="true"
    :reserveSelection="true"
    :loadKey="true"
    :columns="columns"
    :tableData="tableData"
    :ck="true"
    :pagination-hide="false"
    :max-height="380"
    :url="ApiUrl.QueryResourceInfo"
    :defaultLoadPage="false"
    :single="true"
    @rowClick="rowClick"
    @loadBefore="loadBefore"
    @loadAfter="loadAfter"
  ></vol-table>

  <!-- 编辑弹出框 -->
  <vol-box :lazy="true" v-model="show" title="Part维护" :width="800" :padding="5">
    <VolForm ref="form1" :loadKey="true" :formFields="boxFields" :formRules="boxRules"></VolForm>
    <template #footer>
      <div>
        <el-button type="success" icon="Check" size="small" @click="save">保存</el-button>
        <el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
      </div>
    </template>
  </vol-box>
</template>
	
	<script lang="jsx">
import VolTable from '@/components/basic/VolTable.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolHeader from '@/components/basic/VolHeader.vue'
import VolBox from '@/components/basic/VolBox.vue'
import { mapState } from 'vuex'
import Excel from '@/uitils/xlsl.js'
export default {
  components: {
    VolHeader,
    VolForm,
    'vol-table': VolTable,
    'vol-box': VolBox
  },
  computed: {
    ...mapState({
      //获取当前用户的信息
      userInfo: (state) => state.userInfo,
      //获取当前用户的权限
      permission: (state) => state.permission
    })
  },
  data() {
    return {
      ApiUrl: {
        QueryResourceInfo: '/api/Query/QueryResourceInfoDetails', //查询设备台账信息
        SearchPosition:'api/Query/SearchPosition', //查询设备位置
        SearchLocation:'api/Query/SearchLocation', //查询设备区域
        GetResourceObject: "/api/Query/GetResourceByType",//获取NameObject
        GetRevisionObject: "/api/query/GetRevObjByType",
        AddPart: 'api/CDO/PartCreatTxn', //创建Part
        EidtPart: 'api/CDO/PartEidtTxn' //修改Part
      },
      headerFields: {
        cdoName: 'Part',
        StartTime: null,
        EndTime: null
      },
      headerRules: [
        [
          {
            title: this.$ts('开始时间'),
            placeholder: this.$ts(''),
            required: false,
            field: 'StartTime',
            type: 'datetime',
            colSize:3
          },
          {
            title: this.$ts('结束时间'),
            placeholder: this.$ts(''),
            required: false,
            field: 'EndTime',
            type: 'datetime',
            colSize:3
          }
        ]
      ],
      columns: [
        {
          field: 'resourceName',
          title: '设备编号',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'locationName',
          title: '设备区域',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'resourceTypeName',
          title: '设备类型',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'Description',
          title: '描述',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'VendorName',
          title: '供应商',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'PartQty',
          title: 'PartQty',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'PartExpiryDate',
          title: 'Part有效期',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'Comments',
          title: '备注',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'PhysicalLocationName',
          title: '区域',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'PhysicalPositionName',
          title: '位置',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'ProductName',
          title: 'MaterialPart',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'CreationDate',
          title: '创建时间',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'CreationUserName',
          title: '创建人员',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        },
        {
          field: 'YP_SubscriptionCycle',
          title: '购买周期',
          type: 'string',
          width: 130,
          align: 'center',
          hidden: false
        }
      ],
      tableData: [],
      show: false,
      boxFields: {
        MaterialPart: null,
        Qty: null,
        PartNo: null,
        PhysicalLocation: null,
        PhysicalPosition: null,
        Vendor: null,
        YP_SubscriptionCycle: null,
        CommentStr: null,
        UOM: null
      },
      boxRules: [
        [
          {
            //dataKey: 'Biz_EmailGroup', //后台下拉框对应的数据字典编号
            data: [],
            title: this.$ts('备件编码'),
            placeholder: this.$ts(''),
            filter: true,
            required: true, //设置为必选项
            field: 'MaterialPart',
            colSize: 3,
            type: 'select'
          },
          {
            title: this.$ts('数量'),
            placeholder: this.$ts(''),
            filter: true,
            required: true, //设置为必选项
            field: 'Qty',
            colSize: 3,
            type: 'text'
          },
          {
            title: this.$ts('序列号'),
            placeholder: this.$ts(''),
            filter: true,
            required: true, //设置为必选项
            field: 'PartNo',
            colSize: 3,
            type: 'text'
          }
        ],
        [
          {
            title: this.$ts('区域'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'PhysicalLocation',
            colSize: 3,
            type: 'select',
            data: [],
            onChange: (val) => {
              let params = {
                LocationName :val
              }
              let array = []
              this.http.post(this.ApiUrl.SearchPosition, params).then((res) => {
                if(res.status=="1"){
                  this.boxRules[1][1].data = res.rows.map((item)=> {return {key:item.PositionName,value:item.PositionName}});
                  // this.boxRules[1][2].data = res.rows
                }
                else{
                  this.$message.error(res.message)
                }
              })
            }
          },
          {
            title: this.$ts('位置'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'PhysicalPosition',
            colSize: 3,
            type: 'select',
            data: []
          },
          {
            title: this.$ts('供应商'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'Vendor',
            colSize: 3,
            type: 'select',
            data: []
          }
        ],
        [
          {
            title: this.$ts('单位'),
            placeholder: this.$ts(''),
            filter: true,
            required: true, //设置为必选项
            field: 'UOM',
            colSize: 3,
            type: 'select',
            data: [],
            hidden: true
          },
          {
            title: this.$ts('订购周期'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'YP_SubscriptionCycle',
            colSize: 3,
            type: 'text',
          }
        ],
        [
          {
            title: this.$ts('备注'),
            placeholder: this.$ts(''),
            filter: true,
            required: false, //设置为必选项
            field: 'CommentStr',
            colSize: 10,
            type: 'textarea'
          }
        ]
      ],
      eventName: null
    }
  },
  methods: {
    search() {
      this.ResetInfo()
      this.$refs.table.load(null, true)
    },
    loadBefore(params, callBack) {
      let param = {
        startDate: this.headerFields.StartTime,
        endDate: this.headerFields.EndTime,
        cdoName: 'Part',
        PageSize: this.$refs.table.paginations.size,
        PageCount: this.$refs.table.paginations.page
      }
      params = Object.assign(params, param)
      callBack(true)
    },
    loadAfter(rows, callBack, result) {
      if (result.Result == 1) {
        //this.columns = result.Data.colums;
        this.tableData = result.Data.tableData
        this.$refs.table.rowData = result.Data.tableData
        this.$refs.table.paginations.total = result.Data.total
      } else {
        this.$message.error(result.Message)
      }
      callBack(false)
    },
    addRow() {
      // this.tableData.push({ OrderNo: "D2022040600009" })
      this.ResetInfo()
      ;(this.eventName = 'add'), (this.show = true)
    },
    ResetInfo() {
      this.reset(this.boxFields)
      this.reset(this.headerFields)
      this.selectLocation(); //加载区域
      this.boxRules[1][2].data =  this.selectResource("Vendor","Vendor");//供应商
      this.selectProdct();
    },
    reset(object) {
      console.log('重置')
      for (const key in object) {
        object[key] = ''
      }
    },
    rowClick({ row, column, index }) {
      // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
      this.$refs.table.$refs.table.toggleRowSelection(row);
    },
    getRow() {
      const rows = this.$refs.table.getSelected()
      if (!rows.length) {
        this.$message.error('请选中行')
        return
      }
      ;this.eventName = 'edit',
        // this.$message.success(JSON.stringify(rows))
        this.boxFields.CommentStr = rows[0].Comments
        this.boxFields.PartNo = rows[0].resourceName
        this.boxFields.PhysicalLocation = rows[0].PhysicalLocationName
        this.boxFields.PhysicalPosition = rows[0].PhysicalPositionName
        this.boxFields.MaterialPart = rows[0].ProductName + ':' + rows[0].ProductRevision
        this.boxFields.YP_SubscriptionCycle = rows[0].YP_SubscriptionCycle
        this.boxFields.UOM = rows[0].UOM
        this.boxFields.Qty = rows[0].PartQty
        this.boxFields.Vendor = rows[0].VendorName
        this.show = true
    },

    outputRow() {
      // this.tableData.splice(0);
      //导出
      let tableData = this.$refs.table.tableData
      let sortData = this.$refs.table.filterColumns
      let exportData = this.handleTableSortData(tableData, sortData)
      Excel.exportExcel(exportData, '备品备件台账信息' + '-' + this.base.getDate())
    },
    handleTableSortData(tableData, sortData) {
      let newArray = []
      tableData.forEach((data) => {
        let newItem = {}
        sortData.forEach((field) => {
          if (data.hasOwnProperty(field.field)) {
            newItem[field.title] = data[field.field]
          }
        })
        newArray.push(newItem)
      })
      return newArray
    },

    save() {
      //保存
      let params = {
        User: this.userInfo.userName,
        Password: this.userInfo.userPwd,
        Comments: this.boxFields.CommentStr,
        PartNo: this.boxFields.PartNo,
        PhysicalLocation: this.boxFields.PhysicalLocation,
        PhysicalPosition: this.boxFields.PhysicalPosition,
        ProductName: this.boxFields.MaterialPart.split(':')[0],
        ProductRevision: this.boxFields.MaterialPart.split(':')[1],
        YP_SubscriptionCycle: this.boxFields.YP_SubscriptionCycle,
        UOM:this.boxFields.UOM,
        Qty:this.boxFields.Qty,
        Vendor:this.boxFields.Vendor
      }
      if (this.eventName == 'add') {
        this.http.post(this.ApiUrl.AddPart, params).then((res) => {
          if (res.Result == 1) {
            this.show = false
            this.ResetInfo()
            this.search()
            this.$message.success(res.Message)
          } else {
            this.$message.error(res.Message)
          }
        })
      } else if (this.eventName == 'edit') {
        this.http.post(this.ApiUrl.EidtPart, params, true).then((res) => {
          if (res.Result == 1) {
            this.show = false
            this.ResetInfo()
            this.search()
            this.$message.success(res.Message)
          } else {
            this.$message.error(res.Message)
          }
        })
      } else {
        this.$message.error('未知操作')
      }
    },
    close() {
      this.show = false
    },
    selectResource(cdoName,typeName){
      let param={
                // cdo:"ResourceFamily",
                cdo:cdoName,
                type:typeName
            }
            let dataArr=[]
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        
                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
               
            )
            return dataArr;
    },
    selectLocation(){
      let param={
        LocationName:null
      }
      this.http.post(this.ApiUrl.SearchLocation,param).then(res=>{
        if(res.status=="1"){
                 this.boxRules[1][0].data = res.rows.map((item)=> {return {key:item.LocationName,value:item.LocationName}});
                }
                else{
                  this.$message.error(res.message)
                }
      })
    },
    selectProdct(){
      let param ={
        cdo:"Product",
        type:"ResourceMaterialPart"
      }
      this.http.get(this.ApiUrl.GetRevisionObject,param).then(res=>{
        if(res.Result==1){
          this.boxRules[0][0].data = res.Data.map(item=>{
            return{key:item.Name+':'+item.Version,value:item.Name+':'+item.Version}
          })
        }
        else{
          this.$message.error(res.Message)
        }
      })
    }
  }
}
</script>