<template>
  <vol-box :lazy="true" v-model="visible" :title="title" :width="width" :padding="5" :onModelClose="onModelClose">
    <!-- 弹出框内容 -->
    <div class="form-content" :style="style">
      <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
        <!-- <VolHeader :title="this.$ts(title)" :text="text" icon="el-icon-s-grid"></VolHeader> -->
        <!-- <div style="text-align: end;width: 100%; margin-top: 0px;">
          <el-button type="primary" plain @click="getForm">{{ this.$ts('Search') }}</el-button>
          <el-button type="success" plain @click="reset">{{ this.$ts('Reset') }}</el-button>
        </div> -->
      </VolForm>
    </div>

    <template #footer>
      <div>
        <el-button type="primary" size="small" @click="handleModelOk">{{ this.$ts('Confirm') }}</el-button>
        <el-button type="default" size="small" @click="closeModel">{{ this.$ts('Close') }}</el-button>
      </div>
    </template>
  </vol-box>
</template>
<script>
import VolBox from '@/components/basic/VolBox.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
export default {
  components: {
    'vol-box': VolBox,
    VolHeader,
    VolForm
  },
  methods: {},
  data() {
    return {
      visible: false,
      model: {},
      title: this.$ts('Edit'),
      text: this.$ts('Default'),
      width: 900,
      style: { 'max-height': '500px' },
      formFields: {
        ResourceName: null,
        Biz_EquipIMRSup: null,
        Description: null,
        Biz_RepairingAttach: null,
      },
      searchFormFields: { "mfgOrderName": "" },
      formRules: [
        [
          {
            dataKey: "resourcedef",
            data: [],
            title: this.$ts('equipment number'),
            placeholder: this.$ts('equipment number'),
            filter: true,
            required: true,
            field: "ResourceName",
            type: "select",
            colSize: 12,
          },
        ],
        [
          {
            title: this.$ts("Maintenance Manager"),
            placeholder: this.$ts("Maintenance Manager"),
            filter: true,
            required: true,
            disabled: false,
            field: "Biz_EquipIMRSup",
            type: "input",
            colSize: 12,
          },
        ],
        [
          {
            title: this.$ts("Fault description"),
            placeholder: this.$ts("Fault description"),
            required: true,
            field: "Description",
            type: "textarea",
            min: 3,
            max: 5,
            colSize: 12,
          },
        ],
        // [
        //   {
        //     title: this.$ts("affix"),
        //     required: true,
        //     field: "Biz_RepairingAttach",
        //     type: "img",
        //     multiple: true,
        //     maxFile: 5,
        //     maxSize: 5,
        //     url: "api/Demo_Order/Upload",
        //     colSize: 12,
        //   }
        // ],
      ],
      paginationHide: true,
      url: {
        add: '/api/JobOrder/AddJobOrder',
      }
    };
  },
  methods: {

    add() {
      this.edit({});
    },

    edit(record) {
      this.model = Object.assign({}, record);
      this.model.mfgOrderName = record.MfgOrderName
      console.log(this.model, 'record')

      if (!this.model.id) {
        this.model = {};
      } else {
        this.formFields = Object.assign({}, this.model)

      }
      this.visible = true;
    },


    handleModelOk() {
      this.$refs.form.validate((err) => {
        let fileData = []
        if (this.formFields.Biz_RepairingAttach !== null && this.formFields.Biz_RepairingAttach.length > 0) {
          fileData = this.formFields.Biz_RepairingAttach.map(item => item.path)
        }
        this.formFields.Biz_RepairingAttach = fileData.join()
        let params = this.formFields
        console.log(params, '表单数据');
        this.http.post(this.url.add, params, true).then((res) => {
          console.log(res, 'res')
          if (res.status == 1) {
            this.$message.warning(res.message)
          } else {
            this.$message.warning(res.message)
          }
        }).catch((err) => {
          this.$message.warning(err.message)
        })
      })
    },

    // 获取表单数据
    getForm() {
      this.$refs.form.validate((err) => {
        console.log(this.formFields, '表单数据');
      })
      // this.$refs.table.load(null, true)
    },
    search() {
      //查询
      // let query = this.getSearchParameters();
      // this.$refs.table.load(query, true);
      this.$refs.table.load(null, true)
    },
    reset() {
      this.$refs.form.reset(this.searchFormFields);
    },

    onModelClose() {
      this.closeModel()
    },
    closeModel() {
      this.model = {};
      this.visible = false;
    }
  }
};
</script>