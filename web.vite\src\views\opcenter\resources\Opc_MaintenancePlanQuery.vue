<template>
    <!-- <ModelInfo></ModelInfo> -->
    <div style="padding: 15px 20px 15px 5px">
        <div class="pre-text">{{ text }}</div>

        <VolForm ref="form1" :formFields="curFormFields" :formRules="curFormRules">
        </VolForm>

        <div class="table-item">
            <div class="table-item-header">
                <div class="table-item-border"></div> <span class="table-item-text">{{ this.$ts('Equipment') }}</span>
                <div class="table-item-buttons">
                    <div>
                        <!-- <el-tooltip content="支持模糊条件以及多条件(使用回车换行分隔)" placement="top">
                            <i class="el-icon-info" style="cursor: pointer;"></i>
                        </el-tooltip>
                        <el-input type="textarea" style="width: 340px; margin-right: 10px;" v-model="filterText"
                            placeholder="过滤结果"></el-input> -->

                        <!-- <el-button type="primary" @click="reload" color="#95d475" plain>{{ this.$ts('Search')
                            }}</el-button> -->
                    </div>
                </div>
            </div>

            <vol-table ref="table1" @update:sort="sortChange" index :tableData="tableData"
                @rowClick="rowClick" :columns="tableColumns" :max-height="500"
                :rowKey="rowKey" :column-index="true" @pageSizeChanged="pageSizeChanged" :paginationHide="false"

                :load-key="true" @pageSizesChanged_="pageSizeChanged" @selection-change="onSelectionChange"></vol-table>
            <!-- :current-page="paginations.page" :page-sizes="paginations.sizes"
                :page-size="paginations.size" :total="paginations.total" @update:size="handleSizeChange"
                @update:current="handleCurrentChange" -->
        </div>

        <div class="form-btns">
            <el-button type="primary" @click="Submit()" v-loading.fullscreen.lock="fullscreenLoading"><i
                    class="el-icon-check">
                    {{ this.$ts('Submit') }}
                </i>
            </el-button>

        </div>
    </div>
</template>

<script lang="jsx">
import { v4 as uuidv4 } from 'uuid'; // 安装 uuid 库

// import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolForm from '@/components/basic/VolForm.vue';
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'//先引入
import VolTable from "@/components/basic/VolTableEx.vue";
export default {
    components: {
        // ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        //  Opc_EquipBasicInfoEdit,
    },
    mixins: [GlobalElMessageBox],
    created() {
        let maintenanceReqArry = this.getRevisionObject("MaintenanceReq");
        this.curFormRules[0][0].data = maintenanceReqArry;
        let resourceGroupArry = this.getResourceSelectValue(null,"ResourceGroup");
        this.curFormRules[0][1].data = resourceGroupArry;
    },
    data() {
        return {
            text: "",
            flag: false,
            filterText: [],
            // paginationHide: true,
            currentPage: 1,
            selectedNewRows: [],
            selectedResourceIds: [],
            disableSelectionChange: false,
            fullscreenLoading: false,
            //tabsModel: "0",
            curFormFields: { MaintenanceReqName: null, ResourceGroup: null, Activated: null, Description: null, OriginalActivated: null, ObjectType: null,Activated02: null },
            //url: "/api/MaintenanceReq/SearchResourceActivation",
            ApiUrl: {
                GetResourceObject: "/api/Query/GetResourceByType",//获取NameObject
                GetNameObject: "/api/Query/GetNameObject",//获取NameObject
                GetRevisionObject: "/api/query/GetRevisionObject",
                },
            curFormRules:
                [
                    [
                        {
                            field: "MaintenanceReqName", "title": this.$ts('Requirement Name'), "type": "select", "required": true, "readonly": false, "colSize": 3,
                            "data": [], 
                            // "dataKey": "MaintenanceReq",
                            placeholder: this.$ts('Requirement Name'),

                            onChange: async (val) => {
                                this.$refs.table1.reset();
                                if (val === null || val === "") {
                                    this.tableData = [];
                                    return;
                                }
                                let postdata = {
                                    MaintenanceReqName: val
                                };
                                //console.log(val, ',field1_OnChange');
                                this.fullscreenLoading = true;
                                try {
                                    const res = await this.http.post("/api/Query/SearchResourceActivation", postdata);
                                    // this.allData = res.rows;
                                    this.tableData = res.rows.map(item => ({
                                        ...item,
                                        id: uuidv4() // 生成唯一标识符
                                    }));
                                    this.tableData.forEach(row => {
                                        row.Activated02 = row.Activated;
                                    })
                                    // console.log(res.rows, 'res.rows');
                                    // this.$refs.table1.load();

                                    // 调用setRowSelection方法，传入当前页的数据
                                    this.setRowSelection(this.tableData);
                                } catch (error) {
                                    console.error("Error fetching resource activation:", error);
                                } finally {
                                    this.fullscreenLoading = false;
                                }

                                let descPostdata = {
                                    MaintenanceReqName: val,
                                    MaintenanceReqRevision: "1"
                                };
                                try {
                                    const descRes = await this.http.post("/api/Query/SearchMaintenanceReqToDes", descPostdata);
                                    if (descRes.rows && descRes.rows.length > 0) {
                                        this.curFormFields.Description = descRes.rows[0].MaintenanceReqDescription;
                                        // console.log(descRes, 'res.rows222');
                                    }
                                } catch (error) {
                                    console.error("Error fetching maintenance request description:", error);
                                }
                            }
                        },
                        {
                            field: "ResourceGroup", "title": this.$ts('Equipment Group'), "type": "select", "required": false, "readonly": false, "colSize": 2.5,
                            "data": [], 
                            // "dataKey": "BIZ_RESOURCE_GROUP",
                            placeholder: this.$ts('Equipment Group'),
                            onChange: (val) => {
                                this.$refs.table1.reset();
                                let postdata = {
                                    MaintenanceReqName: this.curFormFields.MaintenanceReqName,
                                    ResourceGroup: val
                                };
                                if (this.curFormFields.MaintenanceReqName === "" || this.curFormFields.MaintenanceReqName === null) {
                                    this.$message.error(this.$ts("Please select a plan requirement name first."));
                                    this.$refs.table1.reset();
                                    this.$refs.form1.reset(this.curFormFields);
                                    return;
                                }
                                this.fullscreenLoading = true;
                                this.http.post("/api/Query/SearchResourceActivation", postdata).then(res => {
                                    // this.cacheTable = res.rows;
                                    this.tableData = res.rows.map(item => ({
                                        ...item,
                                        id: uuidv4() // 生成唯一标识符
                                    }));
                                    this.tableData.forEach(row => {
                                        row.Activated02 = row.Activated;
                                    })
                                    this.setRowSelection(this.tableData);
                                    // console.log(res.rows, 'res.rows.group');
                                }).finally(() => {
                                    // console.log(this.flag, 'nextTick');

                                    this.fullscreenLoading = false;
                                    //this.tableData = this.cacheTable;
                                });

                            }
                        },
                        { field: "Description", "title": this.$ts('Description'), "type": "text", "required": false, "readonly": true, "colSize": 4.85 }
                    ]



                ],
            tableColumns: [
                { field: 'ResourceName', type: "string", title: this.$ts('Resource No.'), sort: true },
                { field: 'ResourceDescription', type: "string", title: this.$ts('Description'), sort: false, width: 180, align: 'center' },
                {
                    field: 'Activated', type: "string", sortable: true, title: this.$ts('Activated Status'), hidden: true, bind: { key: 'Activated' }, sort: false, width: 180, align: 'center',
                    formatter: (row) => {
                        return row.Activated === '1' ? this.$ts('Activated_') : this.$ts('Un Activated');
                    }
                },
                {
                    field: 'Activated02', type: "string", sortable: true, title: this.$ts('Activated Status'), hidden: false, bind: { key: 'Activated' }, sort: false, width: 180, align: 'center',
                    formatter: (row) => {
                        return row.Activated02 === '1' ? this.$ts('Activated_') : this.$ts('Un Activated');
                    }
                },
                // { field: 'OriginalActivated', title: this.$ts('OriginalActivated'), hidden: false, bind: { key: 'Activated' } , sort: true, width: 180, align: 'center' },
                // { field: 'ObjectType', title: this.$ts('ObjectType') , hidden: true, sort: true, width: 180, align: 'center' },
            ],
            tableData: [],
        };
    },
    
    // 监听tableData的变化，当激活标记为1并且没有执行过自动勾选方法后，执行自动勾选方法
    watch: {
        // 这里总的监听tableData的变化,这里是对tableData的最终操作,在分页之后
        /* tableData: {
            deep: true,
            handler(newVal, oldVal) {
                // console.log(newVal, 'newVal');
                // console.log(oldVal, 'oldVal');

                // this.restoreCheckedState();
                newVal.forEach(item => {
                    // console.log(item, 'item.Activated');
                    if (item.Activated === "1" && this.disableSelectionChange === false) {
                        this.setRowSelection(this.tableData);

                        // console.log(this.getRowKey(item.Activated.id), 'getRowKey');
                    }
                });
            }
        }, */
        /* // 监听当前页码的变化,设置当前页的勾选状态
        'currentPage': function (newVal, oldVal) {
            this.tableData.forEach(item => {
                if (newVal !== oldVal && this.disableSelectionChange === false) {
                    this.setRowSelection(this.tableData);
                }
            });
        }, */


        //这里的作用是单独对点击复选框改变Activated的值
        // 监听已选择的行的变化,手动勾选行或全选行之后,切换页面也能保留勾选状态
        'selectedNewRows'(newVal, oldVal) {
            if (this.disableSelectionChange) {
                return;
            }
            // 然后，遍历新值，更新或确认它们的Activated状态为"1"
            newVal.forEach((item) => {
                item.Activated = "1";
                // console.log(1, '1111111111111');
            });
            // 首先，将旧值中的所有项的Activated设置为"0"
            oldVal.forEach((item) => {
                const index = this.selectedNewRows.findIndex(selectedItem => selectedItem === item);
                if (index === -1) { // 如果旧值中的项不在新值中，即被取消选择
                    item.Activated = "0";
                    // console.log(0, '0000000000000');
                }
            });
        },
    },
    methods: {
        rowKey(row) {
            // console.log(row.id, 'row.id');
            return row.id; // 使用唯一标识符作为 row-key
        },

        // 切换页面或者设置每页数据行的回调方法
        pageSizeChanged(val) {
            this.currentPage = val;
        },
        setRowSelection(tableData) {
            if (tableData.length === 0) {
                return;
            }

            // 临时禁用onSelectionChange
            this.disableSelectionChange = true;

            this.$nextTick(() => {
                for (const row of tableData) {
                    if (row.Activated === "1") {
                        this.$refs.table1.$refs.table.toggleRowSelection(row, true);
                    }
                }

                //重新启用onSelectionChange
                this.disableSelectionChange = false;
            });
        },


        onSelectionChange(selectedRows) {
            this.selectedNewRows = selectedRows;
            // console.log('onSelectionChange');

            if (this.disableSelectionChange) {
                return;
            }
            // console.log(selectedRows, 'selectedRows');
            /*  this.tableData.forEach((item) => {
                 // 检查当前项是否在selectedRows中
                 if (selectedRows.includes(item)) {
                     // 如果在selectedRows中，设置Activated为"1"
                     item.Activated = "1";
                 } else {
                     // 如果不在selectedRows中，设置Activated为"0"
                     item.Activated = "0";
                 }
             }); */
        },
        async Submit() {
            /* this.tableData.forEach((item) => {
                console.log(item);
            }); */
            const valid = await this.$refs.form1.validate();
            if (!valid) return;
            let postdata = {
                MaintenanceReqName: this.curFormFields.MaintenanceReqName,
                ResourceGroup: this.curFormFields.ResourceGroup,
                serviceDetails: this.tableData

            };


            this.fullscreenLoading = true;
            this.http.post('/api/CDO/ResourceActivation', postdata)
                .then(response => {
                    // 处理响应
                    this.resultMessage(response.status, response.message);
                })
                .catch(error => {
                    console.error('There was a problem with the axios operation:', error);
                })
                .finally(() => {
                    // console.log(postdata, 'postdata');
                    this.fullscreenLoading = false;
                    this.$refs.table1.reset();
                    this.$refs.form1.reset(this.curFormFields);
                });


        },
        delRow(tableRef) {
            if (tableRef === 'table1') {
                this.$refs.table1.delRow();
            }
            //this.$refs.table.delRow();
            this.$message.success(this.$ts('success'))
        },
        // rowClick(event, tableRef) {
        //     const { row, column, index } = event;
        //     if (tableRef === 'table1') {
        //         //这里只是点击行，如果点击的是行的复选框则无效
        //         // row.Activated = row.Activated === "0" ? "1" : "0";
        //         this.$refs.table1.$refs.table.toggleRowSelection(row);
        //         // console.log(event, 'getRowKey');
        //     }
        // },
        rowClick({
                row,
                column,
                event
            }) { //查询界面table点击行选中当前行
                console.log(row, 'row');
                this.$refs.table1.$refs.table.toggleRowSelection(row);
            },

        reset() {
            this.$refs.form1.reset();
            //this.$Message.success("表单已重置")
        },
        download() {
            this.$Message.info("111")
        },
        getRevisionObject(cdoname){
            let param={
                // cdo:"ResourceFamily",
                cdo:cdoname,
            }
            let dataArr=[]
            this.http.get(this.ApiUrl.GetRevisionObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        
                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
               
            )
            return dataArr;
        },
        getResourceSelectValue(typeName,cdoname){
            let param={
                // cdo:"ResourceFamily",
                cdo:cdoname,
                type:typeName
            }
            let dataArr=[]
            this.http.get(this.ApiUrl.GetResourceObject,param).then(
                res =>{
                    if (res.Result == 1) {
                        
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        
                        // this.formRules_Tool[0][0].data  = dataArr;
                    } else {
                        this.$message.error(res.Message);
                    }
                }
               
            )
            return dataArr;
        },
    }
};

</script>
<style lang="less" scoped>
.form-btns {
    text-align: center;
}

.tables {
    padding-left: 15px;

    .table-item {
        padding: 10px;
    }

    .table-header {
        display: flex;
        margin-bottom: 8px;
    }

    .header-text {
        position: relative;
        bottom: -9px;
        flex: 1;
        font-weight: bold;
    }

    .header-btns {
        text-align: right;
    }
}

.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>