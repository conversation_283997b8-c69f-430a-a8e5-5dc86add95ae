.edit-container {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #f7f7ff;
}
.edit-form {
  background: #ffff;
  padding: 10px;
  border-radius: 5px;
}
.edit-form-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
  margin-bottom: 14px;
  display: flex;
  .edit-form-header-table-name {
    flex: 1;
    display: flex;
    align-items: center;
    padding-top: 5px;
    .border {
      height: 15px;
      background: #2d8cf0;
      width: 4px;
      border-radius: 3px;
      position: relative;
      top: 1px;
    }
    .name {
      padding-left: 3px;
      font-weight: bold;
      font-size: 13px;
    }
  }
}
button {
  font-size: 12px !important;
  font-weight: bolder;
}

.edit-detail {
  margin-top: 12px;
  .edit-form-header {
    margin-bottom: 0;
    border-bottom: 0;
  }
}
