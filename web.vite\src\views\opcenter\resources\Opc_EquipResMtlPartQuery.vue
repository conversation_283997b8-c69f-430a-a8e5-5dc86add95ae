<template>
    <div class="container">
        <div class="table-item">
            <div class="table-item-header">
                <div class="table-item-border"></div> <span class="table-item-text">{{ this.$ts(TitleName)
                    }}</span>
                <div style="text-align: end; margin-top: 0px;width:75%;">
                    <el-button type="primary" @click="setCurrentAction('add')"><i class="el-icon-circle-plus-outline">
                            {{ this.$ts('Add') }}
                        </i></el-button>
                    <el-button type="primary" icon="Edit" @click="setCurrentAction('edit')">{{
                        this.$ts('Update')
                    }}</el-button>
                    <el-button type="success" @click="setCurrentAction('copy')"><i class="el-icon-copy-document">
                            {{ this.$ts('Copy') }}
                        </i></el-button>

                    <el-button type="primary" icon="Delete" @click="setCurrentAction('delete')">{{
                        this.$ts('Delete')
                    }}</el-button>


                </div>
            </div>
        </div>
    </div>

    <VolForm ref="form1" :loadKey="true" :formFields="curFormFields" :formRules="curFormRules">
    </VolForm>

    <div class="form-content" v-show="IsEnable">

        <div v-show="false">


            <!-- 新增按钮弹出的内容 -->
            <VolForm ref="form2" :loadKey="true" :formFields="formFields_MTLPart_Add"
                :formRules="formRules_MTLPart_Add">
            </VolForm>
            <VolForm ref="form3" :loadKey="true" :formFields="formFields_MTLPart_Edit"
                :formRules="formRules_MTLPart_Edit">
            </VolForm>
            <VolForm ref="form4" :loadKey="true" :formFields="formFields_MTLPart_Delete"
                :formRules="formRules_MTLPart_Delete">
            </VolForm>
            <VolForm ref="form5" :loadKey="true" :formFields="formFields_MTLPart_Copy"
                :formRules="formRules_MTLPart_Copy">
            </VolForm>
        </div>

        <div style="text-align: center; width: 100%;margin-top:20px">

            <el-button type="primary" @click="formSubmit"><i class="el-icon-check">
                    {{ this.$ts('Submit') }}
                </i>
            </el-button>

            <!-- <el-cascader :props="props"></el-cascader> -->
        </div>
    </div>



</template>



<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import { GlobalElMessageBox } from '@/mixins/commonMixin.js'//先引入

//import { de } from 'element-plus/es/locale';
//import Opc_EquipBasicInfoEdit from './Opc_EquipBasicInfoEdit.vue'
export default {
    components: {
        ModelInfo,
        VolHeader,
        VolForm,
        'vol-table': VolTable,
        //  Opc_EquipBasicInfoEdit,
    },
    mixins: [GlobalElMessageBox],

    data() {
        return {
            IsEnable: false,

            TitleName: '',

            curFormFields: {
            },
            curFormRules: [],



            currentAction: '', // 当前操作
            // title: this.$ts('治工具台账维护'),
            // tableTitleOne: this.$ts('邮件通知地址'),
            // tableTitleTwo: this.$ts('适用料号'),
            //储位tab页表单字段
            formFields_MTLPart_Add: {

                resourceMaterialPart_Name: null,//配件料号
                ResourceMaterial_Revision: null,//版本
                ResourceMaterial_Description: null,//描述
                ResourceMaterial_ProductType: null,//产品类型
                ResourceMaterial_MinQtyReorderLimit: null,//最小订购数量
                ResourceMaterial_MinQtyReorderEmailGroup: null,//最小订购邮件组

            },
            formFields_MTLPart_Delete: {

                resourceMaterialPart_Name: null,//配件料号
                ResourceMaterial_Revision: null,//版本
                ResourceMaterial_Description: null,//描述
                ResourceMaterial_ProductType: null,//产品类型
                ResourceMaterial_MinQtyReorderLimit: null,//最小订购数量
                ResourceMaterial_MinQtyReorderEmailGroup: null,//最小订购邮件组
            },
            formFields_MTLPart_Edit: {

                resourceMaterialPart_Name: null,//配件料号
                newResourceMaterialPart_Name: null,//新配件料号
                ResourceMaterial_Revision: null,//版本
                ResourceMaterial_Description: null,//描述
                ResourceMaterial_ProductType: null,//产品类型
                ResourceMaterial_MinQtyReorderLimit: null,//最小订购数量
                ResourceMaterial_MinQtyReorderEmailGroup: null,//最小订购邮件组
            },
            formFields_MTLPart_Copy: {

                resourceMaterialPart_Name: null,//配件料号
                newResourceMaterialPart_Name: null,//新配件料号
                ResourceMaterial_Revision: null,//版本
                ResourceMaterial_Description: null,//描述
                ResourceMaterial_ProductType: null,//产品类型
                ResourceMaterial_MinQtyReorderLimit: null,//最小订购数量
                ResourceMaterial_MinQtyReorderEmailGroup: null,//最小订购邮件组
            },


            formRules_MTLPart_Copy:
                [
                    [
                        {
                            dataKey: "resourceMateRialPart", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Accessory Part Number'),
                            placeholder: this.$ts('Accessory Part Number'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "resourceMaterialPart_Name",
                            colSize: 3,
                            type: "select",
                            onChange: (value) => {
                                this.CopyCurrentMTLPart(value);
                                let postData_Condition = {
                                    resourceMaterialPart_Name: value, // 表单
                                };
                                this.http.post('api/Qyery/SearchResourceMaterialPart', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {

                                        //console.log(this.curFormFields, 'rows');
                                        // console.log(res.rows, 'rows');
                                        this.formFields_MTLPart_Copy.ResourceMaterial_Revision = res.rows[0].ResourceMaterial_Revision;
                                        this.formFields_MTLPart_Copy.ResourceMaterial_Description = res.rows[0].ResourceMaterial_Description;
                                        this.formFields_MTLPart_Copy.ResourceMaterial_ProductType = res.rows[0].ResourceMaterial_ProductType;
                                        this.formFields_MTLPart_Copy.ResourceMaterial_MinQtyReorderLimit = res.rows[0].ResourceMaterial_MinQtyReorderLimit;
                                        this.formFields_MTLPart_Copy.ResourceMaterial_MinQtyReorderEmailGroup = res.rows[0].ResourceMaterial_MinQtyReorderEmailGroup;

                                    }
                                }).catch(error => {

                                    //this.$message.error(error);

                                });
                            },

                            // extra: {
                            //     style: "color:#2196F3;cursor: pointer;font-size:16px",
                            //     icon: "el-icon-document-copy", //显示图标
                            //     text: this.$ts('Click to copy this item'), //显示文本
                            //     //触发事件
                            //     click: (item) => {
                            //         // this.CopyCurrentMTLPart(item);
                            //     },
                            // },

                        },
                    ],
                    [
                        {
                            title: this.$ts('New Accessory Part Number'),
                            placeholder: this.$ts('New Accessory Part Number'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "newResourceMaterialPart_Name",
                            colSize: 3
                        },
                        {
                            title: this.$ts('Accessory Description'),
                            placeholder: this.$ts('Accessory Description'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_Description",
                            colSize: 3
                        }

                    ],
                    [
                        
                        {
                            dataKey: "partType", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Accessory Type'),
                            placeholder: this.$ts('Accessory Type'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_ProductType",
                            colSize: 3,
                            type: "select"
                        }

                    ],
                    [
                        {
                            title: this.$ts('Minimum stock quantity'),
                            placeholder: this.$ts('Minimum stock quantity'),
                            type: "number",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_MinQtyReorderLimit",
                            colSize: 3
                        },
                        {
                            dataKey: "Biz_EmailGroup", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Email notification group'),
                            placeholder: this.$ts('Email notification group'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_MinQtyReorderEmailGroup",
                            colSize: 3,
                            type: "select"
                        }

                    ]
                ],
            formRules_MTLPart_Edit:
                [
                    [
                        {
                            dataKey: "resourceMateRialPart", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Accessory Part Number'),
                            placeholder: this.$ts('Accessory Part Number'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "resourceMaterialPart_Name",
                            colSize: 3,
                            type: "select",
                            onChange: (value) => {
                                let postData_Condition = {
                                    resourceMaterialPart_Name: value, // 表单
                                };
                                this.http.post('api/Query/SearchResourceMaterialPart', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {
                                        //console.log(this.curFormFields, 'rows');
                                        console.log(res.rows, 'rows');
                                        this.formFields_MTLPart_Edit.newResourceMaterialPart_Name = value//res.rows[0].NewResourceMaterialPart_Name;

                                        this.formFields_MTLPart_Edit.ResourceMaterial_Revision = res.rows[0].ResourceMaterial_Revision;
                                        this.formFields_MTLPart_Edit.ResourceMaterial_Description = res.rows[0].ResourceMaterial_Description;
                                        this.formFields_MTLPart_Edit.ResourceMaterial_ProductType = res.rows[0].ResourceMaterial_ProductType;
                                        this.formFields_MTLPart_Edit.ResourceMaterial_MinQtyReorderLimit = res.rows[0].ResourceMaterial_MinQtyReorderLimit;
                                        this.formFields_MTLPart_Edit.ResourceMaterial_MinQtyReorderEmailGroup = res.rows[0].ResourceMaterial_MinQtyReorderEmailGroup;

                                    }
                                }).catch(error => {

                                    this.$message.error(error);

                                });
                            },
                        },
                    ],
                    [
                        {
                            title: this.$ts('New Accessory Part Number'),
                            placeholder: this.$ts('New Accessory Part Number'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "newResourceMaterialPart_Name",
                            colSize: 3
                        },
                        {
                            title: this.$ts('Accessory Description'),
                            placeholder: this.$ts('Accessory Description'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_Description",
                            colSize: 3
                        }

                    ],
                    [
                        
                        {
                            dataKey: "partType", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Accessory Type'),
                            placeholder: this.$ts('Accessory Type'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_ProductType",
                            colSize: 3,
                            type: "select"
                        }

                    ],
                    [
                        {
                            title: this.$ts('Minimum stock quantity'),
                            placeholder: this.$ts('Minimum stock quantity'),
                            type: "number",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_MinQtyReorderLimit",
                            colSize: 3
                        },
                        {
                            dataKey: "Biz_EmailGroup", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Email notification group'),
                            placeholder: this.$ts('Email notification group'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_MinQtyReorderEmailGroup",
                            colSize: 3,
                            type: "select"
                        }

                    ]
                ],
            formRules_MTLPart_Delete:
                [
                    [
                        {
                            dataKey: "resourceMateRialPart", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Accessory Part Number'),
                            placeholder: this.$ts('Accessory Part Number'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "resourceMaterialPart_Name",
                            colSize: 3,
                            type: "select",
                            onChange: (value) => {
                                let postData_Condition = {
                                    resourceMaterialPart_Name: value, // 表单
                                };
                                this.http.post('api/Query/SearchResourceMaterialPart', postData_Condition).then(res => {
                                    if (res.rows && res.rows.length > 0) {
                                        //console.log(this.curFormFields, 'rows');
                                        console.log(res.rows, 'rows');
                                        this.formFields_MTLPart_Delete.ResourceMaterial_Revision = res.rows[0].ResourceMaterial_Revision;
                                        this.formFields_MTLPart_Delete.ResourceMaterial_Description = res.rows[0].ResourceMaterial_Description;
                                        this.formFields_MTLPart_Delete.ResourceMaterial_ProductType = res.rows[0].ResourceMaterial_ProductType;
                                        this.formFields_MTLPart_Delete.ResourceMaterial_MinQtyReorderLimit = res.rows[0].ResourceMaterial_MinQtyReorderLimit;
                                        this.formFields_MTLPart_Delete.ResourceMaterial_MinQtyReorderEmailGroup = res.rows[0].ResourceMaterial_MinQtyReorderEmailGroup;

                                    }
                                }).catch(error => {

                                    this.$message.error(error);

                                });
                            },
                        },
                        {
                            title: this.$ts('Accessory Description'),
                            placeholder: this.$ts('Accessory Description'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_Description",
                            readonly: true,
                            colSize: 3
                        }
                    ],
                    [
                        
                        
                        {

                            title: this.$ts('Accessory Type'),
                            placeholder: this.$ts('Accessory Type'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_ProductType",
                            readonly: true,
                            colSize: 3,
                        }

                    ],
                    [
                        {
                            title: this.$ts('Minimum stock quantity'),
                            placeholder: this.$ts('Minimum stock quantity'),
                            filter: true,
                            required: true, //设置为必选项
                            readonly: true,
                            field: "ResourceMaterial_MinQtyReorderLimit",
                            colSize: 3
                        },
                        {
                            dataKey: "Biz_EmailGroup", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Email notification group'),
                            placeholder: this.$ts('Email notification group'),
                            filter: true,
                            required: true, //设置为必选项
                            readonly: true,
                            field: "ResourceMaterial_MinQtyReorderEmailGroup",
                            colSize: 3,
                            type: "select"
                        }

                    ]
                ],
            formRules_MTLPart_Add:
                [

                    [
                        {
                            title: this.$ts('Accessory Part Number'),
                            placeholder: this.$ts('Accessory Part Number'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "resourceMaterialPart_Name",
                            colSize: 3
                        },
                        {
                            title: this.$ts('Accessory Description'),
                            placeholder: this.$ts('Accessory Description'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_Description",
                            colSize: 3
                        }

                    ],
                    [
                        
                        {
                            dataKey: "partType", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Accessory Type'),
                            placeholder: this.$ts('Accessory Type'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_ProductType",
                            colSize: 3,
                            type: "select"
                        }

                    ],
                    [
                        {
                            title: this.$ts('Minimum stock quantity'),
                            placeholder: this.$ts('Minimum stock quantity'),
                            type: "number",
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_MinQtyReorderLimit",
                            colSize: 3
                        },
                        {
                            dataKey: "Biz_EmailGroup", //后台下拉框对应的数据字典编号
                            data: [],
                            title: this.$ts('Email notification group'),
                            placeholder: this.$ts('Email notification group'),
                            filter: true,
                            required: true, //设置为必选项
                            field: "ResourceMaterial_MinQtyReorderEmailGroup",
                            colSize: 3,
                            type: "select"
                        }

                    ]
                ],




        }
    },

    created() {
        this.setCurrentAction('add');
    },
    methods: {


        CopyCurrentMTLPart(item) {
            //console.log(item, 'item');
            // console.log(this.curFormFields, 'curFormFields');
            let postData_Condition = {
                resourceMaterialPart_Name: item
            };
            this.http.post('api/Query/SearchResourceMaterialPart', JSON.stringify(postData_Condition)).then(res => {
                console.log(res.rows, 'res');
                console.log(this.curFormFields, 'curFormFields');
                Object.keys(res.rows[0]).forEach(key => {
                    const formattedKey = key.charAt(0).toLowerCase() + key.slice(1);

                    if (this.curFormFields.hasOwnProperty(formattedKey)) {
                        console.log(key, 'formattedKey');

                        res.rows[0]['NewResourceMaterialPart_Name'] = 'Copy of ' + this.curFormFields['resourceMaterialPart_Name'];
                        this.curFormFields[formattedKey] = res.rows[0][key];
                    }
                });

            }).catch(error => {

                this.$message.error(this.$ts('Execution failed, please contact MES team.'));

            });
            //let data = {newResourceFamilyName:this.curFormFields,description:''};
            // this.curFormFields = data;
            //this.curFormFields.description = item.description;
        },

        setCurrentAction(action) {

            this.currentAction = action;
            switch (action) {
                case 'add':
                    this.TitleName = this.$ts('Add Accessory Part Number');
                    this.IsEnable = true;
                    this.curFormFields = this.formFields_MTLPart_Add;
                    this.curFormRules = this.formRules_MTLPart_Add;
                    break;

                case 'edit':
                    this.TitleName = this.$ts('Update Accessory Part Number');
                    this.IsEnable = true;
                    this.curFormFields = this.formFields_MTLPart_Edit;
                    this.curFormRules = this.formRules_MTLPart_Edit;
                    break;
                case 'copy':
                    this.TitleName = this.$ts('Copy from an existing part number.');
                    this.IsEnable = true;
                    this.curFormFields = this.formFields_MTLPart_Copy;
                    this.curFormRules = this.formRules_MTLPart_Copy;
                    break;

                case 'delete':
                    this.TitleName = this.$ts('Delete Accessory P/N');
                    this.IsEnable = true;
                    this.curFormFields = this.formFields_MTLPart_Delete;
                    this.curFormRules = this.formRules_MTLPart_Delete;
                    break;

            }
        },

        async formSubmit() {
            let postData = {
                ...this.curFormFields, // 表单
                ResourceMaterial_Revision: "1", // 版本
            };
            console.log(postData, 'postData');

            try {
                const valid = await this.$refs.form1.validate();
                if (!valid) return;
                let response;
                switch (this.currentAction) {
                    case 'add':
                        response = await this.http.post('api/CDO/AddResourceMaterialPart', postData);
                        break;
                    case 'edit':
                        response = await this.http.post('api/CDO/EditResourceMaterialPart', postData);
                        break;
                    case 'copy':
                        let postData_Copy = {
                            resourceMaterialPart_Name: this.curFormFields.newResourceMaterialPart_Name,
                            ResourceMaterial_Revision: "1",
                            ResourceMaterial_Description: this.curFormFields.ResourceMaterial_Description,
                            ResourceMaterial_ProductType: this.curFormFields.ResourceMaterial_ProductType,
                            ResourceMaterial_MinQtyReorderLimit: this.curFormFields.ResourceMaterial_MinQtyReorderLimit,
                            ResourceMaterial_MinQtyReorderEmailGroup: this.curFormFields.ResourceMaterial_MinQtyReorderEmailGroup,
                        };
                        response = await this.http.post('api/CDO/AddResourceMaterialPart', postData_Copy);
                        break;
                    case 'delete':
                        response = await this.http.post('api/CDO/ResourceMaterialPartDelete', postData);
                        break;
                }

                if (response && this.resultMessage(response.status, response.message) === true) {
                    await Promise.all([
                        this.resetItemSource(this.formRules_MTLPart_Edit[0][0].data, 'resourceMateRialPart'),
                        this.resetItemSource(this.formRules_MTLPart_Copy[0][0].data, 'resourceMateRialPart'),
                        this.resetItemSource(this.formRules_MTLPart_Delete[0][0].data, 'resourceMateRialPart')
                    ]).finally(() => {
                        this.reset();
                    })
                }
            } catch (error) {
                this.$message.error(this.$ts('Execution failed, please contact MES team.'));
            } finally {
                //this.reset();
            }
        },
        async resetItemSource(proxyArray, dicNo) {
            try {
                let newData = await this.SysApi_GetDicData(dicNo);
                if (!Array.isArray(newData)) {
                    throw new Error("返回的数据不是数组类型");
                }
                while (proxyArray.length > 0) {
                    proxyArray.pop();
                }
                newData.forEach(item => {
                    proxyArray.push(item);
                });
            } catch (error) {
                console.error("重置项目源时出错:", error);
            }
        },
        reset() {
            this.$refs.form1.reset();
            //this.$Message.success("表单已重置");
        },
        addRow(tableRef) {
            const newRow = { /* 新行的数据 */ };
            if (tableRef === 'table1') {

                this.$refs.table1.addRow(newRow)
            }
        },
        delRow(tableRef) {
            if (tableRef === 'table1') {
                this.$refs.table1.delRow();
            }
            //this.$refs.table.delRow();
            this.$message.success(this.$ts('Successfully Deleted'))
        },
        rowClick(event, tableRef) {
            // 从event参数中解构出row, column
            const { row, column } = event;
            // 现在可以根据tableRef执行特定操作
            if (tableRef === 'table1') {
                console.log(event, 'row');
                this.$refs.table1.$refs.table.toggleRowSelection(row);
            }
        },

        loadingFormData(value) {

            //console.log(value, 'value');
            let postData_Condition = {
                //familyType: this.formFields_Add.familyType, // 表单
                //description: this.curFormFields.description, // 表单
                physicalLocationName: value, // 表单
            };
            this.http.post('api/Query/Biz_PhysicalLocationSearch', postData_Condition).then(res => {
                if (res.rows && res.rows.length > 0) {
                    //console.log(this.curFormFields, 'rows');
                    //console.log(res.rows, 'rows');
                    this.curFormFields_Location.newPhysicalLocationName = res.rows[0].PhysicalLocationName;
                    this.curFormFields_Location.description = res.rows[0].Description;

                    let tableData1 = [];

                    res.rows.forEach(row => {
                        // 检查每个 row 是否有 PhysicalPositionList
                        if (row.PhysicalPositionList && row.PhysicalPositionList.length > 0) {
                            // 遍历 PhysicalPositionList 并提取需要的信息
                            row.PhysicalPositionList.forEach(position => {
                                //console.log(position.PhysicalPositionName, 'position');
                                tableData1.push({
                                    physicalPositionName: position.PhysicalPositionName,
                                    description: position.Description
                                });
                            });
                        }
                    });
                    this.tableData = tableData1;

                    this.$message.success(res.message);
                } else {
                    // 处理数据不符合预期的情况
                    this.$message.error(res.error);

                }
            }).catch(error => {

                this.$message.error(error);

            });

        }
    }
};
</script>
<style lang="less" scoped>
.container {
    padding: 10px;
    background: #F3F7FC;

    .form-content {
        border-radius: 5px;
        padding: 10px 0;
        background: #fff;
    }

    .table-item-header {
        display: flex;
        align-items: center;
        padding: 6px;

        .table-item-border {
            height: 15px;
            background: rgb(33, 150, 243);
            width: 5px;
            border-radius: 10px;
            position: relative;
            margin-right: 5px;
        }

        .table-item-text {
            font-weight: bolder;
        }

        .table-item-buttons {
            flex: 1;
            text-align: right;
        }

        .small-text {
            font-size: 12px;
            color: #2196F3;
            margin-left: 10px;
            position: relative;
            top: 2px;
        }
    }
}
</style>