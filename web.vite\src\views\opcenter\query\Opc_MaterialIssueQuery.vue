<template>
	<div style="display: flex;margin-top: 5px;">
		<div style="margin-left: 10px;">
			<label style="width: 150px; margin-left: 5px; font-size: 16px;">
				<span>生产工单</span>
			</label>
			<div style="margin-top: 5px;">
				<el-input style="width: 240px" v-model="searchMfgOrder" placeholder="请输入"></el-input>
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 240px; margin-left: 5px; font-size: 16px;">
				<span>生产批次</span>
			</label>
			<div style="margin-top: 5px;">
				<el-input ref="input" style="width: 240px" v-model="searchContainer" placeholder="请输入" autofocus="true"></el-input>
			</div>
		</div>
        <div style="margin-left: 10px;">
			<label style="width: 240px; margin-left: 5px; font-size: 16px;">
				<span>物料批次</span>
			</label>
			<div style="margin-top: 5px;">
				<el-input style="width: 240px" v-model="searchMaterial" placeholder="请输入"></el-input>
			</div>
		</div>
		<div style="margin-left: 10px;">
			<label style="width: 240px; margin-left: 5px; font-size: 16px;">
				<span>工序</span>
			</label>
			<div style="margin-top: 5px;">
				<el-select v-model="searchSpec" clearable filterable placeholder="请选择" style="width: 240px">
					<el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
				</el-select>
			</div>
		</div>
		<div style="margin-left: 10px; ">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>耗用时间</span>
            </label>
            <div style="margin-top: 5px;">
                <el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
                    end-placeholder="结束" :size="size" style="width: 240px" />
            </div>
        </div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<div class="table-item-border"></div> <span class="table-item-text">物料消耗明细</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="getRow" plain>查询</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" :pagination="pagination" @rowClick="rowClick"
			:columns="columns" :height="500" :pagination-hide="false" :load-key="true"
			:column-index="true" :ck="false"></vol-table>
	</div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'
import { ElContainer } from "element-plus";

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			searchMfgOrder: '',
			searchContainer: '',
			searchMaterial: '',
            searchSpec: '',
			searchTxnDate: null,
			specs:[],


			columns: [
				{ field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'Department', title: '部门', type: 'string', width: 120, align: 'center' },
				{ field: 'WorkCenter', title: '车间', type: 'string', width: 120, align: 'center' },
				{ field: 'MfgOrder', title: '生产工单', type: 'string', width: 130, align: 'center' },
				{ field: 'Container', title: '生产批次', type: 'string', width: 120, align: 'center' },
				{ field: 'Product', title: '产品编码', type: 'string', width: 120, align: 'center' },
				{ field: 'Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
				{ field: 'Qty', title: '数量', type: 'string', width: 80, align: 'center' },
				{ field: 'Uom', title: '单位', type: 'string', width: 80, align: 'center' },
				{ field: 'Spec', title: '工序', type: 'string', width: 130, align: 'center' },
				{ field: 'MContainer', title: '物料批次', type: 'string', width: 120, align: 'center' },
				{ field: 'Material', title: '物料编码', type: 'string', width: 120, align: 'center' },
				{ field: 'MDescription', title: '物料描述', type: 'string', width: 120, align: 'center' },
				{ field: 'ActualQtyIssued', title: '消耗数量', type: 'string', width: 80, align: 'center' },
				{ field: 'MUom', title: '消耗单位', type: 'string', width: 80, align: 'center' },
				{ field: 'Employee', title: '操作人', type: 'string', width: 120, align: 'center' },
				{ field: 'TxnDate', title: '操作时间', type: 'string', width: 150, align: 'center' },
			],
			tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/getRevisionObject",
				getComponentIssue: '/api/query/getComponentIssue',
			},
		}
	},
	created() {
		this.getSpec()
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	methods: {
		async getSpec(){
			let params = {
				cdo: "Spec"
			};
			this.http.get(this.apiUrl.getRevisionObject,params).then(res => {
				if (res.Result == 1) {
					this.specs = res.Data;
				} else {
					this.$message.error(res.Message);
				}
			});
		},
        reset(){
            this.searchMfgOrder = '';
            this.searchContainer = '';
            this.searchMaterial = '';
            this.searchSpec = '';
			this.searchTxnDate = null;
            this.tableData = [];
            this.$refs.input.focus();
        },
		rowClick({
			row,
			column,
			index
		}) {
            this.$refs.table.$refs.table.toggleRowSelection(row);
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			// this.$refs.table.$refs.table.toggleRowSelection(row);
		},
		getRow() {
			if((this.searchMfgOrder == '' || this.searchMfgOrder == null)
				&& (this.searchContainer == '' || this.searchContainer == null)){
				this.$message.error("生产工单或生产批次查询条件必须输入。");
				return;
			}
            let params = {
                mfgorder: this.searchMfgOrder,
                container: this.searchContainer,
                materialContainer: this.searchMaterial,
                spec: this.searchSpec,
                StartTime: this.searchTxnDate != null ? this.searchTxnDate[0] : null,
                EndTime: this.searchTxnDate != null ? this.searchTxnDate[1] : null,
            };
            this.http.get(this.apiUrl.getComponentIssue, params, true).then(res => {
                if (res.Result == 1) {                       
                    this.tableData = res.Data;
                    this.$refs.table.rowData = this.tableData;
                    this.$refs.table.paginations.total = this.tableData.length;
                } else {
                    this.$message.error(res.Message);
                }
            });

            this.$refs.input.focus();
		},
		outputRow() {
			// this.tableData.splice(0);
			//导出
			let tableData = this.$refs.table.tableData
			let sortData = this.$refs.table.filterColumns
			let exportData = this.handleTableSortData(tableData, sortData)
			Excel.exportExcel(exportData, "物料消耗信息" + '-' + this.base.getDate());
		},
		handleTableSortData(tableData, sortData) {
			let newArray = [];
			tableData.forEach(data => {
				let newItem = {};
				sortData.forEach(field => {
					if (data.hasOwnProperty(field.field)) {
						newItem[field.title] = data[field.field];
					}
				});
				newArray.push(newItem);
			});
			return newArray
		}

	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	.table-item-text {
		font-weight: bolder;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196F3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>