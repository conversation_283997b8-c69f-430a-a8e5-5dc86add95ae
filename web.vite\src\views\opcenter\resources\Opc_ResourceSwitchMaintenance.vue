<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				 <label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>分类</span>
				</label>
				<div style="margin-top: 5px">
                  <el-select v-model="objecttype" clearable placeholder="请选择分类" style="width: 240px">
                  <el-option
                   v-for="item in objecttypes"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"/>
               </el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>设备/模具编码</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="Resource" clearable filterable placeholder="键入搜索" style="width: 240px"
						remote-show-suffix :remote="true" :remote-method="getResource" :loading="loading">
						<el-option v-for="item in Resources" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
					</el-select>
				</div>
			</div>
			                <div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>设备课别</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="classname" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getclassname">
                    <el-option v-for="item in classnames" 
					:key="item.classname" 
					:label="item.classname" 
					:value="item.classname" />
                </el-select>
            </div>
                  </div>
					<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>日期筛选</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
			</div>
		</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">设备/模具维修登记与查询</span>
			<div class="table-item-buttons">
				<div>
                    <el-button type="success" icon="Plus" @click="addRow" plain>新建</el-button>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="518"
			:pagination-hide="false" :load-key="true" :column-index="true"  :check-row="false" 
            :url="ApiUrl.GetEquipmentRepair"
			@loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false"></vol-table>
	</div>
	<!-- 编辑弹出框 -->
	<vol-box :lazy="true" v-model="show" title="设备维修单登记" :width="800" :padding="5" :onModelClose="onModelClose">
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>设备/模具名称</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="Info_Resource" @change="ResourceSelected" 
					clearable filterable placeholder="请输入"
						style="width: 240px" remote-show-suffix :remote="true" :remote-method="getResource"
						:loading="loading">
						<el-option v-for="item in Resources" 
						:key="item.Name" :label="item.Name" 
						:value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>故障类型</span>
				</label>
				<div style="margin-top: 5px">
					<!--<span>{{ Info_JobModel }}</span>-->
					<el-select v-model="jobmaintenancename"
						clearable filterable placeholder="请输入故障类型"
						style="width: 240px" remote-show-suffix :remote="true" :remote-method="GetJobMaintenance"
						:loading="loading">
						<el-option v-for="item in jobmaintenancenames" 
						:key="item.jobmaintenancename" 
						:label="item.jobmaintenancename" 
						:value="item.jobmaintenancename" />
					</el-select>
					<!--<el-input  v-model="Info_JobModel" placeholder="请输入维修模式" style="width: 240px;" ></el-input>-->
				</div>
			</div>
		</div>
		<div style="margin-left: 10px; margin-top: 30px">
			<label style="width: 200px; margin-left: 5px; font-size: 16px"> <span>故障描述</span></label>
			<div style="margin-top: 5px">
				<el-input v-model="Info_memo" type="textarea"></el-input>
			</div>
		</div>
		<template #footer>
			<div>
				<el-button type="success" icon="Check" size="small" @click="save">保存</el-button>
				<el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
	import { mapState } from 'vuex';
	import Excel from '@/uitils/xlsl.js'

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox
		},
		data() {
			return {
				show: false,
				columns: [
					{ field: 'OrderName', title: '维修单', type: 'string', width: 130, align: 'center' },
					{ field: 'Description', title: '设备/模具名称', type: 'string', width: 130, align: 'center' },					
					{ field: 'ResourceName', title: '设备/模具编码', type: 'string', width: 110, align: 'center' },
					{ field: 'objecttype', title: '分类', type: 'string', width: 110, align: 'center' },
					{ field: 'classnamename', title: '设备课别', type: 'string', width: 120, align: 'center' },
					//{ field: 'Jobmodelname', title: '维修模式', type: 'string', width: 150, align: 'center' },
					{ field: 'jobmaintenancename', title: '故障类型', type: 'string', width: 120, align: 'center' },
					{ field: 'employeename', title: '登记人', type: 'string', width: 120, align: 'center' },
					{ field: 'CreateDate', title: '登记时间', type: 'datetime', width: 120, align: 'center' },
					{ field: 'COMMENTS', title: '故障描述', type: 'datetime', width: 220, align: 'center' },
				],
				tableData: [],
				pagination: { total: 0, size: 30, sortName: "" },

				//搜索框字段
				//Product: null,
				Note: null,
				objecttype: null,
				Spec: null,
				Resource: null,
				jobmaintenancename:null,
				//Products: [],
				Notes: [],
                objecttypes: [
            { label: '设备', value: 'RESOURCE' },
            { label: '模具', value: 'TOOL' } ],
				Specs:[],
				Resources:[],
				Info_JobModels: [],
				jobmaintenancenames:[],
				// 时间筛选字段
				searchPlanDate:null,
                StartDate: '',
                EndDate: ' ',

				//编辑框字段
				ActionFlag: 0,
				Info_CDOName: null,				
				Info_Product: null,
				Info_Description: null,
				Info_Resource: null,
				Info_Capacity: null,
				Info_ChangeModelTime:null,
				Info_PinTime:null,
				Info_StandardTime: null,
				Info_memo: null,
				Info_JobModel: null,
				classnames:[],
                classname: null,
				//Jobmodelname:null,
				

				//接口地址
				ApiUrl: {
					GetNameObject: "/api/query/GetNameObject",
					EquipmentRepairMaint: '/api/CDO/EquipmentRepairMaint',
					GetEquipmentRepair: '/api/Query/GetEquipmentRepair',
					GetJobModel: '/api/Query/GetJobModell',
					GetJobMaintenance: '/api/Query/GetJobMaintenance',
					Getclassname: "/api/query/Getclassname",
					
				}

			}
		},
		created() {

		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
		//	getnotes(query) {
				//if (query) {
					//let params = {
						//objectCategory: "objecttype",
						//cdo: "RESOURCE",
					//	objecttype: query
					//};
					//this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
					//	if (res.Result == 1) {
						//	this.objecttypes = res.Data;
						//} else {
						//	this.$message.error(res.Message);
					//	}
				//	});
				//}
			//},
			getResource(query) {
				if (query) {
					let params = {
						objectCategory: "RESOURCE",
						cdo: "RESOURCE",
						name: query
					};
					this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
						if (res.Result == 1) {
							this.Resources = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			getclassname(query) {
				if (query) {
					let params = {
						//objectCategory: "RESOURCE",
						//cdo: "RESOURCE",
						classname: query
					};
					this.http.get(this.ApiUrl.Getclassname, params).then(res => {
						if (res.Result == 1) {
							this.classnames = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			GetJobModel(query) {
				if (query) {
					let params = {
						//objectCategory: "Jobmodelname",
						//cdo: "RESOURCE",
						Jobmodelname: query
					};
					this.http.get(this.ApiUrl.GetJobModel, params).then(res => {
						if (res.Result == 1) {
							this.Info_JobModels = res.Data.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			GetJobMaintenance(query) {
				if (query) {
					let params = {
						//objectCategory: "Jobmodelname",
						//cdo: "RESOURCE",
						jobmaintenancename: query
					};
					this.http.get(this.ApiUrl.GetJobMaintenance, params).then(res => {
						if (res.Result == 1) {
							this.jobmaintenancenames = res.Data.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			//清除数据
			ResetSeach() {
				this.Note = '',
				this.Spec = '',
				this.Resource = '',
				this.tableData = []
			},
			ResetInfo() {
				this.ActionFlag = 0,
				this.Info_CDOName = '',
				//this.Info_Product = '',
				this.Info_Description = '',
				this.Info_Resource = '',
				this.Info_Capacity = '',
				this.Info_ChangeModelTime = '',
				this.Info_PinTime = '',
				this.Info_StandardTime = '',
				this.Info_memo = '',
				this.Info_JobModel = '',
				this.jobmaintenancename = ''
			},

			rowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
				this.$refs.table.$refs.table.toggleRowSelection(row);
			},
			queryRow() {
				this.$refs.table.load(null, true);
			},			
			addRow() {
				// this.tableData.push({ OrderNo: "D2022040600009" })
				this.ResetInfo();
				this.show = true;
			},
			onClose() {
				// alert('弹出框右上角点击x关闭事件')
			},
			save() {
				//保存	
				 if (!this.jobmaintenancename) {
				    this.$message.error('故障类型是必填项！')
				    return;
				}	
				let params = {
					User:this.userInfo.userName,
					Password:this.userInfo.userPwd,
					CDOName: this.ActionFlag == 1?this.Info_CDOName : this.Info_Product + this.Info_Resource,
					Notes:this.Info_memo,
					Resource:this.Info_Resource,
					JobModel:'test model 1',//this.Info_JobModel,
					JobOrder:this.Info_JobOrder,
					JobMaintenance:this.jobmaintenancename,	
				};
				this.http.post(this.ApiUrl.EquipmentRepairMaint, params, true).then(res => {
					if (res.Result == 1) {
						this.show = false;
						this.ResetInfo();
						this.queryRow();
						this.$message.success(res.Message);
					} else {
						this.$message.error(res.Message);
					}
				});	
			},
			close() {
				this.show = false;
			},
    
			loadBefore(params, callBack) {
			params["objecttype"] = this.objecttype;
			params["classnamename"] = this.classname;
			params["Resource"] = this.Resource;// 添加时间筛选参数
            params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
            params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
				callBack(true)
			},
			loadAfter(rows, callBack, result) {
				if (result.Result == 1) {
					//this.columns = result.Data.colums;
					this.tableData = result.Data.tableData.reverse();
					this.$refs.table.rowData = result.Data.tableData;
					this.$refs.table.paginations.total = result.Data.total;
				}
				else {
					this.$message.error(result.Message);
				}
				callBack(false);
			},
		}
	}
</script>
<style lang="less" scoped>
.table-item-header {
  display: flex;
  align-items: center;
  padding: 6px;

  .table-item-border {
    height: 15px;
    background: rgb(33, 150, 243);
    width: 5px;
    border-radius: 10px;
    position: relative;
    margin-right: 5px;
  }

  // .table-item-text {
  // 	font-weight: bolder;
  // 	border-bottom: 1px solid #0c0c0c;
  // }
  .table-item-text {
    margin-top: 3px;
    padding-bottom: 6px;
    font-weight: bold;
    font-size: 15px;
    color: #484848;
    white-space: nowrap;
    border-bottom: 2px solid #676767;
    margin-bottom: -1px;
    letter-spacing: 1px;
  }
  
.el-table__header th:nth-child(1),
.el-table__body td:nth-child(1) {
  display: none;
}
  .table-item-buttons {
    flex: 1;
    text-align: right;
  }

  .small-text {
    font-size: 12px;
    color: #2196f3;
    margin-left: 10px;
    position: relative;
    top: 2px;
  }
}
</style>