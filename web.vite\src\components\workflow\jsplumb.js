(function(){void 0===Math.sgn&&(Math.sgn=function(t){return 0==t?0:t>0?1:-1});var t=function(t,e){return{x:t.x-e.x,y:t.y-e.y}},e=function(t,e){return t.x*e.x+t.y*e.y},n=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},i=function(t,e){return{x:t.x*e,y:t.y*e}},s=Math.pow(2,-65),o=function(e,i){for(var s=[],o=r(e,i),l=i.length-1,u=a(o,2*l-1,s,0),c=t(e,i[0]),d=n(c),p=0,f=0;f<u;f++){c=t(e,h(i,l,s[f],null,null));var g=n(c);g<d&&(d=g,p=s[f])}return c=t(e,i[l]),(g=n(c))<d&&(d=g,p=1),{location:p,distance:d}},r=function(n,s){for(var o=s.length-1,r=2*o-1,a=[],l=[],u=[],c=[],h=[[1,.6,.3,.1],[.4,.6,.6,.4],[.1,.3,.6,1]],d=0;d<=o;d++)a[d]=t(s[d],n);for(d=0;d<=o-1;d++)l[d]=t(s[d+1],s[d]),l[d]=i(l[d],3);for(var p=0;p<=o-1;p++)for(var f=0;f<=o;f++)u[p]||(u[p]=[]),u[p][f]=e(l[p],a[f]);for(d=0;d<=r;d++)c[d]||(c[d]=[]),c[d].y=0,c[d].x=parseFloat(d)/r;for(var g=o,m=o-1,v=0;v<=g+m;v++){var b=Math.max(0,v-m),y=Math.min(v,g);for(d=b;d<=y;d++){var P=v-d;c[d+P].y+=u[P][d]*h[P][d]}}return c},a=function(t,e,n,i){var s,o,r=[],d=[],p=[],f=[];switch(l(t,e)){case 0:return 0;case 1:if(i>=64)return n[0]=(t[0].x+t[e].x)/2,1;if(u(t,e))return n[0]=c(t,e),1}h(t,e,.5,r,d),s=a(r,e,p,i+1),o=a(d,e,f,i+1);for(var g=0;g<s;g++)n[g]=p[g];for(g=0;g<o;g++)n[g+s]=f[g];return s+o},l=function(t,e){var n,i,s=0;n=i=Math.sgn(t[0].y);for(var o=1;o<=e;o++)(n=Math.sgn(t[o].y))!=i&&s++,i=n;return s},u=function(t,e){var n,i,o,r,a,l,u,c,h;r=t[0].y-t[e].y,a=t[e].x-t[0].x,l=t[0].x*t[e].y-t[e].x*t[0].y,c=h=0;for(var d=1;d<e;d++){var p=r*t[d].x+a*t[d].y+l;p>c?c=p:p<h&&(h=p)}return 0,1,0,n=(1*(l-c)-0*(u=a))*(1/(0*u-1*r)),i=(1*(l-h)-0*(u=a))*(1/(0*u-1*r)),o=Math.min(n,i),Math.max(n,i)-o<s?1:0},c=function(t,e){var n=t[e].x-t[0].x,i=t[e].y-t[0].y,s=t[0].x-0;return 0+1*((n*(t[0].y-0)-i*s)*(1/(0*n-1*i)))},h=function(t,e,n,i,s){for(var o=[[]],r=0;r<=e;r++)o[0][r]=t[r];for(var a=1;a<=e;a++)for(r=0;r<=e-a;r++)o[a]||(o[a]=[]),o[a][r]||(o[a][r]={}),o[a][r].x=(1-n)*o[a-1][r].x+n*o[a-1][r+1].x,o[a][r].y=(1-n)*o[a-1][r].y+n*o[a-1][r+1].y;if(null!=i)for(r=0;r<=e;r++)i[r]=o[r][0];if(null!=s)for(r=0;r<=e;r++)s[r]=o[e-r][r];return o[e][0]},d={},p=function(t,e){for(var n=function(t){var e=d[t];if(!e){var n=function(t){return function(e){return t}},i=function(){return function(t){return t}},s=function(){return function(t){return 1-t}},o=function(t){return function(e){for(var n=1,i=0;i<t.length;i++)n*=t[i](e);return n}};(e=[]).push(new function(){return function(e){return Math.pow(e,t)}});for(var r=1;r<t;r++){for(var a=[new n(t)],l=0;l<t-r;l++)a.push(new i);for(l=0;l<r;l++)a.push(new s);e.push(new o(a))}e.push(new function(){return function(e){return Math.pow(1-e,t)}}),d[t]=e}return e}(t.length-1),i=0,s=0,o=0;o<t.length;o++)i+=t[o].x*n[o](e),s+=t[o].y*n[o](e);return{x:i,y:s}},f=function(t,e){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},g=function(t){return t[0].x===t[1].x&&t[0].y===t[1].y},m=function(t,e,n){if(g(t))return{point:t[0],location:e};for(var i=p(t,e),s=0,o=e,r=n>0?1:-1,a=null;s<Math.abs(n);)a=p(t,o+=.005*r),s+=f(a,i),i=a;return{point:a,location:o}},v=function(t,e){var n=p(t,e),i=p(t.slice(0,t.length-1),e),s=i.y-n.y,o=i.x-n.x;return 0===s?1/0:Math.atan(s/o)},b=function(t,e,n,i,s){var o=i-e,r=t-n,a=t*(e-i)+e*(n-t),l=function(t){return[y(t,"x"),y(t,"y")]}(s),u=[o*l[0][0]+r*l[1][0],o*l[0][1]+r*l[1][1],o*l[0][2]+r*l[1][2],o*l[0][3]+r*l[1][3]+a],c=function(t,e,n,i){var s,o,r=e/t,a=n/t,l=i/t,u=(3*a-Math.pow(r,2))/9,c=(9*r*a-27*l-2*Math.pow(r,3))/54,h=Math.pow(u,3)+Math.pow(c,2),d=[];if(h>=0)s=P(c+Math.sqrt(h))*Math.pow(Math.abs(c+Math.sqrt(h)),1/3),o=P(c-Math.sqrt(h))*Math.pow(Math.abs(c-Math.sqrt(h)),1/3),d[0]=-r/3+(s+o),d[1]=-r/3-(s+o)/2,d[2]=-r/3-(s+o)/2,0!==Math.abs(Math.sqrt(3)*(s-o)/2)&&(d[1]=-1,d[2]=-1);else{var p=Math.acos(c/Math.sqrt(-Math.pow(u,3)));d[0]=2*Math.sqrt(-u)*Math.cos(p/3)-r/3,d[1]=2*Math.sqrt(-u)*Math.cos((p+2*Math.PI)/3)-r/3,d[2]=2*Math.sqrt(-u)*Math.cos((p+4*Math.PI)/3)-r/3}for(var f=0;f<3;f++)(d[f]<0||d[f]>1)&&(d[f]=-1);return d}.apply(null,u),h=[];if(null!=c)for(var d=0;d<3;d++){var p,f=c[d],g=Math.pow(f,2),m=Math.pow(f,3),v=[l[0][0]*m+l[0][1]*g+l[0][2]*f+l[0][3],l[1][0]*m+l[1][1]*g+l[1][2]*f+l[1][3]];p=n-t!=0?(v[0]-t)/(n-t):(v[1]-e)/(i-e),f>=0&&f<=1&&p>=0&&p<=1&&h.push(v)}return h};function y(t,e){return[-t[0][e]+3*t[1][e]+-3*t[2][e]+t[3][e],3*t[0][e]-6*t[1][e]+3*t[2][e],-3*t[0][e]+3*t[1][e],t[0][e]]}function P(t){return t<0?-1:t>0?1:0}var _=this.jsBezier={distanceFromCurve:o,gradientAtPoint:v,gradientAtPointAlongCurveFrom:function(t,e,n){var i=m(t,e,n);return i.location>1&&(i.location=1),i.location<0&&(i.location=0),v(t,i.location)},nearestPointOnCurve:function(t,e){var n=o(t,e);return{point:h(e,e.length-1,n.location,null,null),location:n.location}},pointOnCurve:p,pointAlongCurveFrom:function(t,e,n){return m(t,e,n).point},perpendicularToCurveAt:function(t,e,n,i){var s=m(t,e,i=null==i?0:i),o=v(t,s.location),r=Math.atan(-1/o),a=n/2*Math.sin(r),l=n/2*Math.cos(r);return[{x:s.point.x+l,y:s.point.y+a},{x:s.point.x-l,y:s.point.y-a}]},locationAlongCurveFrom:function(t,e,n){return m(t,e,n).location},getLength:function(t){if(g(t))return 0;for(var e=p(t,0),n=0,i=0,s=null;i<1;)s=p(t,i+=.005),n+=f(s,e),e=s;return n},lineIntersection:b,boxIntersection:function(t,e,n,i,s){var o=[];return o.push.apply(o,b(t,e,t+n,e,s)),o.push.apply(o,b(t+n,e,t+n,e+i,s)),o.push.apply(o,b(t+n,e+i,t,e+i,s)),o.push.apply(o,b(t,e+i,t,e,s)),o},boundingBoxIntersection:function(t,e){var n=[];return n.push.apply(n,b(t.x,t.y,t.x+t.w,t.y,e)),n.push.apply(n,b(t.x+t.w,t.y,t.x+t.w,t.y+t.h,e)),n.push.apply(n,b(t.x+t.w,t.y+t.h,t.x,t.y+t.h,e)),n.push.apply(n,b(t.x,t.y+t.h,t.x,t.y,e)),n},version:"0.9.0"};"undefined"!=typeof exports&&(exports.jsBezier=_)}).call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.Biltong={version:"0.4.0"};"undefined"!=typeof exports&&(exports.Biltong=t);var e=function(t){return"[object Array]"===Object.prototype.toString.call(t)},n=function(t,n,i){return i(t=e(t)?t:[t.x,t.y],n=e(n)?n:[n.x,n.y])},i=t.gradient=function(t,e){return n(t,e,function(t,e){return e[0]==t[0]?e[1]>t[1]?1/0:-1/0:e[1]==t[1]?e[0]>t[0]?0:-0:(e[1]-t[1])/(e[0]-t[0])})},s=(t.normal=function(t,e){return-1/i(t,e)},t.lineLength=function(t,e){return n(t,e,function(t,e){return Math.sqrt(Math.pow(e[1]-t[1],2)+Math.pow(e[0]-t[0],2))})},t.quadrant=function(t,e){return n(t,e,function(t,e){return e[0]>t[0]?e[1]>t[1]?2:1:e[0]==t[0]?e[1]>t[1]?2:1:e[1]>t[1]?3:4})}),o=(t.theta=function(t,e){return n(t,e,function(t,e){var n=i(t,e),o=Math.atan(n),r=s(t,e);return 4!=r&&3!=r||(o+=Math.PI),o<0&&(o+=2*Math.PI),o})},t.intersects=function(t,e){var n=t.x,i=t.x+t.w,s=t.y,o=t.y+t.h,r=e.x,a=e.x+e.w,l=e.y,u=e.y+e.h;return n<=r&&r<=i&&s<=l&&l<=o||n<=a&&a<=i&&s<=l&&l<=o||n<=r&&r<=i&&s<=u&&u<=o||n<=a&&r<=i&&s<=u&&u<=o||r<=n&&n<=a&&l<=s&&s<=u||r<=i&&i<=a&&l<=s&&s<=u||r<=n&&n<=a&&l<=o&&o<=u||r<=i&&n<=a&&l<=o&&o<=u},t.encloses=function(t,e,n){var i=t.x,s=t.x+t.w,o=t.y,r=t.y+t.h,a=e.x,l=e.x+e.w,u=e.y,c=e.y+e.h,h=function(t,e,i,s){return n?t<=e&&i>=s:t<e&&i>s};return h(i,a,s,l)&&h(o,u,r,c)},[null,[1,-1],[1,1],[-1,1],[-1,-1]]),r=[null,[-1,-1],[-1,1],[1,1],[1,-1]];t.pointOnLine=function(t,e,n){var a=i(t,e),l=s(t,e),u=n>0?o[l]:r[l],c=Math.atan(a),h=Math.abs(n*Math.sin(c))*u[1],d=Math.abs(n*Math.cos(c))*u[0];return{x:t.x+d,y:t.y+h}},t.perpendicularLineTo=function(t,e,n){var s=i(t,e),o=Math.atan(-1/s),r=n/2*Math.sin(o),a=n/2*Math.cos(o);return[{x:e.x+a,y:e.y+r},{x:e.x-a,y:e.y-r}]}}.call("undefined"!=typeof window?window:this),function(){"use strict";function t(t,e,n,i,s,o,r,a){return function(){var t=[];return Array.prototype.push.apply(t,arguments),t.item=function(t){return this[t]},t}(function(t,e,n,i,s,o,r,a){return new Touch({target:e,identifier:S(),pageX:n,pageY:i,screenX:s,screenY:o,clientX:r||s,clientY:a||o})}.apply(null,arguments))}var e=function(t,e,n){for(var i=(n=n||t.parentNode).querySelectorAll(e),s=0;s<i.length;s++)if(i[s]===t)return!0;return!1},n=function(t){return"string"==typeof t||t.constructor===String?document.getElementById(t):t},i=function(t){return t.srcElement||t.target},s=function(t,e,n,i){if(i){if(void 0!==t.path&&t.path.indexOf)return{path:t.path,end:t.path.indexOf(n)};var s={path:[],end:-1},o=function(t){s.path.push(t),t===n?s.end=s.path.length-1:null!=t.parentNode&&o(t.parentNode)};return o(e),s}return{path:[e],end:1}},o=function(t,e){for(var n=0,i=t.length;n<i&&t[n]!=e;n++);n<t.length&&t.splice(n,1)},r=1,a=function(t,e,n){var i=r++;return t.__ta=t.__ta||{},t.__ta[e]=t.__ta[e]||{},t.__ta[e][i]=n,n.__tauid=i,i},l=function(t,n,o,r){if(null==t)return o;var a=t.split(","),l=function(r){l.__tauid=o.__tauid;var u=i(r),c=u,h=s(r,u,n,null!=t);if(-1!=h.end)for(var d=0;d<h.end;d++){c=h.path[d];for(var p=0;p<a.length;p++)e(c,a[p],n)&&o.apply(c,arguments)}};return u(o,r,l),l},u=function(t,e,n){t.__taExtra=t.__taExtra||[],t.__taExtra.push([e,n])},c=function(t,e,n,i){if(p&&g[e]){var s=l(i,t,n,g[e]);C(t,g[e],s,n)}"focus"===e&&null==t.getAttribute("tabindex")&&t.setAttribute("tabindex","1"),C(t,e,l(i,t,n,e),n)},h={tap:{touches:1,taps:1},dbltap:{touches:1,taps:2},contextmenu:{touches:2,taps:1}},d=function(t,e,n,i){for(var s in n.__tamee[t])n.__tamee[t].hasOwnProperty(s)&&n.__tamee[t][s].apply(i,[e])},p="ontouchstart"in document.documentElement,f="onmousedown"in document.documentElement,g={mousedown:"touchstart",mouseup:"touchend",mousemove:"touchmove"},m=function(){var t=-1;if("Microsoft Internet Explorer"==navigator.appName){var e=navigator.userAgent;null!=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})").exec(e)&&(t=parseFloat(RegExp.$1))}return t}(),v=m>-1&&m<9,b=function(t,e){if(null==t)return[0,0];var n=_(t),i=P(n,0);return[i[e+"X"],i[e+"Y"]]},y=function(t){return null==t?[0,0]:v?[t.clientX+document.documentElement.scrollLeft,t.clientY+document.documentElement.scrollTop]:b(t,"page")},P=function(t,e){return t.item?t.item(e):t[e]},_=function(t){return t.touches&&t.touches.length>0?t.touches:t.changedTouches&&t.changedTouches.length>0?t.changedTouches:t.targetTouches&&t.targetTouches.length>0?t.targetTouches:[t]},x=function(t){return _(t).length},C=function(t,e,n,i){if(a(t,e,n),i.__tauid=n.__tauid,t.addEventListener)t.addEventListener(e,n,!1);else if(t.attachEvent){var s=e+n.__tauid;t["e"+s]=n,t[s]=function(){t["e"+s]&&t["e"+s](window.event)},t.attachEvent("on"+e,t[s])}},j=function(t,e,i){null!=i&&E(t,function(){var s=n(this);if(function(t,e,n){if(t.__ta&&t.__ta[e]&&delete t.__ta[e][n.__tauid],n.__taExtra){for(var i=0;i<n.__taExtra.length;i++)j(t,n.__taExtra[i][0],n.__taExtra[i][1]);n.__taExtra.length=0}n.__taUnstore&&n.__taUnstore()}(s,e,i),null!=i.__tauid)if(s.removeEventListener)s.removeEventListener(e,i,!1),p&&g[e]&&s.removeEventListener(g[e],i,!1);else if(this.detachEvent){var o=e+i.__tauid;s[o]&&s.detachEvent("on"+e,s[o]),s[o]=null,s["e"+o]=null}i.__taTouchProxy&&j(t,i.__taTouchProxy[1],i.__taTouchProxy[0])})},E=function(t,e){if(null!=t){t="undefined"!=typeof Window&&"unknown"!=typeof t.top&&t==t.top?[t]:"string"!=typeof t&&null==t.tagName&&null!=t.length?t:"string"==typeof t?document.querySelectorAll(t):[t];for(var n=0;n<t.length;n++)e.apply(t[n])}},S=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)})};this.Mottle=function(r){var u=(r=r||{}).clickThreshold||250,m=r.dblClickThreshold||450,v=new function(){var t=[];return function(n,s,o,r){if(!n.__tamee){n.__tamee={over:!1,mouseenter:[],mouseexit:[]};var u=function(s){var o=i(s);(null==r&&o==n&&!n.__tamee.over||e(o,r,n)&&(null==o.__tamee||!o.__tamee.over))&&(d("mouseenter",s,n,o),o.__tamee=o.__tamee||{},o.__tamee.over=!0,t.push(o))},c=function(s){for(var o=i(s),r=0;r<t.length;r++)o!=t[r]||e(s.relatedTarget||s.toElement,"*",o)||(o.__tamee.over=!1,t.splice(r,1),d("mouseexit",s,n,o))};C(n,"mouseover",l(r,n,u,"mouseover"),u),C(n,"mouseout",l(r,n,c,"mouseout"),c)}o.__taUnstore=function(){delete n.__tamee[s][o.__tauid]},a(n,s,o),n.__tamee[s][o.__tauid]=o}},P=new function(t,n){return function(r,a,l,u){if("contextmenu"==a&&f)c(r,a,l,u);else{if(null==r.__taTapHandler){var d=r.__taTapHandler={tap:[],dbltap:[],contextmenu:[],down:!1,taps:0,downSelectors:[]},p=function(){d.down=!1},g=function(){d.taps=0};c(r,"mousedown",function(o){for(var a=i(o),l=s(o,a,r,null!=u),c=!1,h=0;h<l.end;h++){if(c)return;a=l.path[h];for(var f=0;f<d.downSelectors.length;f++)if(null==d.downSelectors[f]||e(a,d.downSelectors[f],r)){d.down=!0,setTimeout(p,t),setTimeout(g,n),c=!0;break}}}),c(r,"mouseup",function(t){if(d.down){var n,o,a=i(t);d.taps++;var l=x(t);for(var u in h)if(h.hasOwnProperty(u)){var c=h[u];if(c.touches===l&&(1===c.taps||c.taps===d.taps))for(var p=0;p<d[u].length;p++){o=s(t,a,r,null!=d[u][p][1]);for(var f=0;f<o.end;f++)if(n=o.path[f],null==d[u][p][1]||e(n,d[u][p][1],r)){d[u][p][0].apply(n,[t]);break}}}}})}r.__taTapHandler.downSelectors.push(u),r.__taTapHandler[a].push([l,u]),l.__taUnstore=function(){o(r.__taTapHandler[a],l)}}}}(u,m),_=r.smartClicks,S=function(t,e,s,r){null!=s&&E(t,function(){var t=n(this);_&&"click"===e?function(t,e,n,s){null==t.__taSmartClicks&&(c(t,"mousedown",function(e){t.__tad=y(e)},s),c(t,"mouseup",function(e){t.__tau=y(e)},s),c(t,"click",function(e){if(t.__tad&&t.__tau&&t.__tad[0]===t.__tau[0]&&t.__tad[1]===t.__tau[1])for(var n=0;n<t.__taSmartClicks.length;n++)t.__taSmartClicks[n].apply(i(e),[e])},s),t.__taSmartClicks=[]);t.__taSmartClicks.push(n),n.__taUnstore=function(){o(t.__taSmartClicks,n)}}(t,0,s,r):"tap"===e||"dbltap"===e||"contextmenu"===e?P(t,e,s,r):"mouseenter"===e||"mouseexit"==e?v(t,e,s,r):c(t,e,s,r)})};this.remove=function(t){return E(t,function(){var t=n(this);if(t.__ta)for(var e in t.__ta)if(t.__ta.hasOwnProperty(e))for(var i in t.__ta[e])t.__ta[e].hasOwnProperty(i)&&j(t,e,t.__ta[e][i]);t.parentNode&&t.parentNode.removeChild(t)}),this},this.on=function(t,e,n,i){var s=arguments[0],o=4==arguments.length?arguments[2]:null,r=arguments[1],a=arguments[arguments.length-1];return S(s,r,a,o),this},this.off=function(t,e,n){return j(t,e,n),this},this.trigger=function(e,i,s,o){var r=f&&("undefined"==typeof MouseEvent||null==s||s.constructor===MouseEvent),a=p&&!f&&g[i]?g[i]:i,l=!(p&&!f&&g[i]),u=y(s),c=b(s,"screen"),h=function(t){return b(t,"client")}(s);return E(e,function(){var e,d=n(this);s=s||{screenX:c[0],screenY:c[1],clientX:h[0],clientY:h[1]};var f=function(t){o&&(t.payload=o)},m={TouchEvent:function(e){var n=t(window,d,0,u[0],u[1],c[0],c[1],h[0],h[1]);(e.initTouchEvent||e.initEvent)(a,!0,!0,window,null,c[0],c[1],h[0],h[1],!1,!1,!1,!1,n,n,n,1,0)},MouseEvents:function(t){t.initMouseEvent(a,!0,!0,window,0,c[0],c[1],h[0],h[1],!1,!1,!1,!1,1,d)}};if(document.createEvent){var v=!l&&!r&&p&&g[i]?"TouchEvent":"MouseEvents";e=document.createEvent(v),m[v](e),f(e),d.dispatchEvent(e)}else document.createEventObject&&((e=document.createEventObject()).eventType=e.eventName=a,e.screenX=c[0],e.screenY=c[1],e.clientX=h[0],e.clientY=h[1],f(e),d.fireEvent("on"+a,e))}),this}},this.Mottle.consume=function(t,e){t.stopPropagation?t.stopPropagation():t.returnValue=!1,!e&&t.preventDefault&&t.preventDefault()},this.Mottle.pageLocation=y,this.Mottle.setForceTouchEvents=function(t){p=t},this.Mottle.setForceMouseEvents=function(t){f=t},this.Mottle.version="0.8.0","undefined"!=typeof exports&&(exports.Mottle=this.Mottle)}.call("undefined"==typeof window?this:window),function(){"use strict";var t=function(t,e,n){return-1===t.indexOf(e)&&(n?t.unshift(e):t.push(e),!0)},e=function(t,e){var n=t.indexOf(e);-1!==n&&t.splice(n,1)},n=function(t){return null!=t&&("string"==typeof t||t.constructor===String)},i=function(t,e,n){for(var i=(n=n||t.parentNode).querySelectorAll(e),s=0;s<i.length;s++)if(i[s]===t)return!0;return!1},s=function(t,e,n){if(i(e,n,t))return e;for(var s=e.parentNode;null!=s&&s!==t;){if(i(s,n,t))return s;s=s.parentNode}},o=function(){var t=-1;if("Microsoft Internet Explorer"===navigator.appName){var e=navigator.userAgent;null!=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})").exec(e)&&(t=parseFloat(RegExp.$1))}return t}(),r=o>-1&&o<9,a=9===o,l=function(t){if(r)return[t.clientX+document.documentElement.scrollLeft,t.clientY+document.documentElement.scrollTop];var e=c(t),n=u(e,0);return a?[n.pageX||n.clientX,n.pageY||n.clientY]:[n.pageX,n.pageY]},u=function(t,e){return t.item?t.item(e):t[e]},c=function(t){return t.touches&&t.touches.length>0?t.touches:t.changedTouches&&t.changedTouches.length>0?t.changedTouches:t.targetTouches&&t.targetTouches.length>0?t.targetTouches:[t]},h={delegatedDraggable:"katavorio-delegated-draggable",draggable:"katavorio-draggable",droppable:"katavorio-droppable",drag:"katavorio-drag",selected:"katavorio-drag-selected",active:"katavorio-drag-active",hover:"katavorio-drag-hover",noSelect:"katavorio-drag-no-select",ghostProxy:"katavorio-ghost-proxy",clonedDrag:"katavorio-clone-drag"},d=["stop","start","drag","drop","over","out","beforeStart"],p=function(){},f=function(){return!0},g=function(t,e,n){for(var i=0;i<t.length;i++)t[i]!=n&&e(t[i])},m=function(t,e,n,i){g(t,function(t){t.setActive(e),e&&t.updatePosition(),n&&t.setHover(i,e)})},v=function(t,e){if(null!=t){t=n(t)||null!=t.tagName||null==t.length?[t]:t;for(var i=0;i<t.length;i++)e.apply(t[i],[t[i]])}},b=function(t){t.stopPropagation?(t.stopPropagation(),t.preventDefault()):t.returnValue=!1},y=function(t,e,n,i){this.params=e||{},this.el=t,this.params.addClass(this.el,this._class),this.uuid=j();var s=!0;return this.setEnabled=function(t){s=t},this.isEnabled=function(){return s},this.toggleEnabled=function(){s=!s},this.setScope=function(t){this.scopes=t?t.split(/\s+/):[i]},this.addScope=function(t){var e={};for(var n in v(this.scopes,function(t){e[t]=!0}),v(t?t.split(/\s+/):[],function(t){e[t]=!0}),this.scopes=[],e)this.scopes.push(n)},this.removeScope=function(t){var e={};for(var n in v(this.scopes,function(t){e[t]=!0}),v(t?t.split(/\s+/):[],function(t){delete e[t]}),this.scopes=[],e)this.scopes.push(n)},this.toggleScope=function(t){var e={};for(var n in v(this.scopes,function(t){e[t]=!0}),v(t?t.split(/\s+/):[],function(t){e[t]?delete e[t]:e[t]=!0}),this.scopes=[],e)this.scopes.push(n)},this.setScope(e.scope),this.k=e.katavorio,e.katavorio},P=function(){return!0},_=function(){return!1},x=function(t,e,o,r){this._class=o.draggable;var a=y.apply(this,arguments);this.rightButtonCanDrag=this.params.rightButtonCanDrag;var u,c,d,p,g=[0,0],v=null,x=null,C=[0,0],S=!1,D=[0,0],w=!1!==this.params.consumeStartEvent,I=this.el,A=this.params.clone,k=(this.params.scroll,!1!==e.multipleDrop),M=!1,O=!0===e.ghostProxy?P:e.ghostProxy&&"function"==typeof e.ghostProxy?e.ghostProxy:_,T=null,L=[],F=null,G=e.ghostProxyParent;if(e.selector){var N=t.getAttribute("katavorio-draggable");null==N&&(N=""+(new Date).getTime(),t.setAttribute("katavorio-draggable",N)),L.push(e)}var U,B=e.snapThreshold,R=function(t,e,n,i,s){var o=e*Math.floor(t[0]/e),r=o+e,a=Math.abs(t[0]-o)<=i?o:Math.abs(r-t[0])<=i?r:t[0],l=n*Math.floor(t[1]/n),u=l+n;return[a,Math.abs(t[1]-l)<=s?l:Math.abs(u-t[1])<=s?u:t[1]]};this.posses=[],this.posseRoles={},this.toGrid=function(t){if(null==this.params.grid)return t;var e=this.params.grid?this.params.grid[0]/2:B||5,n=this.params.grid?this.params.grid[1]/2:B||5;return R(t,this.params.grid[0],this.params.grid[1],e,n)},this.snap=function(t,e){if(null!=I){t=t||(this.params.grid?this.params.grid[0]:10),e=e||(this.params.grid?this.params.grid[1]:10);var n=this.params.getPosition(I),i=this.params.grid?this.params.grid[0]/2:B,s=this.params.grid?this.params.grid[1]/2:B,o=R(n,t,e,i,s);return this.params.setPosition(I,o),o}},this.setUseGhostProxy=function(t){O=t?P:_};var H,X=function(t){return!1===e.allowNegative?[Math.max(0,t[0]),Math.max(0,t[1])]:t},Y=function(t){U="function"==typeof t?t:t?function(t,e,n,i){return X([Math.max(0,Math.min(n.w-i[0],t[0])),Math.max(0,Math.min(n.h-i[1],t[1]))])}.bind(this):function(t){return X(t)}}.bind(this);Y("function"==typeof this.params.constrain?this.params.constrain:this.params.constrain||this.params.containment),this.setConstrain=function(t){Y(t)},this.setRevert=function(t){H=t},this.params.revert&&(H=this.params.revert);var W={},z=this.setFilter=function(e,s){if(e){var o="function"==typeof(r=e)?(r._katavorioId=j(),r._katavorioId):r;W[o]=[function(s){var o,r=s.srcElement||s.target;return n(e)?o=i(r,e,t):"function"==typeof e&&(o=e(s,t)),o},!1!==s]}var r};this.addFilter=z,this.removeFilter=function(t){var e="function"==typeof t?t._katavorioId:t;delete W[e]};this.clearAllFilters=function(){W={}},this.canDrag=this.params.canDrag||f;var V,q=[],J=[];this.addSelector=function(t){t.selector&&L.push(t)},this.downListener=function(t){var e,n,r,u,c,d,p,f,m;if(!t.defaultPrevented&&((this.rightButtonCanDrag||3!==t.which&&2!==t.button)&&this.isEnabled()&&this.canDrag()))if(function(t){for(var e in W){var n=W[e],i=n[0](t);if(n[1]&&(i=!i),!i)return!1}return!0}(t)&&function(t,e,n){var s=t.srcElement||t.target;return!i(s,n.getInputFilterSelector(),e)}(t,this.el,this.k)){if(F=null,T=null,L.length>0){var y=function(t,e,n){for(var o=null,r=e.getAttribute("katavorio-draggable"),a=null!=r?"[katavorio-draggable='"+r+"'] ":"",l=0;l<t.length;l++)if(null!=(o=s(e,n,a+t[l].selector))){if(t[l].filter){var u=i(n,t[l].filter,o);if(!0===t[l].filterExclude&&!u||u)return null}return[t[l],o]}return null}(L,this.el,t.target||t.srcElement);if(null!=y&&(F=y[0],T=y[1]),null==T)return}else T=this.el;if(A)if(I=T.cloneNode(!0),this.params.addClass(I,h.clonedDrag),I.setAttribute("id",null),I.style.position="absolute",null!=this.params.parent){var P=this.params.getPosition(this.el);I.style.left=P[0]+"px",I.style.top=P[1]+"px",this.params.parent.appendChild(I)}else{var _=(e=T.getBoundingClientRect(),n=document.body,r=document.documentElement,u=window.pageYOffset||r.scrollTop||n.scrollTop,c=window.pageXOffset||r.scrollLeft||n.scrollLeft,d=r.clientTop||n.clientTop||0,p=r.clientLeft||n.clientLeft||0,f=e.top+u-d,m=e.left+c-p,{top:Math.round(f),left:Math.round(m)});I.style.left=_.left+"px",I.style.top=_.top+"px",document.body.appendChild(I)}else I=T;w&&b(t),g=l(t),I&&I.parentNode&&(D=[I.parentNode.scrollLeft,I.parentNode.scrollTop]),this.params.bind(document,"mousemove",this.moveListener),this.params.bind(document,"mouseup",this.upListener),a.markSelection(this),a.markPosses(this),this.params.addClass(document.body,o.noSelect),$("beforeStart",{el:this.el,pos:v,e:t,drag:this})}else this.params.consumeFilteredEvents&&b(t)}.bind(this),this.moveListener=function(t){if(g){if(!S)if(!1!==$("start",{el:this.el,pos:v,e:t,drag:this})){if(!g)return;this.mark(!0),S=!0}else this.abort();if(g){J.length=0;var e=l(t),n=e[0]-g[0],i=e[1]-g[1],s=this.params.ignoreZoom?1:a.getZoom();I&&I.parentNode&&(n+=I.parentNode.scrollLeft-D[0],i+=I.parentNode.scrollTop-D[1]),n/=s,i/=s,this.moveBy(n,i,t),a.updateSelection(n,i,this),a.updatePosses(n,i,this)}}}.bind(this),this.upListener=function(t){g&&(g=null,this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.params.removeClass(document.body,o.noSelect),this.unmark(t),a.unmarkSelection(this,t),a.unmarkPosses(this,t),this.stop(t),a.notifyPosseDragStop(this,t),S=!1,J.length=0,A?(I&&I.parentNode&&I.parentNode.removeChild(I),I=null):H&&!0===H(I,this.params.getPosition(I))&&(this.params.setPosition(I,v),$("revert",I)))}.bind(this),this.getFilters=function(){return W},this.abort=function(){null!=g&&this.upListener()},this.getDragElement=function(t){return t?T||this.el:I||this.el};var Z={start:[],drag:[],stop:[],over:[],out:[],beforeStart:[],revert:[]};e.events.start&&Z.start.push(e.events.start),e.events.beforeStart&&Z.beforeStart.push(e.events.beforeStart),e.events.stop&&Z.stop.push(e.events.stop),e.events.drag&&Z.drag.push(e.events.drag),e.events.revert&&Z.revert.push(e.events.revert),this.on=function(t,e){Z[t]&&Z[t].push(e)},this.off=function(t,e){if(Z[t]){for(var n=[],i=0;i<Z[t].length;i++)Z[t][i]!==e&&n.push(Z[t][i]);Z[t]=n}};var K,$=function(t,e){var n=null;if(F&&F[t])n=F[t](e);else if(Z[t])for(var i=0;i<Z[t].length;i++)try{var s=Z[t][i](e);null!=s&&(n=s)}catch(t){}return n};this.notifyStart=function(t){$("start",{el:this.el,pos:this.params.getPosition(I),e:t,drag:this})},this.stop=function(t,e){if(e||S){var n=[],i=a.getSelection(),s=this.params.getPosition(I);if(i.length>0)for(var o=0;o<i.length;o++){var r=this.params.getPosition(i[o].el);n.push([i[o].el,{left:r[0],top:r[1]},i[o]])}else n.push([I,{left:s[0],top:s[1]},this]);$("stop",{el:I,pos:K||s,finalPos:s,e:t,drag:this,selection:n})}},this.mark=function(t){var e;v=this.params.getPosition(I),x=this.params.getPosition(I,!0),C=[x[0]-v[0],x[1]-v[1]],this.size=this.params.getSize(I),q=a.getMatchingDroppables(this),m(q,!0,!1,this),this.params.addClass(I,this.params.dragClass||o.drag),e=this.params.getConstrainingRectangle?this.params.getConstrainingRectangle(I):this.params.getSize(I.parentNode),V={w:e[0],h:e[1]},d=0,p=0,t&&a.notifySelectionDragStart(this)},this.unmark=function(t,n){if(m(q,!1,!0,this),M&&O(T,I)?(K=[I.offsetLeft-d,I.offsetTop-p],I.parentNode.removeChild(I),I=T):K=null,this.params.removeClass(I,this.params.dragClass||o.drag),q.length=0,M=!1,!n){J.length>0&&K&&e.setPosition(T,K),J.sort(E);for(var i=0;i<J.length;i++){if(!0===J[i].drop(this,t))break}}},this.moveBy=function(t,n,i){J.length=0;var s=this.toGrid([v[0]+t,v[1]+n]),o=U(s,I,V,this.size);if(O(this.el,I))if(s[0]!==o[0]||s[1]!==o[1]){if(!M){var r=T.cloneNode(!0);e.addClass(r,h.ghostProxy),G?(G.appendChild(r),u=e.getPosition(T.parentNode,!0),c=e.getPosition(e.ghostProxyParent,!0),d=u[0]-c[0],p=u[1]-c[1]):T.parentNode.appendChild(r),I=r,M=!0}o=s}else M&&(I.parentNode.removeChild(I),I=T,M=!1,u=null,c=null,d=0,p=0);var a={x:o[0],y:o[1],w:this.size[0],h:this.size[1]},l={x:a.x+C[0],y:a.y+C[1],w:a.w,h:a.h},f=null;this.params.setPosition(I,[o[0]+d,o[1]+p]);for(var g=0;g<q.length;g++){var m={x:q[g].pagePosition[0],y:q[g].pagePosition[1],w:q[g].size[0],h:q[g].size[1]};this.params.intersects(l,m)&&(k||null==f||f===q[g].el)&&q[g].canDrop(this)?(f||(f=q[g].el),J.push(q[g]),q[g].setHover(this,!0,i)):q[g].isHover()&&q[g].setHover(this,!1,i)}$("drag",{el:this.el,pos:o,e:i,drag:this})},this.destroy=function(){this.params.unbind(this.el,"mousedown",this.downListener),this.params.unbind(document,"mousemove",this.moveListener),this.params.unbind(document,"mouseup",this.upListener),this.downListener=null,this.upListener=null,this.moveListener=null},this.params.bind(this.el,"mousedown",this.downListener),this.params.handle?z(this.params.handle,!1):z(this.params.filter,this.params.filterExclude)},C=function(t,e,n,i){this._class=n.droppable,this.params=e||{},this.rank=e.rank||0,this._activeClass=this.params.activeClass||n.active,this._hoverClass=this.params.hoverClass||n.hover,y.apply(this,arguments);var s=!1;this.allowLoopback=!1!==this.params.allowLoopback,this.setActive=function(t){this.params[t?"addClass":"removeClass"](this.el,this._activeClass)},this.updatePosition=function(){this.position=this.params.getPosition(this.el),this.pagePosition=this.params.getPosition(this.el,!0),this.size=this.params.getSize(this.el)},this.canDrop=this.params.canDrop||function(t){return!0},this.isHover=function(){return s},this.setHover=function(t,e,n){(e||null==this.el._katavorioDragHover||this.el._katavorioDragHover===t.el._katavorio)&&(this.params[e?"addClass":"removeClass"](this.el,this._hoverClass),this.el._katavorioDragHover=e?t.el._katavorio:null,s!==e&&this.params.events[e?"over":"out"]({el:this.el,e:n,drag:t,drop:this}),s=e)},this.drop=function(t,e){return this.params.events.drop({drag:t,e:e,drop:this})},this.destroy=function(){this._class=null,this._activeClass=null,this._hoverClass=null,s=null}},j=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})},E=function(t,e){return t.rank<e.rank?1:t.rank>e.rank?-1:0},S=function(t){return null==t?null:null==(t="string"==typeof t||t.constructor===String?document.getElementById(t):t)?null:(t._katavorio=t._katavorio||j(),t)};this.Katavorio=function(i){var s=[],o={};this._dragsByScope={},this._dropsByScope={};var r=1,a=function(t,e){v(t,function(t){for(var n=0;n<t.scopes.length;n++)e[t.scopes[n]]=e[t.scopes[n]]||[],e[t.scopes[n]].push(t)})},l=function(t,e){var n=0;return v(t,function(t){for(var s=0;s<t.scopes.length;s++)if(e[t.scopes[s]]){var o=i.indexOf(e[t.scopes[s]],t);-1!==o&&(e[t.scopes[s]].splice(o,1),n++)}}),n>0},u=(this.getMatchingDroppables=function(t){for(var e=[],n={},i=0;i<t.scopes.length;i++){var s=this._dropsByScope[t.scopes[i]];if(s)for(var o=0;o<s.length;o++)!s[o].canDrop(t)||n[s[o].uuid]||!s[o].allowLoopback&&s[o].el===t.el||(n[s[o].uuid]=!0,e.push(s[o]))}return e.sort(E),e},function(t){t=t||{};var e,n={events:{}};for(e in i)n[e]=i[e];for(e in t)n[e]=t[e];for(e=0;e<d.length;e++)n.events[d[e]]=t[d[e]]||p;return n.katavorio=this,n}.bind(this)),c=function(t,e){for(var n=0;n<d.length;n++)e[d[n]]&&t.on(d[n],e[d[n]])}.bind(this),f={},m=i.css||{},b=i.scope||"katavorio-drag-scope";for(var y in h)f[y]=h[y];for(var y in m)f[y]=m[y];var P=i.inputFilterSelector||"input,textarea,select,button,option";this.getInputFilterSelector=function(){return P},this.setInputFilterSelector=function(t){return P=t,this},this.draggable=function(t,e){var n=[];return v(t,function(t){if(null!=(t=S(t)))if(null==t._katavorioDrag){var s=u(e);t._katavorioDrag=new x(t,s,f,b),a(t._katavorioDrag,this._dragsByScope),n.push(t._katavorioDrag),i.addClass(t,s.selector?f.delegatedDraggable:f.draggable)}else c(t._katavorioDrag,e)}.bind(this)),n},this.droppable=function(t,e){var n=[];return v(t,function(t){if(null!=(t=S(t))){var s=new C(t,u(e),f,b);t._katavorioDrop=t._katavorioDrop||[],t._katavorioDrop.push(s),a(s,this._dropsByScope),n.push(s),i.addClass(t,f.droppable)}}.bind(this)),n},this.select=function(t){return v(t,function(){var t=S(this);t&&t._katavorioDrag&&(o[t._katavorio]||(s.push(t._katavorioDrag),o[t._katavorio]=[t,s.length-1],i.addClass(t,f.selected)))}),this},this.deselect=function(t){return v(t,function(){var t=S(this);if(t&&t._katavorio&&o[t._katavorio]){for(var e=[],n=0;n<s.length;n++)s[n].el!==t&&e.push(s[n]);s=e,delete o[t._katavorio],i.removeClass(t,f.selected)}}),this},this.deselectAll=function(){for(var t in o){var e=o[t];i.removeClass(e[0],f.selected)}s.length=0,o={}},this.markSelection=function(t){g(s,function(t){t.mark()},t)},this.markPosses=function(t){t.posses&&v(t.posses,function(e){t.posseRoles[e]&&I[e]&&g(I[e].members,function(t){t.mark()},t)})},this.unmarkSelection=function(t,e){g(s,function(t){t.unmark(e)},t)},this.unmarkPosses=function(t,e){t.posses&&v(t.posses,function(n){t.posseRoles[n]&&I[n]&&g(I[n].members,function(t){t.unmark(e,!0)},t)})},this.getSelection=function(){return s.slice(0)},this.updateSelection=function(t,e,n){g(s,function(n){n.moveBy(t,e)},n)};var _=function(t,e){e.posses&&v(e.posses,function(n){e.posseRoles[n]&&I[n]&&g(I[n].members,function(e){t(e)},e)})};this.updatePosses=function(t,e,n){_(function(n){n.moveBy(t,e)},n)},this.notifyPosseDragStop=function(t,e){_(function(t){t.stop(e,!0)},t)},this.notifySelectionDragStop=function(t,e){g(s,function(t){t.stop(e,!0)},t)},this.notifySelectionDragStart=function(t,e){g(s,function(t){t.notifyStart(e)},t)},this.setZoom=function(t){r=t},this.getZoom=function(){return r};var j=function(t,e,n,i){v(t,function(t){l(t,n),t[i](e),a(t,n)})};v(["set","add","remove","toggle"],function(t){this[t+"Scope"]=function(e,n){j(e._katavorioDrag,n,this._dragsByScope,t+"Scope"),j(e._katavorioDrop,n,this._dropsByScope,t+"Scope")}.bind(this),this[t+"DragScope"]=function(e,n){j(e.constructor===x?e:e._katavorioDrag,n,this._dragsByScope,t+"Scope")}.bind(this),this[t+"DropScope"]=function(e,n){j(e.constructor===C?e:e._katavorioDrop,n,this._dropsByScope,t+"Scope")}.bind(this)}.bind(this)),this.snapToGrid=function(t,e){for(var n in this._dragsByScope)g(this._dragsByScope[n],function(n){n.snap(t,e)})},this.getDragsForScope=function(t){return this._dragsByScope[t]},this.getDropsForScope=function(t){return this._dropsByScope[t]};var D=function(t,e,n){if((t=S(t))[e]){var i=s.indexOf(t[e]);i>=0&&s.splice(i,1),l(t[e],n)&&v(t[e],function(t){t.destroy()}),delete t[e]}},w=function(t,e,n,i){(t=S(t))[e]&&t[e].off(n,i)};this.elementRemoved=function(t){this.destroyDraggable(t),this.destroyDroppable(t)},this.destroyDraggable=function(t,e,n){1===arguments.length?D(t,"_katavorioDrag",this._dragsByScope):w(t,"_katavorioDrag",e,n)},this.destroyDroppable=function(t,e,n){1===arguments.length?D(t,"_katavorioDrop",this._dropsByScope):w(t,"_katavorioDrop",e,n)},this.reset=function(){this._dragsByScope={},this._dropsByScope={},s=[],o={},I={}};var I={},A=function(e,i,s){var o,r=n(i)?i:i.id,a=!!n(i)||!1!==i.active,l=I[r]||(o={name:r,members:[]},I[r]=o,o);return v(e,function(e){if(e._katavorioDrag){if(s&&null!=e._katavorioDrag.posseRoles[l.name])return;t(l.members,e._katavorioDrag),t(e._katavorioDrag.posses,l.name),e._katavorioDrag.posseRoles[l.name]=a}}),l};this.addToPosse=function(t,e){for(var n=[],i=1;i<arguments.length;i++)n.push(A(t,arguments[i]));return 1===n.length?n[0]:n},this.setPosse=function(t,e){for(var n=[],i=1;i<arguments.length;i++)n.push(A(t,arguments[i],!0).name);return v(t,function(t){if(t._katavorioDrag){var e=function(t,e){for(var n=[],i=0;i<t.length;i++)-1===e.indexOf(t[i])&&n.push(t[i]);return n}(t._katavorioDrag.posses,n);Array.prototype.push.apply([],t._katavorioDrag.posses);for(var i=0;i<e.length;i++)this.removeFromPosse(t,e[i])}}.bind(this)),1===n.length?n[0]:n},this.removeFromPosse=function(t,n){if(arguments.length<2)throw new TypeError("No posse id provided for remove operation");for(var i=1;i<arguments.length;i++)n=arguments[i],v(t,function(t){if(t._katavorioDrag&&t._katavorioDrag.posses){var i=t._katavorioDrag;v(n,function(t){e(I[t].members,i),e(i.posses,t),delete i.posseRoles[t]})}})},this.removeFromAllPosses=function(t){v(t,function(t){if(t._katavorioDrag&&t._katavorioDrag.posses){var n=t._katavorioDrag;v(n.posses,function(t){e(I[t].members,n)}),n.posses.length=0,n.posseRoles={}}})},this.setPosseState=function(t,e,n){var i=I[e];i&&v(t,function(t){t._katavorioDrag&&t._katavorioDrag.posses&&(t._katavorioDrag.posseRoles[i.name]=n)})}},this.Katavorio.version="1.0.0","undefined"!=typeof exports&&(exports.Katavorio=this.Katavorio)}.call("undefined"!=typeof window?window:this),function(){this.jsPlumbUtil=this.jsPlumbUtil||{};var t=this.jsPlumbUtil;function e(t){return"[object Array]"===Object.prototype.toString.call(t)}function n(t){return"string"==typeof t}function i(t){return"boolean"==typeof t}function s(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function o(t){return"[object Date]"===Object.prototype.toString.call(t)}function r(t){return"[object Function]"===Object.prototype.toString.call(t)}function a(t){if(n(t))return""+t;if(i(t))return!!t;if(o(t))return new Date(t.getTime());if(r(t))return t;if(e(t)){for(var l=[],u=0;u<t.length;u++)l.push(a(t[u]));return l}if(s(t)){var c={};for(var h in t)c[h]=a(t[h]);return c}return t}function l(t,o,r,l){var u,c,h={},d={};for(r=r||[],l=l||[],c=0;c<r.length;c++)h[r[c]]=!0;for(c=0;c<l.length;c++)d[l[c]]=!0;var p=a(t);for(c in o)if(null==p[c]||d[c])p[c]=o[c];else if(n(o[c])||i(o[c]))h[c]?((u=[]).push.apply(u,e(p[c])?p[c]:[p[c]]),u.push.apply(u,i(o[c])?o[c]:[o[c]]),p[c]=u):p[c]=o[c];else if(e(o[c]))u=[],e(p[c])&&u.push.apply(u,p[c]),u.push.apply(u,o[c]),p[c]=u;else if(s(o[c]))for(var f in s(p[c])||(p[c]={}),o[c])p[c][f]=o[c][f];return p}function u(t,e){if(t)for(var n=0;n<t.length;n++)if(e(t[n]))return n;return-1}function c(t,e){var n=t.indexOf(e);return n>-1&&t.splice(n,1),-1!==n}function h(t,e,n,i){var s=t[e];return null==s&&(s=[],t[e]=s),s[i?"unshift":"push"](n),s}function d(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})}function p(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(t.logEnabled&&"undefined"!=typeof console)try{var i=arguments[arguments.length-1];console.log(i)}catch(t){}}"undefined"!=typeof exports&&(exports.jsPlumbUtil=t),t.isArray=e,t.isNumber=function(t){return"[object Number]"===Object.prototype.toString.call(t)},t.isString=n,t.isBoolean=i,t.isNull=function(t){return null==t},t.isObject=s,t.isDate=o,t.isFunction=r,t.isNamedFunction=function(t){return r(t)&&null!=t.name&&t.name.length>0},t.isEmpty=function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0},t.clone=a,t.merge=l,t.replace=function(t,e,n){if(null!=t){var i=t;return e.replace(/([^\.])+/g,function(t,e,s,o){var r=t.match(/([^\[0-9]+){1}(\[)([0-9+])/),a=function(){return i[r[1]]||(i[r[1]]=[],i[r[1]])};if(s+t.length>=o.length)r?a()[r[3]]=n:i[t]=n;else if(r){var l=a();i=l[r[3]]||(l[r[3]]={},l[r[3]])}else i=i[t]||(i[t]={},i[t]);return""}),t}},t.functionChain=function(t,e,n){for(var i=0;i<n.length;i++){var s=n[i][0][n[i][1]].apply(n[i][0],n[i][2]);if(s===e)return s}return t},t.populate=function(t,i,o,a){var l=function(t){if(null!=t){if(n(t))return function(t){var e=t.match(/(\${.*?})/g);if(null!=e)for(var n=0;n<e.length;n++){var s=i[e[n].substring(2,e[n].length-1)]||"";null!=s&&(t=t.replace(e[n],s))}return t}(t);if(!r(t)||a||null!=o&&0!==(t.name||"").indexOf(o)){if(e(t)){for(var u=[],c=0;c<t.length;c++)u.push(l(t[c]));return u}if(s(t)){var h={};for(var d in t)h[d]=l(t[d]);return h}return t}return t(i)}};return l(t)},t.findWithFunction=u,t.removeWithFunction=function(t,e){var n=u(t,e);return n>-1&&t.splice(n,1),-1!==n},t.remove=c,t.addWithFunction=function(t,e,n){-1===u(t,n)&&t.push(e)},t.addToList=h,t.suggest=function(t,e,n){return-1===t.indexOf(e)&&(n?t.unshift(e):t.push(e),!0)},t.extend=function(t,n,i){var s;n=e(n)?n:[n];var o=function(e){for(var n=e.__proto__;null!=n;)if(null!=n.prototype){for(var i in n.prototype)n.prototype.hasOwnProperty(i)&&!t.prototype.hasOwnProperty(i)&&(t.prototype[i]=n.prototype[i]);n=n.prototype.__proto__}else n=null};for(s=0;s<n.length;s++){for(var r in n[s].prototype)n[s].prototype.hasOwnProperty(r)&&!t.prototype.hasOwnProperty(r)&&(t.prototype[r]=n[s].prototype[r]);o(n[s])}var a=function(t,e){return function(){for(s=0;s<n.length;s++)n[s].prototype[t]&&n[s].prototype[t].apply(this,arguments);return e.apply(this,arguments)}},l=function(e){for(var n in e)t.prototype[n]=a(n,e[n])};if(arguments.length>2)for(s=2;s<arguments.length;s++)l(arguments[s]);return t},t.uuid=d,t.fastTrim=function(t){if(null==t)return null;for(var e=t.replace(/^\s\s*/,""),n=/\s/,i=e.length;n.test(e.charAt(--i)););return e.slice(0,i+1)},t.each=function(t,e){t=null==t.length||"string"==typeof t?[t]:t;for(var n=0;n<t.length;n++)e(t[n])},t.map=function(t,e){for(var n=[],i=0;i<t.length;i++)n.push(e(t[i]));return n},t.mergeWithParents=function(t,e,n){n=n||"parent";var i=function(t){return t?e[t]:null},s=function(t){return t?i(t[n]):null},o=function(t,e){if(null==t)return e;var n=["anchor","anchors","cssClass","connector","paintStyle","hoverPaintStyle","endpoint","endpoints"];"override"===e.mergeStrategy&&Array.prototype.push.apply(n,["events","overlays"]);var i=l(t,e,[],n);return o(s(t),i)},r=function(t){if(null==t)return{};if("string"==typeof t)return i(t);if(t.length){for(var e=!1,n=0,s=void 0;!e&&n<t.length;)(s=r(t[n]))?e=!0:n++;return s}},a=r(t);return a?o(s(a),a):{}},t.logEnabled=!0,t.log=p,t.wrap=function(t,e,n){return function(){var i=null;try{null!=e&&(i=e.apply(this,arguments))}catch(t){p("jsPlumb function failed : "+t)}if(null!=t&&(null==n||i!==n))try{i=t.apply(this,arguments)}catch(t){p("wrapped function failed : "+t)}return i}};var f=function(){return function(){var t=this;this._listeners={},this.eventsSuspended=!1,this.tick=!1,this.eventsToDieOn={ready:!0},this.queue=[],this.bind=function(e,n,i){var s=function(e){h(t._listeners,e,n,i),n.__jsPlumb=n.__jsPlumb||{},n.__jsPlumb[d()]=e};if("string"==typeof e)s(e);else if(null!=e.length)for(var o=0;o<e.length;o++)s(e[o]);return t},this.fire=function(t,e,n){if(this.tick)this.queue.unshift(arguments);else{if(this.tick=!0,!this.eventsSuspended&&this._listeners[t]){var i=this._listeners[t].length,s=0,o=!1,r=null;if(!this.shouldFireEvent||this.shouldFireEvent(t,e,n))for(;!o&&s<i&&!1!==r;){if(this.eventsToDieOn[t])this._listeners[t][s].apply(this,[e,n]);else try{r=this._listeners[t][s].apply(this,[e,n])}catch(e){p("jsPlumb: fire failed for event "+t+" : "+e)}s++,null!=this._listeners&&null!=this._listeners[t]||(o=!0)}}this.tick=!1,this._drain()}return this},this._drain=function(){var e=t.queue.pop();e&&t.fire.apply(t,e)},this.unbind=function(t,e){if(0===arguments.length)this._listeners={};else if(1===arguments.length){if("string"==typeof t)delete this._listeners[t];else if(t.__jsPlumb){var n=void 0;for(var i in t.__jsPlumb)n=t.__jsPlumb[i],c(this._listeners[n]||[],t)}}else 2===arguments.length&&c(this._listeners[t]||[],e);return this},this.getListener=function(e){return t._listeners[e]},this.setSuspendEvents=function(e){t.eventsSuspended=e},this.isSuspendEvents=function(){return t.eventsSuspended},this.silently=function(e){t.setSuspendEvents(!0);try{e()}catch(t){p("Cannot execute silent function "+t)}t.setSuspendEvents(!1)},this.cleanupListeners=function(){for(var e in t._listeners)t._listeners[e]=null}}}();t.EventGenerator=f}.call("undefined"!=typeof window?window:this),function(){"use strict";this.jsPlumbUtil.matchesSelector=function(t,e,n){for(var i=(n=n||t.parentNode).querySelectorAll(e),s=0;s<i.length;s++)if(i[s]===t)return!0;return!1},this.jsPlumbUtil.consume=function(t,e){t.stopPropagation?t.stopPropagation():t.returnValue=!1,!e&&t.preventDefault&&t.preventDefault()},this.jsPlumbUtil.sizeElement=function(t,e,n,i,s){t&&(t.style.height=s+"px",t.height=s,t.style.width=i+"px",t.width=i,t.style.left=e+"px",t.style.top=n+"px")}}.call("undefined"!=typeof window?window:this),function(){var t={deriveAnchor:function(t,e,n,i){return{top:["TopRight","TopLeft"],bottom:["BottomRight","BottomLeft"]}[t][e]}},e=function(t){this.count=0,this.instance=t,this.lists={},this.instance.addList=function(t,e){return this.listManager.addList(t,e)},this.instance.removeList=function(t){this.listManager.removeList(t)},this.instance.bind("manageElement",function(t){for(var e=this.instance.getSelector(t.el,"[jtk-scrollable-list]"),n=0;n<e.length;n++)this.addList(e[n])}.bind(this)),this.instance.bind("unmanageElement",function(t){this.removeList(t.el)}),this.instance.bind("connection",function(t,e){null==e&&(this._maybeUpdateParentList(t.source),this._maybeUpdateParentList(t.target))}.bind(this))};this.jsPlumbListManager=e,e.prototype={addList:function(e,i){var s=this.instance.extend({},t);i=this.instance.extend(s,i||{});var o=[this.instance.getInstanceIndex(),this.count++].join("_");this.lists[o]=new n(this.instance,e,i,o)},removeList:function(t){var e=this.lists[t._jsPlumbList];e&&(e.destroy(),delete this.lists[t._jsPlumbList])},_maybeUpdateParentList:function(t){for(var e=t.parentNode,n=this.instance.getContainer();null!=e&&e!==n;){if(null!=e._jsPlumbList&&null!=this.lists[e._jsPlumbList])return void e._jsPlumbScrollHandler();e=e.parentNode}}};var n=function(t,e,n,i){function s(t,e,i,s){return n.anchor?n.anchor:n.deriveAnchor(t,e,i,s)}function o(t,e,i,s){return n.deriveEndpoint?n.deriveEndpoint(t,e,i,s):n.endpoint?n.endpoint:i.type}e._jsPlumbList=i;var r=function(n){for(var i=t.getSelector(e,".jtk-managed"),r=t.getId(e),a=0;a<i.length;a++){if(i[a].offsetTop<e.scrollTop)i[a]._jsPlumbProxies||(i[a]._jsPlumbProxies=i[a]._jsPlumbProxies||[],t.select({source:i[a]}).each(function(n){t.proxyConnection(n,0,e,r,function(){return o("top",0,n.endpoints[0],n)},function(){return s("top",0,n.endpoints[0],n)}),i[a]._jsPlumbProxies.push([n,0])}),t.select({target:i[a]}).each(function(n){t.proxyConnection(n,1,e,r,function(){return o("top",1,n.endpoints[1],n)},function(){return s("top",1,n.endpoints[1],n)}),i[a]._jsPlumbProxies.push([n,1])}));else if(i[a].offsetTop>e.scrollTop+e.offsetHeight)i[a]._jsPlumbProxies||(i[a]._jsPlumbProxies=i[a]._jsPlumbProxies||[],t.select({source:i[a]}).each(function(n){t.proxyConnection(n,0,e,r,function(){return o("bottom",0,n.endpoints[0],n)},function(){return s("bottom",0,n.endpoints[0],n)}),i[a]._jsPlumbProxies.push([n,0])}),t.select({target:i[a]}).each(function(n){t.proxyConnection(n,1,e,r,function(){return o("bottom",1,n.endpoints[1],n)},function(){return s("bottom",1,n.endpoints[1],n)}),i[a]._jsPlumbProxies.push([n,1])}));else if(i[a]._jsPlumbProxies){for(var l=0;l<i[a]._jsPlumbProxies.length;l++)t.unproxyConnection(i[a]._jsPlumbProxies[l][0],i[a]._jsPlumbProxies[l][1],r);delete i[a]._jsPlumbProxies}t.revalidate(i[a])}!function(e){for(var n=e.parentNode,i=t.getContainer();null!=n&&n!==i;){if(t.hasClass(n,"jtk-managed"))return void t.recalculateOffsets(n);n=n.parentNode}}(e)};t.setAttribute(e,"jtk-scrollable-list","true"),e._jsPlumbScrollHandler=r,t.on(e,"scroll",r),r(),this.destroy=function(){t.off(e,"scroll",r),delete e._jsPlumbScrollHandler;for(var n=t.getSelector(e,".jtk-managed"),i=t.getId(e),s=0;s<n.length;s++)if(n[s]._jsPlumbProxies){for(var o=0;o<n[s]._jsPlumbProxies.length;o++)t.unproxyConnection(n[s]._jsPlumbProxies[o][0],n[s]._jsPlumbProxies[o][1],i);delete n[s]._jsPlumbProxies}}}}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumbUtil,n=function(){return""+(new Date).getTime()},i=function(t){if(t._jsPlumb.paintStyle&&t._jsPlumb.hoverPaintStyle){var e={};d.extend(e,t._jsPlumb.paintStyle),d.extend(e,t._jsPlumb.hoverPaintStyle),delete t._jsPlumb.hoverPaintStyle,e.gradient&&t._jsPlumb.paintStyle.fill&&delete e.gradient,t._jsPlumb.hoverPaintStyle=e}},s=["tap","dbltap","click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","contextmenu"],o=function(t){return null==t?null:t.split(" ")},r=function(t,e,n){for(var i in e)t[i]=n},a=function(t,n,i){if(t.getDefaultType){var s=t.getTypeDescriptor(),o={},a=t.getDefaultType(),l=e.merge({},a);r(o,a,"__default");for(var u=0,c=t._jsPlumb.types.length;u<c;u++){var h=t._jsPlumb.types[u];if("__default"!==h){var d=t._jsPlumb.instance.getType(h,s);if(null!=d){var p=["anchor","anchors","connector","paintStyle","hoverPaintStyle","endpoint","endpoints","connectorOverlays","connectorStyle","connectorHoverStyle","endpointStyle","endpointHoverStyle"],f=[];"override"===d.mergeStrategy?Array.prototype.push.apply(p,["events","overlays","cssClass"]):f.push("cssClass"),l=e.merge(l,d,f,p),r(o,d,h)}}}n&&(l=e.populate(l,n,"_")),t.applyType(l,i,o),i||t.repaint()}},l=t.jsPlumbUIComponent=function(t){e.EventGenerator.apply(this,arguments);var n=arguments,i=this.idPrefix+(new Date).getTime();this._jsPlumb={instance:t._jsPlumb,parameters:t.parameters||{},paintStyle:null,hoverPaintStyle:null,paintStyleInUse:null,hover:!1,beforeDetach:t.beforeDetach,beforeDrop:t.beforeDrop,overlayPlacements:[],hoverClass:t.hoverClass||t._jsPlumb.Defaults.HoverClass,types:[],typeCache:{}},this.cacheTypeItem=function(t,e,n){this._jsPlumb.typeCache[n]=this._jsPlumb.typeCache[n]||{},this._jsPlumb.typeCache[n][t]=e},this.getCachedTypeItem=function(t,e){return this._jsPlumb.typeCache[e]?this._jsPlumb.typeCache[e][t]:null},this.getId=function(){return i};var s=t.overlays||[],o={};if(this.defaultOverlayKeys){for(var r=0;r<this.defaultOverlayKeys.length;r++)Array.prototype.push.apply(s,this._jsPlumb.instance.Defaults[this.defaultOverlayKeys[r]]||[]);for(r=0;r<s.length;r++){var a=d.convertToFullOverlaySpec(s[r]);o[a[1].id]=a}}var l={overlays:o,parameters:t.parameters||{},scope:t.scope||this._jsPlumb.instance.getDefaultScope()};if(this.getDefaultType=function(){return l},this.appendToDefaultType=function(t){for(var e in t)l[e]=t[e]},t.events)for(var u in t.events)this.bind(u,t.events[u]);this.clone=function(){var t=Object.create(this.constructor.prototype);return this.constructor.apply(t,n),t}.bind(this),this.isDetachAllowed=function(t){var n=!0;if(this._jsPlumb.beforeDetach)try{n=this._jsPlumb.beforeDetach(t)}catch(t){e.log("jsPlumb: beforeDetach callback failed",t)}return n},this.isDropAllowed=function(t,n,i,s,o,r,a){var l=this._jsPlumb.instance.checkCondition("beforeDrop",{sourceId:t,targetId:n,scope:i,connection:s,dropEndpoint:o,source:r,target:a});if(this._jsPlumb.beforeDrop)try{l=this._jsPlumb.beforeDrop({sourceId:t,targetId:n,scope:i,connection:s,dropEndpoint:o,source:r,target:a})}catch(t){e.log("jsPlumb: beforeDrop callback failed",t)}return l};var c=[];this.setListenerComponent=function(t){for(var e=0;e<c.length;e++)c[e][3]=t}},u=function(t,e){var n=t._jsPlumb.types[e],i=t._jsPlumb.instance.getType(n,t.getTypeDescriptor());null!=i&&i.cssClass&&t.canvas&&t._jsPlumb.instance.removeClass(t.canvas,i.cssClass)};e.extend(t.jsPlumbUIComponent,e.EventGenerator,{getParameter:function(t){return this._jsPlumb.parameters[t]},setParameter:function(t,e){this._jsPlumb.parameters[t]=e},getParameters:function(){return this._jsPlumb.parameters},setParameters:function(t){this._jsPlumb.parameters=t},getClass:function(){return d.getClass(this.canvas)},hasClass:function(t){return d.hasClass(this.canvas,t)},addClass:function(t){d.addClass(this.canvas,t)},removeClass:function(t){d.removeClass(this.canvas,t)},updateClasses:function(t,e){d.updateClasses(this.canvas,t,e)},setType:function(t,e,n){this.clearTypes(),this._jsPlumb.types=o(t)||[],a(this,e,n)},getType:function(){return this._jsPlumb.types},reapplyTypes:function(t,e){a(this,t,e)},hasType:function(t){return-1!==this._jsPlumb.types.indexOf(t)},addType:function(t,e,n){var i=o(t),s=!1;if(null!=i){for(var r=0,l=i.length;r<l;r++)this.hasType(i[r])||(this._jsPlumb.types.push(i[r]),s=!0);s&&a(this,e,n)}},removeType:function(t,e,n){var i=o(t),s=!1,r=function(t){var e=this._jsPlumb.types.indexOf(t);return-1!==e&&(u(this,e),this._jsPlumb.types.splice(e,1),!0)}.bind(this);if(null!=i){for(var l=0,c=i.length;l<c;l++)s=r(i[l])||s;s&&a(this,e,n)}},clearTypes:function(t,e){for(var n=this._jsPlumb.types.length,i=0;i<n;i++)u(this,0),this._jsPlumb.types.splice(0,1);a(this,t,e)},toggleType:function(t,e,n){var i=o(t);if(null!=i){for(var s=0,r=i.length;s<r;s++){var l=this._jsPlumb.types.indexOf(i[s]);-1!==l?(u(this,l),this._jsPlumb.types.splice(l,1)):this._jsPlumb.types.push(i[s])}a(this,e,n)}},applyType:function(t,e){if(this.setPaintStyle(t.paintStyle,e),this.setHoverPaintStyle(t.hoverPaintStyle,e),t.parameters)for(var n in t.parameters)this.setParameter(n,t.parameters[n]);this._jsPlumb.paintStyleInUse=this.getPaintStyle()},setPaintStyle:function(t,e){this._jsPlumb.paintStyle=t,this._jsPlumb.paintStyleInUse=this._jsPlumb.paintStyle,i(this),e||this.repaint()},getPaintStyle:function(){return this._jsPlumb.paintStyle},setHoverPaintStyle:function(t,e){this._jsPlumb.hoverPaintStyle=t,i(this),e||this.repaint()},getHoverPaintStyle:function(){return this._jsPlumb.hoverPaintStyle},destroy:function(t){(t||null==this.typeId)&&(this.cleanupListeners(),this.clone=null,this._jsPlumb=null)},isHover:function(){return this._jsPlumb.hover},setHover:function(t,e,i){if(this._jsPlumb&&!this._jsPlumb.instance.currentlyDragging&&!this._jsPlumb.instance.isHoverSuspended()){this._jsPlumb.hover=t;var s=t?"addClass":"removeClass";null!=this.canvas&&(null!=this._jsPlumb.instance.hoverClass&&this._jsPlumb.instance[s](this.canvas,this._jsPlumb.instance.hoverClass),null!=this._jsPlumb.hoverClass&&this._jsPlumb.instance[s](this.canvas,this._jsPlumb.hoverClass)),null!=this._jsPlumb.hoverPaintStyle&&(this._jsPlumb.paintStyleInUse=t?this._jsPlumb.hoverPaintStyle:this._jsPlumb.paintStyle,this._jsPlumb.instance.isSuspendDrawing()||(i=i||n(),this.repaint({timestamp:i,recalc:!1}))),this.getAttachedElements&&!e&&function(t,e,n,i){var s=t.getAttachedElements();if(s)for(var o=0,r=s.length;o<r;o++)i&&i===s[o]||s[o].setHover(e,!0,n)}(this,t,n(),this)}}});var c=0,h=t.jsPlumbInstance=function(i){this.version="2.12.6",this.Defaults={Anchor:"Bottom",Anchors:[null,null],ConnectionsDetachable:!0,ConnectionOverlays:[],Connector:"Bezier",Container:null,DoNotThrowErrors:!1,DragOptions:{},DropOptions:{},Endpoint:"Dot",EndpointOverlays:[],Endpoints:[null,null],EndpointStyle:{fill:"#456"},EndpointStyles:[null,null],EndpointHoverStyle:null,EndpointHoverStyles:[null,null],HoverPaintStyle:null,LabelStyle:{color:"black"},LogEnabled:!1,Overlays:[],MaxConnections:1,PaintStyle:{"stroke-width":4,stroke:"#456"},ReattachConnections:!1,RenderMode:"svg",Scope:"jsPlumb_DefaultScope"},i&&d.extend(this.Defaults,i),this.logEnabled=this.Defaults.LogEnabled,this._connectionTypes={},this._endpointTypes={},e.EventGenerator.apply(this);var o=this,r=function(){var t=c+1;return c++,t}(),a=o.bind,u={},h=1,p=function(t){if(null==t)return null;if(3===t.nodeType||8===t.nodeType)return{el:t,text:!0};var n=o.getElement(t);return{el:n,id:e.isString(t)&&null==n?t:R(n)}};for(var f in this.getInstanceIndex=function(){return r},this.setZoom=function(t,e){return h=t,o.fire("zoom",h),e&&o.repaintEverything(),!0},this.getZoom=function(){return h},this.Defaults)u[f]=this.Defaults[f];var g,m=[];this.unbindContainer=function(){if(null!=g&&m.length>0)for(var t=0;t<m.length;t++)o.off(g,m[t][0],m[t][1])},this.setContainer=function(t){this.unbindContainer(),t=this.getElement(t),this.select().each(function(e){e.moveParent(t)}),this.selectEndpoints().each(function(e){e.moveParent(t)});var e=g;g=t,m.length=0;for(var n={endpointclick:"endpointClick",endpointdblclick:"endpointDblClick"},i=function(t,e,i){var s=e.srcElement||e.target,r=(s&&s.parentNode?s.parentNode._jsPlumb:null)||(s?s._jsPlumb:null)||(s&&s.parentNode&&s.parentNode.parentNode?s.parentNode.parentNode._jsPlumb:null);if(r){r.fire(t,r,e);var a=i&&n[i+t]||t;o.fire(a,r.component||r,e)}},r=function(t,e,n){m.push([t,n]),o.on(g,t,e,n)},a=function(t){r(t,".jtk-connector",function(e){i(t,e)}),r(t,".jtk-endpoint",function(e){i(t,e,"endpoint")}),r(t,".jtk-overlay",function(e){i(t,e)})},l=0;l<s.length;l++)a(s[l]);for(var u in x){var c=x[u].el;c.parentNode===e&&(e.removeChild(c),g.appendChild(c))}},this.getContainer=function(){return g},this.bind=function(t,e){"ready"===t&&b?e():a.apply(o,[t,e])},o.importDefaults=function(t){for(var e in t)o.Defaults[e]=t[e];return t.Container&&o.setContainer(t.Container),o},o.restoreDefaults=function(){return o.Defaults=d.extend({},u),o};var v=null,b=!1,y=[],P={},_={},x={},C={},j={},E=!1,S=[],D=!1,w=null,I=this.Defaults.Scope,A=1,k=function(){return""+A++},M=function(t,e){g?g.appendChild(t):e?this.getElement(e).appendChild(t):this.appendToRoot(t)}.bind(this),O=function(t,e,i,s){if(!D){var r,a=R(t),l=o.getDragManager();l&&(r=l.getElementsForDraggable(a)),null==i&&(i=n());var u=nt({elId:a,offset:e,recalc:!1,timestamp:i});if(r&&u&&u.o)for(var c in r)nt({elId:r[c].id,offset:{left:u.o.left+r[c].offset.left,top:u.o.top+r[c].offset.top},recalc:!1,timestamp:i});if(o.anchorManager.redraw(a,e,i,null,s),r)for(var h in r)o.anchorManager.redraw(r[h].id,e,i,r[h].offset,s,!0)}},T=function(t){return _[t]},L=function(t,n){var i=d.extend({},t);if(n&&d.extend(i,n),i.source&&(i.source.endpoint?i.sourceEndpoint=i.source:i.source=o.getElement(i.source)),i.target&&(i.target.endpoint?i.targetEndpoint=i.target:i.target=o.getElement(i.target)),t.uuids&&(i.sourceEndpoint=T(t.uuids[0]),i.targetEndpoint=T(t.uuids[1])),i.sourceEndpoint&&i.sourceEndpoint.isFull())e.log(o,"could not add connection; source endpoint is full");else if(i.targetEndpoint&&i.targetEndpoint.isFull())e.log(o,"could not add connection; target endpoint is full");else{if(!i.type&&i.sourceEndpoint&&(i.type=i.sourceEndpoint.connectionType),i.sourceEndpoint&&i.sourceEndpoint.connectorOverlays){i.overlays=i.overlays||[];for(var s=0,r=i.sourceEndpoint.connectorOverlays.length;s<r;s++)i.overlays.push(i.sourceEndpoint.connectorOverlays[s])}i.sourceEndpoint&&i.sourceEndpoint.scope&&(i.scope=i.sourceEndpoint.scope),!i["pointer-events"]&&i.sourceEndpoint&&i.sourceEndpoint.connectorPointerEvents&&(i["pointer-events"]=i.sourceEndpoint.connectorPointerEvents);var a=function(t,e,n){var s=function(t,e){var n=d.extend({},t);for(var i in e)e[i]&&(n[i]=e[i]);return n}(e,{anchor:i.anchors?i.anchors[n]:i.anchor,endpoint:i.endpoints?i.endpoints[n]:i.endpoint,paintStyle:i.endpointStyles?i.endpointStyles[n]:i.endpointStyle,hoverPaintStyle:i.endpointHoverStyles?i.endpointHoverStyles[n]:i.endpointHoverStyle});return o.addEndpoint(t,s)},l=function(t,e,n,s){if(i[t]&&!i[t].endpoint&&!i[t+"Endpoint"]&&!i.newConnection){var o=n[R(i[t])];if(o=o?o[s]:null){if(!o.enabled)return!1;var r=d.extend({},o.def);delete r.label;var l=null!=o.endpoint&&o.endpoint._jsPlumb?o.endpoint:a(i[t],r,e);if(l.isFull())return!1;i[t+"Endpoint"]=l,!i.scope&&r.scope&&(i.scope=r.scope),o.uniqueEndpoint?o.endpoint?l.finalEndpoint=o.endpoint:(o.endpoint=l,l.setDeleteOnEmpty(!1)):l.setDeleteOnEmpty(!0),0===e&&o.def.connectorOverlays&&(i.overlays=i.overlays||[],Array.prototype.push.apply(i.overlays,o.def.connectorOverlays))}}};if(!1!==l("source",0,this.sourceEndpointDefinitions,i.type||"default")&&!1!==l("target",1,this.targetEndpointDefinitions,i.type||"default"))return i.sourceEndpoint&&i.targetEndpoint&&(function(t,e){for(var n=t.scope.split(/\s/),i=e.scope.split(/\s/),s=0;s<n.length;s++)for(var o=0;o<i.length;o++)if(i[o]===n[s])return!0;return!1}(i.sourceEndpoint,i.targetEndpoint)||(i=null)),i}}.bind(o),F=function(t){var e=o.Defaults.ConnectionType||o.getDefaultConnectionType();t._jsPlumb=o,t.newConnection=F,t.newEndpoint=N,t.endpointsByUUID=_,t.endpointsByElement=P,t.finaliseConnection=G,t.id="con_"+k();var n=new e(t);return n.isDetachable()&&(n.endpoints[0].initDraggable("_jsPlumbSource"),n.endpoints[1].initDraggable("_jsPlumbTarget")),n},G=o.finaliseConnection=function(t,e,n,i){if(e=e||{},t.suspendedEndpoint||y.push(t),t.pending=null,t.endpoints[0].isTemporarySource=!1,!1!==i&&o.anchorManager.newConnection(t),O(t.source),!e.doNotFireConnectionEvent&&!1!==e.fireEvent){var s={connection:t,source:t.source,target:t.target,sourceId:t.sourceId,targetId:t.targetId,sourceEndpoint:t.endpoints[0],targetEndpoint:t.endpoints[1]};o.fire("connection",s,n)}},N=function(t,e){var n=o.Defaults.EndpointType||d.Endpoint,i=d.extend({},t);i._jsPlumb=o,i.newConnection=F,i.newEndpoint=N,i.endpointsByUUID=_,i.endpointsByElement=P,i.fireDetachEvent=Y,i.elementId=e||R(i.source);var s=new n(i);return s.id="ep_"+k(),et(i.elementId,i.source),d.headless||o.getDragManager().endpointAdded(i.source,e),s},U=function(t,e,n){var i=P[t];if(i&&i.length)for(var s=0,o=i.length;s<o;s++){for(var r=0,a=i[s].connections.length;r<a;r++){if(e(i[s].connections[r]))return}n&&n(i[s])}},B=function(t,e,n){e="block"===e;var i=null;n&&(i=function(t){t.setVisible(e,!0,!0)});var s=p(t);U(s.id,function(t){if(e&&n){var i=t.sourceId===s.id?1:0;t.endpoints[i].isVisible()&&t.setVisible(!0)}else t.setVisible(e)},i)},R=function(t,n,i){if(e.isString(t))return t;if(null==t)return null;var s=o.getAttribute(t,"id");return s&&"undefined"!==s||(2===arguments.length&&void 0!==arguments[1]?s=n:(1===arguments.length||3===arguments.length&&!arguments[2])&&(s="jsPlumb_"+r+"_"+k()),i||o.setAttribute(t,"id",s)),s};this.setConnectionBeingDragged=function(t){E=t},this.isConnectionBeingDragged=function(){return E},this.getManagedElements=function(){return x},this.connectorClass="jtk-connector",this.connectorOutlineClass="jtk-connector-outline",this.connectedClass="jtk-connected",this.hoverClass="jtk-hover",this.endpointClass="jtk-endpoint",this.endpointConnectedClass="jtk-endpoint-connected",this.endpointFullClass="jtk-endpoint-full",this.endpointDropAllowedClass="jtk-endpoint-drop-allowed",this.endpointDropForbiddenClass="jtk-endpoint-drop-forbidden",this.overlayClass="jtk-overlay",this.draggingClass="jtk-dragging",this.elementDraggingClass="jtk-element-dragging",this.sourceElementDraggingClass="jtk-source-element-dragging",this.targetElementDraggingClass="jtk-target-element-dragging",this.endpointAnchorClassPrefix="jtk-endpoint-anchor",this.hoverSourceClass="jtk-source-hover",this.hoverTargetClass="jtk-target-hover",this.dragSelectClass="jtk-drag-select",this.Anchors={},this.Connectors={svg:{}},this.Endpoints={svg:{}},this.Overlays={svg:{}},this.ConnectorRenderers={},this.SVG="svg",this.addEndpoint=function(t,n,i){i=i||{};var s=d.extend({},i);d.extend(s,n),s.endpoint=s.endpoint||o.Defaults.Endpoint,s.paintStyle=s.paintStyle||o.Defaults.EndpointStyle;for(var r=[],a=e.isArray(t)||null!=t.length&&!e.isString(t)?t:[t],l=0,u=a.length;l<u;l++){s.source=o.getElement(a[l]),tt(s.source);var c=R(s.source),h=N(s,c),p=et(c,s.source).info.o;e.addToList(P,c,h),D||h.paint({anchorLoc:h.anchor.compute({xy:[p.left,p.top],wh:S[c],element:h,timestamp:w}),timestamp:w}),r.push(h)}return 1===r.length?r[0]:r},this.addEndpoints=function(t,n,i){for(var s=[],r=0,a=n.length;r<a;r++){var l=o.addEndpoint(t,n[r],i);e.isArray(l)?Array.prototype.push.apply(s,l):s.push(l)}return s},this.animate=function(t,n,i){if(!this.animationSupported)return!1;i=i||{};var s=o.getElement(t),r=R(s),a=d.animEvents.step,l=d.animEvents.complete;i[a]=e.wrap(i[a],function(){o.revalidate(r)}),i[l]=e.wrap(i[l],function(){o.revalidate(r)}),o.doAnimate(s,n,i)},this.checkCondition=function(t,n){var i=o.getListener(t),s=!0;if(i&&i.length>0){var r=Array.prototype.slice.call(arguments,1);try{for(var a=0,l=i.length;a<l;a++)s=s&&i[a].apply(i[a],r)}catch(n){e.log(o,"cannot check condition ["+t+"]"+n)}}return s},this.connect=function(t,n){var i,s=L(t,n);if(s){if(null==s.source&&null==s.sourceEndpoint)return void e.log("Cannot establish connection - source does not exist");if(null==s.target&&null==s.targetEndpoint)return void e.log("Cannot establish connection - target does not exist");tt(s.source),i=F(s),G(i,s)}return i};var H=[{el:"source",elId:"sourceId",epDefs:"sourceEndpointDefinitions"},{el:"target",elId:"targetId",epDefs:"targetEndpointDefinitions"}],X=function(t,e,n,i){var s,o,r,a=H[n],l=t[a.elId],u=(t[a.el],t.endpoints[n]),c={index:n,originalSourceId:0===n?l:t.sourceId,newSourceId:t.sourceId,originalTargetId:1===n?l:t.targetId,newTargetId:t.targetId,connection:t};if(e.constructor===d.Endpoint)(s=e).addConnection(t),e=s.element;else if(o=R(e),r=this[a.epDefs][o],o===t[a.elId])s=null;else if(r)for(var h in r){if(!r[h].enabled)return;s=null!=r[h].endpoint&&r[h].endpoint._jsPlumb?r[h].endpoint:this.addEndpoint(e,r[h].def),r[h].uniqueEndpoint&&(r[h].endpoint=s),s.addConnection(t)}else s=t.makeEndpoint(0===n,e,o);return null!=s&&(u.detachFromConnection(t),t.endpoints[n]=s,t[a.el]=s.element,t[a.elId]=s.elementId,c[0===n?"newSourceId":"newTargetId"]=s.elementId,W(c),i||t.repaint()),c.element=e,c}.bind(this);this.setSource=function(t,e,n){var i=X(t,e,0,n);this.anchorManager.sourceChanged(i.originalSourceId,i.newSourceId,t,i.el)},this.setTarget=function(t,e,n){var i=X(t,e,1,n);this.anchorManager.updateOtherEndpoint(i.originalSourceId,i.originalTargetId,i.newTargetId,t)},this.deleteEndpoint=function(t,e,n){var i="string"==typeof t?_[t]:t;return i&&o.deleteObject({endpoint:i,dontUpdateHover:e,deleteAttachedObjects:n}),o},this.deleteEveryEndpoint=function(){var t=o.setSuspendDrawing(!0);for(var e in P){var n=P[e];if(n&&n.length)for(var i=0,s=n.length;i<s;i++)o.deleteEndpoint(n[i],!0)}P={},x={},_={},C={},j={},o.anchorManager.reset();var r=o.getDragManager();return r&&r.reset(),t||o.setSuspendDrawing(!1),o};var Y=function(t,e,n){var i=o.Defaults.ConnectionType||o.getDefaultConnectionType(),s=t.constructor===i?{connection:t,source:t.source,target:t.target,sourceId:t.sourceId,targetId:t.targetId,sourceEndpoint:t.endpoints[0],targetEndpoint:t.endpoints[1]}:t;e&&o.fire("connectionDetached",s,n),o.fire("internal.connectionDetached",s,n),o.anchorManager.connectionDetached(s)},W=o.fireMoveEvent=function(t,e){o.fire("connectionMoved",t,e)};this.unregisterEndpoint=function(t){for(var e in t._jsPlumb.uuid&&(_[t._jsPlumb.uuid]=null),o.anchorManager.deleteEndpoint(t),P){var n=P[e];if(n){for(var i=[],s=0,r=n.length;s<r;s++)n[s]!==t&&i.push(n[s]);P[e]=i}P[e].length<1&&delete P[e]}};this.deleteConnection=function(t,n){return!(null==t||!(n=n||{}).force&&!e.functionChain(!0,!1,[[t.endpoints[0],"isDetachAllowed",[t]],[t.endpoints[1],"isDetachAllowed",[t]],[t,"isDetachAllowed",[t]],[o,"checkCondition",["beforeDetach",t]]]))&&(t.setHover(!1),Y(t,!t.pending&&!1!==n.fireEvent,n.originalEvent),t.endpoints[0].detachFromConnection(t),t.endpoints[1].detachFromConnection(t),e.removeWithFunction(y,function(e){return t.id===e.id}),t.cleanup(),t.destroy(),!0)},this.deleteEveryConnection=function(t){t=t||{};var e=y.length,n=0;return o.batch(function(){for(var i=0;i<e;i++)n+=o.deleteConnection(y[0],t)?1:0}),n},this.deleteConnectionsForElement=function(t,e){e=e||{},t=o.getElement(t);var n=R(t),i=P[n];if(i&&i.length)for(var s=0,r=i.length;s<r;s++)i[s].deleteEveryConnection(e);return o},this.deleteObject=function(t){var n={endpoints:{},connections:{},endpointCount:0,connectionCount:0},i=!1!==t.deleteAttachedObjects,s=function(e){null!=e&&null==n.connections[e.id]&&(t.dontUpdateHover||null==e._jsPlumb||e.setHover(!1),n.connections[e.id]=e,n.connectionCount++)};for(var r in t.connection?s(t.connection):function(e){if(null!=e&&null==n.endpoints[e.id]&&(t.dontUpdateHover||null==e._jsPlumb||e.setHover(!1),n.endpoints[e.id]=e,n.endpointCount++,i))for(var o=0;o<e.connections.length;o++){var r=e.connections[o];s(r)}}(t.endpoint),n.connections){var a=n.connections[r];if(a._jsPlumb){e.removeWithFunction(y,function(t){return a.id===t.id}),Y(a,!1!==t.fireEvent&&!a.pending,t.originalEvent);var l=null==t.deleteAttachedObjects?null:!t.deleteAttachedObjects;a.endpoints[0].detachFromConnection(a,null,l),a.endpoints[1].detachFromConnection(a,null,l),a.cleanup(!0),a.destroy(!0)}}for(var u in n.endpoints){var c=n.endpoints[u];c._jsPlumb&&(o.unregisterEndpoint(c),c.cleanup(!0),c.destroy(!0))}return n};var z=function(t,e,n){return function(){return function(t,e,n,i){for(var s=0,o=t.length;s<o;s++)t[s][e].apply(t[s],n);return i(t)}(t,e,arguments,n)}},V=function(t,e){return function(){return function(t,e,n){for(var i=[],s=0,o=t.length;s<o;s++)i.push([t[s][e].apply(t[s],n),t[s]]);return i}(t,e,arguments)}},q=function(t,e){var n=[];if(t)if("string"==typeof t){if("*"===t)return t;n.push(t)}else if(e)n=t;else if(t.length)for(var i=0,s=t.length;i<s;i++)n.push(p(t[i]).id);else n.push(p(t).id);return n},J=function(t,e,n){return"*"===t||(t.length>0?-1!==t.indexOf(e):!n)};this.getConnections=function(t,e){t?t.constructor===String&&(t={scope:t}):t={};for(var n=t.scope||o.getDefaultScope(),i=q(n,!0),s=q(t.source),r=q(t.target),a=!e&&i.length>1?{}:[],l=function(t,n){if(!e&&i.length>1){var s=a[t];null==s&&(s=a[t]=[]),s.push(n)}else a.push(n)},u=0,c=y.length;u<c;u++){var h=y[u],d=h.proxies&&h.proxies[0]?h.proxies[0].originalEp.elementId:h.sourceId,p=h.proxies&&h.proxies[1]?h.proxies[1].originalEp.elementId:h.targetId;J(i,h.scope)&&J(s,d)&&J(r,p)&&l(h.scope,h)}return a};var Z=function(t,e){var n,i,s={length:t.length,each:function(t,e){return function(n){for(var i=0,s=t.length;i<s;i++)n(t[i]);return e(t)}}(t,e),get:function(t){return function(e){return t[e]}}(t)},o=["setHover","removeAllOverlays","setLabel","addClass","addOverlay","removeOverlay","removeOverlays","showOverlay","hideOverlay","showOverlays","hideOverlays","setPaintStyle","setHoverPaintStyle","setSuspendEvents","setParameter","setParameters","setVisible","repaint","addType","toggleType","removeType","removeClass","setType","bind","unbind"],r=["getLabel","getOverlay","isHover","getParameter","getParameters","getPaintStyle","getHoverPaintStyle","isVisible","hasType","getType","isSuspendEvents"];for(n=0,i=o.length;n<i;n++)s[o[n]]=z(t,o[n],e);for(n=0,i=r.length;n<i;n++)s[r[n]]=V(t,r[n]);return s},K=function(t){var e=Z(t,K);return d.extend(e,{setDetachable:z(t,"setDetachable",K),setReattach:z(t,"setReattach",K),setConnector:z(t,"setConnector",K),delete:function(){for(var e=0,n=t.length;e<n;e++)o.deleteConnection(t[e])},isDetachable:V(t,"isDetachable"),isReattach:V(t,"isReattach")})},$=function(t){var e=Z(t,$);return d.extend(e,{setEnabled:z(t,"setEnabled",$),setAnchor:z(t,"setAnchor",$),isEnabled:V(t,"isEnabled"),deleteEveryConnection:function(){for(var e=0,n=t.length;e<n;e++)t[e].deleteEveryConnection()},delete:function(){for(var e=0,n=t.length;e<n;e++)o.deleteEndpoint(t[e])}})};this.select=function(t){return(t=t||{}).scope=t.scope||"*",K(t.connections||o.getConnections(t,!0))},this.selectEndpoints=function(t){(t=t||{}).scope=t.scope||"*";var e=!t.element&&!t.source&&!t.target,n=e?"*":q(t.element),i=e?"*":q(t.source),s=e?"*":q(t.target),o=q(t.scope,!0),r=[];for(var a in P){var l=J(n,a,!0),u=J(i,a,!0),c="*"!==i,h=J(s,a,!0),d="*"!==s;if(l||u||h)t:for(var p=0,f=P[a].length;p<f;p++){var g=P[a][p];if(J(o,g.scope,!0)){var m=c&&i.length>0&&!g.isSource,v=d&&s.length>0&&!g.isTarget;if(m||v)continue t;r.push(g)}}}return $(r)},this.getAllConnections=function(){return y},this.getDefaultScope=function(){return I},this.getEndpoint=T,this.getEndpoints=function(t){return P[p(t).id]||[]},this.getDefaultEndpointType=function(){return d.Endpoint},this.getDefaultConnectionType=function(){return d.Connection},this.getId=R,this.draw=O,this.info=p,this.appendElement=M;var Q=!1;this.isHoverSuspended=function(){return Q},this.setHoverSuspended=function(t){Q=t},this.hide=function(t,e){return B(t,"none",e),o},this.idstamp=k;var tt=function(t){if(!g&&t){var e=o.getElement(t);e.offsetParent&&o.setContainer(e.offsetParent)}},et=o.manage=function(t,e,n){return x[t]||(x[t]={el:e,endpoints:[],connections:[]},x[t].info=nt({elId:t,timestamp:w}),o.addClass(e,"jtk-managed"),n||o.fire("manageElement",{id:t,info:x[t].info,el:e})),x[t]},nt=(o.unmanage=function(t){if(x[t]){var e=x[t].el;o.removeClass(e,"jtk-managed"),delete x[t],o.fire("unmanageElement",{id:t,el:e})}},function(t){var e,n=t.timestamp,i=t.recalc,s=t.offset,r=t.elId;return D&&!n&&(n=w),!i&&n&&n===j[r]?{o:t.offset||C[r],s:S[r]}:(i||!s&&null==C[r]?null!=(e=x[r]?x[r].el:null)&&(S[r]=o.getSize(e),C[r]=o.getOffset(e),j[r]=n):(C[r]=s||C[r],null==S[r]&&null!=(e=x[r].el)&&(S[r]=o.getSize(e)),j[r]=n),C[r]&&!C[r].right&&(C[r].right=C[r].left+S[r][0],C[r].bottom=C[r].top+S[r][1],C[r].width=S[r][0],C[r].height=S[r][1],C[r].centerx=C[r].left+C[r].width/2,C[r].centery=C[r].top+C[r].height/2),{o:C[r],s:S[r]})});this.updateOffset=nt,this.init=function(){b||(o.Defaults.Container&&o.setContainer(o.Defaults.Container),o.anchorManager=new t.jsPlumb.AnchorManager({jsPlumbInstance:o}),b=!0,o.fire("ready",o))}.bind(this),this.log=v,this.jsPlumbUIComponent=l,this.makeAnchor=function(){var n,i=function(e,n){if(t.jsPlumb.Anchors[e])return new t.jsPlumb.Anchors[e](n);if(!o.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown anchor type '"+e+"'"}};if(0===arguments.length)return null;var s=arguments[0],r=arguments[1],a=(arguments[2],null);if(s.compute&&s.getOrientation)return s;if("string"==typeof s)a=i(arguments[0],{elementId:r,jsPlumbInstance:o});else if(e.isArray(s))if(e.isArray(s[0])||e.isString(s[0]))2===s.length&&e.isObject(s[1])?e.isString(s[0])?(n=t.jsPlumb.extend({elementId:r,jsPlumbInstance:o},s[1]),a=i(s[0],n)):(n=t.jsPlumb.extend({elementId:r,jsPlumbInstance:o,anchors:s[0]},s[1]),a=new t.jsPlumb.DynamicAnchor(n)):a=new d.DynamicAnchor({anchors:s,selector:null,elementId:r,jsPlumbInstance:o});else{var l={x:s[0],y:s[1],orientation:s.length>=4?[s[2],s[3]]:[0,0],offsets:s.length>=6?[s[4],s[5]]:[0,0],elementId:r,jsPlumbInstance:o,cssClass:7===s.length?s[6]:null};(a=new t.jsPlumb.Anchor(l)).clone=function(){return new t.jsPlumb.Anchor(l)}}return a.id||(a.id="anchor_"+k()),a},this.makeAnchors=function(n,i,s){for(var r=[],a=0,l=n.length;a<l;a++)"string"==typeof n[a]?r.push(t.jsPlumb.Anchors[n[a]]({elementId:i,jsPlumbInstance:s})):e.isArray(n[a])&&r.push(o.makeAnchor(n[a],i,s));return r},this.makeDynamicAnchor=function(e,n){return new t.jsPlumb.DynamicAnchor({anchors:e,selector:n,elementId:null,jsPlumbInstance:o})},this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={};var it=function(n,i,s,r,a){var u=new l(i),c=i._jsPlumb.EndpointDropHandler({jsPlumb:o,enabled:function(){return n.def.enabled},isFull:function(){var t=o.select({target:n.id}).length;return n.def.maxConnections>0&&t>=n.def.maxConnections},element:n.el,elementId:n.id,isSource:r,isTarget:a,addClass:function(t){o.addClass(n.el,t)},removeClass:function(t){o.removeClass(n.el,t)},onDrop:function(t){t.endpoints[0].anchor.unlock()},isDropAllowed:function(){return u.isDropAllowed.apply(u,arguments)},isRedrop:function(t){return null!=t.suspendedElement&&null!=t.suspendedEndpoint&&t.suspendedEndpoint.element===n.el},getEndpoint:function(e){var s=n.def.endpoint;if(null==s||null==s._jsPlumb){var r=o.deriveEndpointAndAnchorSpec(e.getType().join(" "),!0),a=r.endpoints?t.jsPlumb.extend(i,{endpoint:n.def.def.endpoint||r.endpoints[1]}):i;r.anchors&&(a=t.jsPlumb.extend(a,{anchor:n.def.def.anchor||r.anchors[1]})),(s=o.addEndpoint(n.el,a))._mtNew=!0}if(i.uniqueEndpoint&&(n.def.endpoint=s),s.setDeleteOnEmpty(!0),e.isDetachable()&&s.initDraggable(),null!=s.anchor.positionFinder){var l=o.getUIPosition(arguments,o.getZoom()),u=o.getOffset(n.el),c=o.getSize(n.el),h=null==l?[0,0]:s.anchor.positionFinder(l,u,c,s.anchor.constructorParams);s.anchor.x=h[0],s.anchor.y=h[1]}return s},maybeCleanup:function(t){t._mtNew&&0===t.connections.length?o.deleteObject({endpoint:t}):delete t._mtNew}}),h=t.jsPlumb.dragEvents.drop;return s.scope=s.scope||i.scope||o.Defaults.Scope,s[h]=e.wrap(s[h],c,!0),s.rank=i.rank||0,a&&(s[t.jsPlumb.dragEvents.over]=function(){return!0}),!1===i.allowLoopback&&(s.canDrop=function(t){return t.getDragElement()._jsPlumbRelatedElement!==n.el}),o.initDroppable(n.el,s,"internal"),c};this.makeTarget=function(e,n,i){var s=t.jsPlumb.extend({_jsPlumb:this},i);t.jsPlumb.extend(s,n);for(var r=s.maxConnections||-1,a=function(e){var n=p(e),i=n.id,a=t.jsPlumb.extend({},s.dropOptions||{}),l=s.connectionType||"default";this.targetEndpointDefinitions[i]=this.targetEndpointDefinitions[i]||{},tt(i),n.el._isJsPlumbGroup&&null==a.rank&&(a.rank=-1);var u={def:t.jsPlumb.extend({},s),uniqueEndpoint:s.uniqueEndpoint,maxConnections:r,enabled:!0};s.createEndpoint&&(u.uniqueEndpoint=!0,u.endpoint=o.addEndpoint(e,u.def),u.endpoint.setDeleteOnEmpty(!1)),n.def=u,this.targetEndpointDefinitions[i][l]=u,it(n,s,a,!0===s.isSource,!0),n.el._katavorioDrop[n.el._katavorioDrop.length-1].targetDef=u}.bind(this),l=e.length&&e.constructor!==String?e:[e],u=0,c=l.length;u<c;u++)a(l[u]);return this},this.unmakeTarget=function(t,e){var n=p(t);return o.destroyDroppable(n.el,"internal"),e||delete this.targetEndpointDefinitions[n.id],this},this.makeSource=function(n,i,s){var r=t.jsPlumb.extend({_jsPlumb:this},s);t.jsPlumb.extend(r,i);var a=r.connectionType||"default",l=o.deriveEndpointAndAnchorSpec(a);r.endpoint=r.endpoint||l.endpoints[0],r.anchor=r.anchor||l.anchors[0];for(var u=r.maxConnections||-1,c=r.onMaxConnections,d=function(i){var s=i.id,l=this.getElement(i.el);this.sourceEndpointDefinitions[s]=this.sourceEndpointDefinitions[s]||{},tt(s);var d={def:t.jsPlumb.extend({},r),uniqueEndpoint:r.uniqueEndpoint,maxConnections:u,enabled:!0};r.createEndpoint&&(d.uniqueEndpoint=!0,d.endpoint=o.addEndpoint(n,d.def),d.endpoint.setDeleteOnEmpty(!1)),this.sourceEndpointDefinitions[s][a]=d,i.def=d;var p=t.jsPlumb.dragEvents.stop,f=t.jsPlumb.dragEvents.drag,g=t.jsPlumb.extend({},r.dragOptions||{}),m=g.drag,v=g.stop,b=null,y=!1;g.scope=g.scope||r.scope,g[f]=e.wrap(g[f],function(){m&&m.apply(this,arguments),y=!1}),g[p]=e.wrap(g[p],function(){if(v&&v.apply(this,arguments),this.currentlyDragging=!1,null!=b._jsPlumb){var t=r.anchor||this.Defaults.Anchor,e=b.anchor,n=b.connections[0],i=this.makeAnchor(t,s,this),a=b.element;if(null!=i.positionFinder){var l=o.getOffset(a),u=this.getSize(a),c={left:l.left+e.x*u[0],top:l.top+e.y*u[1]},h=i.positionFinder(c,l,u,i.constructorParams);i.x=h[0],i.y=h[1]}b.setAnchor(i,!0),b.repaint(),this.repaint(b.elementId),null!=n&&this.repaint(n.targetId)}}.bind(this));var P=function(n){if(3!==n.which&&2!==n.button){var d=this.sourceEndpointDefinitions[s][a];if(d.enabled){if(s=this.getId(this.getElement(i.el)),r.filter)if(!1===(e.isString(r.filter)?function(t,e,n,i,s){for(var o=t.target||t.srcElement,r=!1,a=i.getSelector(e,n),l=0;l<a.length;l++)if(a[l]===o){r=!0;break}return s?!r:r}(n,i.el,r.filter,this,r.filterExclude):r.filter(n,i.el)))return;var p=this.select({source:s}).length;if(d.maxConnections>=0&&p>=d.maxConnections)return c&&c({element:i.el,maxConnections:u},n),!1;var f=t.jsPlumb.getPositionOnElement(n,l,h),m={};t.jsPlumb.extend(m,d.def),m.isTemporarySource=!0,m.anchor=[f[0],f[1],0,0],m.dragOptions=g,d.def.scope&&(m.scope=d.def.scope),b=this.addEndpoint(s,m),y=!0,b.setDeleteOnEmpty(!0),d.uniqueEndpoint&&(d.endpoint?b.finalEndpoint=d.endpoint:(d.endpoint=b,b.setDeleteOnEmpty(!1)));var v=function(){o.off(b.canvas,"mouseup",v),o.off(i.el,"mouseup",v),y&&(y=!1,o.deleteEndpoint(b))};o.on(b.canvas,"mouseup",v),o.on(i.el,"mouseup",v);var P={};if(d.def.extract)for(var _ in d.def.extract){var x=(n.srcElement||n.target).getAttribute(_);x&&(P[d.def.extract[_]]=x)}o.trigger(b.canvas,"mousedown",n,P),e.consume(n)}}}.bind(this);this.on(i.el,"mousedown",P),d.trigger=P,r.filter&&(e.isString(r.filter)||e.isFunction(r.filter))&&o.setDragFilter(i.el,r.filter);var _=t.jsPlumb.extend({},r.dropOptions||{});it(i,r,_,!0,!0===r.isTarget)}.bind(this),f=n.length&&n.constructor!==String?n:[n],g=0,m=f.length;g<m;g++)d(p(f[g]));return this},this.unmakeSource=function(t,e,n){var i=p(t);o.destroyDroppable(i.el,"internal");var s=this.sourceEndpointDefinitions[i.id];if(s)for(var r in s)if(null==e||e===r){var a=s[r].trigger;a&&o.off(i.el,"mousedown",a),n||delete this.sourceEndpointDefinitions[i.id][r]}return this},this.unmakeEverySource=function(){for(var t in this.sourceEndpointDefinitions)o.unmakeSource(t,null,!0);return this.sourceEndpointDefinitions={},this};var st=function(t,n,i){n=e.isArray(n)?n:[n];var s=R(t);i=i||"default";for(var o=0;o<n.length;o++){var r=this[n[o]][s];if(r&&r[i])return r[i].def.scope||this.Defaults.Scope}}.bind(this),ot=function(t,n,i,s){i=e.isArray(i)?i:[i];var o=R(t);s=s||"default";for(var r=0;r<i.length;r++){var a=this[i[r]][o];a&&a[s]&&(a[s].def.scope=n)}}.bind(this);this.getScope=function(t,e){return st(t,["sourceEndpointDefinitions","targetEndpointDefinitions"])},this.getSourceScope=function(t){return st(t,"sourceEndpointDefinitions")},this.getTargetScope=function(t){return st(t,"targetEndpointDefinitions")},this.setScope=function(t,e,n){this.setSourceScope(t,e,n),this.setTargetScope(t,e,n)},this.setSourceScope=function(t,e,n){ot(t,e,"sourceEndpointDefinitions",n),this.setDragScope(t,e)},this.setTargetScope=function(t,e,n){ot(t,e,"targetEndpointDefinitions",n),this.setDropScope(t,e)},this.unmakeEveryTarget=function(){for(var t in this.targetEndpointDefinitions)o.unmakeTarget(t,!0);return this.targetEndpointDefinitions={},this};var rt=function(t,n,i,s,r){var a,l,u,c="source"===t?this.sourceEndpointDefinitions:this.targetEndpointDefinitions;if(r=r||"default",n.length&&!e.isString(n)){a=[];for(var h=0,d=n.length;h<d;h++)c[(l=p(n[h])).id]&&c[l.id][r]&&(a[h]=c[l.id][r].enabled,u=s?!a[h]:i,c[l.id][r].enabled=u,o[u?"removeClass":"addClass"](l.el,"jtk-"+t+"-disabled"))}else{var f=(l=p(n)).id;c[f]&&c[f][r]&&(a=c[f][r].enabled,u=s?!a:i,c[f][r].enabled=u,o[u?"removeClass":"addClass"](l.el,"jtk-"+t+"-disabled"))}return a}.bind(this),at=function(t,n){return e.isString(t)||!t.length?n.apply(this,[t]):t.length?n.apply(this,[t[0]]):void 0}.bind(this);this.toggleSourceEnabled=function(t,e){return rt("source",t,null,!0,e),this.isSourceEnabled(t,e)},this.setSourceEnabled=function(t,e,n){return rt("source",t,e,null,n)},this.isSource=function(t,e){return e=e||"default",at(t,function(t){var n=this.sourceEndpointDefinitions[p(t).id];return null!=n&&null!=n[e]}.bind(this))},this.isSourceEnabled=function(t,e){return e=e||"default",at(t,function(t){var n=this.sourceEndpointDefinitions[p(t).id];return n&&n[e]&&!0===n[e].enabled}.bind(this))},this.toggleTargetEnabled=function(t,e){return rt("target",t,null,!0,e),this.isTargetEnabled(t,e)},this.isTarget=function(t,e){return e=e||"default",at(t,function(t){var n=this.targetEndpointDefinitions[p(t).id];return null!=n&&null!=n[e]}.bind(this))},this.isTargetEnabled=function(t,e){return e=e||"default",at(t,function(t){var n=this.targetEndpointDefinitions[p(t).id];return n&&n[e]&&!0===n[e].enabled}.bind(this))},this.setTargetEnabled=function(t,e,n){return rt("target",t,e,null,n)},this.ready=function(t){o.bind("ready",t)};var lt=function(t,e){if("object"==typeof t&&t.length)for(var n=0,i=t.length;n<i;n++)e(t[n]);else e(t);return o};this.repaint=function(t,e,n){return lt(t,function(t){O(t,e,n)})},this.revalidate=function(t,e,n){return lt(t,function(t){var i=n?t:o.getId(t);o.updateOffset({elId:i,recalc:!0,timestamp:e});var s=o.getDragManager();s&&s.updateOffsets(i),o.repaint(t)})},this.repaintEverything=function(){var t,e=n();for(t in P)o.updateOffset({elId:t,recalc:!0,timestamp:e});for(t in P)O(t,null,e);return this},this.removeAllEndpoints=function(t,e,n){n=n||[];var i=function(t){var s,r,a=p(t),l=P[a.id];if(l)for(n.push(a),s=0,r=l.length;s<r;s++)o.deleteEndpoint(l[s],!1);if(delete P[a.id],e&&a.el&&3!==a.el.nodeType&&8!==a.el.nodeType)for(s=0,r=a.el.childNodes.length;s<r;s++)i(a.el.childNodes[s])};return i(t),this};var ut=function(t,e){o.removeAllEndpoints(t.id,!0,e);for(var n=o.getDragManager(),i=function(t){n&&n.elementRemoved(t.id),o.anchorManager.clearFor(t.id),o.anchorManager.removeFloatingConnection(t.id),o.isSource(t.el)&&o.unmakeSource(t.el),o.isTarget(t.el)&&o.unmakeTarget(t.el),o.destroyDraggable(t.el),o.destroyDroppable(t.el),delete o.floatingConnections[t.id],delete x[t.id],delete C[t.id],t.el&&(o.removeElement(t.el),t.el._jsPlumb=null)},s=1;s<e.length;s++)i(e[s]);i(t)};this.remove=function(t,e){var n=p(t),i=[];return n.text&&n.el.parentNode?n.el.parentNode.removeChild(n.el):n.id&&o.batch(function(){ut(n,i)},!0===e),o},this.empty=function(t,e){var n=[],i=function(t,e){var s=p(t);if(s.text)s.el.parentNode.removeChild(s.el);else if(s.el){for(;s.el.childNodes.length>0;)i(s.el.childNodes[0]);e||ut(s,n)}};return o.batch(function(){i(t,!0)},!1===e),o},this.reset=function(t){o.silently(function(){Q=!1,o.removeAllGroups(),o.removeGroupManager(),o.deleteEveryEndpoint(),t||o.unbind(),this.targetEndpointDefinitions={},this.sourceEndpointDefinitions={},y.length=0,this.doReset&&this.doReset()}.bind(this))};var ct=function(t){t.canvas&&t.canvas.parentNode&&t.canvas.parentNode.removeChild(t.canvas),t.cleanup(),t.destroy()};this.clear=function(){o.select().each(ct),o.selectEndpoints().each(ct),P={},_={}},this.setDefaultScope=function(t){return I=t,o},this.deriveEndpointAndAnchorSpec=function(t,e){for(var n=((e?"":"default ")+t).split(/[\s]/),i=null,s=null,r=null,a=null,l=0;l<n.length;l++){var u=o.getType(n[l],"connection");u&&(u.endpoints&&(i=u.endpoints),u.endpoint&&(s=u.endpoint),u.anchors&&(a=u.anchors),u.anchor&&(r=u.anchor))}return{endpoints:i||[s,s],anchors:a||[r,r]}},this.setId=function(t,n,i){var s;e.isString(t)?s=t:(t=this.getElement(t),s=this.getId(t));var o=this.getConnections({source:s,scope:"*"},!0),r=this.getConnections({target:s,scope:"*"},!0);n=""+n,i?t=this.getElement(n):(t=this.getElement(s),this.setAttribute(t,"id",n)),P[n]=P[s]||[];for(var a=0,l=P[n].length;a<l;a++)P[n][a].setElementId(n),P[n][a].setReferenceElement(t);delete P[s],this.sourceEndpointDefinitions[n]=this.sourceEndpointDefinitions[s],delete this.sourceEndpointDefinitions[s],this.targetEndpointDefinitions[n]=this.targetEndpointDefinitions[s],delete this.targetEndpointDefinitions[s],this.anchorManager.changeId(s,n);var u=this.getDragManager();u&&u.changeId(s,n),x[n]=x[s],delete x[s];var c=function(e,i,s){for(var o=0,r=e.length;o<r;o++)e[o].endpoints[i].setElementId(n),e[o].endpoints[i].setReferenceElement(t),e[o][s+"Id"]=n,e[o][s]=t};c(o,0,"source"),c(r,1,"target"),this.repaint(n)},this.setDebugLog=function(t){v=t},this.setSuspendDrawing=function(t,e){var n=D;return D=t,w=t?(new Date).getTime():null,e&&this.repaintEverything(),n},this.isSuspendDrawing=function(){return D},this.getSuspendedAt=function(){return w},this.batch=function(t,n){var i=this.isSuspendDrawing();i||this.setSuspendDrawing(!0);try{t()}catch(t){e.log("Function run while suspended failed",t)}i||this.setSuspendDrawing(!1,!n)},this.doWhileSuspended=this.batch,this.getCachedData=function(t){var e=C[t];return e?{o:e,s:S[t]}:nt({elId:t})},this.timestamp=n,this.show=function(t,e){return B(t,"block",e),o},this.toggleVisible=function(t,e){var n=null;e&&(n=function(t){var e=t.isVisible();t.setVisible(!e)}),U(t,function(t){var e=t.isVisible();t.setVisible(!e)},n)},this.addListener=this.bind;var ht=[];this.registerFloatingConnection=function(t,n,i){ht[t.id]=n,e.addToList(P,t.id,i)},this.getFloatingConnectionFor=function(t){return ht[t]},this.listManager=new t.jsPlumbListManager(this)};e.extend(t.jsPlumbInstance,e.EventGenerator,{setAttribute:function(t,e,n){this.setAttribute(t,e,n)},getAttribute:function(e,n){return this.getAttribute(t.jsPlumb.getElement(e),n)},convertToFullOverlaySpec:function(t){return e.isString(t)&&(t=[t,{}]),t[1].id=t[1].id||e.uuid(),t},registerConnectionType:function(e,n){if(this._connectionTypes[e]=t.jsPlumb.extend({},n),n.overlays){for(var i={},s=0;s<n.overlays.length;s++){var o=this.convertToFullOverlaySpec(n.overlays[s]);i[o[1].id]=o}this._connectionTypes[e].overlays=i}},registerConnectionTypes:function(t){for(var e in t)this.registerConnectionType(e,t[e])},registerEndpointType:function(e,n){if(this._endpointTypes[e]=t.jsPlumb.extend({},n),n.overlays){for(var i={},s=0;s<n.overlays.length;s++){var o=this.convertToFullOverlaySpec(n.overlays[s]);i[o[1].id]=o}this._endpointTypes[e].overlays=i}},registerEndpointTypes:function(t){for(var e in t)this.registerEndpointType(e,t[e])},getType:function(t,e){return"connection"===e?this._connectionTypes[t]:this._endpointTypes[t]},setIdChanged:function(t,e){this.setId(t,e,!0)},setParent:function(t,e){var n=this.getElement(t),i=this.getId(n),s=this.getElement(e),o=this.getId(s),r=this.getDragManager();n.parentNode.removeChild(n),s.appendChild(n),r&&r.setParent(n,i,s,o)},extend:function(t,e,n){var i;if(n)for(i=0;i<n.length;i++)t[n[i]]=e[n[i]];else for(i in e)t[i]=e[i];return t},floatingConnections:{},getFloatingAnchorIndex:function(t){return t.endpoints[0].isFloating()?0:t.endpoints[1].isFloating()?1:-1},proxyConnection:function(t,e,n,i,s,o){var r,a=t.endpoints[e].elementId,l=t.endpoints[e];t.proxies=t.proxies||[],(r=t.proxies[e]?t.proxies[e].ep:this.addEndpoint(n,{endpoint:s(t,e),anchor:o(t,e),parameters:{isProxyEndpoint:!0}})).setDeleteOnEmpty(!0),t.proxies[e]={ep:r,originalEp:l},0===e?this.anchorManager.sourceChanged(a,i,t,n):(this.anchorManager.updateOtherEndpoint(t.endpoints[0].elementId,a,i,t),t.target=n,t.targetId=i),l.detachFromConnection(t,null,!0),r.connections=[t],t.endpoints[e]=r,l.setVisible(!1),t.setVisible(!0),this.revalidate(n)},unproxyConnection:function(t,e,n){if(null!=t._jsPlumb&&null!=t.proxies&&null!=t.proxies[e]){var i=t.proxies[e].originalEp.element,s=t.proxies[e].originalEp.elementId;t.endpoints[e]=t.proxies[e].originalEp,0===e?this.anchorManager.sourceChanged(n,s,t,i):(this.anchorManager.updateOtherEndpoint(t.endpoints[0].elementId,n,s,t),t.target=i,t.targetId=s),t.proxies[e].ep.detachFromConnection(t,null),t.proxies[e].originalEp.addConnection(t),t.isVisible()&&t.proxies[e].originalEp.setVisible(!0),delete t.proxies[e]}}});var d=new h;t.jsPlumb=d,d.getInstance=function(t,e){var n=new h(t);if(e)for(var i in e)n[i]=e[i];return n.init(),n},d.each=function(t,e){if(null!=t)if("string"==typeof t)e(d.getElement(t));else if(null!=t.length)for(var n=0;n<t.length;n++)e(d.getElement(t[n]));else e(t)},"undefined"!=typeof exports&&(exports.jsPlumb=d)}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil;e.OverlayCapableJsPlumbUIComponent=function(e){t.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.overlays={},this._jsPlumb.overlayPositions={},e.label?this.getDefaultType().overlays.__label=["Label",{label:e.label,location:e.labelLocation||this.defaultLabelLocation||.5,labelStyle:e.labelStyle||this._jsPlumb.instance.Defaults.LabelStyle,id:"__label"}]:e.id&&(this.getDefaultType().overlays.__label=["Label",{label:e.label,location:e.labelLocation||this.defaultLabelLocation||.5,labelStyle:e.emptyLabelStyle||this._jsPlumb.instance.Defaults.emptyLabelStyle,id:"__label"}]),this.setListenerComponent=function(t){if(this._jsPlumb)for(var e in this._jsPlumb.overlays)this._jsPlumb.overlays[e].setListenerComponent(t)}},e.OverlayCapableJsPlumbUIComponent.applyType=function(t,e){if(e.overlays){var n,i={};for(n in e.overlays){var s=t._jsPlumb.overlays[e.overlays[n][1].id];if(s)s.updateFrom(e.overlays[n][1]),i[e.overlays[n][1].id]=!0;else{var o=t.getCachedTypeItem("overlay",e.overlays[n][1].id);null!=o?(o.reattach(t._jsPlumb.instance,t),o.setVisible(!0),o.updateFrom(e.overlays[n][1]),t._jsPlumb.overlays[o.id]=o):o=t.addOverlay(e.overlays[n],!0),i[o.id]=!0}}for(n in t._jsPlumb.overlays)null==i[t._jsPlumb.overlays[n].id]&&t.removeOverlay(t._jsPlumb.overlays[n].id,!0)}},n.extend(e.OverlayCapableJsPlumbUIComponent,t.jsPlumbUIComponent,{setHover:function(t,e){if(this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged())for(var n in this._jsPlumb.overlays)this._jsPlumb.overlays[n][t?"addClass":"removeClass"](this._jsPlumb.instance.hoverClass)},addOverlay:function(t,i){var s=function(t,i){var s=null;if(n.isArray(i)){var o=i[0],r=e.extend({component:t,_jsPlumb:t._jsPlumb.instance},i[1]);3===i.length&&e.extend(r,i[2]),s=new(e.Overlays[t._jsPlumb.instance.getRenderMode()][o])(r)}else s=i.constructor===String?new(e.Overlays[t._jsPlumb.instance.getRenderMode()][i])({component:t,_jsPlumb:t._jsPlumb.instance}):i;return s.id=s.id||n.uuid(),t.cacheTypeItem("overlay",s,s.id),t._jsPlumb.overlays[s.id]=s,s}(this,t);if(this.getData&&"Label"===s.type&&n.isArray(t)){var o=this.getData(),r=t[1];if(o){var a=r.labelLocationAttribute||"labelLocation",l=o?o[a]:null;l&&(s.loc=l)}}return i||this.repaint(),s},getOverlay:function(t){return this._jsPlumb.overlays[t]},getOverlays:function(){return this._jsPlumb.overlays},hideOverlay:function(t){var e=this.getOverlay(t);e&&e.hide()},hideOverlays:function(){for(var t in this._jsPlumb.overlays)this._jsPlumb.overlays[t].hide()},showOverlay:function(t){var e=this.getOverlay(t);e&&e.show()},showOverlays:function(){for(var t in this._jsPlumb.overlays)this._jsPlumb.overlays[t].show()},removeAllOverlays:function(t){for(var e in this._jsPlumb.overlays)this._jsPlumb.overlays[e].cleanup&&this._jsPlumb.overlays[e].cleanup();this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null,this._jsPlumb.overlayPlacements={},t||this.repaint()},removeOverlay:function(t,e){var n=this._jsPlumb.overlays[t];n&&(n.setVisible(!1),!e&&n.cleanup&&n.cleanup(),delete this._jsPlumb.overlays[t],this._jsPlumb.overlayPositions&&delete this._jsPlumb.overlayPositions[t],this._jsPlumb.overlayPlacements&&delete this._jsPlumb.overlayPlacements[t])},removeOverlays:function(){for(var t=0,e=arguments.length;t<e;t++)this.removeOverlay(arguments[t])},moveParent:function(t){if(this.bgCanvas&&(this.bgCanvas.parentNode.removeChild(this.bgCanvas),t.appendChild(this.bgCanvas)),this.canvas&&this.canvas.parentNode)for(var e in this.canvas.parentNode.removeChild(this.canvas),t.appendChild(this.canvas),this._jsPlumb.overlays)if(this._jsPlumb.overlays[e].isAppendedAtTopLevel){var n=this._jsPlumb.overlays[e].getElement();n.parentNode.removeChild(n),t.appendChild(n)}},getLabel:function(){var t=this.getOverlay("__label");return null!=t?t.getLabel():null},getLabelOverlay:function(){return this.getOverlay("__label")},setLabel:function(t){var n=this.getOverlay("__label");n?t.constructor===String||t.constructor===Function?n.setLabel(t):(t.label||(t.label=""),n.setLabel(t.label),t.location&&n.setLocation(t.location)):(n=function(t,n){var i={cssClass:n.cssClass,labelStyle:t.labelStyle,id:"__label",component:t,_jsPlumb:t._jsPlumb.instance},s=e.extend(i,n);return new(e.Overlays[t._jsPlumb.instance.getRenderMode()].Label)(s)}(this,t.constructor===String||t.constructor===Function?{label:t}:t),this._jsPlumb.overlays.__label=n);this._jsPlumb.instance.isSuspendDrawing()||this.repaint()},cleanup:function(t){for(var e in this._jsPlumb.overlays)this._jsPlumb.overlays[e].cleanup(t),this._jsPlumb.overlays[e].destroy(t);t&&(this._jsPlumb.overlays={},this._jsPlumb.overlayPositions=null)},setVisible:function(t){this[t?"showOverlays":"hideOverlays"]()},setAbsoluteOverlayPosition:function(t,e){this._jsPlumb.overlayPositions[t.id]=e},getAbsoluteOverlayPosition:function(t){return this._jsPlumb.overlayPositions?this._jsPlumb.overlayPositions[t.id]:null},_clazzManip:function(t,e,n){if(!n)for(var i in this._jsPlumb.overlays)this._jsPlumb.overlays[i][t+"Class"](e)},addClass:function(t,e){this._clazzManip("add",t,e)},removeClass:function(t,e){this._clazzManip("remove",t,e)}})}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.jsPlumb,e=this.jsPlumbUtil,n=["connectorStyle","connectorHoverStyle","connectorOverlays","connector","connectionType","connectorClass","connectorHoverClass"];t.Endpoint=function(i){var s=i._jsPlumb,o=i.newConnection,r=i.newEndpoint;this.idPrefix="_jsplumb_e_",this.defaultLabelLocation=[.5,.5],this.defaultOverlayKeys=["Overlays","EndpointOverlays"],t.OverlayCapableJsPlumbUIComponent.apply(this,arguments),this.appendToDefaultType({connectionType:i.connectionType,maxConnections:null==i.maxConnections?this._jsPlumb.instance.Defaults.MaxConnections:i.maxConnections,paintStyle:i.endpointStyle||i.paintStyle||i.style||this._jsPlumb.instance.Defaults.EndpointStyle||t.Defaults.EndpointStyle,hoverPaintStyle:i.endpointHoverStyle||i.hoverPaintStyle||this._jsPlumb.instance.Defaults.EndpointHoverStyle||t.Defaults.EndpointHoverStyle,connectorStyle:i.connectorStyle,connectorHoverStyle:i.connectorHoverStyle,connectorClass:i.connectorClass,connectorHoverClass:i.connectorHoverClass,connectorOverlays:i.connectorOverlays,connector:i.connector,connectorTooltip:i.connectorTooltip}),this._jsPlumb.enabled=!(!1===i.enabled),this._jsPlumb.visible=!0,this.element=t.getElement(i.source),this._jsPlumb.uuid=i.uuid,this._jsPlumb.floatingEndpoint=null;this._jsPlumb.uuid&&(i.endpointsByUUID[this._jsPlumb.uuid]=this),this.elementId=i.elementId,this.dragProxy=i.dragProxy,this._jsPlumb.connectionCost=i.connectionCost,this._jsPlumb.connectionsDirected=i.connectionsDirected,this._jsPlumb.currentAnchorClass="",this._jsPlumb.events={};var a=!0===i.deleteOnEmpty;this.setDeleteOnEmpty=function(t){a=t};var l=function(){var e=s.endpointAnchorClassPrefix+"-"+this._jsPlumb.currentAnchorClass;this._jsPlumb.currentAnchorClass=this.anchor.getCssClass();var n=s.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");this.removeClass(e),this.addClass(n),t.updateClasses(this.element,n,e)}.bind(this);this.prepareAnchor=function(t){var e=this._jsPlumb.instance.makeAnchor(t,this.elementId,s);return e.bind("anchorChanged",function(t){this.fire("anchorChanged",{endpoint:this,anchor:t}),l()}.bind(this)),e},this.setPreparedAnchor=function(t,e){return this._jsPlumb.instance.continuousAnchorFactory.clear(this.elementId),this.anchor=t,l(),e||this._jsPlumb.instance.repaint(this.elementId),this},this.setAnchor=function(t,e){var n=this.prepareAnchor(t);return this.setPreparedAnchor(n,e),this};var u=function(t){if(this.connections.length>0)for(var e=0;e<this.connections.length;e++)this.connections[e].setHover(t,!1);else this.setHover(t)}.bind(this);this.bind("mouseover",function(){u(!0)}),this.bind("mouseout",function(){u(!1)}),i._transient||this._jsPlumb.instance.anchorManager.add(this,this.elementId),this.prepareEndpoint=function(n,o){var r,a=function(e,n){var i=s.getRenderMode();if(t.Endpoints[i][e])return new t.Endpoints[i][e](n);if(!s.Defaults.DoNotThrowErrors)throw{msg:"jsPlumb: unknown endpoint type '"+e+"'"}},l={_jsPlumb:this._jsPlumb.instance,cssClass:i.cssClass,container:i.container,tooltip:i.tooltip,connectorTooltip:i.connectorTooltip,endpoint:this};return e.isString(n)?r=a(n,l):e.isArray(n)?(l=e.merge(n[1],l),r=a(n[0],l)):r=n.clone(),r.clone=function(){return e.isString(n)?a(n,l):e.isArray(n)?(l=e.merge(n[1],l),a(n[0],l)):void 0}.bind(this),r.typeId=o,r},this.setEndpoint=function(t,e){var n=this.prepareEndpoint(t);this.setPreparedEndpoint(n,!0)},this.setPreparedEndpoint=function(t,e){null!=this.endpoint&&(this.endpoint.cleanup(),this.endpoint.destroy()),this.endpoint=t,this.type=this.endpoint.type,this.canvas=this.endpoint.canvas},t.extend(this,i,n),this.isSource=i.isSource||!1,this.isTemporarySource=i.isTemporarySource||!1,this.isTarget=i.isTarget||!1,this.connections=i.connections||[],this.connectorPointerEvents=i["connector-pointer-events"],this.scope=i.scope||s.getDefaultScope(),this.timestamp=null,this.reattachConnections=i.reattach||s.Defaults.ReattachConnections,this.connectionsDetachable=s.Defaults.ConnectionsDetachable,!1!==i.connectionsDetachable&&!1!==i.detachable||(this.connectionsDetachable=!1),this.dragAllowedWhenFull=!1!==i.dragAllowedWhenFull,i.onMaxConnections&&this.bind("maxConnections",i.onMaxConnections),this.addConnection=function(t){this.connections.push(t),this[(this.connections.length>0?"add":"remove")+"Class"](s.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](s.endpointFullClass)},this.detachFromConnection=function(t,e,n){(e=null==e?this.connections.indexOf(t):e)>=0&&(this.connections.splice(e,1),this[(this.connections.length>0?"add":"remove")+"Class"](s.endpointConnectedClass),this[(this.isFull()?"add":"remove")+"Class"](s.endpointFullClass)),!n&&a&&0===this.connections.length&&s.deleteObject({endpoint:this,fireEvent:!1,deleteAttachedObjects:!0!==n})},this.deleteEveryConnection=function(t){for(var e=this.connections.length,n=0;n<e;n++)s.deleteConnection(this.connections[0],t)},this.detachFrom=function(t,e,n){for(var i=[],o=0;o<this.connections.length;o++)this.connections[o].endpoints[1]!==t&&this.connections[o].endpoints[0]!==t||i.push(this.connections[o]);for(var r=0,a=i.length;r<a;r++)s.deleteConnection(i[0]);return this},this.getElement=function(){return this.element},this.setElement=function(n){var o=this._jsPlumb.instance.getId(n),r=this.elementId;return e.removeWithFunction(i.endpointsByElement[this.elementId],function(t){return t.id===this.id}.bind(this)),this.element=t.getElement(n),this.elementId=s.getId(this.element),s.anchorManager.rehomeEndpoint(this,r,this.element),s.dragManager.endpointAdded(this.element),e.addToList(i.endpointsByElement,o,this),this},this.makeInPlaceCopy=function(){var t=this.anchor.getCurrentLocation({element:this}),e=this.anchor.getOrientation(this),n=this.anchor.getCssClass(),s={bind:function(){},compute:function(){return[t[0],t[1]]},getCurrentLocation:function(){return[t[0],t[1]]},getOrientation:function(){return e},getCssClass:function(){return n}};return r({dropOptions:i.dropOptions,anchor:s,source:this.element,paintStyle:this.getPaintStyle(),endpoint:i.hideOnDrag?"Blank":this.endpoint,_transient:!0,scope:this.scope,reference:this})},this.connectorSelector=function(){return this.connections[0]},this.setStyle=this.setPaintStyle,this.paint=function(t){var e=(t=t||{}).timestamp,n=!(!1===t.recalc);if(!e||this.timestamp!==e){var i=s.updateOffset({elId:this.elementId,timestamp:e}),o=t.offset?t.offset.o:i.o;if(null!=o){var r=t.anchorPoint,a=t.connectorPaintStyle;if(null==r){var l=t.dimensions||i.s,u={xy:[o.left,o.top],wh:l,element:this,timestamp:e};if(n&&this.anchor.isDynamic&&this.connections.length>0){var c=function(t,e){var n=0;if(null!=e)for(var i=0;i<t.connections.length;i++)if(t.connections[i].sourceId===e||t.connections[i].targetId===e){n=i;break}return t.connections[n]}(this,t.elementWithPrecedence),h=c.endpoints[0]===this?1:0,d=0===h?c.sourceId:c.targetId,p=s.getCachedData(d),f=p.o,g=p.s;u.index=0===h?1:0,u.connection=c,u.txy=[f.left,f.top],u.twh=g,u.tElement=c.endpoints[h]}else this.connections.length>0&&(u.connection=this.connections[0]);r=this.anchor.compute(u)}for(var m in this.endpoint.compute(r,this.anchor.getOrientation(this),this._jsPlumb.paintStyleInUse,a||this.paintStyleInUse),this.endpoint.paint(this._jsPlumb.paintStyleInUse,this.anchor),this.timestamp=e,this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(m)){var v=this._jsPlumb.overlays[m];v.isVisible()&&(this._jsPlumb.overlayPlacements[m]=v.draw(this.endpoint,this._jsPlumb.paintStyleInUse),v.paint(this._jsPlumb.overlayPlacements[m]))}}}},this.getTypeDescriptor=function(){return"endpoint"},this.isVisible=function(){return this._jsPlumb.visible},this.repaint=this.paint;var c=!1;this.initDraggable=function(){if(!c&&t.isDragSupported(this.element)){var n,a={id:null,element:null},l=null,u=!1,h=null,d=function(t,e,n){var i=!1;return{drag:function(){if(i)return i=!1,!0;if(e.element){var s=n.getUIPosition(arguments,n.getZoom());null!=s&&n.setPosition(e.element,s),n.repaint(e.element,s),t.paint({anchorPoint:t.anchor.getCurrentLocation({element:t})})}},stopDrag:function(){i=!0}}}(this,a,s),p=i.dragOptions||{},f=t.dragEvents.start,g=t.dragEvents.stop,m=t.dragEvents.drag,v=t.dragEvents.beforeStart,b=function(e){l=this.connectorSelector();var c=!0;this.isEnabled()||(c=!1),null!=l||this.isSource||this.isTemporarySource||(c=!1),!this.isSource||!this.isFull()||null!=l&&this.dragAllowedWhenFull||(c=!1),null==l||l.isDetachable(this)||(this.isFull()?c=!1:l=null);var p=s.checkCondition(null==l?"beforeDrag":"beforeStartDetach",{endpoint:this,source:this.element,sourceId:this.elementId,connection:l});if(!1===p?c=!1:"object"==typeof p?t.extend(p,n||{}):p=n||{},!1===c)return s.stopDrag&&s.stopDrag(this.canvas),d.stopDrag(),!1;for(var f=0;f<this.connections.length;f++)this.connections[f].setHover(!1);this.addClass("endpointDrag"),s.setConnectionBeingDragged(!0),l&&!this.isFull()&&this.isSource&&(l=null),s.updateOffset({elId:this.elementId});var g=this._jsPlumb.instance.getOffset(this.canvas),m=this.canvas,v=this._jsPlumb.instance.getSize(this.canvas);!function(t,e,n,i){var s=e.createElement("div",{position:"absolute"});e.appendElement(s);var o=e.getId(s);e.setPosition(s,n),s.style.width=i[0]+"px",s.style.height=i[1]+"px",e.manage(o,s,!0),t.id=o,t.element=s}(a,s,g,v),s.setAttributes(this.canvas,{dragId:a.id,elId:this.elementId});var b=this.dragProxy||this.endpoint;if(null==this.dragProxy&&null!=this.connectionType){var y=this._jsPlumb.instance.deriveEndpointAndAnchorSpec(this.connectionType);y.endpoints[1]&&(b=y.endpoints[1])}var P=this._jsPlumb.instance.makeAnchor("Center");P.isFloating=!0,this._jsPlumb.floatingEndpoint=function(e,n,i,s,o,r,a,l){return a({paintStyle:e,endpoint:i,anchor:new t.FloatingAnchor({reference:n,referenceCanvas:s,jsPlumbInstance:r}),source:o,scope:l})}(this.getPaintStyle(),P,b,this.canvas,a.element,s,r,this.scope);var _=this._jsPlumb.floatingEndpoint.anchor;if(null==l)this.setHover(!1,!1),(l=o({sourceEndpoint:this,targetEndpoint:this._jsPlumb.floatingEndpoint,source:this.element,target:a.element,anchors:[this.anchor,this._jsPlumb.floatingEndpoint.anchor],paintStyle:i.connectorStyle,hoverPaintStyle:i.connectorHoverStyle,connector:i.connector,overlays:i.connectorOverlays,type:this.connectionType,cssClass:this.connectorClass,hoverClass:this.connectorHoverClass,scope:i.scope,data:p})).pending=!0,l.addClass(s.draggingClass),this._jsPlumb.floatingEndpoint.addClass(s.draggingClass),this._jsPlumb.floatingEndpoint.anchor=_,s.fire("connectionDrag",l),s.anchorManager.newConnection(l);else{u=!0,l.setHover(!1);var x=l.endpoints[0].id===this.id?0:1;this.detachFromConnection(l,null,!0);var C=s.getDragScope(m);s.setAttribute(this.canvas,"originalScope",C),s.fire("connectionDrag",l),0===x?(h=[l.source,l.sourceId,m,C],s.anchorManager.sourceChanged(l.endpoints[x].elementId,a.id,l,a.element)):(h=[l.target,l.targetId,m,C],l.target=a.element,l.targetId=a.id,s.anchorManager.updateOtherEndpoint(l.sourceId,l.endpoints[x].elementId,l.targetId,l)),l.suspendedEndpoint=l.endpoints[x],l.suspendedElement=l.endpoints[x].getElement(),l.suspendedElementId=l.endpoints[x].elementId,l.suspendedElementType=0===x?"source":"target",l.suspendedEndpoint.setHover(!1),this._jsPlumb.floatingEndpoint.referenceEndpoint=l.suspendedEndpoint,l.endpoints[x]=this._jsPlumb.floatingEndpoint,l.addClass(s.draggingClass),this._jsPlumb.floatingEndpoint.addClass(s.draggingClass)}s.registerFloatingConnection(a,l,this._jsPlumb.floatingEndpoint),s.currentlyDragging=!0}.bind(this),y=function(){if(s.setConnectionBeingDragged(!1),l&&null!=l.endpoints){var t=s.getDropEvent(arguments),e=s.getFloatingAnchorIndex(l);if(l.endpoints[0===e?1:0].anchor.unlock(),l.removeClass(s.draggingClass),this._jsPlumb&&(l.deleteConnectionNow||l.endpoints[e]===this._jsPlumb.floatingEndpoint)&&u&&l.suspendedEndpoint){0===e?(l.floatingElement=l.source,l.floatingId=l.sourceId,l.floatingEndpoint=l.endpoints[0],l.floatingIndex=0,l.source=h[0],l.sourceId=h[1]):(l.floatingElement=l.target,l.floatingId=l.targetId,l.floatingEndpoint=l.endpoints[1],l.floatingIndex=1,l.target=h[0],l.targetId=h[1]);var n=this._jsPlumb.floatingEndpoint;s.setDragScope(h[2],h[3]),l.endpoints[e]=l.suspendedEndpoint,l.isReattach()||l._forceReattach||l._forceDetach||!s.deleteConnection(l,{originalEvent:t})?(l.setHover(!1),l._forceDetach=null,l._forceReattach=null,this._jsPlumb.floatingEndpoint.detachFromConnection(l),l.suspendedEndpoint.addConnection(l),1===e?s.anchorManager.updateOtherEndpoint(l.sourceId,l.floatingId,l.targetId,l):s.anchorManager.sourceChanged(l.floatingId,l.sourceId,l,l.source),s.repaint(h[1])):s.deleteObject({endpoint:n})}this.deleteAfterDragStop?s.deleteObject({endpoint:this}):this._jsPlumb&&this.paint({recalc:!1}),s.fire("connectionDragStop",l,t),l.pending&&s.fire("connectionAborted",l,t),s.currentlyDragging=!1,l.suspendedElement=null,l.suspendedEndpoint=null,l=null}a&&a.element&&s.remove(a.element,!1,!1),this._jsPlumb&&(this.canvas.style.visibility="visible",this.anchor.unlock(),this._jsPlumb.floatingEndpoint=null)}.bind(this);(p=t.extend({},p)).scope=this.scope||p.scope,p[v]=e.wrap(p[v],function(t){n=t.e.payload||{}},!1),p[f]=e.wrap(p[f],b,!1),p[m]=e.wrap(p[m],d.drag),p[g]=e.wrap(p[g],y),p.multipleDrop=!1,p.canDrag=function(){return this.isSource||this.isTemporarySource||this.connections.length>0&&!1!==this.connectionsDetachable}.bind(this),s.initDraggable(this.canvas,p,"internal"),this.canvas._jsPlumbRelatedElement=this.element,c=!0}};var h=i.endpoint||this._jsPlumb.instance.Defaults.Endpoint||t.Defaults.Endpoint;this.setEndpoint(h,!0);var d=i.anchor?i.anchor:i.anchors?i.anchors:s.Defaults.Anchor||"Top";this.setAnchor(d,!0);var p=["default",i.type||""].join(" ");this.addType(p,i.data,!0),this.canvas=this.endpoint.canvas,this.canvas._jsPlumb=this,this.initDraggable();var f=function(n,o,r,a){if(t.isDropSupported(this.element)){var l=i.dropOptions||s.Defaults.DropOptions||t.Defaults.DropOptions;(l=t.extend({},l)).scope=l.scope||this.scope;var u=t.dragEvents.drop,c=t.dragEvents.over,h=t.dragEvents.out,d=this,p=s.EndpointDropHandler({getEndpoint:function(){return d},jsPlumb:s,enabled:function(){return null==r||r.isEnabled()},isFull:function(){return r.isFull()},element:this.element,elementId:this.elementId,isSource:this.isSource,isTarget:this.isTarget,addClass:function(t){d.addClass(t)},removeClass:function(t){d.removeClass(t)},isDropAllowed:function(){return d.isDropAllowed.apply(d,arguments)},reference:a,isRedrop:function(t,e){return t.suspendedEndpoint&&e.reference&&t.suspendedEndpoint.id===e.reference.id}});l[u]=e.wrap(l[u],p,!0),l[c]=e.wrap(l[c],function(){var e=t.getDragObject(arguments),n=s.getAttribute(t.getElement(e),"dragId"),i=s.getFloatingConnectionFor(n);if(null!=i){var o=s.getFloatingAnchorIndex(i);if(this.isTarget&&0!==o||i.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===i.suspendedEndpoint.id){var r=s.checkCondition("checkDropAllowed",{sourceEndpoint:i.endpoints[o],targetEndpoint:this,connection:i});this[(r?"add":"remove")+"Class"](s.endpointDropAllowedClass),this[(r?"remove":"add")+"Class"](s.endpointDropForbiddenClass),i.endpoints[o].anchor.over(this.anchor,this)}}}.bind(this)),l[h]=e.wrap(l[h],function(){var e=t.getDragObject(arguments),n=null==e?null:s.getAttribute(t.getElement(e),"dragId"),i=n?s.getFloatingConnectionFor(n):null;if(null!=i){var o=s.getFloatingAnchorIndex(i);(this.isTarget&&0!==o||i.suspendedEndpoint&&this.referenceEndpoint&&this.referenceEndpoint.id===i.suspendedEndpoint.id)&&(this.removeClass(s.endpointDropAllowedClass),this.removeClass(s.endpointDropForbiddenClass),i.endpoints[o].anchor.out())}}.bind(this)),s.initDroppable(n,l,"internal",o)}}.bind(this);return this.anchor.isFloating||f(this.canvas,!(i._transient||this.anchor.isFloating),this,i.reference),this},e.extend(t.Endpoint,t.OverlayCapableJsPlumbUIComponent,{setVisible:function(t,e,n){if(this._jsPlumb.visible=t,this.canvas&&(this.canvas.style.display=t?"block":"none"),this[t?"showOverlays":"hideOverlays"](),!e)for(var i=0;i<this.connections.length;i++)if(this.connections[i].setVisible(t),!n){var s=this===this.connections[i].endpoints[0]?1:0;1===this.connections[i].endpoints[s].connections.length&&this.connections[i].endpoints[s].setVisible(t,!0,!0)}},getAttachedElements:function(){return this.connections},applyType:function(e,i){this.setPaintStyle(e.endpointStyle||e.paintStyle,i),this.setHoverPaintStyle(e.endpointHoverStyle||e.hoverPaintStyle,i),null!=e.maxConnections&&(this._jsPlumb.maxConnections=e.maxConnections),e.scope&&(this.scope=e.scope),t.extend(this,e,n),null!=e.cssClass&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,e.cssClass),t.OverlayCapableJsPlumbUIComponent.applyType(this,e)},isEnabled:function(){return this._jsPlumb.enabled},setEnabled:function(t){this._jsPlumb.enabled=t},cleanup:function(){var e=this._jsPlumb.instance.endpointAnchorClassPrefix+(this._jsPlumb.currentAnchorClass?"-"+this._jsPlumb.currentAnchorClass:"");t.removeClass(this.element,e),this.anchor=null,this.endpoint.cleanup(!0),this.endpoint.destroy(),this.endpoint=null,this._jsPlumb.instance.destroyDraggable(this.canvas,"internal"),this._jsPlumb.instance.destroyDroppable(this.canvas,"internal")},setHover:function(t){this.endpoint&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&this.endpoint.setHover(t)},isFull:function(){return 0===this._jsPlumb.maxConnections||!(this.isFloating()||this._jsPlumb.maxConnections<0||this.connections.length<this._jsPlumb.maxConnections)},isFloating:function(){return null!=this.anchor&&this.anchor.isFloating},isConnectedTo:function(t){var e=!1;if(t)for(var n=0;n<this.connections.length;n++)if(this.connections[n].endpoints[1]===t||this.connections[n].endpoints[0]===t){e=!0;break}return e},getConnectionCost:function(){return this._jsPlumb.connectionCost},setConnectionCost:function(t){this._jsPlumb.connectionCost=t},areConnectionsDirected:function(){return this._jsPlumb.connectionsDirected},setConnectionsDirected:function(t){this._jsPlumb.connectionsDirected=t},setElementId:function(t){this.elementId=t,this.anchor.elementId=t},setReferenceElement:function(e){this.element=t.getElement(e)},setDragAllowedWhenFull:function(t){this.dragAllowedWhenFull=t},equals:function(t){return this.anchor.equals(t.anchor)},getUuid:function(){return this._jsPlumb.uuid},computeAnchor:function(t){return this.anchor.compute(t)}}),this.jsPlumbInstance.prototype.EndpointDropHandler=function(t){return function(n){var i=t.jsPlumb;t.removeClass(i.endpointDropAllowedClass),t.removeClass(i.endpointDropForbiddenClass);var s=i.getDropEvent(arguments),o=i.getDragObject(arguments),r=i.getAttribute(o,"dragId"),a=(i.getAttribute(o,"elId"),i.getAttribute(o,"originalScope")),l=i.getFloatingConnectionFor(r);if(null!=l){var u=null!=l.suspendedEndpoint;if(!u||null!=l.suspendedEndpoint._jsPlumb){var c=t.getEndpoint(l);if(null!=c){if(t.isRedrop(l,t))return l._forceReattach=!0,l.setHover(!1),void(t.maybeCleanup&&t.maybeCleanup(c));var h=i.getFloatingAnchorIndex(l);if(0===h&&!t.isSource||1===h&&!t.isTarget)t.maybeCleanup&&t.maybeCleanup(c);else{t.onDrop&&t.onDrop(l),a&&i.setDragScope(o,a);var d=t.isFull(n);if(d&&c.fire("maxConnections",{endpoint:this,connection:l,maxConnections:c._jsPlumb.maxConnections},s),!d&&t.enabled()){var p=!0;0===h?(l.floatingElement=l.source,l.floatingId=l.sourceId,l.floatingEndpoint=l.endpoints[0],l.floatingIndex=0,l.source=t.element,l.sourceId=t.elementId):(l.floatingElement=l.target,l.floatingId=l.targetId,l.floatingEndpoint=l.endpoints[1],l.floatingIndex=1,l.target=t.element,l.targetId=t.elementId),u&&l.suspendedEndpoint.id!==c.id&&(l.isDetachAllowed(l)&&l.endpoints[h].isDetachAllowed(l)&&l.suspendedEndpoint.isDetachAllowed(l)&&i.checkCondition("beforeDetach",l)||(p=!1));var f=function(n){l.endpoints[h].detachFromConnection(l),l.suspendedEndpoint&&l.suspendedEndpoint.detachFromConnection(l),l.endpoints[h]=c,c.addConnection(l);var o=c.getParameters();for(var r in o)l.setParameter(r,o[r]);if(u){var a=l.suspendedEndpoint.elementId;i.fireMoveEvent({index:h,originalSourceId:0===h?a:l.sourceId,newSourceId:0===h?c.elementId:l.sourceId,originalTargetId:1===h?a:l.targetId,newTargetId:1===h?c.elementId:l.targetId,originalSourceEndpoint:0===h?l.suspendedEndpoint:l.endpoints[0],newSourceEndpoint:0===h?c:l.endpoints[0],originalTargetEndpoint:1===h?l.suspendedEndpoint:l.endpoints[1],newTargetEndpoint:1===h?c:l.endpoints[1],connection:l},s)}else o.draggable&&i.initDraggable(this.element,t.dragOptions,"internal",i);(1===h?i.anchorManager.updateOtherEndpoint(l.sourceId,l.floatingId,l.targetId,l):i.anchorManager.sourceChanged(l.floatingId,l.sourceId,l,l.source),l.endpoints[0].finalEndpoint)&&(l.endpoints[0].detachFromConnection(l),l.endpoints[0]=l.endpoints[0].finalEndpoint,l.endpoints[0].addConnection(l));e.isObject(n)&&l.mergeData(n),i.finaliseConnection(l,null,s,!1),l.setHover(!1),i.revalidate(l.endpoints[0].element)}.bind(this);if(p=p&&t.isDropAllowed(l.sourceId,l.targetId,l.scope,l,c))return f(p),!0;l.suspendedEndpoint&&(l.endpoints[h]=l.suspendedEndpoint,l.setHover(!1),l._forceDetach=!0,0===h?(l.source=l.suspendedEndpoint.element,l.sourceId=l.suspendedEndpoint.elementId):(l.target=l.suspendedEndpoint.element,l.targetId=l.suspendedEndpoint.elementId),l.suspendedEndpoint.addConnection(l),1===h?i.anchorManager.updateOtherEndpoint(l.sourceId,l.floatingId,l.targetId,l):i.anchorManager.sourceChanged(l.floatingId,l.sourceId,l,l.source),i.repaint(l.sourceId),l._forceDetach=!1)}t.maybeCleanup&&t.maybeCleanup(c),i.currentlyDragging=!1}}}}}}}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=function(t,i,s,o,r){if(e.Connectors[i]=e.Connectors[i]||{},null==e.Connectors[i][s]){if(null==e.Connectors[s]){if(t.Defaults.DoNotThrowErrors)return null;throw new TypeError("jsPlumb: unknown connector type '"+s+"'")}e.Connectors[i][s]=function(){e.Connectors[s].apply(this,arguments),e.ConnectorRenderers[i].apply(this,arguments)},n.extend(e.Connectors[i][s],[e.Connectors[s],e.ConnectorRenderers[i]])}return new e.Connectors[i][s](o,r)},s=function(t,e,n){return t?n.makeAnchor(t,e,n):null},o=function(t,e,i,s){null!=e&&(e._jsPlumbConnections=e._jsPlumbConnections||{},s?delete e._jsPlumbConnections[t.id]:e._jsPlumbConnections[t.id]=!0,n.isEmpty(e._jsPlumbConnections)?i.removeClass(e,i.connectedClass):i.addClass(e,i.connectedClass))};e.Connection=function(t){var i=t.newEndpoint;this.id=t.id,this.connector=null,this.idPrefix="_jsplumb_c_",this.defaultLabelLocation=.5,this.defaultOverlayKeys=["Overlays","ConnectionOverlays"],this.previousConnection=t.previousConnection,this.source=e.getElement(t.source),this.target=e.getElement(t.target),e.OverlayCapableJsPlumbUIComponent.apply(this,arguments),t.sourceEndpoint?(this.source=t.sourceEndpoint.getElement(),this.sourceId=t.sourceEndpoint.elementId):this.sourceId=this._jsPlumb.instance.getId(this.source),t.targetEndpoint?(this.target=t.targetEndpoint.getElement(),this.targetId=t.targetEndpoint.elementId):this.targetId=this._jsPlumb.instance.getId(this.target),this.scope=t.scope,this.endpoints=[],this.endpointStyles=[];var s=this._jsPlumb.instance;s.manage(this.sourceId,this.source),s.manage(this.targetId,this.target),this._jsPlumb.visible=!0,this._jsPlumb.params={cssClass:t.cssClass,container:t.container,"pointer-events":t["pointer-events"],editorParams:t.editorParams,overlays:t.overlays},this._jsPlumb.lastPaintedAt=null,this.bind("mouseover",function(){this.setHover(!0)}.bind(this)),this.bind("mouseout",function(){this.setHover(!1)}.bind(this)),this.makeEndpoint=function(e,n,o,r,a){return o=o||this._jsPlumb.instance.getId(n),this.prepareEndpoint(s,i,this,r,e?0:1,t,n,o,a)},t.type&&(t.endpoints=t.endpoints||this._jsPlumb.instance.deriveEndpointAndAnchorSpec(t.type).endpoints);var o=this.makeEndpoint(!0,this.source,this.sourceId,t.sourceEndpoint),r=this.makeEndpoint(!1,this.target,this.targetId,t.targetEndpoint);o&&n.addToList(t.endpointsByElement,this.sourceId,o),r&&n.addToList(t.endpointsByElement,this.targetId,r),this.scope||(this.scope=this.endpoints[0].scope),null!=t.deleteEndpointsOnEmpty&&(this.endpoints[0].setDeleteOnEmpty(t.deleteEndpointsOnEmpty),this.endpoints[1].setDeleteOnEmpty(t.deleteEndpointsOnEmpty));var a=s.Defaults.ConnectionsDetachable;!1===t.detachable&&(a=!1),!1===this.endpoints[0].connectionsDetachable&&(a=!1),!1===this.endpoints[1].connectionsDetachable&&(a=!1);var l=t.reattach||this.endpoints[0].reattachConnections||this.endpoints[1].reattachConnections||s.Defaults.ReattachConnections;this.appendToDefaultType({detachable:a,reattach:l,paintStyle:this.endpoints[0].connectorStyle||this.endpoints[1].connectorStyle||t.paintStyle||s.Defaults.PaintStyle||e.Defaults.PaintStyle,hoverPaintStyle:this.endpoints[0].connectorHoverStyle||this.endpoints[1].connectorHoverStyle||t.hoverPaintStyle||s.Defaults.HoverPaintStyle||e.Defaults.HoverPaintStyle});var u=s.getSuspendedAt();if(!s.isSuspendDrawing()){var c=s.getCachedData(this.sourceId),h=c.o,d=c.s,p=s.getCachedData(this.targetId),f=p.o,g=p.s,m=u||s.timestamp(),v=this.endpoints[0].anchor.compute({xy:[h.left,h.top],wh:d,element:this.endpoints[0],elementId:this.endpoints[0].elementId,txy:[f.left,f.top],twh:g,tElement:this.endpoints[1],timestamp:m});this.endpoints[0].paint({anchorLoc:v,timestamp:m}),v=this.endpoints[1].anchor.compute({xy:[f.left,f.top],wh:g,element:this.endpoints[1],elementId:this.endpoints[1].elementId,txy:[h.left,h.top],twh:d,tElement:this.endpoints[0],timestamp:m}),this.endpoints[1].paint({anchorLoc:v,timestamp:m})}this.getTypeDescriptor=function(){return"connection"},this.getAttachedElements=function(){return this.endpoints},this.isDetachable=function(t){return!1!==this._jsPlumb.detachable&&(null!=t?!0===t.connectionsDetachable:!0===this._jsPlumb.detachable)},this.setDetachable=function(t){this._jsPlumb.detachable=!0===t},this.isReattach=function(){return!0===this._jsPlumb.reattach||!0===this.endpoints[0].reattachConnections||!0===this.endpoints[1].reattachConnections},this.setReattach=function(t){this._jsPlumb.reattach=!0===t},this._jsPlumb.cost=t.cost||this.endpoints[0].getConnectionCost(),this._jsPlumb.directed=t.directed,null==t.directed&&(this._jsPlumb.directed=this.endpoints[0].areConnectionsDirected());var b=e.extend({},this.endpoints[1].getParameters());e.extend(b,this.endpoints[0].getParameters()),e.extend(b,this.getParameters()),this.setParameters(b),this.setConnector(this.endpoints[0].connector||this.endpoints[1].connector||t.connector||s.Defaults.Connector||e.Defaults.Connector,!0);var y=null!=t.data&&n.isObject(t.data)?t.data:{};this.getData=function(){return y},this.setData=function(t){y=t||{}},this.mergeData=function(t){y=e.extend(y,t)};var P=["default",this.endpoints[0].connectionType,this.endpoints[1].connectionType,t.type].join(" ");/[^\s]/.test(P)&&this.addType(P,t.data,!0),this.updateConnectedClass()},n.extend(e.Connection,e.OverlayCapableJsPlumbUIComponent,{applyType:function(t,n,i){var s=null;null!=t.connector&&(null==(s=this.getCachedTypeItem("connector",i.connector))&&(s=this.prepareConnector(t.connector,i.connector),this.cacheTypeItem("connector",s,i.connector)),this.setPreparedConnector(s)),null!=t.detachable&&this.setDetachable(t.detachable),null!=t.reattach&&this.setReattach(t.reattach),t.scope&&(this.scope=t.scope),null!=t.cssClass&&this.canvas&&this._jsPlumb.instance.addClass(this.canvas,t.cssClass);var o=null;t.anchor?null==(o=this.getCachedTypeItem("anchors",i.anchor))&&(o=[this._jsPlumb.instance.makeAnchor(t.anchor),this._jsPlumb.instance.makeAnchor(t.anchor)],this.cacheTypeItem("anchors",o,i.anchor)):t.anchors&&null==(o=this.getCachedTypeItem("anchors",i.anchors))&&(o=[this._jsPlumb.instance.makeAnchor(t.anchors[0]),this._jsPlumb.instance.makeAnchor(t.anchors[1])],this.cacheTypeItem("anchors",o,i.anchors)),null!=o&&(this.endpoints[0].anchor=o[0],this.endpoints[1].anchor=o[1],this.endpoints[1].anchor.isDynamic&&this._jsPlumb.instance.repaint(this.endpoints[1].elementId)),e.OverlayCapableJsPlumbUIComponent.applyType(this,t)},addClass:function(t,e){e&&(this.endpoints[0].addClass(t),this.endpoints[1].addClass(t),this.suspendedEndpoint&&this.suspendedEndpoint.addClass(t)),this.connector&&this.connector.addClass(t)},removeClass:function(t,e){e&&(this.endpoints[0].removeClass(t),this.endpoints[1].removeClass(t),this.suspendedEndpoint&&this.suspendedEndpoint.removeClass(t)),this.connector&&this.connector.removeClass(t)},isVisible:function(){return this._jsPlumb.visible},setVisible:function(t){this._jsPlumb.visible=t,this.connector&&this.connector.setVisible(t),this.repaint()},cleanup:function(){this.updateConnectedClass(!0),this.endpoints=null,this.source=null,this.target=null,null!=this.connector&&(this.connector.cleanup(!0),this.connector.destroy(!0)),this.connector=null},updateConnectedClass:function(t){this._jsPlumb&&(o(this,this.source,this._jsPlumb.instance,t),o(this,this.target,this._jsPlumb.instance,t))},setHover:function(e){this.connector&&this._jsPlumb&&!this._jsPlumb.instance.isConnectionBeingDragged()&&(this.connector.setHover(e),t.jsPlumb[e?"addClass":"removeClass"](this.source,this._jsPlumb.instance.hoverSourceClass),t.jsPlumb[e?"addClass":"removeClass"](this.target,this._jsPlumb.instance.hoverTargetClass))},getUuids:function(){return[this.endpoints[0].getUuid(),this.endpoints[1].getUuid()]},getCost:function(){return this._jsPlumb?this._jsPlumb.cost:-1/0},setCost:function(t){this._jsPlumb.cost=t},isDirected:function(){return this._jsPlumb.directed},getConnector:function(){return this.connector},prepareConnector:function(t,e){var s,o={_jsPlumb:this._jsPlumb.instance,cssClass:this._jsPlumb.params.cssClass,container:this._jsPlumb.params.container,"pointer-events":this._jsPlumb.params["pointer-events"]},r=this._jsPlumb.instance.getRenderMode();return n.isString(t)?s=i(this._jsPlumb.instance,r,t,o,this):n.isArray(t)&&(s=1===t.length?i(this._jsPlumb.instance,r,t[0],o,this):i(this._jsPlumb.instance,r,t[0],n.merge(t[1],o),this)),null!=e&&(s.typeId=e),s},setPreparedConnector:function(t,e,n,i){if(this.connector!==t){var s,o="";if(null!=this.connector&&(o=(s=this.connector).getClass(),this.connector.cleanup(),this.connector.destroy()),this.connector=t,i&&this.cacheTypeItem("connector",t,i),this.canvas=this.connector.canvas,this.bgCanvas=this.connector.bgCanvas,this.connector.reattach(this._jsPlumb.instance),this.addClass(o),this.canvas&&(this.canvas._jsPlumb=this),this.bgCanvas&&(this.bgCanvas._jsPlumb=this),null!=s)for(var r=this.getOverlays(),a=0;a<r.length;a++)r[a].transfer&&r[a].transfer(this.connector);n||this.setListenerComponent(this.connector),e||this.repaint()}},setConnector:function(t,e,n,i){var s=this.prepareConnector(t,i);this.setPreparedConnector(s,e,n,i)},paint:function(t){if(!this._jsPlumb.instance.isSuspendDrawing()&&this._jsPlumb.visible){var e=(t=t||{}).timestamp,n=this.targetId,i=this.sourceId;if(null==e||e!==this._jsPlumb.lastPaintedAt){var s=this._jsPlumb.instance.updateOffset({elId:i}).o,o=this._jsPlumb.instance.updateOffset({elId:n}).o,r=this.endpoints[0],a=this.endpoints[1],l=r.anchor.getCurrentLocation({xy:[s.left,s.top],wh:[s.width,s.height],element:r,timestamp:e}),u=a.anchor.getCurrentLocation({xy:[o.left,o.top],wh:[o.width,o.height],element:a,timestamp:e});this.connector.resetBounds(),this.connector.compute({sourcePos:l,targetPos:u,sourceOrientation:r.anchor.getOrientation(r),targetOrientation:a.anchor.getOrientation(a),sourceEndpoint:this.endpoints[0],targetEndpoint:this.endpoints[1],"stroke-width":this._jsPlumb.paintStyleInUse.strokeWidth,sourceInfo:s,targetInfo:o});var c={minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0};for(var h in this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(h)){var d=this._jsPlumb.overlays[h];d.isVisible()&&(this._jsPlumb.overlayPlacements[h]=d.draw(this.connector,this._jsPlumb.paintStyleInUse,this.getAbsoluteOverlayPosition(d)),c.minX=Math.min(c.minX,this._jsPlumb.overlayPlacements[h].minX),c.maxX=Math.max(c.maxX,this._jsPlumb.overlayPlacements[h].maxX),c.minY=Math.min(c.minY,this._jsPlumb.overlayPlacements[h].minY),c.maxY=Math.max(c.maxY,this._jsPlumb.overlayPlacements[h].maxY))}var p=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||1)/2,f=parseFloat(this._jsPlumb.paintStyleInUse.strokeWidth||0),g={xmin:Math.min(this.connector.bounds.minX-(p+f),c.minX),ymin:Math.min(this.connector.bounds.minY-(p+f),c.minY),xmax:Math.max(this.connector.bounds.maxX+(p+f),c.maxX),ymax:Math.max(this.connector.bounds.maxY+(p+f),c.maxY)};for(var m in this.connector.paintExtents=g,this.connector.paint(this._jsPlumb.paintStyleInUse,null,g),this._jsPlumb.overlays)if(this._jsPlumb.overlays.hasOwnProperty(m)){var v=this._jsPlumb.overlays[m];v.isVisible()&&v.paint(this._jsPlumb.overlayPlacements[m],g)}}this._jsPlumb.lastPaintedAt=e}},repaint:function(t){var e=jsPlumb.extend(t||{},{});e.elId=this.sourceId,this.paint(e)},prepareEndpoint:function(t,n,i,o,r,a,l,u,c){var h;if(o)i.endpoints[r]=o,o.addConnection(i);else{a.endpoints||(a.endpoints=[null,null]);var d=c||a.endpoints[r]||a.endpoint||t.Defaults.Endpoints[r]||e.Defaults.Endpoints[r]||t.Defaults.Endpoint||e.Defaults.Endpoint;a.endpointStyles||(a.endpointStyles=[null,null]),a.endpointHoverStyles||(a.endpointHoverStyles=[null,null]);var p=a.endpointStyles[r]||a.endpointStyle||t.Defaults.EndpointStyles[r]||e.Defaults.EndpointStyles[r]||t.Defaults.EndpointStyle||e.Defaults.EndpointStyle;null==p.fill&&null!=a.paintStyle&&(p.fill=a.paintStyle.stroke),null==p.outlineStroke&&null!=a.paintStyle&&(p.outlineStroke=a.paintStyle.outlineStroke),null==p.outlineWidth&&null!=a.paintStyle&&(p.outlineWidth=a.paintStyle.outlineWidth);var f=a.endpointHoverStyles[r]||a.endpointHoverStyle||t.Defaults.EndpointHoverStyles[r]||e.Defaults.EndpointHoverStyles[r]||t.Defaults.EndpointHoverStyle||e.Defaults.EndpointHoverStyle;null!=a.hoverPaintStyle&&(null==f&&(f={}),null==f.fill&&(f.fill=a.hoverPaintStyle.stroke));var g=a.anchors?a.anchors[r]:a.anchor?a.anchor:s(t.Defaults.Anchors[r],u,t)||s(e.Defaults.Anchors[r],u,t)||s(t.Defaults.Anchor,u,t)||s(e.Defaults.Anchor,u,t);h=n({paintStyle:p,hoverPaintStyle:f,endpoint:d,connections:[i],uuid:a.uuids?a.uuids[r]:null,anchor:g,source:l,scope:a.scope,reattach:a.reattach||t.Defaults.ReattachConnections,detachable:a.detachable||t.Defaults.ConnectionsDetachable}),null==o&&h.setDeleteOnEmpty(!0),i.endpoints[r]=h,!1===a.drawEndpoints&&h.setVisible(!1,!0,!0)}return h},replaceEndpoint:function(t,e){var n=this.endpoints[t],i=n.elementId,s=this._jsPlumb.instance.getEndpoints(i),o=s.indexOf(n),r=this.makeEndpoint(0===t,n.element,i,null,e);this.endpoints[t]=r,s.splice(o,1,r),this._jsPlumb.instance.deleteObject({endpoint:n,deleteAttachedObjects:!1}),this._jsPlumb.instance.fire("endpointReplaced",{previous:n,current:r}),this._jsPlumb.instance.anchorManager.updateOtherEndpoint(this.endpoints[0].elementId,this.endpoints[1].elementId,this.endpoints[1].elementId,this)}})}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.jsPlumbUtil,e=this.jsPlumb;e.AnchorManager=function(n){var i={},s={},o={},r={},a=this,l={},u=n.jsPlumbInstance,c={},h=function(t){return function(e,n){return!1===(t?e[0][0]<n[0][0]:e[0][0]>n[0][0])?-1:1}},d={top:function(t,e){return t[0]>e[0]?1:-1},right:h(!0),bottom:h(!0),left:function(t,e){return(t[0][0]<0?-Math.PI-t[0][0]:Math.PI-t[0][0])>(e[0][0]<0?-Math.PI-e[0][0]:Math.PI-e[0][0])?1:-1}},p=function(t,e){var n=u.getCachedData(t),i=n.s,r=n.o,a=function(e,n,i,r,a,l,u){if(r.length>0)for(var c=function(t,e,n,i,s,o,r){for(var a=[],l=e[s?0:1]/(i.length+1),u=0;u<i.length;u++){var c=(u+1)*l,h=o*e[s?1:0];r&&(c=e[s?0:1]-c);var d=s?c:h,p=n[0]+d,f=d/e[0],g=s?h:c,m=n[1]+g,v=g/e[1];a.push([p,m,f,v,i[u][1],i[u][2]])}return a}(0,n,i,(v=d[e],r.sort(v)),a,l,"right"===e||"top"===e),h=function(t,e){s[t.id]=[e[0],e[1],e[2],e[3]],o[t.id]=u},p=0;p<c.length;p++){var f=c[p][4],g=f.endpoints[0].elementId===t,m=f.endpoints[1].elementId===t;g&&h(f.endpoints[0],c[p]),m&&h(f.endpoints[1],c[p])}var v};a("bottom",i,[r.left,r.top],e.bottom,!0,1,[0,1]),a("top",i,[r.left,r.top],e.top,!0,0,[0,-1]),a("left",i,[r.left,r.top],e.left,!1,0,[-1,0]),a("right",i,[r.left,r.top],e.right,!1,1,[1,0])};this.reset=function(){i={},r={},l={}},this.addFloatingConnection=function(t,e){c[t]=e},this.removeFloatingConnection=function(t){delete c[t]},this.newConnection=function(n){var i=n.sourceId,s=n.targetId,o=n.endpoints,a=!0,l=function(l,u,c,h,d){i===s&&c.isContinuous&&(n._jsPlumb.instance.removeElement(o[1].canvas),a=!1),t.addToList(r,h,[d,u,c.constructor===e.DynamicAnchor])};l(0,o[0],o[0].anchor,s,n),a&&l(0,o[1],o[1].anchor,i,n)};var f=function(e){!function(e,n){if(e){var i=function(t){return t[4]===n};t.removeWithFunction(e.top,i),t.removeWithFunction(e.left,i),t.removeWithFunction(e.bottom,i),t.removeWithFunction(e.right,i)}}(l[e.elementId],e.id)};this.connectionDetached=function(e,n){var i=e.connection||e,s=e.sourceId,o=e.targetId,l=i.endpoints,u=function(e,n,i,s,o){t.removeWithFunction(r[s],function(t){return t[0].id===o.id})};u(0,l[1],l[1].anchor,s,i),u(0,l[0],l[0].anchor,o,i),i.floatingId&&(u(i.floatingIndex,i.floatingEndpoint,i.floatingEndpoint.anchor,i.floatingId,i),f(i.floatingEndpoint)),f(i.endpoints[0]),f(i.endpoints[1]),n||(a.redraw(i.sourceId),i.targetId!==i.sourceId&&a.redraw(i.targetId))},this.add=function(e,n){t.addToList(i,n,e)},this.changeId=function(t,e){r[e]=r[t],i[e]=i[t],delete r[t],delete i[t]},this.getConnectionsFor=function(t){return r[t]||[]},this.getEndpointsFor=function(t){return i[t]||[]},this.deleteEndpoint=function(e){t.removeWithFunction(i[e.elementId],function(t){return t.id===e.id}),f(e)},this.clearFor=function(t){delete i[t],i[t]=[]};var g=function(e,i,s,o,r,a,l,u,c,h,d,p){var f,g,m=-1,v=o.endpoints[l],b=v.id,y=[1,0][l],P=[[i,s],o,r,a,b],_=e[c],x=v._continuousAnchorEdge?e[v._continuousAnchorEdge]:null;if(x){var C=t.findWithFunction(x,function(t){return t[4]===b});if(-1!==C)for(x.splice(C,1),f=0;f<x.length;f++)g=x[f][1],t.addWithFunction(d,g,function(t){return t.id===g.id}),t.addWithFunction(p,x[f][1].endpoints[l],function(t){return t.id===g.endpoints[l].id}),t.addWithFunction(p,x[f][1].endpoints[y],function(t){return t.id===g.endpoints[y].id})}for(f=0;f<_.length;f++)g=_[f][1],1===n.idx&&_[f][3]===a&&-1===m&&(m=f),t.addWithFunction(d,g,function(t){return t.id===g.id}),t.addWithFunction(p,_[f][1].endpoints[l],function(t){return t.id===g.endpoints[l].id}),t.addWithFunction(p,_[f][1].endpoints[y],function(t){return t.id===g.endpoints[y].id});var j=u?-1!==m?m:0:_.length;_.splice(j,0,P),v._continuousAnchorEdge=c};this.updateOtherEndpoint=function(n,i,s,o){var a=t.findWithFunction(r[n],function(t){return t[0].id===o.id}),l=t.findWithFunction(r[i],function(t){return t[0].id===o.id});-1!==a&&(r[n][a][0]=o,r[n][a][1]=o.endpoints[1],r[n][a][2]=o.endpoints[1].anchor.constructor===e.DynamicAnchor),l>-1&&(r[i].splice(l,1),t.addToList(r,s,[o,o.endpoints[0],o.endpoints[0].anchor.constructor===e.DynamicAnchor])),o.updateConnectedClass()},this.sourceChanged=function(n,i,s,o){if(n!==i){s.sourceId=i,s.source=o,t.removeWithFunction(r[n],function(t){return t[0].id===s.id});var a=t.findWithFunction(r[s.targetId],function(t){return t[0].id===s.id});a>-1&&(r[s.targetId][a][0]=s,r[s.targetId][a][1]=s.endpoints[0],r[s.targetId][a][2]=s.endpoints[0].anchor.constructor===e.DynamicAnchor),t.addToList(r,i,[s,s.endpoints[1],s.endpoints[1].anchor.constructor===e.DynamicAnchor]),s.endpoints[1].anchor.isContinuous&&(s.source===s.target?s._jsPlumb.instance.removeElement(s.endpoints[1].canvas):null==s.endpoints[1].canvas.parentNode&&s._jsPlumb.instance.appendElement(s.endpoints[1].canvas)),s.updateConnectedClass()}},this.rehomeEndpoint=function(t,e,n){var s=i[e]||[],o=u.getId(n);if(o!==e){var r=s.indexOf(t);if(r>-1){var l=s.splice(r,1)[0];a.add(l,o)}}for(var c=0;c<t.connections.length;c++)t.connections[c].sourceId===e?a.sourceChanged(e,t.elementId,t.connections[c],t.element):t.connections[c].targetId===e&&(t.connections[c].targetId=t.elementId,t.connections[c].target=t.element,a.updateOtherEndpoint(t.connections[c].sourceId,e,t.elementId,t.connections[c]))},this.redraw=function(n,s,o,a,h,d){if(!u.isSuspendDrawing()){var f=i[n]||[],m=r[n]||[],v=[],b=[],y=[];o=o||u.timestamp(),a=a||{left:0,top:0},s&&(s={left:s.left+a.left,top:s.top+a.top});for(var P=u.updateOffset({elId:n,offset:s,recalc:!1,timestamp:o}),_={},x=0;x<m.length;x++){var C=m[x][0],j=C.sourceId,E=C.targetId,S=C.endpoints[0].anchor.isContinuous,D=C.endpoints[1].anchor.isContinuous;if(S||D){var w=j+"_"+E,I=_[w],A=C.sourceId===n?1:0;S&&!l[j]&&(l[j]={top:[],right:[],bottom:[],left:[]}),D&&!l[E]&&(l[E]={top:[],right:[],bottom:[],left:[]}),n!==E&&u.updateOffset({elId:E,timestamp:o}),n!==j&&u.updateOffset({elId:j,timestamp:o});var k=u.getCachedData(E),M=u.getCachedData(j);E===j&&(S||D)?(g(l[j],-Math.PI/2,0,C,!1,E,0,!1,"top",0,v,b),g(l[E],-Math.PI/2,0,C,!1,j,1,!1,"top",0,v,b)):(I||(I=this.calculateOrientation(j,E,M.o,k.o,C.endpoints[0].anchor,C.endpoints[1].anchor,C),_[w]=I),S&&g(l[j],I.theta,0,C,!1,E,0,!1,I.a[0],0,v,b),D&&g(l[E],I.theta2,-1,C,!0,j,1,!0,I.a[1],0,v,b)),S&&t.addWithFunction(y,j,function(t){return t===j}),D&&t.addWithFunction(y,E,function(t){return t===E}),t.addWithFunction(v,C,function(t){return t.id===C.id}),(S&&0===A||D&&1===A)&&t.addWithFunction(b,C.endpoints[A],function(t){return t.id===C.endpoints[A].id})}}for(x=0;x<f.length;x++)0===f[x].connections.length&&f[x].anchor.isContinuous&&(l[n]||(l[n]={top:[],right:[],bottom:[],left:[]}),g(l[n],-Math.PI/2,0,{endpoints:[f[x],f[x]],paint:function(){}},!1,n,0,!1,f[x].anchor.getDefaultFace(),0,v,b),t.addWithFunction(y,n,function(t){return t===n}));for(x=0;x<y.length;x++)p(y[x],l[y[x]]);for(x=0;x<f.length;x++)f[x].paint({timestamp:o,offset:P,dimensions:P.s,recalc:!0!==d});for(x=0;x<b.length;x++){var O=u.getCachedData(b[x].elementId);b[x].paint({timestamp:null,offset:O,dimensions:O.s})}for(x=0;x<m.length;x++){var T=m[x][1];if(T.anchor.constructor===e.DynamicAnchor){T.paint({elementWithPrecedence:n,timestamp:o}),t.addWithFunction(v,m[x][0],function(t){return t.id===m[x][0].id});for(var L=0;L<T.connections.length;L++)T.connections[L]!==m[x][0]&&t.addWithFunction(v,T.connections[L],function(t){return t.id===T.connections[L].id})}else t.addWithFunction(v,m[x][0],function(t){return t.id===m[x][0].id})}var F=c[n];for(F&&F.paint({timestamp:o,recalc:!1,elId:n}),x=0;x<v.length;x++)v[x].paint({elId:n,timestamp:null,recalc:!1,clearEdits:h})}};u.continuousAnchorFactory={get:function(e){return new function(e){t.EventGenerator.apply(this),this.type="Continuous",this.isDynamic=!0,this.isContinuous=!0;for(var n=e.faces||["top","right","bottom","left"],i=!(!1===e.clockwise),r={},a={top:"bottom",right:"left",left:"right",bottom:"top"},l={top:"right",right:"bottom",left:"top",bottom:"left"},u={top:"left",right:"top",left:"bottom",bottom:"right"},c=i?l:u,h=i?u:l,d=e.cssClass||"",p=null,f=null,g=["left","right"],m=["top","bottom"],v=null,b=0;b<n.length;b++)r[n[b]]=!0;this.getDefaultFace=function(){return 0===n.length?"top":n[0]},this.isRelocatable=function(){return!0},this.isSnapOnRelocate=function(){return!0},this.verifyEdge=function(t){return r[t]?t:r[a[t]]?a[t]:r[c[t]]?c[t]:r[h[t]]?h[t]:t},this.isEdgeSupported=function(t){return null==v?null==f?!0===r[t]:f===t:-1!==v.indexOf(t)},this.setCurrentFace=function(t,e){p=t,e&&null!=f&&(f=p)},this.getCurrentFace=function(){return p},this.getSupportedFaces=function(){var t=[];for(var e in r)r[e]&&t.push(e);return t},this.lock=function(){f=p},this.unlock=function(){f=null},this.isLocked=function(){return null!=f},this.lockCurrentAxis=function(){null!=p&&(v="left"===p||"right"===p?g:m)},this.unlockCurrentAxis=function(){v=null},this.compute=function(t){return s[t.element.id]||[0,0]},this.getCurrentLocation=function(t){return s[t.element.id]||[0,0]},this.getOrientation=function(t){return o[t.id]||[0,0]},this.getCssClass=function(){return d}}(e)},clear:function(t){delete s[t]}}},e.AnchorManager.prototype.calculateOrientation=function(t,e,n,i,s,o){var r=["left","top","right","bottom"];if(t===e)return{orientation:"identity",a:["top","top"]};var a=Math.atan2(i.centery-n.centery,i.centerx-n.centerx),l=Math.atan2(n.centery-i.centery,n.centerx-i.centerx),u=[],c={};!function(t,e){for(var n=0;n<t.length;n++)c[t[n]]={left:[e[n].left,e[n].centery],right:[e[n].right,e[n].centery],top:[e[n].centerx,e[n].top],bottom:[e[n].centerx,e[n].bottom]}}(["source","target"],[n,i]);for(var h=0;h<r.length;h++)for(var d=0;d<r.length;d++)u.push({source:r[h],target:r[d],dist:Biltong.lineLength(c.source[r[h]],c.target[r[d]])});u.sort(function(t,e){return t.dist<e.dist?-1:t.dist>e.dist?1:0});for(var p=u[0].source,f=u[0].target,g=0;g<u.length&&(p=!s.isContinuous||s.isEdgeSupported(u[g].source)?u[g].source:null,f=!o.isContinuous||o.isEdgeSupported(u[g].target)?u[g].target:null,null==p||null==f);g++);return s.isContinuous&&s.setCurrentFace(p),o.isContinuous&&o.setCurrentFace(f),{a:[p,f],theta:a,theta2:l}},e.Anchor=function(e){this.x=e.x||0,this.y=e.y||0,this.elementId=e.elementId,this.cssClass=e.cssClass||"",this.userDefinedLocation=null,this.orientation=e.orientation||[0,0],this.lastReturnValue=null,this.offsets=e.offsets||[0,0],this.timestamp=null;var n=!1!==e.relocatable;this.isRelocatable=function(){return n},this.setRelocatable=function(t){n=t};var i=!1!==e.snapOnRelocate;this.isSnapOnRelocate=function(){return i};var s=!1;this.lock=function(){s=!0},this.unlock=function(){s=!1},this.isLocked=function(){return s},t.EventGenerator.apply(this),this.compute=function(t){var e=t.xy,n=t.wh,i=t.timestamp;return t.clearUserDefinedLocation&&(this.userDefinedLocation=null),i&&i===this.timestamp?this.lastReturnValue:(null!=this.userDefinedLocation?this.lastReturnValue=this.userDefinedLocation:this.lastReturnValue=[e[0]+this.x*n[0]+this.offsets[0],e[1]+this.y*n[1]+this.offsets[1],this.x,this.y],this.timestamp=i,this.lastReturnValue)},this.getCurrentLocation=function(t){return t=t||{},null==this.lastReturnValue||null!=t.timestamp&&this.timestamp!==t.timestamp?this.compute(t):this.lastReturnValue},this.setPosition=function(t,e,n,i,o){s&&!o||(this.x=t,this.y=e,this.orientation=[n,i],this.lastReturnValue=null)}},t.extend(e.Anchor,t.EventGenerator,{equals:function(t){if(!t)return!1;var e=t.getOrientation(),n=this.getOrientation();return this.x===t.x&&this.y===t.y&&this.offsets[0]===t.offsets[0]&&this.offsets[1]===t.offsets[1]&&n[0]===e[0]&&n[1]===e[1]},getUserDefinedLocation:function(){return this.userDefinedLocation},setUserDefinedLocation:function(t){this.userDefinedLocation=t},clearUserDefinedLocation:function(){this.userDefinedLocation=null},getOrientation:function(){return this.orientation},getCssClass:function(){return this.cssClass}}),e.FloatingAnchor=function(t){e.Anchor.apply(this,arguments);var n=t.reference,i=t.referenceCanvas,s=e.getSize(i),o=null,r=null;this.orientation=null,this.x=0,this.y=0,this.isFloating=!0,this.compute=function(t){var e=t.xy,n=[e[0]+s[0]/2,e[1]+s[1]/2];return r=n,n},this.getOrientation=function(t){if(o)return o;var e=n.getOrientation(t);return[0*Math.abs(e[0])*-1,0*Math.abs(e[1])*-1]},this.over=function(t,e){o=t.getOrientation(e)},this.out=function(){o=null},this.getCurrentLocation=function(t){return null==r?this.compute(t):r}},t.extend(e.FloatingAnchor,e.Anchor);e.DynamicAnchor=function(t){e.Anchor.apply(this,arguments),this.isDynamic=!0,this.anchors=[],this.elementId=t.elementId,this.jsPlumbInstance=t.jsPlumbInstance;for(var n=0;n<t.anchors.length;n++)this.anchors[n]=(i=t.anchors[n],s=this.jsPlumbInstance,o=this.elementId,i.constructor===e.Anchor?i:s.makeAnchor(i,o,s));var i,s,o;this.getAnchors=function(){return this.anchors};var r=this.anchors.length>0?this.anchors[0]:null,a=r,l=this,u=function(t,e,n,i,s){var o=i[0]+t.x*s[0],r=i[1]+t.y*s[1],a=i[0]+s[0]/2,l=i[1]+s[1]/2;return Math.sqrt(Math.pow(e-o,2)+Math.pow(n-r,2))+Math.sqrt(Math.pow(a-o,2)+Math.pow(l-r,2))},c=t.selector||function(t,e,n,i,s){for(var o=n[0]+i[0]/2,r=n[1]+i[1]/2,a=-1,l=1/0,c=0;c<s.length;c++){var h=u(s[c],o,r,t,e);h<l&&(a=c+0,l=h)}return s[a]};this.compute=function(t){var e=t.xy,n=t.wh,i=t.txy,s=t.twh;this.timestamp=t.timestamp;var o=l.getUserDefinedLocation();return null!=o?o:this.isLocked()||null==i||null==s?r.compute(t):(t.timestamp=null,r=c(e,n,i,s,this.anchors),this.x=r.x,this.y=r.y,r!==a&&this.fire("anchorChanged",r),a=r,r.compute(t))},this.getCurrentLocation=function(t){return this.getUserDefinedLocation()||(null!=r?r.getCurrentLocation(t):null)},this.getOrientation=function(t){return null!=r?r.getOrientation(t):[0,0]},this.over=function(t,e){null!=r&&r.over(t,e)},this.out=function(){null!=r&&r.out()},this.setAnchor=function(t){r=t},this.getCssClass=function(){return r&&r.getCssClass()||""},this.setAnchorCoordinates=function(t){var e=jsPlumbUtil.findWithFunction(this.anchors,function(e){return e.x===t[0]&&e.y===t[1]});return-1!==e&&(this.setAnchor(this.anchors[e]),!0)}},t.extend(e.DynamicAnchor,e.Anchor);var n=function(t,n,i,s,o,r){e.Anchors[o]=function(e){var a=e.jsPlumbInstance.makeAnchor([t,n,i,s,0,0],e.elementId,e.jsPlumbInstance);return a.type=o,r&&r(a,e),a}};n(.5,0,0,-1,"TopCenter"),n(.5,1,0,1,"BottomCenter"),n(0,.5,-1,0,"LeftMiddle"),n(1,.5,1,0,"RightMiddle"),n(.5,0,0,-1,"Top"),n(.5,1,0,1,"Bottom"),n(0,.5,-1,0,"Left"),n(1,.5,1,0,"Right"),n(.5,.5,0,0,"Center"),n(1,0,0,-1,"TopRight"),n(1,1,0,1,"BottomRight"),n(0,0,0,-1,"TopLeft"),n(0,1,0,1,"BottomLeft"),e.Defaults.DynamicAnchors=function(t){return t.jsPlumbInstance.makeAnchors(["TopCenter","RightMiddle","BottomCenter","LeftMiddle"],t.elementId,t.jsPlumbInstance)},e.Anchors.AutoDefault=function(t){var n=t.jsPlumbInstance.makeDynamicAnchor(e.Defaults.DynamicAnchors(t));return n.type="AutoDefault",n};var i=function(t,n){e.Anchors[t]=function(e){var i=e.jsPlumbInstance.makeAnchor(["Continuous",{faces:n}],e.elementId,e.jsPlumbInstance);return i.type=t,i}};e.Anchors.Continuous=function(t){return t.jsPlumbInstance.continuousAnchorFactory.get(t)},i("ContinuousLeft",["left"]),i("ContinuousTop",["top"]),i("ContinuousBottom",["bottom"]),i("ContinuousRight",["right"]),n(0,0,0,0,"Assign",function(t,e){var n=e.position||"Fixed";t.positionFinder=n.constructor===String?e.jsPlumbInstance.AnchorPositionFinders[n]:n,t.constructorParams=e}),this.jsPlumbInstance.prototype.AnchorPositionFinders={Fixed:function(t,e,n){return[(t.left-e.left)/n[0],(t.top-e.top)/n[1]]},Grid:function(t,e,n,i){var s=t.left-e.left,o=t.top-e.top,r=n[0]/i.grid[0],a=n[1]/i.grid[1],l=Math.floor(s/r),u=Math.floor(o/a);return[(l*r+r/2)/n[0],(u*a+a/2)/n[1]]}},e.Anchors.Perimeter=function(t){var e=(t=t||{}).anchorCount||60,n=t.shape;if(!n)throw new Error("no shape supplied to Perimeter Anchor type");var i=function(){for(var t=2*Math.PI/e,n=0,i=[],s=0;s<e;s++){var o=.5+.5*Math.sin(n),r=.5+.5*Math.cos(n);i.push([o,r,0,0]),n+=t}return i},s=function(t){for(var n=e/t.length,i=[],s=function(t,s,o,r,a,l,u){for(var c=(o-t)/(n=e*a),h=(r-s)/n,d=0;d<n;d++)i.push([t+c*d,s+h*d,null==l?0:l,null==u?0:u])},o=0;o<t.length;o++)s.apply(null,t[o]);return i},o=function(t){for(var e=[],n=0;n<t.length;n++)e.push([t[n][0],t[n][1],t[n][2],t[n][3],1/t.length,t[n][4],t[n][5]]);return s(e)},r=function(){return o([[0,0,1,0,0,-1],[1,0,1,1,1,0],[1,1,0,1,0,1],[0,1,0,0,-1,0]])},a={Circle:i,Ellipse:i,Diamond:function(){return o([[.5,0,1,.5],[1,.5,.5,1],[.5,1,0,.5],[0,.5,.5,0]])},Rectangle:r,Square:r,Triangle:function(){return o([[.5,0,1,1],[1,1,0,1],[0,1,.5,0]])},Path:function(t){for(var e=t.points,n=[],i=0,o=0;o<e.length-1;o++){var r=Math.sqrt(Math.pow(e[o][2]-e[o][0])+Math.pow(e[o][3]-e[o][1]));i+=r,n.push([e[o][0],e[o][1],e[o+1][0],e[o+1][1],r])}for(var a=0;a<n.length;a++)n[a][4]=n[a][4]/i;return s(n)}};if(!a[n])throw new Error("Shape ["+n+"] is unknown by Perimeter Anchor type");var l=a[n](t);t.rotation&&(l=function(t,e){for(var n=[],i=e/180*Math.PI,s=0;s<t.length;s++){var o=t[s][0]-.5,r=t[s][1]-.5;n.push([o*Math.cos(i)-r*Math.sin(i)+.5,o*Math.sin(i)+r*Math.cos(i)+.5,t[s][2],t[s][3]])}return n}(l,t.rotation));var u=t.jsPlumbInstance.makeDynamicAnchor(l);return u.type="Perimeter",u}}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=t.Biltong;e.Segments={AbstractSegment:function(t){this.params=t,this.findClosestPointOnPath=function(t,e){return{d:1/0,x:null,y:null,l:null}},this.getBounds=function(){return{minX:Math.min(t.x1,t.x2),minY:Math.min(t.y1,t.y2),maxX:Math.max(t.x1,t.x2),maxY:Math.max(t.y1,t.y2)}},this.lineIntersection=function(t,e,n,i){return[]},this.boxIntersection=function(t,e,n,i){var s=[];return s.push.apply(s,this.lineIntersection(t,e,t+n,e)),s.push.apply(s,this.lineIntersection(t+n,e,t+n,e+i)),s.push.apply(s,this.lineIntersection(t+n,e+i,t,e+i)),s.push.apply(s,this.lineIntersection(t,e+i,t,e)),s},this.boundingBoxIntersection=function(t){return this.boxIntersection(t.x,t.y,t.w,t.y)}},Straight:function(t){var n,s,o,r,a,l,u;e.Segments.AbstractSegment.apply(this,arguments);this.type="Straight",this.getLength=function(){return n},this.getGradient=function(){return s},this.getCoordinates=function(){return{x1:r,y1:l,x2:a,y2:u}},this.setCoordinates=function(t){r=t.x1,l=t.y1,a=t.x2,u=t.y2,n=Math.sqrt(Math.pow(a-r,2)+Math.pow(u-l,2)),s=i.gradient({x:r,y:l},{x:a,y:u}),o=-1/s},this.setCoordinates({x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2}),this.getBounds=function(){return{minX:Math.min(r,a),minY:Math.min(l,u),maxX:Math.max(r,a),maxY:Math.max(l,u)}},this.pointOnPath=function(t,e){if(0!==t||e){if(1!==t||e){var s=e?t>0?t:n+t:t*n;return i.pointOnLine({x:r,y:l},{x:a,y:u},s)}return{x:a,y:u}}return{x:r,y:l}},this.gradientAtPoint=function(t){return s},this.pointAlongPathFrom=function(t,e,n){var s=this.pointOnPath(t,n),o=e<=0?{x:r,y:l}:{x:a,y:u};return e<=0&&Math.abs(e)>1&&(e*=-1),i.pointOnLine(s,o,e)};var c=function(t,e,n){return n>=Math.min(t,e)&&n<=Math.max(t,e)},h=function(t,e,n){return Math.abs(n-t)<Math.abs(n-e)?t:e};this.findClosestPointOnPath=function(t,e){var d={d:1/0,x:null,y:null,l:null,x1:r,x2:a,y1:l,y2:u};if(0===s)d.y=l,d.x=c(r,a,t)?t:h(r,a,t);else if(s===1/0||s===-1/0)d.x=r,d.y=c(l,u,e)?e:h(l,u,e);else{var p=l-s*r,f=(e-o*t-p)/(s-o),g=s*f+p;d.x=c(r,a,f)?f:h(r,a,f),d.y=c(l,u,g)?g:h(l,u,g)}var m=i.lineLength([d.x,d.y],[r,l]);return d.d=i.lineLength([t,e],[d.x,d.y]),d.l=m/n,d};var d=function(t,e,n){return n>e?e<=t&&t<=n:e>=t&&t>=n};this.lineIntersection=function(t,e,n,o){var c=Math.abs(i.gradient({x:t,y:e},{x:n,y:o})),h=Math.abs(s),p=h===1/0?r:l-h*r,f=[],g=c===1/0?t:e-c*t;if(c!==h)if(c===1/0&&0===h)d(t,r,a)&&d(l,e,o)&&(f=[t,l]);else if(0===c&&h===1/0)d(e,l,u)&&d(r,t,n)&&(f=[r,e]);else{var m,v;c===1/0?d(m=t,r,a)&&d(v=h*t+p,e,o)&&(f=[m,v]):0===c?d(v=e,l,u)&&d(m=(e-p)/h,t,n)&&(f=[m,v]):(v=h*(m=(g-p)/(h-c))+p,d(m,r,a)&&d(v,l,u)&&(f=[m,v]))}return f},this.boxIntersection=function(t,e,n,i){var s=[];return s.push.apply(s,this.lineIntersection(t,e,t+n,e)),s.push.apply(s,this.lineIntersection(t+n,e,t+n,e+i)),s.push.apply(s,this.lineIntersection(t+n,e+i,t,e+i)),s.push.apply(s,this.lineIntersection(t,e+i,t,e)),s},this.boundingBoxIntersection=function(t){return this.boxIntersection(t.x,t.y,t.w,t.h)}},Arc:function(t){e.Segments.AbstractSegment.apply(this,arguments);var n=function(e,n){return i.theta([t.cx,t.cy],[e,n])},s=2*Math.PI;this.radius=t.r,this.anticlockwise=t.ac,this.type="Arc",t.startAngle&&t.endAngle?(this.startAngle=t.startAngle,this.endAngle=t.endAngle,this.x1=t.cx+this.radius*Math.cos(t.startAngle),this.y1=t.cy+this.radius*Math.sin(t.startAngle),this.x2=t.cx+this.radius*Math.cos(t.endAngle),this.y2=t.cy+this.radius*Math.sin(t.endAngle)):(this.startAngle=n(t.x1,t.y1),this.endAngle=n(t.x2,t.y2),this.x1=t.x1,this.y1=t.y1,this.x2=t.x2,this.y2=t.y2),this.endAngle<0&&(this.endAngle+=s),this.startAngle<0&&(this.startAngle+=s);var o=this.endAngle<this.startAngle?this.endAngle+s:this.endAngle;this.sweep=Math.abs(o-this.startAngle),this.anticlockwise&&(this.sweep=s-this.sweep);var r=2*Math.PI*this.radius,a=this.sweep/s,l=r*a;this.getLength=function(){return l},this.getBounds=function(){return{minX:t.cx-t.r,maxX:t.cx+t.r,minY:t.cy-t.r,maxY:t.cy+t.r}};var u=function(t){var e=Math.floor(t),n=Math.ceil(t);return t-e<1e-10?e:n-t<1e-10?n:t};this.pointOnPath=function(e,n){if(0===e)return{x:this.x1,y:this.y1,theta:this.startAngle};if(1===e)return{x:this.x2,y:this.y2,theta:this.endAngle};n&&(e/=l);var i=function(t,e){if(t.anticlockwise){var n=t.startAngle<t.endAngle?t.startAngle+s:t.startAngle;return n-Math.abs(n-t.endAngle)*e}var i=t.endAngle<t.startAngle?t.endAngle+s:t.endAngle,o=Math.abs(i-t.startAngle);return t.startAngle+o*e}(this,e),o=t.cx+t.r*Math.cos(i),r=t.cy+t.r*Math.sin(i);return{x:u(o),y:u(r),theta:i}},this.gradientAtPoint=function(e,n){var s=this.pointOnPath(e,n),o=i.normal([t.cx,t.cy],[s.x,s.y]);return this.anticlockwise||o!==1/0&&o!==-1/0||(o*=-1),o},this.pointAlongPathFrom=function(e,n,i){var s=this.pointOnPath(e,i),o=n/r*2*Math.PI,a=this.anticlockwise?-1:1,l=s.theta+a*o;return{x:t.cx+this.radius*Math.cos(l),y:t.cy+this.radius*Math.sin(l)}}},Bezier:function(n){this.curve=[{x:n.x1,y:n.y1},{x:n.cp1x,y:n.cp1y},{x:n.cp2x,y:n.cp2y},{x:n.x2,y:n.y2}];e.Segments.AbstractSegment.apply(this,arguments);this.bounds={minX:Math.min(n.x1,n.x2,n.cp1x,n.cp2x),minY:Math.min(n.y1,n.y2,n.cp1y,n.cp2y),maxX:Math.max(n.x1,n.x2,n.cp1x,n.cp2x),maxY:Math.max(n.y1,n.y2,n.cp1y,n.cp2y)},this.type="Bezier";var i=function(e,n,i){return i&&(n=t.jsBezier.locationAlongCurveFrom(e,n>0?0:1,n)),n};this.pointOnPath=function(e,n){return e=i(this.curve,e,n),t.jsBezier.pointOnCurve(this.curve,e)},this.gradientAtPoint=function(e,n){return e=i(this.curve,e,n),t.jsBezier.gradientAtPoint(this.curve,e)},this.pointAlongPathFrom=function(e,n,s){return e=i(this.curve,e,s),t.jsBezier.pointAlongCurveFrom(this.curve,e,n)},this.getLength=function(){return t.jsBezier.getLength(this.curve)},this.getBounds=function(){return this.bounds},this.findClosestPointOnPath=function(e,n){var i=t.jsBezier.nearestPointOnCurve({x:e,y:n},this.curve);return{d:Math.sqrt(Math.pow(i.point.x-e,2)+Math.pow(i.point.y-n,2)),x:i.point.x,y:i.point.y,l:1-i.location,s:this}},this.lineIntersection=function(e,n,i,s){return t.jsBezier.lineIntersection(e,n,i,s,this.curve)}}},e.SegmentRenderer={getPath:function(t,e){return{Straight:function(e){var n=t.getCoordinates();return(e?"M "+n.x1+" "+n.y1+" ":"")+"L "+n.x2+" "+n.y2},Bezier:function(e){var n=t.params;return(e?"M "+n.x2+" "+n.y2+" ":"")+"C "+n.cp2x+" "+n.cp2y+" "+n.cp1x+" "+n.cp1y+" "+n.x1+" "+n.y1},Arc:function(e){var n=t.params,i=t.sweep>Math.PI?1:0,s=t.anticlockwise?0:1;return(e?"M"+t.x1+" "+t.y1+" ":"")+"A "+t.radius+" "+n.r+" 0 "+i+","+s+" "+t.x2+" "+t.y2}}[t.type](e)}};var s=function(){this.resetBounds=function(){this.bounds={minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}},this.resetBounds()};e.Connectors.AbstractConnector=function(t){s.apply(this,arguments);var o=[],r=0,a=[],l=[],u=t.stub||0,c=n.isArray(u)?u[0]:u,h=n.isArray(u)?u[1]:u,d=t.gap||0,p=n.isArray(d)?d[0]:d,f=n.isArray(d)?d[1]:d,g=null,m=null;this.getPathData=function(){for(var t="",n=0;n<o.length;n++)t+=e.SegmentRenderer.getPath(o[n],0===n),t+=" ";return t},this.findSegmentForPoint=function(t,e){for(var n={d:1/0,s:null,x:null,y:null,l:null},i=0;i<o.length;i++){var s=o[i].findClosestPointOnPath(t,e);s.d<n.d&&(n.d=s.d,n.l=s.l,n.x=s.x,n.y=s.y,n.s=o[i],n.x1=s.x1,n.x2=s.x2,n.y1=s.y1,n.y2=s.y2,n.index=i,n.connectorLocation=a[i][0]+s.l*(a[i][1]-a[i][0]))}return n},this.lineIntersection=function(t,e,n,i){for(var s=[],r=0;r<o.length;r++)s.push.apply(s,o[r].lineIntersection(t,e,n,i));return s},this.boxIntersection=function(t,e,n,i){for(var s=[],r=0;r<o.length;r++)s.push.apply(s,o[r].boxIntersection(t,e,n,i));return s},this.boundingBoxIntersection=function(t){for(var e=[],n=0;n<o.length;n++)e.push.apply(e,o[n].boundingBoxIntersection(t));return e};var v=function(t,e){e&&(t=t>0?t/r:(r+t)/r);for(var n=a.length-1,i=1,s=0;s<a.length;s++)if(a[s][1]>=t){n=s,i=1===t?1:0===t?0:(t-a[s][0])/l[s];break}return{segment:o[n],proportion:i,index:n}};this.setSegments=function(t){g=[],r=0;for(var e=0;e<t.length;e++)g.push(t[e]),r+=t[e].getLength()},this.getLength=function(){return r};var b=function(t){this.strokeWidth=t.strokeWidth;var e=i.quadrant(t.sourcePos,t.targetPos),n=t.targetPos[0]<t.sourcePos[0],s=t.targetPos[1]<t.sourcePos[1],o=t.strokeWidth||1,r=t.sourceEndpoint.anchor.getOrientation(t.sourceEndpoint),a=t.targetEndpoint.anchor.getOrientation(t.targetEndpoint),l=n?t.targetPos[0]:t.sourcePos[0],u=s?t.targetPos[1]:t.sourcePos[1],d=Math.abs(t.targetPos[0]-t.sourcePos[0]),g=Math.abs(t.targetPos[1]-t.sourcePos[1]);if(0===r[0]&&0===r[1]||0===a[0]&&0===a[1]){var m=d>g?0:1,v=[1,0][m];a=[],(r=[])[m]=t.sourcePos[m]>t.targetPos[m]?-1:1,a[m]=t.sourcePos[m]>t.targetPos[m]?1:-1,r[v]=0,a[v]=0}var b=n?d+p*r[0]:p*r[0],y=s?g+p*r[1]:p*r[1],P=n?f*a[0]:d+f*a[0],_=s?f*a[1]:g+f*a[1],x=r[0]*a[0]+r[1]*a[1],C={sx:b,sy:y,tx:P,ty:_,lw:o,xSpan:Math.abs(P-b),ySpan:Math.abs(_-y),mx:(b+P)/2,my:(y+_)/2,so:r,to:a,x:l,y:u,w:d,h:g,segment:e,startStubX:b+r[0]*c,startStubY:y+r[1]*c,endStubX:P+a[0]*h,endStubY:_+a[1]*h,isXGreaterThanStubTimes2:Math.abs(b-P)>c+h,isYGreaterThanStubTimes2:Math.abs(y-_)>c+h,opposite:-1===x,perpendicular:0===x,orthogonal:1===x,sourceAxis:0===r[0]?"y":"x",points:[l,u,d,g,b,y,P,_],stubs:[c,h]};return C.anchorOrientation=C.opposite?"opposite":C.orthogonal?"orthogonal":"perpendicular",C};this.getSegments=function(){return o},this.updateBounds=function(t){var e=t.getBounds();this.bounds.minX=Math.min(this.bounds.minX,e.minX),this.bounds.maxX=Math.max(this.bounds.maxX,e.maxX),this.bounds.minY=Math.min(this.bounds.minY,e.minY),this.bounds.maxY=Math.max(this.bounds.maxY,e.maxY)};return this.pointOnPath=function(t,e){var n=v(t,e);return n.segment&&n.segment.pointOnPath(n.proportion,!1)||[0,0]},this.gradientAtPoint=function(t,e){var n=v(t,e);return n.segment&&n.segment.gradientAtPoint(n.proportion,!1)||0},this.pointAlongPathFrom=function(t,e,n){var i=v(t,n);return i.segment&&i.segment.pointAlongPathFrom(i.proportion,e,!1)||[0,0]},this.compute=function(t){m=b.call(this,t),r=o.length=a.length=l.length=0,this._compute(m,t),this.x=m.points[0],this.y=m.points[1],this.w=m.points[2],this.h=m.points[3],this.segment=m.segment,function(){for(var t=0,e=0;e<o.length;e++){var n=o[e].getLength();l[e]=n/r,a[e]=[t,t+=n/r]}}()},{addSegment:function(t,n,i){if(i.x1!==i.x2||i.y1!==i.y2){var s=new e.Segments[n](i);o.push(s),r+=s.getLength(),t.updateBounds(s)}},prepareCompute:b,sourceStub:c,targetStub:h,maxStub:Math.max(c,h),sourceGap:p,targetGap:f,maxGap:Math.max(p,f)}},n.extend(e.Connectors.AbstractConnector,s),e.Endpoints.AbstractEndpoint=function(t){return s.apply(this,arguments),{compute:this.compute=function(t,e,n,i){var s=this._compute.apply(this,arguments);return this.x=s[0],this.y=s[1],this.w=s[2],this.h=s[3],this.bounds.minX=this.x,this.bounds.minY=this.y,this.bounds.maxX=this.x+this.w,this.bounds.maxY=this.y+this.h,s},cssClass:t.cssClass}},n.extend(e.Endpoints.AbstractEndpoint,s),e.Endpoints.Dot=function(t){this.type="Dot";e.Endpoints.AbstractEndpoint.apply(this,arguments);t=t||{},this.radius=t.radius||10,this.defaultOffset=.5*this.radius,this.defaultInnerRadius=this.radius/3,this._compute=function(t,e,n,i){this.radius=n.radius||this.radius;var s=t[0]-this.radius,o=t[1]-this.radius,r=2*this.radius,a=2*this.radius;if(n.stroke){var l=n.strokeWidth||1;s-=l,o-=l,r+=2*l,a+=2*l}return[s,o,r,a,this.radius]}},n.extend(e.Endpoints.Dot,e.Endpoints.AbstractEndpoint),e.Endpoints.Rectangle=function(t){this.type="Rectangle";e.Endpoints.AbstractEndpoint.apply(this,arguments);t=t||{},this.width=t.width||20,this.height=t.height||20,this._compute=function(t,e,n,i){var s=n.width||this.width,o=n.height||this.height;return[t[0]-s/2,t[1]-o/2,s,o]}},n.extend(e.Endpoints.Rectangle,e.Endpoints.AbstractEndpoint);var o=function(t){e.jsPlumbUIComponent.apply(this,arguments),this._jsPlumb.displayElements=[]};n.extend(o,e.jsPlumbUIComponent,{getDisplayElements:function(){return this._jsPlumb.displayElements},appendDisplayElement:function(t){this._jsPlumb.displayElements.push(t)}}),e.Endpoints.Image=function(i){this.type="Image",o.apply(this,arguments),e.Endpoints.AbstractEndpoint.apply(this,arguments);var s=i.onload,r=i.src||i.url,a=i.cssClass?" "+i.cssClass:"";this._jsPlumb.img=new Image,this._jsPlumb.ready=!1,this._jsPlumb.initialized=!1,this._jsPlumb.deleted=!1,this._jsPlumb.widthToUse=i.width,this._jsPlumb.heightToUse=i.height,this._jsPlumb.endpoint=i.endpoint,this._jsPlumb.img.onload=function(){null!=this._jsPlumb&&(this._jsPlumb.ready=!0,this._jsPlumb.widthToUse=this._jsPlumb.widthToUse||this._jsPlumb.img.width,this._jsPlumb.heightToUse=this._jsPlumb.heightToUse||this._jsPlumb.img.height,s&&s(this))}.bind(this),this._jsPlumb.endpoint.setImage=function(t,e){var n=t.constructor===String?t:t.src;s=e,this._jsPlumb.img.src=n,null!=this.canvas&&this.canvas.setAttribute("src",this._jsPlumb.img.src)}.bind(this),this._jsPlumb.endpoint.setImage(r,s),this._compute=function(t,e,n,i){return this.anchorPoint=t,this._jsPlumb.ready?[t[0]-this._jsPlumb.widthToUse/2,t[1]-this._jsPlumb.heightToUse/2,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse]:[0,0,0,0]},this.canvas=e.createElement("img",{position:"absolute",margin:0,padding:0,outline:0},this._jsPlumb.instance.endpointClass+a),this._jsPlumb.widthToUse&&this.canvas.setAttribute("width",this._jsPlumb.widthToUse),this._jsPlumb.heightToUse&&this.canvas.setAttribute("height",this._jsPlumb.heightToUse),this._jsPlumb.instance.appendElement(this.canvas),this.actuallyPaint=function(t,e,i){if(!this._jsPlumb.deleted){this._jsPlumb.initialized||(this.canvas.setAttribute("src",this._jsPlumb.img.src),this.appendDisplayElement(this.canvas),this._jsPlumb.initialized=!0);var s=this.anchorPoint[0]-this._jsPlumb.widthToUse/2,o=this.anchorPoint[1]-this._jsPlumb.heightToUse/2;n.sizeElement(this.canvas,s,o,this._jsPlumb.widthToUse,this._jsPlumb.heightToUse)}},this.paint=function(e,n){null!=this._jsPlumb&&(this._jsPlumb.ready?this.actuallyPaint(e,n):t.setTimeout(function(){this.paint(e,n)}.bind(this),200))}},n.extend(e.Endpoints.Image,[o,e.Endpoints.AbstractEndpoint],{cleanup:function(t){t&&(this._jsPlumb.deleted=!0,this.canvas&&this.canvas.parentNode.removeChild(this.canvas),this.canvas=null)}}),e.Endpoints.Blank=function(t){e.Endpoints.AbstractEndpoint.apply(this,arguments);this.type="Blank",o.apply(this,arguments),this._compute=function(t,e,n,i){return[t[0],t[1],10,0]};var i=t.cssClass?" "+t.cssClass:"";this.canvas=e.createElement("div",{display:"block",width:"1px",height:"1px",background:"transparent",position:"absolute"},this._jsPlumb.instance.endpointClass+i),this._jsPlumb.instance.appendElement(this.canvas),this.paint=function(t,e){n.sizeElement(this.canvas,this.x,this.y,this.w,this.h)}},n.extend(e.Endpoints.Blank,[e.Endpoints.AbstractEndpoint,o],{cleanup:function(){this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas)}}),e.Endpoints.Triangle=function(t){this.type="Triangle",e.Endpoints.AbstractEndpoint.apply(this,arguments);var n=this;(t=t||{}).width=t.width||55,t.height=t.height||55,this.width=t.width,this.height=t.height,this._compute=function(t,e,i,s){var o=i.width||n.width,r=i.height||n.height;return[t[0]-o/2,t[1]-r/2,o,r]}};var r=e.Overlays.AbstractOverlay=function(t){this.visible=!0,this.isAppendedAtTopLevel=!0,this.component=t.component,this.loc=null==t.location?.5:t.location,this.endpointLoc=null==t.endpointLocation?[.5,.5]:t.endpointLocation,this.visible=!1!==t.visible};r.prototype={cleanup:function(t){t&&(this.component=null,this.canvas=null,this.endpointLoc=null)},reattach:function(t,e){},setVisible:function(t){this.visible=t,this.component.repaint()},isVisible:function(){return this.visible},hide:function(){this.setVisible(!1)},show:function(){this.setVisible(!0)},incrementLocation:function(t){this.loc+=t,this.component.repaint()},setLocation:function(t){this.loc=t,this.component.repaint()},getLocation:function(){return this.loc},updateFrom:function(){}},e.Overlays.Arrow=function(t){this.type="Arrow",r.apply(this,arguments),this.isAppendedAtTopLevel=!1,t=t||{};var s=this;this.length=t.length||20,this.width=t.width||20,this.id=t.id,this.direction=(t.direction||1)<0?-1:1;var o=t.paintStyle||{"stroke-width":1},a=t.foldback||.623;this.computeMaxSize=function(){return 1.5*s.width},this.elementCreated=function(n,i){if(this.path=n,t.events)for(var s in t.events)e.on(n,s,t.events[s])},this.draw=function(t,e){var s,r,l,u;if(t.pointAlongPathFrom){if(n.isString(this.loc)||this.loc>1||this.loc<0){var c=parseInt(this.loc,10),h=this.loc<0?1:0;s=t.pointAlongPathFrom(h,c,!1),r=t.pointAlongPathFrom(h,c-this.direction*this.length/2,!1),l=i.pointOnLine(s,r,this.length)}else if(1===this.loc){if(s=t.pointOnPath(this.loc),r=t.pointAlongPathFrom(this.loc,-this.length),l=i.pointOnLine(s,r,this.length),-1===this.direction){var d=l;l=s,s=d}}else if(0===this.loc){if(l=t.pointOnPath(this.loc),r=t.pointAlongPathFrom(this.loc,this.length),s=i.pointOnLine(l,r,this.length),-1===this.direction){var p=l;l=s,s=p}}else s=t.pointAlongPathFrom(this.loc,this.direction*this.length/2),r=t.pointOnPath(this.loc),l=i.pointOnLine(s,r,this.length);var f={hxy:s,tail:u=i.perpendicularLineTo(s,l,this.width),cxy:i.pointOnLine(s,l,a*this.length)},g=o.stroke||e.stroke,m=o.fill||e.stroke;return{component:t,d:f,"stroke-width":o.strokeWidth||e.strokeWidth,stroke:g,fill:m,minX:Math.min(s.x,u[0].x,u[1].x),maxX:Math.max(s.x,u[0].x,u[1].x),minY:Math.min(s.y,u[0].y,u[1].y),maxY:Math.max(s.y,u[0].y,u[1].y)}}return{component:t,minX:0,maxX:0,minY:0,maxY:0}}},n.extend(e.Overlays.Arrow,r,{updateFrom:function(t){this.length=t.length||this.length,this.width=t.width||this.width,this.direction=null!=t.direction?t.direction:this.direction,this.foldback=t.foldback||this.foldback},cleanup:function(){this.path&&this.canvas&&this.canvas.removeChild(this.path)}}),e.Overlays.PlainArrow=function(t){t=t||{};var n=e.extend(t,{foldback:1});e.Overlays.Arrow.call(this,n),this.type="PlainArrow"},n.extend(e.Overlays.PlainArrow,e.Overlays.Arrow),e.Overlays.Diamond=function(t){var n=(t=t||{}).length||40,i=e.extend(t,{length:n/2,foldback:2});e.Overlays.Arrow.call(this,i),this.type="Diamond"},n.extend(e.Overlays.Diamond,e.Overlays.Arrow);var a=function(t,e){return(null==t._jsPlumb.cachedDimensions||e)&&(t._jsPlumb.cachedDimensions=t.getDimensions()),t._jsPlumb.cachedDimensions},l=function(t){e.jsPlumbUIComponent.apply(this,arguments),r.apply(this,arguments);var i=this.fire;this.fire=function(){i.apply(this,arguments),this.component&&this.component.fire.apply(this.component,arguments)},this.detached=!1,this.id=t.id,this._jsPlumb.div=null,this._jsPlumb.initialised=!1,this._jsPlumb.component=t.component,this._jsPlumb.cachedDimensions=null,this._jsPlumb.create=t.create,this._jsPlumb.initiallyInvisible=!1===t.visible,this.getElement=function(){if(null==this._jsPlumb.div){var n=this._jsPlumb.div=e.getElement(this._jsPlumb.create(this._jsPlumb.component));n.style.position="absolute",jsPlumb.addClass(n,this._jsPlumb.instance.overlayClass+" "+(this.cssClass?this.cssClass:t.cssClass?t.cssClass:"")),this._jsPlumb.instance.appendElement(n),this._jsPlumb.instance.getId(n),this.canvas=n;var i="translate(-50%, -50%)";n.style.webkitTransform=i,n.style.mozTransform=i,n.style.msTransform=i,n.style.oTransform=i,n.style.transform=i,n._jsPlumb=this,!1===t.visible&&(n.style.display="none")}return this._jsPlumb.div},this.draw=function(t,e,i){var s=a(this);if(null!=s&&2===s.length){var o={x:0,y:0};if(i)o={x:i[0],y:i[1]};else if(t.pointOnPath){var r=this.loc,l=!1;(n.isString(this.loc)||this.loc<0||this.loc>1)&&(r=parseInt(this.loc,10),l=!0),o=t.pointOnPath(r,l)}else{var u=this.loc.constructor===Array?this.loc:this.endpointLoc;o={x:u[0]*t.w,y:u[1]*t.h}}var c=o.x-s[0]/2,h=o.y-s[1]/2;return{component:t,d:{minx:c,miny:h,td:s,cxy:o},minX:c,maxX:c+s[0],minY:h,maxY:h+s[1]}}return{minX:0,maxX:0,minY:0,maxY:0}}};n.extend(l,[e.jsPlumbUIComponent,r],{getDimensions:function(){return[1,1]},setVisible:function(t){this._jsPlumb.div&&(this._jsPlumb.div.style.display=t?"block":"none",t&&this._jsPlumb.initiallyInvisible&&(a(this,!0),this.component.repaint(),this._jsPlumb.initiallyInvisible=!1))},clearCachedDimensions:function(){this._jsPlumb.cachedDimensions=null},cleanup:function(t){t?null!=this._jsPlumb.div&&(this._jsPlumb.div._jsPlumb=null,this._jsPlumb.instance.removeElement(this._jsPlumb.div)):(this._jsPlumb&&this._jsPlumb.div&&this._jsPlumb.div.parentNode&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div),this.detached=!0)},reattach:function(t,e){null!=this._jsPlumb.div&&t.getContainer().appendChild(this._jsPlumb.div),this.detached=!1},computeMaxSize:function(){var t=a(this);return Math.max(t[0],t[1])},paint:function(t,e){this._jsPlumb.initialised||(this.getElement(),t.component.appendDisplayElement(this._jsPlumb.div),this._jsPlumb.initialised=!0,this.detached&&this._jsPlumb.div.parentNode.removeChild(this._jsPlumb.div)),this._jsPlumb.div.style.left=t.component.x+t.d.minx+"px",this._jsPlumb.div.style.top=t.component.y+t.d.miny+"px"}}),e.Overlays.Custom=function(t){this.type="Custom",l.apply(this,arguments)},n.extend(e.Overlays.Custom,l),e.Overlays.GuideLines=function(){var t=this;t.length=50,t.strokeWidth=5,this.type="GuideLines",r.apply(this,arguments),e.jsPlumbUIComponent.apply(this,arguments),this.draw=function(e,n){var s=e.pointAlongPathFrom(t.loc,t.length/2),o=e.pointOnPath(t.loc),r=i.pointOnLine(s,o,t.length),a=i.perpendicularLineTo(s,r,40),l=i.perpendicularLineTo(r,s,20);return{connector:e,head:s,tail:r,headLine:l,tailLine:a,minX:Math.min(s.x,r.x,l[0].x,l[1].x),minY:Math.min(s.y,r.y,l[0].y,l[1].y),maxX:Math.max(s.x,r.x,l[0].x,l[1].x),maxY:Math.max(s.y,r.y,l[0].y,l[1].y)}}},e.Overlays.Label=function(t){this.labelStyle=t.labelStyle;this.cssClass=null!=this.labelStyle?this.labelStyle.cssClass:null;var n=e.extend({create:function(){return e.createElement("div")}},t);if(e.Overlays.Custom.call(this,n),this.type="Label",this.label=t.label||"",this.labelText=null,this.labelStyle){var i=this.getElement();if(this.labelStyle.font=this.labelStyle.font||"12px sans-serif",i.style.font=this.labelStyle.font,i.style.color=this.labelStyle.color||"black",this.labelStyle.fill&&(i.style.background=this.labelStyle.fill),this.labelStyle.borderWidth>0){var s=this.labelStyle.borderStyle?this.labelStyle.borderStyle:"black";i.style.border=this.labelStyle.borderWidth+"px solid "+s}this.labelStyle.padding&&(i.style.padding=this.labelStyle.padding)}},n.extend(e.Overlays.Label,e.Overlays.Custom,{cleanup:function(t){t&&(this.div=null,this.label=null,this.labelText=null,this.cssClass=null,this.labelStyle=null)},getLabel:function(){return this.label},setLabel:function(t){this.label=t,this.labelText=null,this.clearCachedDimensions(),this.update(),this.component.repaint()},getDimensions:function(){return this.update(),l.prototype.getDimensions.apply(this,arguments)},update:function(){if("function"==typeof this.label){var t=this.label(this);this.getElement().innerHTML=t.replace(/\r\n/g,"<br/>")}else null==this.labelText&&(this.labelText=this.label,this.getElement().innerHTML=this.labelText.replace(/\r\n/g,"<br/>"))},updateFrom:function(t){null!=t.label&&this.setLabel(t.label)}})}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumbUtil,n=t.jsPlumbInstance,i="stop",s="revert",o="_jsPlumbGroup",r="show",a="hide",l=function(t){var n={},i={},s={},l=this;function u(e,n){for(var i=e.getMembers(),s=0;s<i.length;s++)t[n?r:a](i[s],!0)}t.bind("connection",function(t){null!=t.source[o]&&null!=t.target[o]&&t.source[o]===t.target[o]?(i[t.connection.id]=t.source[o],s[t.connection.id]=t.source[o]):(null!=t.source[o]&&(e.suggest(t.source[o].connections.source,t.connection),i[t.connection.id]=t.source[o]),null!=t.target[o]&&(e.suggest(t.target[o].connections.target,t.connection),s[t.connection.id]=t.target[o]))}),t.bind("internal.connectionDetached",function(t){!function(t){delete t.proxies;var n,o=i[t.id];null!=o&&(n=function(e){return e.id===t.id},e.removeWithFunction(o.connections.source,n),e.removeWithFunction(o.connections.target,n),delete i[t.id]),null!=(o=s[t.id])&&(n=function(e){return e.id===t.id},e.removeWithFunction(o.connections.source,n),e.removeWithFunction(o.connections.target,n),delete s[t.id])}(t.connection)}),t.bind("connectionMoved",function(t){var e=(0===t.index?i:s)[t.connection.id];if(e){var n=e.connections[0===t.index?"source":"target"],o=n.indexOf(t.connection);-1!==o&&n.splice(o,1)}}),this.addGroup=function(e){t.addClass(e.getEl(),"jtk-group-expanded"),n[e.id]=e,e.manager=this,d(e),t.fire("group:add",{group:e})},this.addToGroup=function(e,n,i){if(e=this.getGroup(e)){var s=e.getEl();if(n._isJsPlumbGroup)return;var o=n._jsPlumbGroup;if(o!==e){var r=t.getOffset(n,!0),a=e.collapsed?t.getOffset(s,!0):t.getOffset(e.getDragArea(),!0);null!=o&&(o.remove(n,!1,i,!1,e),l.updateConnectionsForGroup(o)),e.add(n,i);var u=function(t,n){var i=0===n?1:0;t.each(function(t){t.setVisible(!1),t.endpoints[i].element._jsPlumbGroup===e?(t.endpoints[i].setVisible(!1),h(t,i,e)):(t.endpoints[n].setVisible(!1),c(t,n,e))})};e.collapsed&&(u(t.select({source:n}),0),u(t.select({target:n}),1));var d=t.getId(n);t.dragManager.setParent(n,d,s,t.getId(s),r);var p={left:r.left-a.left,top:r.top-a.top};if(t.setPosition(n,p),t.dragManager.revalidateParent(n,d,r),l.updateConnectionsForGroup(e),t.revalidate(d),!i){var f={group:e,el:n,pos:p};o&&(f.sourceGroup=o),t.fire("group:addMember",f)}}}},this.removeFromGroup=function(t,e,n){(t=this.getGroup(t))&&t.remove(e,null,n)},this.getGroup=function(t){var i=t;if(e.isString(t)&&null==(i=n[t]))throw new TypeError("No such group ["+t+"]");return i},this.getGroups=function(){var t=[];for(var e in n)t.push(n[e]);return t},this.removeGroup=function(e,i,s,o){e=this.getGroup(e),this.expandGroup(e,!0);var r=e[i?"removeAll":"orphanAll"](s,o);return t.remove(e.getEl()),delete n[e.id],delete t._groups[e.id],t.fire("group:remove",{group:e}),r},this.removeAllGroups=function(t,e,i){for(var s in n)this.removeGroup(n[s],t,e,i)};var c=function(e,n,i){var s=e.endpoints[0===n?1:0].element;if(!s[o]||s[o].shouldProxy()||!s[o].collapsed){var r=i.getEl(),a=t.getId(r);t.proxyConnection(e,n,r,a,function(t,e){return i.getEndpoint(t,e)},function(t,e){return i.getAnchor(t,e)})}};this.collapseGroup=function(e){if(null!=(e=this.getGroup(e))&&!e.collapsed){var n=e.getEl();if(u(e,!1),e.shouldProxy()){var i=function(t,n){for(var i=0;i<t.length;i++){var s=t[i];c(s,n,e)}};i(e.connections.source,0),i(e.connections.target,1)}e.collapsed=!0,t.removeClass(n,"jtk-group-expanded"),t.addClass(n,"jtk-group-collapsed"),t.revalidate(n),t.fire("group:collapse",{group:e})}};var h=function(e,n,i){t.unproxyConnection(e,n,t.getId(i.getEl()))};function d(e){var n=e.getMembers(),o=t.getConnections({source:n,scope:"*"},!0),r=t.getConnections({target:n,scope:"*"},!0),a={};e.connections.source.length=0,e.connections.target.length=0;var l=function(t){for(var n=0;n<t.length;n++)a[t[n].id]||(a[t[n].id]=!0,t[n].source._jsPlumbGroup===e?(t[n].target._jsPlumbGroup!==e&&e.connections.source.push(t[n]),i[t[n].id]=e):t[n].target._jsPlumbGroup===e&&(e.connections.target.push(t[n]),s[t[n].id]=e))};l(o),l(r)}this.expandGroup=function(e,n){if(null!=(e=this.getGroup(e))&&e.collapsed){var i=e.getEl();if(u(e,!0),e.shouldProxy()){var s=function(t,n){for(var i=0;i<t.length;i++){var s=t[i];h(s,n,e)}};s(e.connections.source,0),s(e.connections.target,1)}e.collapsed=!1,t.addClass(i,"jtk-group-expanded"),t.removeClass(i,"jtk-group-collapsed"),t.revalidate(i),this.repaintGroup(e),n||t.fire("group:expand",{group:e})}},this.repaintGroup=function(e){for(var n=(e=this.getGroup(e)).getMembers(),i=0;i<n.length;i++)t.revalidate(n[i])},this.updateConnectionsForGroup=d,this.refreshAllGroups=function(){for(var e in n)d(n[e]),t.dragManager.updateOffsets(t.getId(n[e].getEl()))}};n.prototype.addGroup=function(n){var r=this;if(r._groups=r._groups||{},null!=r._groups[n.id])throw new TypeError("cannot create Group ["+n.id+"]; a Group with that ID exists");if(null!=n.el[o])throw new TypeError("cannot create Group ["+n.id+"]; the given element is already a Group");var a=new function(n,r){var a=this,l=r.el;this.getEl=function(){return l},this.id=r.id||e.uuid(),l._isJsPlumbGroup=!0;var u=this.getDragArea=function(){var t=n.getSelector(l,"[jtk-group-content]");return t&&t.length>0?t[0]:l},c=!0===r.ghost,h=c||!0===r.constrain,d=!1!==r.revert,p=!0===r.orphan,f=!0===r.prune,g=!0===r.dropOverride,m=!1!==r.proxied,v=[];if(this.connections={source:[],target:[],internal:[]},this.getAnchor=function(t,e){return r.anchor||"Continuous"},this.getEndpoint=function(t,e){return r.endpoint||["Dot",{radius:10}]},this.collapsed=!1,!1!==r.draggable){var b={stop:function(t){n.fire("groupDragStop",jsPlumb.extend(t,{group:a}))},scope:"_jsPlumbGroupDrag"};r.dragOptions&&t.jsPlumb.extend(b,r.dragOptions),n.draggable(r.el,b)}!1!==r.droppable&&n.droppable(r.el,{drop:function(t){var e=t.drag.el;if(!e._isJsPlumbGroup){var i=e._jsPlumbGroup;if(i!==a){if(null!=i&&i.overrideDrop(e,a))return;n.getGroupManager().addToGroup(a,e,!1)}}}});var y=function(t,e){for(var n=null==t.nodeType?t:[t],i=0;i<n.length;i++)e(n[i])};function P(t,e){var i=function(t){return t.offsetParent}(t),s=n.getSize(i),o=n.getSize(t),r=e[0],a=r+o[0],l=e[1],u=l+o[1];return a>0&&r<s[0]&&u>0&&l<s[1]}function _(t){var e=n.getId(t),i=n.getOffset(t);return t.parentNode.removeChild(t),n.getContainer().appendChild(t),n.setPosition(t,i),j(t),n.dragManager.clearParent(t,e),[e,i]}function x(t){var e=[];function i(t,e,i){var s=null;if(!P(t,[e,i])){var o=t._jsPlumbGroup;f?n.remove(t):s=_(t),o.remove(t)}return s}for(var s=0;s<t.selection.length;s++)e.push(i(t.selection[s][0],t.selection[s][1].left,t.selection[s][1].top));return 1===e.length?e[0]:e}function C(t){var e=n.getId(t);n.revalidate(t),n.dragManager.revalidateParent(t,e)}function j(t){t._katavorioDrag&&((f||p)&&t._katavorioDrag.off(i,x),f||p||!d||(t._katavorioDrag.off(s,C),t._katavorioDrag.setRevert(null)))}function E(t){t._katavorioDrag&&((f||p)&&t._katavorioDrag.on(i,x),h&&t._katavorioDrag.setConstrain(!0),c&&t._katavorioDrag.setUseGhostProxy(!0),f||p||!d||(t._katavorioDrag.on(s,C),t._katavorioDrag.setRevert(function(t,e){return!P(t,e)})))}this.overrideDrop=function(t,e){return g&&(d||f||p)},this.add=function(t,e){var i=u();y(t,function(t){if(null!=t._jsPlumbGroup){if(t._jsPlumbGroup===a)return;t._jsPlumbGroup.remove(t,!0,e,!1)}t._jsPlumbGroup=a,v.push(t),n.isAlreadyDraggable(t)&&E(t),t.parentNode!==i&&i.appendChild(t)}),n.getGroupManager().updateConnectionsForGroup(a)},this.remove=function(t,i,s,o,r){y(t,function(t){if(t._jsPlumbGroup===a){if(delete t._jsPlumbGroup,e.removeWithFunction(v,function(e){return e===t}),i)try{a.getDragArea().removeChild(t)}catch(t){jsPlumbUtil.log("Could not remove element from Group "+t)}if(j(t),!s){var o={group:a,el:t};r&&(o.targetGroup=r),n.fire("group:removeMember",o)}}}),o||n.getGroupManager().updateConnectionsForGroup(a)},this.removeAll=function(t,e){for(var i=0,s=v.length;i<s;i++){var o=v[0];a.remove(o,t,e,!0),n.remove(o,!0)}v.length=0,n.getGroupManager().updateConnectionsForGroup(a)},this.orphanAll=function(){for(var t={},e=0;e<v.length;e++){var n=_(v[e]);t[n[0]]=n[1]}return v.length=0,t},this.getMembers=function(){return v},l[o]=this,n.bind("elementDraggable",function(t){t.el._jsPlumbGroup===this&&E(t.el)}.bind(this)),this.shouldProxy=function(){return m},n.getGroupManager().addGroup(this)}(r,n);return r._groups[a.id]=a,n.collapsed&&this.collapseGroup(a),a},n.prototype.addToGroup=function(t,e,n){var i=function(e){var i=this.getId(e);this.manage(i,e),this.getGroupManager().addToGroup(t,e,n)}.bind(this);if(Array.isArray(e))for(var s=0;s<e.length;s++)i(e[s]);else i(e)},n.prototype.removeFromGroup=function(t,e,n){this.getGroupManager().removeFromGroup(t,e,n)},n.prototype.removeGroup=function(t,e,n,i){return this.getGroupManager().removeGroup(t,e,n,i)},n.prototype.removeAllGroups=function(t,e,n){this.getGroupManager().removeAllGroups(t,e,n)},n.prototype.getGroup=function(t){return this.getGroupManager().getGroup(t)},n.prototype.getGroups=function(){return this.getGroupManager().getGroups()},n.prototype.expandGroup=function(t){this.getGroupManager().expandGroup(t)},n.prototype.collapseGroup=function(t){this.getGroupManager().collapseGroup(t)},n.prototype.repaintGroup=function(t){this.getGroupManager().repaintGroup(t)},n.prototype.toggleGroup=function(t){null!=(t=this.getGroupManager().getGroup(t))&&this.getGroupManager()[t.collapsed?"expandGroup":"collapseGroup"](t)},n.prototype.getGroupManager=function(){var t=this._groupManager;return null==t&&(t=this._groupManager=new l(this)),t},n.prototype.removeGroupManager=function(){delete this._groupManager},n.prototype.getGroupFor=function(t){if(t=this.getElement(t))return t[o]}}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.jsPlumb,e=this.jsPlumbUtil;t.Connectors.Flowchart=function(e){this.type="Flowchart",(e=e||{}).stub=null==e.stub?30:e.stub;var n,i=t.Connectors.AbstractConnector.apply(this,arguments),s=null==e.midpoint?.5:e.midpoint,o=!0===e.alwaysRespectStubs,r=null,a=null,l=null!=e.cornerRadius?e.cornerRadius:0,u=(e.loopbackRadius,function(t){return t<0?-1:0===t?0:1}),c=function(t){return[u(t[2]-t[0]),u(t[3]-t[1])]},h=function(t,e,n,i){if(r!==e||a!==n){var s=null==r?i.sx:r,o=null==a?i.sy:a,l=s===e?"v":"h";r=e,a=n,t.push([s,o,e,n,l])}},d=function(t){return Math.sqrt(Math.pow(t[0]-t[2],2)+Math.pow(t[1]-t[3],2))},p=function(t){var e=[];return e.push.apply(e,t),e};this._compute=function(t,e){n=[],r=null,a=null;var u=function(){return[t.startStubX,t.startStubY,t.endStubX,t.endStubY]},f={perpendicular:u,orthogonal:u,opposite:function(e){var n=t,i="x"===e?0:1;return!o&&{x:function(){return 1===n.so[i]&&(n.startStubX>n.endStubX&&n.tx>n.startStubX||n.sx>n.endStubX&&n.tx>n.sx)||-1===n.so[i]&&(n.startStubX<n.endStubX&&n.tx<n.startStubX||n.sx<n.endStubX&&n.tx<n.sx)},y:function(){return 1===n.so[i]&&(n.startStubY>n.endStubY&&n.ty>n.startStubY||n.sy>n.endStubY&&n.ty>n.sy)||-1===n.so[i]&&(n.startStubY<n.endStubY&&n.ty<n.startStubY||n.sy<n.endStubY&&n.ty<n.sy)}}[e]()?{x:[(t.sx+t.tx)/2,t.startStubY,(t.sx+t.tx)/2,t.endStubY],y:[t.startStubX,(t.sy+t.ty)/2,t.endStubX,(t.sy+t.ty)/2]}[e]:[t.startStubX,t.startStubY,t.endStubX,t.endStubY]}}[t.anchorOrientation](t.sourceAxis),g="x"===t.sourceAxis?0:1,m="x"===t.sourceAxis?1:0,v=f[g],b=f[m],y=f[g+2],P=f[m+2];h(n,f[0],f[1],t);var _=t.startStubX+(t.endStubX-t.startStubX)*s,x=t.startStubY+(t.endStubY-t.startStubY)*s,C={x:[0,1],y:[1,0]},j={perpendicular:function(e){var n=t,i={x:[[n.startStubX,n.endStubX],null,[n.endStubX,n.startStubX]],y:[[n.startStubY,n.endStubY],null,[n.endStubY,n.startStubY]]},s={x:[[_,n.startStubY],[_,n.endStubY]],y:[[n.startStubX,x],[n.endStubX,x]]},o={x:[[n.endStubX,n.startStubY]],y:[[n.startStubX,n.endStubY]]},r={x:[[n.startStubX,n.endStubY],[n.endStubX,n.endStubY]],y:[[n.endStubX,n.startStubY],[n.endStubX,n.endStubY]]},a={x:[[n.startStubX,x],[n.endStubX,x],[n.endStubX,n.endStubY]],y:[[_,n.startStubY],[_,n.endStubY],[n.endStubX,n.endStubY]]},l={x:[n.startStubY,n.endStubY],y:[n.startStubX,n.endStubX]},u=C[e][0],c=C[e][1],h=n.so[u]+1,d=n.to[c]+1,p=-1===n.to[c]&&l[e][1]<l[e][0]||1===n.to[c]&&l[e][1]>l[e][0],f=i[e][h][0],g=i[e][h][1],m={x:[[[1,2,3,4],null,[2,1,4,3]],null,[[4,3,2,1],null,[3,4,1,2]]],y:[[[3,2,1,4],null,[2,3,4,1]],null,[[4,1,2,3],null,[1,4,3,2]]]}[e][h][d];return n.segment===m[3]||n.segment===m[2]&&p?s[e]:n.segment===m[2]&&g<f?o[e]:n.segment===m[2]&&g>=f||n.segment===m[1]&&!p?a[e]:n.segment===m[0]||n.segment===m[1]&&p?r[e]:void 0},orthogonal:function(e,n,i,s,o){var r=t,a={x:-1===r.so[0]?Math.min(n,s):Math.max(n,s),y:-1===r.so[1]?Math.min(n,s):Math.max(n,s)}[e];return{x:[[a,i],[a,o],[s,o]],y:[[i,a],[o,a],[o,s]]}[e]},opposite:function(n,s,o,r){var a=t,l={x:"y",y:"x"}[n],u={x:"height",y:"width"}[n],c=a["is"+n.toUpperCase()+"GreaterThanStubTimes2"];if(e.sourceEndpoint.elementId===e.targetEndpoint.elementId){var h=o+(1-e.sourceEndpoint.anchor[l])*e.sourceInfo[u]+i.maxStub;return{x:[[s,h],[r,h]],y:[[h,s],[h,r]]}[n]}return!c||1===a.so[g]&&s>r||-1===a.so[g]&&s<r?{x:[[s,x],[r,x]],y:[[_,s],[_,r]]}[n]:1===a.so[g]&&s<r||-1===a.so[g]&&s>r?{x:[[_,a.sy],[_,a.ty]],y:[[a.sx,x],[a.tx,x]]}[n]:void 0}}[t.anchorOrientation](t.sourceAxis,v,b,y,P);if(j)for(var E=0;E<j.length;E++)h(n,j[E][0],j[E][1],t);h(n,f[2],f[3],t),h(n,t.tx,t.ty,t),function(t,e,n){for(var s,o,r,a=null,u=0;u<e.length-1;u++){if(a=a||p(e[u]),s=p(e[u+1]),o=c(a),r=c(s),l>0&&a[4]!==s[4]){var h=Math.min(d(a),d(s)),f=Math.min(l,h/2);a[2]-=o[0]*f,a[3]-=o[1]*f,s[0]+=r[0]*f,s[1]+=r[1]*f;var g=o[1]===r[0]&&1===r[0]||o[1]===r[0]&&0===r[0]&&o[0]!==r[1]||o[1]===r[0]&&-1===r[0],m=(s[1]>a[3]?1:-1)==(s[0]>a[2]?1:-1),v=m&&g||!m&&!g?s[0]:a[2],b=m&&g||!m&&!g?a[3]:s[1];i.addSegment(t,"Straight",{x1:a[0],y1:a[1],x2:a[2],y2:a[3]}),i.addSegment(t,"Arc",{r:f,x1:a[2],y1:a[3],x2:s[0],y2:s[1],cx:v,cy:b,ac:g})}else{var y=a[2]===a[0]?0:a[2]>a[0]?n.lw/2:-n.lw/2,P=a[3]===a[1]?0:a[3]>a[1]?n.lw/2:-n.lw/2;i.addSegment(t,"Straight",{x1:a[0]-y,y1:a[1]-P,x2:a[2]+y,y2:a[3]+P})}a=s}null!=s&&i.addSegment(t,"Straight",{x1:s[0],y1:s[1],x2:s[2],y2:s[3]})}(this,n,t)}},e.extend(t.Connectors.Flowchart,t.Connectors.AbstractConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.jsPlumb,e=this.jsPlumbUtil;t.Connectors.AbstractBezierConnector=function(e){var n,i=!1!==(e=e||{}).showLoopback,s=(e.curviness,e.margin||5),o=(e.proximityLimit,e.orientation&&"clockwise"===e.orientation),r=e.loopbackRadius||25;return this._compute=function(t,e){var a=e.sourcePos,l=e.targetPos,u=Math.abs(a[0]-l[0]),c=Math.abs(a[1]-l[1]);if(i&&e.sourceEndpoint.elementId===e.targetEndpoint.elementId){!0;var h=e.sourcePos[0],d=e.sourcePos[1]-s,p=h,f=d-r,g=p-r,m=f-r;u=2*r,c=2*r,t.points[0]=g,t.points[1]=m,t.points[2]=u,t.points[3]=c,n.addSegment(this,"Arc",{loopback:!0,x1:h-g+4,y1:d-m,startAngle:0,endAngle:2*Math.PI,r:r,ac:!o,x2:h-g-4,y2:d-m,cx:p-g,cy:f-m})}else!1,this._computeBezier(t,e,a,l,u,c)},n=t.Connectors.AbstractConnector.apply(this,arguments)},e.extend(t.Connectors.AbstractBezierConnector,t.Connectors.AbstractConnector);var n=function(e){e=e||{},this.type="Bezier";var n=t.Connectors.AbstractBezierConnector.apply(this,arguments),i=e.curviness||150;this.getCurviness=function(){return i},this._findControlPoint=function(t,e,n,s,o,r,a){var l=[];return r[0]!==a[0]||r[1]===a[1]?(0===a[0]?l.push(n[0]<e[0]?t[0]+10:t[0]-10):l.push(t[0]+i*a[0]),0===a[1]?l.push(n[1]<e[1]?t[1]+10:t[1]-10):l.push(t[1]+i*r[1])):(0===r[0]?l.push(e[0]<n[0]?t[0]+10:t[0]-10):l.push(t[0]-i*r[0]),0===r[1]?l.push(e[1]<n[1]?t[1]+10:t[1]-10):l.push(t[1]+i*a[1])),l},this._computeBezier=function(t,e,i,s,o,r){var a,l,u=i[0]<s[0]?o:0,c=i[1]<s[1]?r:0,h=i[0]<s[0]?0:o,d=i[1]<s[1]?0:r;a=this._findControlPoint([u,c],i,s,e.sourceEndpoint,e.targetEndpoint,t.so,t.to),l=this._findControlPoint([h,d],s,i,e.targetEndpoint,e.sourceEndpoint,t.to,t.so),n.addSegment(this,"Bezier",{x1:u,y1:c,x2:h,y2:d,cp1x:a[0],cp1y:a[1],cp2x:l[0],cp2y:l[1]})}};t.Connectors.Bezier=n,e.extend(n,t.Connectors.AbstractBezierConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.jsPlumb,e=this.jsPlumbUtil,n=function(e){e=e||{},this.type="StateMachine";var n,i=t.Connectors.AbstractBezierConnector.apply(this,arguments),s=e.curviness||10,o=e.margin||5,r=e.proximityLimit||80;e.orientation&&e.orientation;this._computeBezier=function(t,e,a,l,u,c){var h=e.sourcePos[0]<e.targetPos[0]?0:u,d=e.sourcePos[1]<e.targetPos[1]?0:c,p=e.sourcePos[0]<e.targetPos[0]?u:0,f=e.sourcePos[1]<e.targetPos[1]?c:0;0===e.sourcePos[2]&&(h-=o),1===e.sourcePos[2]&&(h+=o),0===e.sourcePos[3]&&(d-=o),1===e.sourcePos[3]&&(d+=o),0===e.targetPos[2]&&(p-=o),1===e.targetPos[2]&&(p+=o),0===e.targetPos[3]&&(f-=o),1===e.targetPos[3]&&(f+=o);var g,m,v,b,y,P,_,x,C=(h+p)/2,j=(d+f)/2,E=(P=d,x=f,(y=h)<=(_=p)&&x<=P?1:y<=_&&P<=x?2:_<=y&&x>=P?3:4),S=Math.sqrt(Math.pow(p-h,2)+Math.pow(f-d,2));g=(n=function(t,e,n,i,s,o,r,a,l){return a<=l?[t,e]:1===n?i[3]<=0&&s[3]>=1?[t+(i[2]<.5?-1*o:o),e]:i[2]>=1&&s[2]<=0?[t,e+(i[3]<.5?-1*r:r)]:[t+-1*o,e+-1*r]:2===n?i[3]>=1&&s[3]<=0?[t+(i[2]<.5?-1*o:o),e]:i[2]>=1&&s[2]<=0?[t,e+(i[3]<.5?-1*r:r)]:[t+o,e+-1*r]:3===n?i[3]>=1&&s[3]<=0?[t+(i[2]<.5?-1*o:o),e]:i[2]<=0&&s[2]>=1?[t,e+(i[3]<.5?-1*r:r)]:[t+-1*o,e+-1*r]:4===n?i[3]<=0&&s[3]>=1?[t+(i[2]<.5?-1*o:o),e]:i[2]<=0&&s[2]>=1?[t,e+(i[3]<.5?-1*r:r)]:[t+o,e+-1*r]:void 0}(C,j,E,e.sourcePos,e.targetPos,s,s,S,r))[0],m=n[0],v=n[1],b=n[1],i.addSegment(this,"Bezier",{x1:p,y1:f,x2:h,y2:d,cp1x:g,cp1y:v,cp2x:m,cp2y:b})}};t.Connectors.StateMachine=n,e.extend(n,t.Connectors.AbstractBezierConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.jsPlumb,e=this.jsPlumbUtil,n=function(e){this.type="Straight";var n=t.Connectors.AbstractConnector.apply(this,arguments);this._compute=function(t,e){n.addSegment(this,"Straight",{x1:t.sx,y1:t.sy,x2:t.startStubX,y2:t.startStubY}),n.addSegment(this,"Straight",{x1:t.startStubX,y1:t.startStubY,x2:t.endStubX,y2:t.endStubY}),n.addSegment(this,"Straight",{x1:t.endStubX,y1:t.endStubY,x2:t.tx,y2:t.ty})}};t.Connectors.Straight=n,e.extend(n,t.Connectors.AbstractConnector)}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this.jsPlumb,e=this.jsPlumbUtil,n={"stroke-linejoin":"stroke-linejoin","stroke-dashoffset":"stroke-dashoffset","stroke-linecap":"stroke-linecap"},i="http://www.w3.org/2000/svg",s=function(t,e){for(var n in e)t.setAttribute(n,""+e[n])},o=function(e,n){return(n=n||{}).version="1.1",n.xmlns=i,t.createElementNS(i,e,null,null,n)},r=function(t){return"position:absolute;left:"+t[0]+"px;top:"+t[1]+"px"},a=function(t){for(var e=t.querySelectorAll(" defs,linearGradient,radialGradient"),n=0;n<e.length;n++)e[n].parentNode.removeChild(e[n])},l=function(t,e,i,s,r){if(e.setAttribute("fill",i.fill?i.fill:"none"),e.setAttribute("stroke",i.stroke?i.stroke:"none"),i.gradient?function(t,e,n,i,s){var r,l="jsplumb_gradient_"+s._jsPlumb.instance.idstamp();a(t),r=n.gradient.offset?o("radialGradient",{id:l}):o("linearGradient",{id:l,gradientUnits:"userSpaceOnUse"});var u=o("defs");t.appendChild(u),u.appendChild(r);for(var c=0;c<n.gradient.stops.length;c++){var h=1===s.segment||2===s.segment?c:n.gradient.stops.length-1-c,d=n.gradient.stops[h][1],p=o("stop",{offset:Math.floor(100*n.gradient.stops[c][0])+"%","stop-color":d});r.appendChild(p)}var f=n.stroke?"stroke":"fill";e.setAttribute(f,"url(#"+l+")")}(t,e,i,0,r):(a(t),e.setAttribute("style","")),i.strokeWidth&&e.setAttribute("stroke-width",i.strokeWidth),i.dashstyle&&i.strokeWidth&&!i["stroke-dasharray"]){var l=-1===i.dashstyle.indexOf(",")?" ":",",u=i.dashstyle.split(l),c="";u.forEach(function(t){c+=Math.floor(t*i.strokeWidth)+l}),e.setAttribute("stroke-dasharray",c)}else i["stroke-dasharray"]&&e.setAttribute("stroke-dasharray",i["stroke-dasharray"]);for(var h in n)i[h]&&e.setAttribute(n[h],i[h])},u=function(t,e,n){t.childNodes.length>n?t.insertBefore(e,t.childNodes[n]):t.appendChild(e)};e.svg={node:o,attr:s,pos:r};var c=function(n){var i=n.pointerEventsSpec||"all",a={};t.jsPlumbUIComponent.apply(this,n.originalArgs),this.canvas=null,this.path=null,this.svg=null,this.bgCanvas=null;var l=n.cssClass+" "+(n.originalArgs[0].cssClass||""),u={style:"",width:0,height:0,"pointer-events":i,position:"absolute"};this.svg=o("svg",u),n.useDivWrapper?(this.canvas=t.createElement("div",{position:"absolute"}),e.sizeElement(this.canvas,0,0,1,1),this.canvas.className=l):(s(this.svg,{class:l}),this.canvas=this.svg),n._jsPlumb.appendElement(this.canvas,n.originalArgs[0].parent),n.useDivWrapper&&this.canvas.appendChild(this.svg);var c=[this.canvas];return this.getDisplayElements=function(){return c},this.appendDisplayElement=function(t){c.push(t)},this.paint=function(t,i,o){if(null!=t){var l,u=[this.x,this.y],c=[this.w,this.h];null!=o&&(o.xmin<0&&(u[0]+=o.xmin),o.ymin<0&&(u[1]+=o.ymin),c[0]=o.xmax+(o.xmin<0?-o.xmin:0),c[1]=o.ymax+(o.ymin<0?-o.ymin:0)),n.useDivWrapper?(e.sizeElement(this.canvas,u[0],u[1],c[0],c[1]),u[0]=0,u[1]=0,l=r([0,0])):l=r([u[0],u[1]]),a.paint.apply(this,arguments),s(this.svg,{style:l,width:c[0]||0,height:c[1]||0})}},{renderer:a}};e.extend(c,t.jsPlumbUIComponent,{cleanup:function(t){t||null==this.typeId?(this.canvas&&(this.canvas._jsPlumb=null),this.svg&&(this.svg._jsPlumb=null),this.bgCanvas&&(this.bgCanvas._jsPlumb=null),this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.svg=null,this.canvas=null,this.path=null,this.group=null):(this.canvas&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas),this.bgCanvas&&this.bgCanvas.parentNode&&this.bgCanvas.parentNode.removeChild(this.bgCanvas))},reattach:function(t){var e=t.getContainer();this.canvas&&null==this.canvas.parentNode&&e.appendChild(this.canvas),this.bgCanvas&&null==this.bgCanvas.parentNode&&e.appendChild(this.bgCanvas)},setVisible:function(t){this.canvas&&(this.canvas.style.display=t?"block":"none")}}),t.ConnectorRenderers.svg=function(e){var n=this;c.apply(this,[{cssClass:e._jsPlumb.connectorClass,originalArgs:arguments,pointerEventsSpec:"none",_jsPlumb:e._jsPlumb}]).renderer.paint=function(i,r,a){var c=n.getSegments(),h=[0,0];if(a.xmin<0&&(h[0]=-a.xmin),a.ymin<0&&(h[1]=-a.ymin),c.length>0){var d={d:n.getPathData(),transform:"translate("+h[0]+","+h[1]+")","pointer-events":e["pointer-events"]||"visibleStroke"},p=null;n.x,n.y,n.w,n.h;if(i.outlineStroke){var f=i.outlineWidth||1,g=i.strokeWidth+2*f;delete(p=t.extend({},i)).gradient,p.stroke=i.outlineStroke,p.strokeWidth=g,null==n.bgPath?(n.bgPath=o("path",d),t.addClass(n.bgPath,t.connectorOutlineClass),u(n.svg,n.bgPath,0)):s(n.bgPath,d),l(n.svg,n.bgPath,p,0,n)}null==n.path?(n.path=o("path",d),u(n.svg,n.path,i.outlineStroke?1:0)):s(n.path,d),l(n.svg,n.path,i,0,n)}}},e.extend(t.ConnectorRenderers.svg,c);var h=t.SvgEndpoint=function(e){c.apply(this,[{cssClass:e._jsPlumb.endpointClass,originalArgs:arguments,pointerEventsSpec:"all",useDivWrapper:!0,_jsPlumb:e._jsPlumb}]).renderer.paint=function(e){var n=t.extend({},e);n.outlineStroke&&(n.stroke=n.outlineStroke),null==this.node?(this.node=this.makeNode(n),this.svg.appendChild(this.node)):null!=this.updateNode&&this.updateNode(this.node),l(this.svg,this.node,n,(this.x,this.y,this.w,this.h),this),r(this.node,(this.x,this.y))}.bind(this)};e.extend(h,c),t.Endpoints.svg.Dot=function(){t.Endpoints.Dot.apply(this,arguments),h.apply(this,arguments),this.makeNode=function(t){return o("circle",{cx:this.w/2,cy:this.h/2,r:this.radius})},this.updateNode=function(t){s(t,{cx:this.w/2,cy:this.h/2,r:this.radius})}},e.extend(t.Endpoints.svg.Dot,[t.Endpoints.Dot,h]),t.Endpoints.svg.Rectangle=function(){t.Endpoints.Rectangle.apply(this,arguments),h.apply(this,arguments),this.makeNode=function(t){return o("rect",{width:this.w,height:this.h})},this.updateNode=function(t){s(t,{width:this.w,height:this.h})}},e.extend(t.Endpoints.svg.Rectangle,[t.Endpoints.Rectangle,h]),t.Endpoints.svg.Image=t.Endpoints.Image,t.Endpoints.svg.Blank=t.Endpoints.Blank,t.Overlays.svg.Label=t.Overlays.Label,t.Overlays.svg.Custom=t.Overlays.Custom;var d=function(e,n){e.apply(this,n),t.jsPlumbUIComponent.apply(this,n),this.isAppendedAtTopLevel=!1;this.path=null,this.paint=function(t,e){if(t.component.svg&&e){null==this.path&&(this.path=o("path",{"pointer-events":"all"}),t.component.svg.appendChild(this.path),this.elementCreated&&this.elementCreated(this.path,t.component),this.canvas=t.component.svg);var r=n&&1===n.length&&n[0].cssClass||"",a=[0,0];e.xmin<0&&(a[0]=-e.xmin),e.ymin<0&&(a[1]=-e.ymin),s(this.path,{d:i(t.d),class:r,stroke:t.stroke?t.stroke:null,fill:t.fill?t.fill:null,transform:"translate("+a[0]+","+a[1]+")"})}};var i=function(t){return isNaN(t.cxy.x)||isNaN(t.cxy.y)?"":"M"+t.hxy.x+","+t.hxy.y+" L"+t.tail[0].x+","+t.tail[0].y+" L"+t.cxy.x+","+t.cxy.y+" L"+t.tail[1].x+","+t.tail[1].y+" L"+t.hxy.x+","+t.hxy.y};this.transfer=function(t){t.canvas&&this.path&&this.path.parentNode&&(this.path.parentNode.removeChild(this.path),t.canvas.appendChild(this.path))}};e.extend(d,[t.jsPlumbUIComponent,t.Overlays.AbstractOverlay],{cleanup:function(t){null!=this.path&&(t?this._jsPlumb.instance.removeElement(this.path):this.path.parentNode&&this.path.parentNode.removeChild(this.path))},reattach:function(t,e){this.path&&e.canvas&&e.canvas.appendChild(this.path)},setVisible:function(t){null!=this.path&&(this.path.style.display=t?"block":"none")}}),t.Overlays.svg.Arrow=function(){d.apply(this,[t.Overlays.Arrow,arguments])},e.extend(t.Overlays.svg.Arrow,[t.Overlays.Arrow,d]),t.Overlays.svg.PlainArrow=function(){d.apply(this,[t.Overlays.PlainArrow,arguments])},e.extend(t.Overlays.svg.PlainArrow,[t.Overlays.PlainArrow,d]),t.Overlays.svg.Diamond=function(){d.apply(this,[t.Overlays.Diamond,arguments])},e.extend(t.Overlays.svg.Diamond,[t.Overlays.Diamond,d]),t.Overlays.svg.GuideLines=function(){var e,n,i=null,r=this;t.Overlays.GuideLines.apply(this,arguments),this.paint=function(t,l){null==i&&(i=o("path"),t.connector.svg.appendChild(i),r.attachListeners(i,t.connector),r.attachListeners(i,r),e=o("path"),t.connector.svg.appendChild(e),r.attachListeners(e,t.connector),r.attachListeners(e,r),n=o("path"),t.connector.svg.appendChild(n),r.attachListeners(n,t.connector),r.attachListeners(n,r));var u=[0,0];l.xmin<0&&(u[0]=-l.xmin),l.ymin<0&&(u[1]=-l.ymin),s(i,{d:a(t.head,t.tail),stroke:"red",fill:null,transform:"translate("+u[0]+","+u[1]+")"}),s(e,{d:a(t.tailLine[0],t.tailLine[1]),stroke:"blue",fill:null,transform:"translate("+u[0]+","+u[1]+")"}),s(n,{d:a(t.headLine[0],t.headLine[1]),stroke:"green",fill:null,transform:"translate("+u[0]+","+u[1]+")"})};var a=function(t,e){return"M "+t.x+","+t.y+" L"+e.x+","+e.y}},e.extend(t.Overlays.svg.GuideLines,t.Overlays.GuideLines)}.call("undefined"!=typeof window?window:this),function(){"use strict";var t=this,e=t.jsPlumb,n=t.jsPlumbUtil,i=t.Katavorio,s=t.Biltong,o=function(t,n){var o="_katavorio_"+(n=n||"main"),r=t[o],a=t.getEventManager();return r||((r=new i({bind:a.on,unbind:a.off,getSize:e.getSize,getConstrainingRectangle:function(t){return[t.parentNode.scrollWidth,t.parentNode.scrollHeight]},getPosition:function(e,n){var i=t.getOffset(e,n,e._katavorioDrag?e.offsetParent:null);return[i.left,i.top]},setPosition:function(t,e){t.style.left=e[0]+"px",t.style.top=e[1]+"px"},addClass:e.addClass,removeClass:e.removeClass,intersects:s.intersects,indexOf:function(t,e){return t.indexOf(e)},scope:t.getDefaultScope(),css:{noSelect:t.dragSelectClass,droppable:"jtk-droppable",draggable:"jtk-draggable",drag:"jtk-drag",selected:"jtk-drag-selected",active:"jtk-drag-active",hover:"jtk-drag-hover",ghostProxy:"jtk-ghost-proxy"}})).setZoom(t.getZoom()),t[o]=r,t.bind("zoom",r.setZoom)),r},r=function(t,e){if(null==e)return[0,0];var n=h(e),i=c(n,0);return[i[t+"X"],i[t+"Y"]]},a=r.bind(this,"page"),l=r.bind(this,"screen"),u=r.bind(this,"client"),c=function(t,e){return t.item?t.item(e):t[e]},h=function(t){return t.touches&&t.touches.length>0?t.touches:t.changedTouches&&t.changedTouches.length>0?t.changedTouches:t.targetTouches&&t.targetTouches.length>0?t.targetTouches:[t]},d=function(t,e,i){e=n.fastTrim(e),void 0!==t.className.baseVal?t.className.baseVal=e:t.className=e;try{var s=t.classList;if(null!=s){for(;s.length>0;)s.remove(s.item(0));for(var o=0;o<i.length;o++)i[o]&&s.add(i[o])}}catch(t){n.log("JSPLUMB: cannot set class list",t)}},p=function(t){return void 0===t.className.baseVal?t.className:t.className.baseVal},f=function(t,e,i){e=null==e?[]:n.isArray(e)?e:e.split(/\s+/),i=null==i?[]:n.isArray(i)?i:i.split(/\s+/);var s=p(t).split(/\s+/),o=function(t,e){for(var n=0;n<e.length;n++)if(t)-1===s.indexOf(e[n])&&s.push(e[n]);else{var i=s.indexOf(e[n]);-1!==i&&s.splice(i,1)}};o(!0,e),o(!1,i),d(t,s.join(" "),s)};t.jsPlumb.extend(t.jsPlumbInstance.prototype,{headless:!1,pageLocation:a,screenLocation:l,clientLocation:u,getDragManager:function(){return null==this.dragManager&&(this.dragManager=new function(t){var e={},n=[],i={},s={},o={};this.register=function(r){var a,l=t.getId(r);e[l]||(e[l]=r,n.push(r),i[l]={});var u=function(e){if(e)for(var n=0;n<e.childNodes.length;n++)if(3!==e.childNodes[n].nodeType&&8!==e.childNodes[n].nodeType){var c=jsPlumb.getElement(e.childNodes[n]),h=t.getId(e.childNodes[n],null,!0);if(h&&s[h]&&s[h]>0){a||(a=t.getOffset(r));var d=t.getOffset(c);i[l][h]={id:h,offset:{left:d.left-a.left,top:d.top-a.top}},o[h]=l}u(e.childNodes[n])}};u(r)},this.updateOffsets=function(e,n){if(null!=e){n=n||{};var s,r=jsPlumb.getElement(e),a=t.getId(r),l=i[a];if(l)for(var u in l)if(l.hasOwnProperty(u)){var c=jsPlumb.getElement(u),h=n[u]||t.getOffset(c);if(null==c.offsetParent&&null!=i[a][u])continue;s||(s=t.getOffset(r)),i[a][u]={id:u,offset:{left:h.left-s.left,top:h.top-s.top}},o[u]=a}}},this.endpointAdded=function(n,r){r=r||t.getId(n);var a=document.body,l=n.parentNode;for(s[r]=s[r]?s[r]+1:1;null!=l&&l!==a;){var u=t.getId(l,null,!0);if(u&&e[u]){var c=t.getOffset(l);if(null==i[u][r]){var h=t.getOffset(n);i[u][r]={id:r,offset:{left:h.left-c.left,top:h.top-c.top}},o[r]=u}break}l=l.parentNode}},this.endpointDeleted=function(t){if(s[t.elementId]&&(s[t.elementId]--,s[t.elementId]<=0))for(var e in i)i.hasOwnProperty(e)&&i[e]&&(delete i[e][t.elementId],delete o[t.elementId])},this.changeId=function(t,e){i[e]=i[t],i[t]={},o[e]=o[t],o[t]=null},this.getElementsForDraggable=function(t){return i[t]},this.elementRemoved=function(t){var e=o[t];e&&(delete i[e][t],delete o[t])},this.reset=function(){e={},n=[],i={},s={}},this.dragEnded=function(e){if(null!=e.offsetParent){var n=t.getId(e),i=o[n];i&&this.updateOffsets(i)}},this.setParent=function(e,n,s,r,a){var l=o[n];i[r]||(i[r]={});var u=t.getOffset(s),c=a||t.getOffset(e);l&&i[l]&&delete i[l][n],i[r][n]={id:n,offset:{left:c.left-u.left,top:c.top-u.top}},o[n]=r},this.clearParent=function(t,e){var n=o[e];n&&(delete i[n][e],delete o[e])},this.revalidateParent=function(e,n,i){var s=o[n];if(s){var r={};r[n]=i,this.updateOffsets(s,r),t.revalidate(s)}},this.getDragAncestor=function(e){var n=jsPlumb.getElement(e),i=t.getId(n),s=o[i];return s?jsPlumb.getElement(s):null}}(this)),this.dragManager},recalculateOffsets:function(t){this.getDragManager().updateOffsets(t)},createElement:function(t,e,n,i){return this.createElementNS(null,t,e,n,i)},createElementNS:function(t,e,n,i,s){var o,r=null==t?document.createElement(e):document.createElementNS(t,e);for(o in n=n||{})r.style[o]=n[o];for(o in i&&(r.className=i),s=s||{})r.setAttribute(o,""+s[o]);return r},getAttribute:function(t,e){return null!=t.getAttribute?t.getAttribute(e):null},setAttribute:function(t,e,n){null!=t.setAttribute&&t.setAttribute(e,n)},setAttributes:function(t,e){for(var n in e)e.hasOwnProperty(n)&&t.setAttribute(n,e[n])},appendToRoot:function(t){document.body.appendChild(t)},getRenderModes:function(){return["svg"]},getClass:p,addClass:function(t,e){jsPlumb.each(t,function(t){f(t,e)})},hasClass:function(t,e){return(t=jsPlumb.getElement(t)).classList?t.classList.contains(e):-1!==p(t).indexOf(e)},removeClass:function(t,e){jsPlumb.each(t,function(t){f(t,null,e)})},toggleClass:function(t,e){jsPlumb.hasClass(t,e)?jsPlumb.removeClass(t,e):jsPlumb.addClass(t,e)},updateClasses:function(t,e,n){jsPlumb.each(t,function(t){f(t,e,n)})},setClass:function(t,e){null!=e&&jsPlumb.each(t,function(t){d(t,e,e.split(/\s+/))})},setPosition:function(t,e){t.style.left=e.left+"px",t.style.top=e.top+"px"},getPosition:function(t){var e=function(e){var n=t.style[e];return n?n.substring(0,n.length-2):0};return{left:e("left"),top:e("top")}},getStyle:function(t,e){return void 0!==window.getComputedStyle?getComputedStyle(t,null).getPropertyValue(e):t.currentStyle[e]},getSelector:function(t,e){return 1===arguments.length?null!=t.nodeType?t:document.querySelectorAll(t):t.querySelectorAll(e)},getOffset:function(t,e,n){t=jsPlumb.getElement(t),n=n||this.getContainer();for(var i={left:t.offsetLeft,top:t.offsetTop},s=e||null!=n&&t!==n&&t.offsetParent!==n?t.offsetParent:null,o=function(t){null!=t&&t!==document.body&&(t.scrollTop>0||t.scrollLeft>0)&&(i.left-=t.scrollLeft,i.top-=t.scrollTop)}.bind(this);null!=s;)i.left+=s.offsetLeft,i.top+=s.offsetTop,o(s),s=e?s.offsetParent:s.offsetParent===n?null:s.offsetParent;if(null!=n&&!e&&(n.scrollTop>0||n.scrollLeft>0)){var r=null!=t.offsetParent?this.getStyle(t.offsetParent,"position"):"static",a=this.getStyle(t,"position");"absolute"!==a&&"fixed"!==a&&"absolute"!==r&&"fixed"!==r&&(i.left-=n.scrollLeft,i.top-=n.scrollTop)}return i},getPositionOnElement:function(t,e,n){var i=void 0!==e.getBoundingClientRect?e.getBoundingClientRect():{left:0,top:0,width:0,height:0},s=document.body,o=document.documentElement,r=window.pageYOffset||o.scrollTop||s.scrollTop,a=window.pageXOffset||o.scrollLeft||s.scrollLeft,l=o.clientTop||s.clientTop||0,u=o.clientLeft||s.clientLeft||0,c=i.top+r-l+0*n,h=i.left+a-u+0*n,d=jsPlumb.pageLocation(t),p=i.width||e.offsetWidth*n,f=i.height||e.offsetHeight*n;return[(d[0]-h)/p,(d[1]-c)/f]},getAbsolutePosition:function(t){var e=function(e){var n=t.style[e];if(n)return parseFloat(n.substring(0,n.length-2))};return[e("left"),e("top")]},setAbsolutePosition:function(t,e,n,i){n?this.animate(t,{left:"+="+(e[0]-n[0]),top:"+="+(e[1]-n[1])},i):(t.style.left=e[0]+"px",t.style.top=e[1]+"px")},getSize:function(t){return[t.offsetWidth,t.offsetHeight]},getWidth:function(t){return t.offsetWidth},getHeight:function(t){return t.offsetHeight},getRenderMode:function(){return"svg"},draggable:function(t,e){var i;return t=n.isArray(t)||null!=t.length&&!n.isString(t)?t:[t],Array.prototype.slice.call(t).forEach(function(t){(i=this.info(t)).el&&this._initDraggableIfNecessary(i.el,!0,e,i.id,!0)}.bind(this)),this},snapToGrid:function(t,e,n){var i=[],s=function(t){var s=this.info(t);if(null!=s.el&&s.el._katavorioDrag){var o=s.el._katavorioDrag.snap(e,n);this.revalidate(s.el),i.push([s.el,o])}}.bind(this);if(1===arguments.length||3===arguments.length)s(t,e,n);else{var o=this.getManagedElements();for(var r in o)s(r,arguments[0],arguments[1])}return i},initDraggable:function(t,e,n){o(this,n).draggable(t,e),t._jsPlumbDragOptions=e},destroyDraggable:function(t,e){o(this,e).destroyDraggable(t),delete t._jsPlumbDragOptions},unbindDraggable:function(t,e,n,i){o(this,i).destroyDraggable(t,e,n)},setDraggable:function(t,e){return jsPlumb.each(t,function(t){this.isDragSupported(t)&&(this._draggableStates[this.getAttribute(t,"id")]=e,this.setElementDraggable(t,e))}.bind(this))},_draggableStates:{},toggleDraggable:function(t){var e;return jsPlumb.each(t,function(t){var n=this.getAttribute(t,"id");return e=!(e=null!=this._draggableStates[n]&&this._draggableStates[n]),this._draggableStates[n]=e,this.setDraggable(t,e),e}.bind(this)),e},_initDraggableIfNecessary:function(t,e,i,s,o){if(!jsPlumb.headless&&(null!=e&&e&&jsPlumb.isDragSupported(t,this))){var r=i||this.Defaults.DragOptions;if(r=jsPlumb.extend({},r),jsPlumb.isAlreadyDraggable(t,this))i.force&&this.initDraggable(t,r);else{var a=jsPlumb.dragEvents.drag,l=jsPlumb.dragEvents.stop,u=jsPlumb.dragEvents.start;this.manage(s,t),r[u]=n.wrap(r[u],function(t){var e=t.el._jsPlumbDragOptions,n=!0;return e.canDrag&&(n=e.canDrag()),n&&(this.setHoverSuspended(!0),this.select({source:t.el}).addClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:t.el}).addClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),this.setConnectionBeingDragged(!0)),n}.bind(this)),r[a]=n.wrap(r[a],function(t){var e=this.getUIPosition(arguments,this.getZoom());if(null!=e){var n=t.el._jsPlumbDragOptions;this.draw(t.el,e,null,!0),n._dragging&&this.addClass(t.el,"jtk-dragged"),n._dragging=!0}}.bind(this)),r[l]=n.wrap(r[l],function(t){for(var e,n=t.selection,i=function(t){null!=t[1]&&(e=this.getUIPosition([{el:t[2].el,pos:[t[1].left,t[1].top]}]),this.draw(t[2].el,e)),null!=t[0]._jsPlumbDragOptions&&delete t[0]._jsPlumbDragOptions._dragging,this.removeClass(t[0],"jtk-dragged"),this.select({source:t[2].el}).removeClass(this.elementDraggingClass+" "+this.sourceElementDraggingClass,!0),this.select({target:t[2].el}).removeClass(this.elementDraggingClass+" "+this.targetElementDraggingClass,!0),this.getDragManager().dragEnded(t[2].el)}.bind(this),s=0;s<n.length;s++)i(n[s]);this.setHoverSuspended(!1),this.setConnectionBeingDragged(!1)}.bind(this));var c=this.getId(t);this._draggableStates[c]=!0;var h=this._draggableStates[c];r.disabled=null!=h&&!h,this.initDraggable(t,r),this.getDragManager().register(t),o&&this.fire("elementDraggable",{el:t,options:r})}}},animationSupported:!0,getElement:function(t){return null==t?null:"string"==typeof(t="string"==typeof t?t:null!=t.length&&null==t.enctype?t[0]:t)?document.getElementById(t):t},removeElement:function(t){o(this).elementRemoved(t),this.getEventManager().remove(t)},doAnimate:function(t,i,s){s=s||{};var o=this.getOffset(t),r=function(t,e){var i=function(i){if(null!=e[i]){if(n.isString(e[i])){var s=e[i].match(/-=/)?-1:1,o=e[i].substring(2);return t[i]+s*o}return e[i]}return t[i]};return[i("left"),i("top")]}(o,i),a=r[0]-o.left,l=r[1]-o.top,u=s.duration||250,c=u/15,h=15/u*a,d=15/u*l,p=0,f=setInterval(function(){e.setPosition(t,{left:o.left+h*(p+1),top:o.top+d*(p+1)}),null!=s.step&&s.step(p,Math.ceil(c)),++p>=c&&(window.clearInterval(f),null!=s.complete&&s.complete())},15)},destroyDroppable:function(t,e){o(this,e).destroyDroppable(t)},unbindDroppable:function(t,e,n,i){o(this,i).destroyDroppable(t,e,n)},droppable:function(t,e){var i;return t=n.isArray(t)||null!=t.length&&!n.isString(t)?t:[t],(e=e||{}).allowLoopback=!1,Array.prototype.slice.call(t).forEach(function(t){(i=this.info(t)).el&&this.initDroppable(i.el,e)}.bind(this)),this},initDroppable:function(t,e,n){o(this,n).droppable(t,e)},isAlreadyDraggable:function(t){return null!=t._katavorioDrag},isDragSupported:function(t,e){return!0},isDropSupported:function(t,e){return!0},isElementDraggable:function(t){return(t=e.getElement(t))._katavorioDrag&&t._katavorioDrag.isEnabled()},getDragObject:function(t){return t[0].drag.getDragElement()},getDragScope:function(t){return t._katavorioDrag&&t._katavorioDrag.scopes.join(" ")||""},getDropEvent:function(t){return t[0].e},getUIPosition:function(t,e){var n=t[0].el;if(null==n.offsetParent)return null;var i=t[0].finalPos||t[0].pos,s={left:i[0],top:i[1]};if(n._katavorioDrag&&n.offsetParent!==this.getContainer()){var o=this.getOffset(n.offsetParent);s.left+=o.left,s.top+=o.top}return s},setDragFilter:function(t,e,n){t._katavorioDrag&&t._katavorioDrag.setFilter(e,n)},setElementDraggable:function(t,n){(t=e.getElement(t))._katavorioDrag&&t._katavorioDrag.setEnabled(n)},setDragScope:function(t,e){t._katavorioDrag&&t._katavorioDrag.k.setDragScope(t,e)},setDropScope:function(t,e){t._katavorioDrop&&t._katavorioDrop.length>0&&t._katavorioDrop[0].k.setDropScope(t,e)},addToPosse:function(t,n){var i=Array.prototype.slice.call(arguments,1),s=o(this);e.each(t,function(t){(t=[e.getElement(t)]).push.apply(t,i),s.addToPosse.apply(s,t)})},setPosse:function(t,n){var i=Array.prototype.slice.call(arguments,1),s=o(this);e.each(t,function(t){(t=[e.getElement(t)]).push.apply(t,i),s.setPosse.apply(s,t)})},removeFromPosse:function(t,n){var i=Array.prototype.slice.call(arguments,1),s=o(this);e.each(t,function(t){(t=[e.getElement(t)]).push.apply(t,i),s.removeFromPosse.apply(s,t)})},removeFromAllPosses:function(t){var n=o(this);e.each(t,function(t){n.removeFromAllPosses(e.getElement(t))})},setPosseState:function(t,n,i){var s=o(this);e.each(t,function(t){s.setPosseState(e.getElement(t),n,i)})},dragEvents:{start:"start",stop:"stop",drag:"drag",step:"step",over:"over",out:"out",drop:"drop",complete:"complete",beforeStart:"beforeStart"},animEvents:{step:"step",complete:"complete"},stopDrag:function(t){t._katavorioDrag&&t._katavorioDrag.abort()},addToDragSelection:function(t){o(this).select(t)},removeFromDragSelection:function(t){o(this).deselect(t)},clearDragSelection:function(){o(this).deselectAll()},trigger:function(t,e,n,i){this.getEventManager().trigger(t,e,n,i)},doReset:function(){for(var t in this)0===t.indexOf("_katavorio_")&&this[t].reset()},getEventManager:function(){return(n=(e=this)._mottle)||(n=e._mottle=new t.Mottle),n;var e,n},on:function(t,e,n){return this.getEventManager().on.apply(this,arguments),this},off:function(t,e,n){return this.getEventManager().off.apply(this,arguments),this}});var g,m;g=e.init,(m=function(){/complete|loaded|interactive/.test(document.readyState)&&void 0!==document.body&&null!=document.body?g():setTimeout(m,9)})()}.call("undefined"!=typeof window?window:this);