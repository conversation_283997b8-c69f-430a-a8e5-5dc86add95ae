<template>
  <!-- <div class="UploadExcelModal"> -->
  <vol-box :lazy="true" v-model="visible" :title="title" :width="width" :height="height" :padding="5"
    :onModelClose="onModelClose">

    <div class="upload-container">
      <a :href="upload.template.url" ref="template"></a>
      <!-- <a :href="template.url" ref="template"></a> -->
      <div class="button-group">
        <el-upload style="float: left" ref="uploadFile" :max-size="maxSize" :on-change="clearMsg"
          :before-upload="beforeUpload" :action="upload.url">
          <el-button size="small"><i class="el-icon-folder-opened"></i>{{ $ts('选择文件') }}</el-button>
        </el-upload>
        <el-button v-if="upload.template.url" style="margin-left: 10px" type="primary" size="small"
          @click="dowloadTemplate" :loading="loadingStatus">
          <!-- <el-button v-if="template.url" style="margin-left: 10px" type="primary" size="small" @click="dowloadTemplate"
          :loading="loadingStatus"> -->
          <!-- <i v-show="!loadingStatus" class="el-icon-bottom"></i> -->
          <i class="el-icon-bottom"></i> {{ $ts('下载模板') }} </el-button>
        <el-button type="success" size="small" @click="uploadClick" :loading="loadingStatus">
          <!-- <i v-show="!loadingStatus" class="el-icon-top"></i> -->
          <i class="el-icon-top"></i> {{ $ts('上传文件') }} </el-button>
      </div>
      <div class="alert">
        <el-alert :title="$ts('只能上传excel文件,文件大小不超过5M')" type="warning" :closable="false" show-icon></el-alert>
        <div v-html="desc"></div>
      </div>

      <div v-if="file">
        <h3>{{ $ts('文件列表') }}</h3>
        <div class="file-info">
          <span>{{ $ts('文件名') }}：{{ file.name }}</span>
          <span>{{ $ts('大小') }}{{ (file.size / 1024).toFixed(2) }}KB</span>
        </div>
      </div>
      <div v-show="message" class="v-r-message">
        <h3 class="title">{{ $ts('上传结果') }}</h3>
        <div class="text" :class="resultClass" v-html="message"></div>
      </div>
      <slot></slot>
    </div>


  </vol-box>
  <!-- </div> -->
</template>
<script>
import VolBox from '@/components/basic/VolBox.vue';
import UploadExcel from "@/components/basic/UploadExcel.vue"
export default {
  name: 'UploadExcelModal',
  components: {
    'vol-box': VolBox,
    UploadExcel,
  },
  props: {
    desc: { //导入excel弹出框的描述
      type: String,
      default: "",
    },
  },
  data() {
    return {
      title: this.$ts('UploadExcel'),
      width: 600,
      height: 350,
      visible: false,
      upload: {
        //导入上传excel对象
        excel: false, //导入的弹出框是否显示
        url: "/api/Demo_Order/Import", //导入的路径,如果没有值，则不渲染导入功能
        template: {
          //下载模板对象
          url: "/api/Demo_Order/DownLoadTemplate", //下载模板路径
          fileName: "未定义文件名", //模板下载的中文名
        },
        init: false, //是否有导入权限，有才渲染导入组件
      },
      maxSize: 102 * 5,
      model: true,
      file: null,
      loadingStatus: false,
      message: "",
      resultClass: "",
    }
  },
  methods: {

    // //2020.10.31添加导入前的方法
    importExcelBefore(formData) { //导入excel导入前
      return this.importBefore(formData);
    },
    importBefore(formData) { //导入excel导入前
      //往formData写一些其他参数提交到后台，
      // formData.append("val2", "xxx");
      //后台按下面方法获取请求的参数
      // Core.Utilities.HttpContext.Current.Request("val2");
      // console.log(this.$refs.table.getSelected())
      return true;
    },
    clearMsg() {
      console.log('AAAAAAAA')
      this.message = "";
    },
    reset() {
      this.file = null;
      this.message = "";
      this.resultClass = "";
    },
    getFileType() {
      let fileName = this.file.name.split(".").pop().toLocaleLowerCase() || "";
      if (["numbers", "csv", "xls", "xlsx"].indexOf(fileName) == -1) {
        this.$Message.error(this.$ts("只能选择excel文件"));
        return false;
      }
      return true;
    },
    beforeUpload(file) {
      this.file = file;
      if (!this.getFileType()) {
        return false;
      }
      return false;
    },
    uploadClick() {
      // let _url = this.url;
      let _url = this.upload.url;
      console.log(_url, !_url, '_url')
      if (!_url) {
        return this.$Message.error(this.$ts("没有配置好Url"));
      }

      if (!this.file) {
        return this.$Message.error(this.$ts(['请选择', "文件"]))
      }
      var formData = new FormData();
      formData.append("fileInput", this.file);
      if (!this.importExcelBefore(formData)) {
        return;
      }
      this.loadingStatus = true;
      this.http.post(_url, formData, true, { headers: { 'Content-Type': 'multipart/form-data' } }).then(
        (x) => {
          // this.$refs.uploadFile.clearFiles();
          this.loadingStatus = false;
          this.file = null;
          if (x.status) {
            this.$emit("importExcelAfter", x);
          }

          this.message = this.$ts(x.message || "导入失败");
          this.resultClass = x.status ? "v-r-success" : "v-r-error";
        },
        (error) => {
          this.loadingStatus = false;
        }
      );
    },
    dowloadTemplate() {
      let url = this.upload.template.url;
      // let url = this.template.url;
      let xmlResquest = new XMLHttpRequest();
      xmlResquest.open("GET", url, true);
      xmlResquest.setRequestHeader("lang", localStorage.getItem(lang_storage_key));
      xmlResquest.setRequestHeader('serviceId', localStorage.getItem('serviceId'));
      xmlResquest.setRequestHeader('deptId', localStorage.getItem('deptId'));

      xmlResquest.setRequestHeader("Content-type", "application/json");
      xmlResquest.setRequestHeader(
        "Authorization",
        this.$store.getters.getToken()
      );
      let fileName = this.upload.template.fileName + ".xlsx";
      // let fileName = this.template.fileName + ".xlsx";
      let elink = this.$refs.template;
      xmlResquest.responseType = "blob";
      this.loadingStatus = true;
      xmlResquest.onload = (oEvent) => {
        this.loadingStatus = false;
        if (xmlResquest.response.type == "application/json") {
          return this.message.error("未找到下载文件");
        }
        let content = xmlResquest.response;
        elink.download = fileName;
        let blob = new Blob([content]);
        elink.href = URL.createObjectURL(blob);
        elink.click();
      };
      xmlResquest.send();
    },

    importExcelAfter(data) {
      //2022.01.08增加明细表导入后方法判断

      if (!data.status) {
        return // this.$message.error(data.message);
      }
      if (data.data && typeof data.data == 'string') {
        data.data = JSON.parse(data.data)
      }
      let b = this.importAfter(data)
      if (b === false) {
        return
      }
      //明细表导入
      if (this.boxModel) {
        if (data.data) {
          data.data = JSON.parse(data.data)
        } else {
          data.data = []
        }
        data.data.forEach((x) => {
          x[this.detail.key] = undefined
          x[this.table.key] = undefined
        })
        this.importDetailAfter(data) //增加明细表导入后处理
        this.$refs.detail.rowData.unshift(...data.data)
        this.upload.excel = false
        return
      }
    },

    importDetailAfter(data) {
      //2022.01.08增加明细表导入后处理
    },

    importAfter(data) { //导入excel后刷新table表格数据
      this.search();
    },

    search() {
      //查询
      // let query = this.getSearchParameters();
      // this.$refs.table.load(query, true);
      this.$refs.table.load(null, true)
    },

    show() {
      this.visible = true
    },

    onModelClose() {
      this.closeModel();
    },
    closeModel() {
      this.$emit('ok')
      this.visible = false;
    },

  }
}
</script>
<style lang="less" scoped>
.upload-container {
  min-height: 276px !important;
  display: inline-block;
  width: 100%;
  padding: 10px;
  border: 1px dashed #989898;
  min-height: 250px;
  border-radius: 5px;

  .alert {
    margin-top: 12px;
  }

  .el-button-group>* {
    display: flex;
  }

  h3 {
    margin: 9px 0px;
  }

  .file-info>span {
    margin-right: 20px;
  }

  .v-r-message {
    margin-top: 10px;

    .title {
      margin-bottom: 2px;
    }

    >.text {
      font-size: 13px;
    }

    .v-r-success {
      color: #02b702;
    }

    .v-r-error {
      color: #dc0909;
    }
  }
}
</style>