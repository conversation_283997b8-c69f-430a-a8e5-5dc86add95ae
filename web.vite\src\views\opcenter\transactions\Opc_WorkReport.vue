<template>
    <div class="page-header">
    </div>
    <!-- 数据执行 -->
    <div class="table-item">
        <div class="table-item-header">
            <!-- <div class="table-item-border"></div>-->
            <span class="table-item-text">生产报工</span>
            <div class="table-item-buttons">
                <div>
                    <el-button type="success" icon="Plus" @click="addRow" plain>添加</el-button>
                    <el-button type="info" icon="Message" @click="viewMove" plain>过站明细</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                    <el-button type="success" icon="Check" @click="submit" plain>提交</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="masterTable" index :tableData="masterTableData" :columns="masterTableCols" :height="518"
            :pagination-hide="true" :load-key="true" :column-index="true" :defaultLoadPage="false"
            :ck="false"></vol-table>
    </div>
    <vol-box :lazy="true" v-model="show" title="过站明细" :width="1200" :padding="5">
        <div class="page-header">
            <!-- 搜索条件 -->
            <div style="display: flex; margin-top: 5px">
                <div style="margin-left: 10px">
                    <label style="width: 200px; margin-left: 5px; font-size: 16px">
                        <span>工单</span>
                    </label>
                    <div style="margin-top: 5px">
                        <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                            remote-show-suffix :remote="true" :remote-method="getMfgOrder">
                            <el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name"
                                :value="item.Name" />
                        </el-select>
                    </div>
                </div>
                <div style="margin-left: 10px">
                    <label style="width: 200px; margin-left: 5px; font-size: 16px">
                        <span>工序</span></label>
                    <div style="margin-top: 5px">
                        <el-select v-model="searchSpec" clearable filterable placeholder="键入搜索" style="width: 200px"
                            remote-show-suffix :remote="true" :remote-method="getSpec">
                            <el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
                        </el-select>
                    </div>
                </div>
                <div style="margin-left: 10px; ">
                    <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                        <span>时间</span>
                    </label>
                    <div style="margin-top: 5px;">
                        <el-date-picker v-model="searchDate" type="daterange" range-separator="To"
                            start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
                    </div>
                </div>
            </div>
        </div>
        <!-- 数据执行 -->
        <div class="table-item">
            <div class="table-item-header">
                <!-- <div class="table-item-border"></div>-->
                <span class="table-item-text">过站明细</span>
                <div class="table-item-buttons">
                    <div>
                        <el-button icon="Search" @click="queryRow" plain>查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery" plain>重置</el-button>
                    </div>
                </div>
            </div>
            <vol-table ref="moveTable" index :tableData="moveTableData" :columns="moveTableCols" :height="500"
                :pagination-hide="false" :load-key="true" :column-index="true" :url="apiUrl.getMoveHistory"
                @loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false" :ck="false"></vol-table>
        </div>
    </vol-box>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';

export default {
    components: {
        'vol-table': VolTable,
        'vol-box': VolBox
    },
    data() {
        return {
            show: false,
            searchMfgOrder: null,
            searchSpec: null,
            searchDate: null,
            specs: [],
            mfgorders: [],
            masterTableData: [],
            moveTableData: [],
            pagination: { total: 0, size: 30, sortName: "" },

            masterTableCols: [
                {
                    field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center',
                    render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <el-select v-model={row.MfgOrder} clearable filterable placeholder="键入搜索"
                                    remote
                                    remote-method={
                                        (query) => {
                                            this.getMfgOrder(query);
                                        }
                                    }
                                    onChange={
                                        (value) => {
                                            this.getSpecs(row);
                                            this.getWorkTime(row);
                                        }
                                    }
                                >
                                    {this.mfgorders.map(item => (
                                        <el-option key={item.Name} label={item.Name} value={item.Name} />
                                    ))}
                                </el-select>
                            </div>
                        );
                    }
                },
                {
                    field: 'Spec', title: '工序', type: 'string', width: 120, align: 'center',
                    render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <el-select v-model={row.Spec} clearable filterable placeholder="键入搜索"
                                   /* remote
                                    remote-method={
                                        (query) => {
                                            this.getSpec(query);
                                        }
                                    }*/
                                    onChange={
                                        (value) => {
                                            this.getWorkTime(row);
                                        }
                                    }
                                >
                                    {this.specs.map(item => (
                                        <el-option key={item.Name} label={item.Name} value={item.Name} />
                                    ))}
                                </el-select>
                            </div>
                        );
                    }
                },
                
                { field: 'Product', title: '产品编码', type: 'string', width: 120, align: 'center' },
                { field: 'P_Description', title: '产品描述', type: 'string', width: 120, align: 'center' },
                { field: 'Qty', title: '生产数量', type: 'string', width: 80, align: 'center' },
                { field: 'StandardTime', title: '标准工时', type: 'string', width: 100, align: 'center' },
                { field: 'ActualTime', title: '实际工时', type: 'string', width: 100, align: 'center' },
                { field: 'ReportQty', title: '报工数量', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
                { field: 'mfgOrderQty', title: '工单数量',  type: 'string', width: 100, align: 'center' },
                { field: 'txnDate', title: '生产日期', type: 'datetime', edit: { type: 'datetime' }, width: 100, align: 'center' },
                { field: 'WorkTime', title: '工时', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
                {
                    title: '', field: 'Action', align: 'center', width: 50, fixed: 'right', render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <el-button
                                    onClick={($e) => {
                                        this.masterTableData.splice(index, 1);
                                    }}
                                    size="small"
                                    type="danger"
                                    icon="Delete"
                                    circle
                                    plain>
                                </el-button>
                                {/* 这里可以接着放按钮或者其他组件 */}
                            </div>
                        );
                    }
                }
            ],
            moveTableCols: [
                // { field: 'StepName', title: '工步', type: 'string', width: 120, align: 'center' },
                { field: 'Spec', title: '工序', type: 'string', width: 120, align: 'center' },
                { field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },
                { field: 'Container', title: '批次', type: 'string', width: 120, align: 'center' },
                { field: 'Product', title: '产品编码', type: 'string', width: 120, align: 'center' },
                { field: 'P_Description', title: '产品描述', type: 'string', width: 120, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
                { field: 'ResourceName', title: '设备', type: 'string', width: 100, align: 'center' },
                { field: 'StandardTime', title: '标准工时', type: 'string', width: 100, align: 'center' },
                { field: 'ActualTime', title: '实际工时', type: 'string', width: 100, align: 'center' },
                
            ],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/getRevisionObject",
                getNameObject: "/api/query/getNameObject",
                getWorkTime: "/api/query/getWorkTime",
                getMoveHistory: '/api/query/getMoveHistory',
                workReport: '/api/cdo/workReport',
                getspecname:'/api/query/GetSpecByOrder',
            }

        }
    },
    created() {

    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        getMfgOrder(query) {
            if (query) {
                let params = {
                    cdo: "mfgorder",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.mfgorders = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getSpec(query) {
            if (query) {
                let params = {
                    cdo: "spec",
                    name: query
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.specs = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getSpecs(row) {
            let params = {
                order: row.MfgOrder,
            };
            this.http.get(this.apiUrl.getspecname, params).then(res => {
                if (res.Result == 1) {
                    this.specs = [{Name: res.Data.Name}];
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        getWorkTime(row) {

            if (row.Spec && row.MfgOrder) {
                let params = {
                    spec: row.Spec,
                    mfgorder: row.MfgOrder
                };
                this.http.post(this.apiUrl.getWorkTime, params, true).then(res => {
                    if (res.Result == 1) {
                        row.Spec  = res.Data.tableData[0].Spec;
                        row.MfgOrder  = res.Data.tableData[0].MfgOrder;
                        row.Product  = res.Data.tableData[0].Product;
                        row.P_Description  = res.Data.tableData[0].P_Description;
                        row.Qty  = res.Data.tableData[0].Qty;
                        row.StandardTime  = res.Data.tableData[0].StandardTime;
                        row.ActualTime  = res.Data.tableData[0].ActualTime;
                        row.mfgOrderQty  = res.Data.tableData[0].MfgOrderQty;
                        
                    } else {
                       this.$message.error(res.Message);
                    }
                })
            }
        },
        submit(){
            if (this.masterTableData.length == 0) {
                this.$message.error("请添加报工数据");
                return;
            }

            // 检查所有工单是否相同
            const firstMfgOrder = this.masterTableData[0].MfgOrder;
            for (let i = 1; i < this.masterTableData.length; i++) {
                if (this.masterTableData[i].MfgOrder !== firstMfgOrder) {
                    this.$message.error("所有报工数据的工单必须相同");
                    return;
                }
            }

            // 检查是否有重复的 Spec 和 MfgOrder 数据
            const seen = new Set();
            for (let i = 0; i < this.masterTableData.length; i++) {
                const row = this.masterTableData[i];
                const key = `${row.Spec}-${row.MfgOrder}`;
                if (seen.has(key)) {
                    this.$message.error(`第 ${i + 1} 行数据中，工序和工单与已有数据重复`);
                    return;
                }
                seen.add(key);
            }

            // 检查每个数据项的必填字段是否为空
            for (let i = 0; i < this.masterTableData.length; i++) {
                const row = this.masterTableData[i];
                if (!row.Spec || !row.MfgOrder || !row.Qty || !row.ReportQty || !row.WorkTime) {
                    this.$message.error(`第 ${i + 1} 行数据中，工序、工单、生产数量、报工数量、工时不能为空`);
                    return;
                }

                // 检查 ReportQty 是否超过 Qty
                if (row.ReportQty > row.Qty) {
                    this.$message.error(`第 ${i + 1} 行数据中，报工数量不能超过生产数量`);
                    row.ReportQty = null; // 清空 ReportQty
                    return;
                }
            }

            if (this.masterTableData.length > 0) {
                let params = {
                    User: this.userInfo.userName,
					Password: this.userInfo.userPwd,
                    requestData: this.masterTableData
                };
                this.http.post(this.apiUrl.workReport, params, true).then(res => {
                    if (res.Result == 1) {
                        this.reset();
                        this.$message.success('报工成功。');
                    } else {
                       this.$message.error(res.Message);
                    }
                })
            }
        },
        queryRow() {
            if  (!this.searchMfgOrder) {
                this.$message.error("请选择工单");
                return;
            }
            if (!this.searchSpec) {
                this.$message.error("请选择工序");
                return;
            }
            this.$refs.moveTable.load(null, true);
        },
        loadBefore(params, callBack) {
            params["mfgorder"] = this.searchMfgOrder;
            params["spec"] = this.searchSpec;
            params["StartTime"] = this.searchDate != null ? this.searchDate[0] : '';
            params["EndTime"] = this.searchDate != null ? this.searchDate[1] : '';
            callBack(true)
        },
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                this.moveTableData = result.Data.tableData;
                this.$refs.moveTable.rowData = result.Data.tableData;
                this.$refs.moveTable.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },
        reset() {
            this.masterTableData = [];
        },
        resetQuery(){
            this.searchMfgOrder = null;
            this.searchSpec = null;
            this.searchDate = null;
            this.moveTableData = [];
            this.$refs.moveTable.rowData = [];
            this.$refs.moveTable.paginations.total = 0;
        },
        addRow() {
            this.masterTableData.push({               // 取东8区当前时间
                txnDate : new Date().getFullYear() + '-' + 
                          (new Date().getMonth() + 1).toString().padStart(2, '0') + '-' + 
                          new Date().getDate().toString().padStart(2, '0') + ' ' +
                          new Date().getHours().toString().padStart(2, '0') + ':' +
                          new Date().getMinutes().toString().padStart(2, '0') + ':' +
                          new Date().getSeconds().toString().padStart(2, '0'),
            });
        },
        viewMove() {
            this.show = true;
        }
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    // .table-item-text {
    // 	font-weight: bolder;
    // 	border-bottom: 1px solid #0c0c0c;
    // }
    .table-item-text {
        margin-top: 3px;
        padding-bottom: 6px;
        font-weight: bold;
        font-size: 15px;
        color: #484848;
        white-space: nowrap;
        border-bottom: 2px solid #676767;
        margin-bottom: -1px;
        letter-spacing: 1px;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196f3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>