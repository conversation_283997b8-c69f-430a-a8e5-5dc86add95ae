<template>
    <!-- 上料区域 -->
    <div class="table-item-header">
        <div class="table-item-border"></div> <span class="table-item-text">包装管理</span>
    </div>
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>流转批次</span>
            </label>
            <div style="margin-top: 5px;">
                <el-input ref="inputContainer" style="width: 200px;" @change="changeContainer" v-model="container"
                    clearable placeholder="请输入/扫描"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px">
            <label style="width: 100px; margin-left: 5px; font-size: 16px">
                <span>工单</span>
            </label>
            <div style="margin-top: 5px">
                <el-input disabled style="width: 200px" v-model="infoMfgOrder"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px">
            <label style="width: 100px; margin-left: 5px; font-size: 16px">
                <span>产品编码</span>
            </label>
            <div style="margin-top: 5px">
                <el-input disabled style="width: 200px" v-model="infoProduct"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px">
            <label style="width: 100px; margin-left: 5px; font-size: 16px">
                <span>产品描述</span>
            </label>
            <div style="margin-top: 5px">
                <el-input disabled style="width: 310px" v-model="infoDescription"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px">
            <label style="width: 100px; margin-left: 5px; font-size: 16px">
                <span>流转批数量</span>
            </label>
            <div style="margin-top: 5px">
                <el-input disabled style="width: 100px" v-model="infoQty"></el-input>
            </div>
        </div>
    </div>
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>内箱码</span>
            </label>
            <div style="margin-top: 5px;">
                <el-input ref="inputInnerLabel" style="width: 200px;" @change="changeInnerLabel" v-model="innerLabel"
                    clearable placeholder="请输入/扫描"></el-input>
            </div>
        </div>
    </div>
    <el-divider />
    <div class="table-item">
        <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">待包装批次详情</span>
            <div class="table-item-buttons">
                <div>
                    <el-button type="success" icon="Check" @click="submit" plain>提交</el-button>
                    <el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="table" index :tableData="tableData" :columns="columns" :height="380" :pagination-hide="true"
            :load-key="true" :column-index="true" :ck="false"></vol-table>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from "@/components/basic/VolForm.vue";
import { mapState } from 'vuex';

export default {
    components: {
        'vol-table': VolTable,
        VolForm,
    },
    data() {
        return {
            container: null,
            innerLabel: null,
            infoMfgOrder: null,
            infoProduct: null,
            infoDescription: null,
            infoQty: null,
            tableData: [],
            tempTableData: [],

            columns: [
                { field: 'InnerLabelId', title: '内箱码', type: 'string', width: 130, align: 'center' },
                { field: 'InnerQty', title: '内箱数量', type: 'string', width: 100, align: 'center' },
                { field: 'PackingQty', title: '包装数量', edit:{type: 'string'}, type: 'string', width: 100, align: 'center' },
                {
                    title: '', field: 'Action', align: 'center', width: 50, fixed: 'right', render: (h, { row, column, index }) => {
                        return (
                            <div>
                                <el-button
                                    onClick={($e) => {
                                        this.tableData.splice(index, 1);
                                    }}
                                    size="small"
                                    type="danger"
                                    icon="Delete"
                                    circle
                                    plain>
                                </el-button>
                                {/* 这里可以接着放按钮或者其他组件 */}
                            </div>
                        );
                    }
                }
            ],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getPackingInfo: "/api/query/GetPackingInfo",
                getInnerLabelInfo: "/api/query/getInnerLabelInfo",
                packingManagement: '/api/CDO/packingManagement',
            },
        }
    },
    created() {

    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        changeContainer() {
            if (!this.container) {
                this.infoMfgOrder = null;
                this.infoProduct = null;
                this.infoDescription = null;
                this.infoQty = null;
                return;
            };
            let params = {
                container: this.container
            };
            this.http.post(this.apiUrl.getPackingInfo, params, true).then(res => {
                if (res.Result == 1) {
                    if (res.Data.tableData.length == 0) {
                        this.$message.error('未找到批次信息');
                        return;
                    }
                    if (res.Data.tableData[0].Qty <= 0) {
                        this.$message.error('批次数量为0，请输入/扫描其它流转批次');
                        return;
                    }
                    this.infoMfgOrder = res.Data.tableData[0].MfgOrder;
                    this.infoProduct = res.Data.tableData[0].Product;
                    this.infoDescription = res.Data.tableData[0].P_Description;
                    this.infoQty = res.Data.tableData[0].Qty;
                    this.tableData = res.Data.tableData[0].Detail;
                    // 深拷贝tempTableData，确保与tableData独立
                    this.tempTableData = JSON.parse(JSON.stringify(res.Data.tableData[0].Detail));
                    this.$refs.inputInnerLabel.focus();
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        changeInnerLabel(newLabel) {
            // 检查tableData中是否存在相同的innerLabel
            const isDuplicate = this.tableData.some(item => item.InnerLabelId === newLabel);
            if (isDuplicate) {
                this.$message.error('该内箱标签已存在，请重新输入！');
                this.$refs.inputInnerLabel.focus();
                this.$refs.inputInnerLabel.select();
                return;
            }
            if (!this.innerLabel) {
                return;
            }
            let params = {
                innerLabelId: this.innerLabel
            };
            this.http.post(this.apiUrl.getInnerLabelInfo, params, true).then(res => {
                if (res.Result == 1) {
                    if (res.Data.tableData.length == 0) {
                        this.$message.error('未找到标签信息');
                        return;
                    }
                    this.tableData.push({ InnerLabelId: res.Data.tableData[0].InnerLabelId, 
                        InnerQty: res.Data.tableData[0].InnerQty,
                        PackingQty: res.Data.tableData[0].InnerQty })
                    this.$refs.inputInnerLabel.focus();
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        submit() {
            if (!this.container) {
                this.$message.error('请输入/扫描流转批');
                return;
            }
            if (this.tableData.length == 0) {
                this.$message.error('请输入/扫描内箱标签');
                return;
            }
            // 比较tableData和tempTableData，找出tempTableData中不存在于tableData的数据
            const requestData2 = this.tempTableData.filter(tempItem => 
                !this.tableData.some(item => item.InnerLabelId === tempItem.InnerLabelId)
            );
            let params = {
                user: this.userInfo.userName,
                password: this.userInfo.userPwd,
                container: this.container,
                qty: this.infoQty,
                requestData: this.tableData,
                requestData2: requestData2
            };
            this.http.post(this.apiUrl.packingManagement, params, true).then(res => {
                if (res.Result == 1) {
                    this.reset();
                    this.$message.success('操作成功');
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        reset() {
            this.container = null;
            this.innerLabel = null;
            this.infoMfgOrder = null;
            this.infoProduct = null;
            this.infoDescription = null;
            this.infoQty = null;
            this.tableData = [];
            this.tempTableData = [];
        }
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>