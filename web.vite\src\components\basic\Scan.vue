<template>

  <div class="scan-overlay" v-show="modelValue">
    <div class="scanner-container">
      <video ref="video" class="scanner-video" id="video" autoplay></video>
      <div class="scan-frame">
        <div class="scan-line"></div>  
      </div>
    </div>
    <div class="close-btn" @click="close">×</div>
  </div>
</template>

<script>
import 'webrtc-adapter'
import { BrowserMultiFormatReader,DecodeHintType } from '@zxing/library'

export default {
  name: 'BarScan',
  props: {
    modelValue: Boolean  // 修改属性名为标准v-model命名
  },
  watch: {
    modelValue(val) {  // 同步修改watch属性名
      if (val) {
        this.openScan()
      }
    }
  },
  mounted() {
    this.codeReader = new BrowserMultiFormatReader()
  },
  beforeUnmount() {
    this.codeReader && this.codeReader.reset()
  },
  methods: {
    async openScan() {
      try {
        const hints = new Map();
        hints.set(DecodeHintType.POSSIBLE_FORMATS, [
          // 添加数字条码格式支持
          'EAN_13',
          'EAN_8',
          'UPC_A',
          'UPC_E',
          'CODE_128',  // CODE_128格式支持数字
          'ITF',        // ITF格式用于数字
          'CODE_11'
        ]);
        const devices = await this.codeReader.listVideoInputDevices()
        const firstDeviceId = devices.find(d => d.label.includes('back'))?.deviceId || devices[0]?.deviceId
        this.codeReader.hints = hints;
        this.decodeFromDevice(firstDeviceId)
      } catch (error) {
        // console.error('摄像头访问失败:', error)
        // this.$emit('value', false)
      }
    },
    decodeFromDevice(deviceId) {
      this.codeReader.decodeFromInputVideoDeviceContinuously(
        deviceId,
        'video',
        (result, error) => {
          if (result) {
            this.$emit('decoded', result.text)
            this.$emit('input', false)
            this.codeReader && this.codeReader.reset();
          }
          if (error) {
            
          }
        }
      )
    },
    
    close() {
      this.$emit('update:modelValue', false);  // 修改事件名为标准v-model格式
      this.codeReader && this.codeReader.reset();
    }
  }
}
</script>

  
<style scoped>
.scan-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
}

.scanner-container {
  position: relative;
  width: 80%;
  height: 80%;
  margin: 5% auto;
}

.scanner-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 60%;
  border: 2px solid #00ff33;
  box-shadow: 0 0 20px rgba(0, 255, 51, 0.3);
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  color: white;
  font-size: 40px;
  cursor: pointer;
}
</style>