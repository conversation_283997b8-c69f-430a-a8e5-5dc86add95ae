<template>
  <vol-box :lazy="true" v-model="visible" :title="title" :width="width" :padding="5" :onModelClose="onModelClose" :modal="false">
    <!-- 弹出框内容 -->
    <div class="form-content" :style="style">
      <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
        <!-- <VolHeader :title="this.$ts(title)" :text="text" icon="el-icon-s-grid"></VolHeader> -->
        <!-- <div style="text-align: end;width: 100%; margin-top: 0px;">
          <el-button type="primary" plain @click="getForm">{{ this.$ts('Search') }}</el-button>
          <el-button type="success" plain @click="reset">{{ this.$ts('Reset') }}</el-button>
        </div> -->
      </VolForm>
    </div>

    <template #footer>
      <div>
        <el-button type="primary" size="small" @click="handleModelOk">{{ this.$ts('Confirm') }}</el-button>
        <el-button type="default" size="small" @click="closeModel">{{ this.$ts('Close') }}</el-button>
      </div>
    </template>
  </vol-box>
</template>
<script lang="jsx">
import VolBox from '@/components/basic/VolBox.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import {GlobalElMessageBox} from '@/mixins/commonMixin.js'
export default {
  components: {
    'vol-box': VolBox,
    VolHeader,
    VolForm
  },
  mixins: [GlobalElMessageBox],
  data() {
    return {
      visible: false,
      model: [],
      title: this.$ts('Edit'),
      text: this.$ts('Default'),
      width: 1000,
      style: { 'max-height': '550px' },
      formFields: {
        JobOrderName: null,
        ResourceName: null,
        YP_RepairingApplicant: null,
        YP_TaskStartTime: null,
        ResourceDescription: null,
        Description: null,
        YP_RepairingAttach: null,
        JobModel: null,
        YP_EquipRepairExpenseType: null,
        YP_TaskStatus: null,
        YP_EquipIMRTech: null,
        IsCanCancel: null,
      },
      formRules: [
        [
          {
            title: this.$ts('Repair Order Number'),
            placeholder: this.$ts('Repair Order Number'),
            required: false,
            disabled: true,
            field: "JobOrderName",
            type: "text",
            colSize: 6,
          },
          {
            title: this.$ts('Equipment code'),
            placeholder: this.$ts('Equipment code'),
            required: false,
            disabled: true,
            field: "ResourceName",
            type: "text",
            colSize: 6,
          },
        ],
        [
          {
            title: this.$ts('Applicant'),
            placeholder: this.$ts('Applicant'),
            required: false,
            disabled: true,
            field: "YP_RepairingApplicant",
            type: "text",
            colSize: 6,
          },
          {
            title: this.$ts('Application Time'),
            placeholder: this.$ts('Application Time'),
            required: false,
            disabled: true,
            field: "YP_TaskStartTime",
            type: "text",
            colSize: 6,
          },
        ],
        [
          {
            title: this.$ts('Equipment Description'),
            placeholder: this.$ts('Equipment Description'),
            required: false,
            disabled: true,
            field: "ResourceDescription",
            type: "textarea",
            min: 3,
            max: 5,
            colSize: 6,
          },
          {
            title: this.$ts('Malfunction Description'),
            placeholder: this.$ts('Malfunction Description'),
            required: false,
            disabled: true,
            field: "Description",
            type: "textarea",
            min: 3,
            max: 5,
            colSize: 6,
          },
        ],
        // [
        //   {
        //     title: this.$ts('Attachment'),
        //     required: false,
        //     field: "YP_RepairingAttach",
        //     type: "img",
        //     hidden: false,
        //     multiple: true,
        //     disabled: true,
        //     maxFile: 5,
        //     maxSize: 5,
        //     url: "api/Demo_Order/Upload",
        //     colSize: 6,
        //   }
        // ],
        [
          {
            dataKey: "JobModel",
            data: [],
            title: this.$ts('Maintenance Method'),// 维修方式
            placeholder: this.$ts('Maintenance Method'),
            filter: true,
            required: true,
            field: "JobModel",
            type: "select",
            colSize: 6,
            onChange: (value, option) => { this.handleonSelect(value, option) },
          },
          {
            dataKey: "EquipRepairExpenseType",
            data: [],
            title: this.$ts('Fee Type Audit'),// 费用类型审核
            placeholder: this.$ts('Fee Type Audit'),
            filter: true,
            required: true,
            field: "YP_EquipRepairExpenseType",
            type: "select",
            colSize: 6,
          },
        ],
        [
          {
            dataKey: "TaskStatus",
            data: [],
            title: this.$ts('Repair Order Status'),// 维修单状态
            placeholder: this.$ts('Repair Order Status'),
            filter: true,
            required: true,
            field: "YP_TaskStatus",
            type: "select",
            colSize: 6,
          },
          {
            dataKey: "EquipIMRTech",
            data: [],
            title: this.$ts('Technician'),// 技术员
            placeholder: this.$ts('Technician'),
            filter: true,
            required: true,
            field: "YP_EquipIMRTech",
            type: "select",
            colSize: 6,
          },
        ],
        [
          {
            title: this.$ts('Can I cancel'),
            placeholder: this.$ts('Can I cancel'),
            required: true,
            disabled: true,
            hidden: true,
            field: "IsCanCancel",
            type: "text",
            colSize: 6,
          },
        ]
      ],
      paginationHide: true,
      url: {
        add: 'api/CDO/ConfirmJobOrder',
      }
    };
  },
  methods: {

    add() {
      this.edit({});
    },

    edit(record) {
      this.model = Object.assign({}, record[0]);
      console.log(this.model, 'record');
      console.log(this.model.IsCanCancel);

      this.formFields = Object.assign({}, this.model)
      // this.formFields.YP_TaskStartTime = this.base.getDate(true);

      // IsCanCancel 等于 0 时 Repair Order Status 不可选择
      debugger;
      if (this.model.IsCanCancel !== null && this.model.IsCanCancel == "0") {
        this.formRules[4][0].required = false;
        this.formRules[4][0].disabled = true;
      } else {
        this.formRules[4][0].disabled = false;
        //   this.formFields = Object.assign({}, this.model)
      }
      this.visible = true;
    },

    handleonSelect(value, option) {
      this.formFields.YP_EquipRepairExpenseType = null;
      // 维修方式为内修时，费用类型审核可以不必填
      if (value !== '内修') {
        this.formRules[4][1].required = true;
        this.formRules[4][1].disabled = false;
      } else {
        this.formRules[4][1].required = false;
        this.formRules[4][1].disabled = true;
      }
    },

    handleModelOk() {
      this.$refs.form.validate(() => {
        let params = {
          JobOrderName: this.model.JobOrderName,
          IsCanCancel: this.model.IsCanCancel,
          JobModel: this.formFields.JobModel,
          YP_EquipRepairExpenseType: this.formFields.YP_EquipRepairExpenseType,
          YP_TaskStatus: this.formFields.YP_TaskStatus,
          YP_EquipIMRTech: this.formFields.YP_EquipIMRTech,
        }
        // console.log(params, 'params');
        this.http.post(this.url.add, params, true).then((res) => {
          if (res.status == 1) {
            this.onModelClose('success')
            this.$message.success(this.$ts('Execution Success!'))
            this.$message.success(res.message)
          } else {
            this.$message.error(this.$ts('Execution failed, please contact MES team.'))
            this.$message.warning(res.message)
          }
        }).catch((err) => {this.$message.error(err.message)}).finally(() => {this.$emit('ok')})
      })
    },

    reset() {
      this.$refs.form.reset(this.searchFormFields);
    },

    onModelClose(status) {
      if(status == 'success'){
        this.closeModel()
      }else{
        this.handleGlobalMessageBox()
      }
    },
    closeModel() {
      this.reset()
      this.model = {};
      this.visible = false;
    },
  }
};
</script>