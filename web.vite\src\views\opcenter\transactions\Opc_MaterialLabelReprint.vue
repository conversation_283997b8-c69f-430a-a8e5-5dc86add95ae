<template>
    <div class="table-item-header">
        <div class="table-item-border"></div> <span class="table-item-text">物料标签打印</span>
    </div>
    <!-- 搜索条件 -->
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>物料批次</span></label>
            <div style=" margin-top: 5px;">
                <el-input style="width: 200px;" v-model="searchContainer" clearable placeholder="请输入"></el-input>
            </div>
        </div>
        <div style="margin-left: 10px; ">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>物料编码</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="searchProduct" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getProduct" :loading="loading">
                    <el-option v-for="item in products" :key="item.Name" :label="item.Name" :value="item.Name" />
                </el-select>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 240px; margin-left: 5px; font-size: 16px;">
                <span>工单</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getMfgOrder" :loading="loading">
                    <el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
                </el-select>
            </div>
        </div>
        <div style="margin-left: 10px; ">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>接收时间</span>
            </label>
            <div style="margin-top: 5px;">
                <el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
                    end-placeholder="结束" :size="size" style="width: 240px" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD hh:mm:ss" />
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span> </span>
            </label>
            <div style="margin-top: 6px;">
                <el-button icon="Search" @click="queryRow" plain>查询</el-button>
                <el-button icon="Refresh" type="info" plain @click="reset">重置</el-button>
            </div>
        </div>
    </div>
    <el-divider />
    <div class="table-item">
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="420"
            :pagination-hide="false" :load-key="true" :column-index="true" :single="true" :ck="true"
            :url="apiUrl.getMaterialManageInfo" @loadBefore="loadBefore" @loadAfter="loadAfter"
            :defaultLoadPage="false"></vol-table>
    </div>
    <div style="display: flex;margin-top: 5px;">
        <div style="margin-left: 10px">
            <label style="width: 100px; margin-left: 5px; font-size: 16px">
                <span>打印机</span></label>
            <div style="margin-top: 5px">
                <el-select v-model="printQueue" clearable filterable placeholder="键入搜索" style="width: 200px"
                    remote-show-suffix :remote="true" :remote-method="getPrinter" :loading="loading">
                    <el-option v-for="item in printers" :key="item.Name" :label="item.Name" :value="item.Description" />
                </el-select>
            </div>
        </div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span></span>
            </label>
            <div style="margin-top: 7px;">
                <el-button type="primary" icon="printer" @click="materialLabelPrint" plain>打印</el-button>

            </div>
        </div>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from "@/components/basic/VolForm.vue";
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        VolForm,
    },
    data() {
        return {
            containerTemp: null,//传递选择的container
            searchContainer: null,
            searchMfgOrder: null,
            searchProduct: null,
            searchTxnDate: null,
            printQueue: null,

            mfgorders: [],
            products: [],
            printers: [],

            columns: [
                { field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
                { field: 'Container', title: '物料批次', type: 'string', width: 130, align: 'center' },
                { field: 'Product', title: '物料编号', type: 'string', width: 100, align: 'center' },
                { field: 'P_Description', title: '物料描述', type: 'string', width: 100, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
                { field: 'Vendor', title: '供应商', type: 'string', width: 80, align: 'center' },
                { field: 'Uom', title: '单位', type: 'string', width: 100, align: 'center' },
                { field: 'MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
                { field: 'TxnDate', title: '接收时间', type: 'datetime', width: 150, align: 'center' },
                // { field: 'Comments', title: '备注', type: 'string', width: 130, align: 'center' },
            ],
            tableData: [],

            //接口地址
            apiUrl: {
                getProductByType: "/api/query/GetProductByType",
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getDefaultPrinter: '/api/query/getDefaultPrinter',
                getMaterialManageInfo: "/api/query/GetMaterialManageInfo",
                ERPReprint: '/api/CDO/ERPReprint',
            },
        }
    },
    created() {
        this.getDefaultPrinter();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        addDays(dateStr, days) {
            var date = new Date(dateStr);
            date.setDate(date.getDate() + days);
            return date.toLocaleDateString();
        },
        getMfgOrder(query) {
            if (query) {
                let params = {
                    cdo: "mfgorder",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.mfgorders = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getProduct(query) {
            if (query) {
                let params = {
                    cdo: "product",
                    name: query
                };
                this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
                    if (res.Result == 1) {
                        this.products = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getPrinter(query) {
            if (query) {
                let params = {
                    cdo: "PrintQueue",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.printers = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        getDefaultPrinter() {
            if (this.userInfo.userName) {
                let params = {
                    username: this.userInfo.userName
                };
                this.http.post(this.apiUrl.getDefaultPrinter, params).then(res => {
                    if (res.Result == 1) {
                        this.printers = [{ Name: res.Data.Printer, Description: res.Data.Description }];
                        this.printQueue = res.Data.Description; // 设置默认选中第一个打印机
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        reset() {
            this.searchContainer = null;
            this.searchProduct = null;
            this.searchMfgOrder = null;
            this.searchTxnDate = null;
            this.tableData = null;
            this.$refs.table.rowData = null;
            this.$refs.table.paginations.total = 0;
        },
        rowClick({ row, column, event }) {
            this.$refs.table.$refs.table.toggleRowSelection(row);
        },
        queryRow() {
            if (!this.searchMfgOrder
                && !this.searchProduct
                && !this.searchContainer
                && !this.searchTxnDate
            ) {
                this.$message.error('请选择查询条件。')
                return;
            }
            this.$refs.table.load(null, true);
        },
        loadBefore(params, callBack) {
            params["container"] = this.searchContainer;
            params["mfgorder"] = this.searchMfgOrder;
            params["product"] = this.searchProduct;
            params["type"] = 'active';
            params["startTime"] = this.searchTxnDate != null ? this.searchTxnDate[0] : '';
            params["endTime"] = this.searchTxnDate != null ? this.searchTxnDate[1] : '';
            callBack(true)
        },
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                this.tableData = result.Data.tableData;
                this.$refs.table.rowData = result.Data.tableData;
                this.$refs.table.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },
        materialLabelPrint() {
            const rows = this.$refs.table.getSelected();
            if (!rows.length) {
                this.$message.error('请选中待打印的物料批次')
                return;
            }
            if (!this.printQueue) {
                this.$message.error('打印机不能为空')
                return;
            }
            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                Container: rows[0].Container,
                Printer: this.printQueue,
                Type: "materialLabel"
            };
            this.http.post(this.apiUrl.ERPReprint, params, true).then(res => {
                if (res.Result == 1) {
                    this.$message.success(res.Message);
                } else {
                    this.$message.error(res.Message);
                }
            });
        }

    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>