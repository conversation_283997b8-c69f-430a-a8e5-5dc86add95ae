/*****************************************************************************************
 **  Author:jxx 2022
 **  QQ:283591387
 **完整文档见：http://v2.volcore.xyz/document/api 【代码生成页面ViewGrid】
 **常用示例见：http://v2.volcore.xyz/document/vueDev
 **后台操作见：http://v2.volcore.xyz/document/netCoreDev
 *****************************************************************************************/
//此js文件是用来自定义扩展业务代码，可以扩展一些自定义页面或者重新配置生成的代码
import { h, resolveComponent } from 'vue';
import gridBody from './Sys_RegionGridBody';
let extension = {
  components: {
    //查询界面扩展组件
    gridHeader: {
      render() {
        return [
          h(
            resolveComponent('el-alert'),
            {
              style: { 'margin-top': '2px', 'margin-bottom': '-7px' },
              'show-icon': true,
              type: 'success',
              closable: false,
              title: '代码生成器可配置编辑模式，直接在table上进行删除、修改操作,2024.01.18已支持多个快捷查询字段'
            },
            ''
          )
        ];
      }
    },
    gridBody: gridBody,
    gridFooter: '',
    //新建、编辑弹出框扩展组件
    modelHeader: '',
    modelBody: '',
    modelFooter: ''
  },
  tableAction: '', //指定某张表的权限(这里填写表名,默认不用填写)
  buttons: { view: [], box: [], detail: [] }, //扩展的按钮
  methods: {
    //下面这些方法可以保留也可以删除
    searchAfter(result) {
      //2、查询后方法，调用自定义列表设置值
      this.$refs.gridBody.loadList(result, this.columns);
      return true;
    },
    //下面这些方法可以保留也可以删除
    onInit() {
      //设置多个快捷查询字段
      this.queryFields = ['mername', 'name'];
      //1、自定义按钮切换页面显示
      this.isList = true;
      this.buttons.push({
        plain: true,
        name: '列表', //按钮名称
        icon: 'el-icon-document', //按钮图标，参照iview图标
        type: 'primary',
        onClick: () => {
          this.isList = !this.isList;
          this.buttons[this.buttons.length - 1].name = this.isList
            ? '表格'
            : '列表';
          //设置自定义列表显示
          this.$refs.gridBody.show();
        }
      });
      //示例：在按钮的最前面添加一个按钮
      //   this.buttons.unshift({  //也可以用push或者splice方法来修改buttons数组
      //     name: '按钮', //按钮名称
      //     icon: 'el-icon-document', //按钮图标vue2版本见iview文档icon，vue3版本见element ui文档icon(注意不是element puls文档)
      //     type: 'primary', //按钮样式vue2版本见iview文档button，vue3版本见element ui文档button
      //     onClick: function () {
      //       this.$Message.success('点击了按钮');
      //     }
      //   });

      //示例：设置修改新建、编辑弹出框字段标签的长度
      // this.boxOptions.labelWidth = 150;
    },
    onInited() {
      this.height = this.height - 30;
      //框架初始化配置后
      //如果要配置明细表,在此方法操作
      //this.detailOptions.columns.forEach(column=>{ });
    },
    searchBefore(param) {
      //界面查询前,可以给param.wheres添加查询参数
      //返回false，则不会执行查询
      return true;
    },
    addBefore(formData) {
      //新建保存前formData为对象，包括明细表，可以给给表单设置值，自己输出看formData的值
      return true;
    },
    updateBefore(formData) {
      //编辑保存前formData为对象，包括明细表、删除行的Id
      return true;
    },
    rowClick({ row, column, event }) {
      //查询界面点击行事件
      // this.$refs.table.$refs.table.toggleRowSelection(row); //单击行时选中当前行;
    },
    modelOpenAfter(row) {
      //点击编辑、新建按钮弹出框后，可以在此处写逻辑，如，从后台获取数据
      //(1)判断是编辑还是新建操作： this.currentAction=='Add';
      //(2)给弹出框设置默认值
      //(3)this.editFormFields.字段='xxx';
      //如果需要给下拉框设置默认值，请遍历this.editFormOptions找到字段配置对应data属性的key值
      //看不懂就把输出看：console.log(this.editFormOptions)
    }
  }
};
export default extension;
