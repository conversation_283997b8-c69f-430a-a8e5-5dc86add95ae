<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="Product" clearable filterable placeholder="键入搜索" style="width: 240px"
						remote-show-suffix :remote="true" :remote-method="getProduct" :loading="loading">
						<el-option v-for="item in Products" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>机台编号</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="Resource" clearable filterable placeholder="键入搜索" style="width: 240px"
						remote-show-suffix :remote="true" :remote-method="getResource" :loading="loading">
						<el-option v-for="item in Resources" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">机台产品标准工时配置表</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="success" icon="Plus" @click="addRow" plain>新建</el-button>
					<el-button type="primary" icon="Edit" @click="getRow" plain>编辑</el-button>
					<el-button type="danger" icon="Delete" @click="delRow" plain>删除</el-button>
					<el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="518"
			:pagination-hide="false" :load-key="true" :column-index="true" :single="true" :url="ApiUrl.GetStandardTime"
			@loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false"></vol-table>
	</div>
	<!-- 编辑弹出框 -->
	<vol-box :lazy="true" v-model="show" title="机台产品标准工时配置" :width="800" :padding="5" :onModelClose="onModelClose">
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="Info_Product" @change="ProductSelected" clearable filterable placeholder="请输入"
						style="width: 240px" remote-show-suffix :remote="true" :remote-method="getProduct"
						:loading="loading">
						<el-option v-for="item in Products" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品描述</span>
				</label>
				<div style="margin-top: 5px">
					<el-input v-model="Info_Description" disabled></el-input>
				</div>
			</div>
		</div>
		<div style="display: flex; margin-top: 15px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>机台编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="Info_Resource" clearable filterable placeholder="请选择" style="width: 240px"
						remote-show-suffix :remote="true" :remote-method="getResource" :loading="loading">
						<el-option v-for="item in Resources" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>标准产能</span>
				</label>
				<div style="margin-top: 5px">
					<el-input v-model="Info_Capacity" type="number" oninput="form.ekHour = form.ekHour
                .replace(/[^\d|\.]/g, '')
                .replace(/^00/g, '0')
                .replace(/^\./g, '0.')
						"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>标准工时</span></label>
				<div style="margin-top: 5px">
					<el-input v-model="Info_StandardTime" type="number" @input="
						form.ekHour = form.ekHour
							.replace(/[^\d|\.]/g, '')
							.replace(/^00/g, '0')
							.replace(/^\./g, '0.')
						"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>换PIN标准工时</span></label>
				<div style="margin-top: 5px">
					<el-input v-model="Info_PinTime" type="number" oninput="
						form.ekHour = form.ekHour
							.replace(/[^\d|\.]/g, '')
							.replace(/^00/g, '0')
							.replace(/^\./g, '0.')
						"></el-input>
				</div>
			</div>
		</div>
		<div style="display: flex; margin-top: 15px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>换形态标准工时</span>
				</label>
				<div style="margin-top: 5px">
					<el-input v-model="Info_ChangeModelTime" type="number" @input="
              form.ekHour = form.ekHour
                .replace(/[^\d|\.]/g, '')
                .replace(/^00/g, '0')
                .replace(/^\./g, '0.')
            "></el-input>
				</div>
			</div>
		</div>
		<div style="margin-left: 10px; margin-top: 30px">
			<label style="width: 200px; margin-left: 5px; font-size: 16px"> <span>备注</span></label>
			<div style="margin-top: 5px">
				<el-input v-model="Info_Note" type="textarea"></el-input>
			</div>
		</div>
		<template #footer>
			<div>
				<el-button type="success" icon="Check" size="small" @click="save">保存</el-button>
				<el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
	import { mapState } from 'vuex';
	import Excel from '@/uitils/xlsl.js'

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox
		},
		data() {
			return {
				show: false,
				columns: [{field: 'CDOName',title: '标准工时建模',type: 'string',width: 0,	hidden: true,	align: 'center'	},
					{ field: 'CDOID', title: '建模ID', type: 'string', width: 0, hidden: true, align: 'center' },
					{ field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center' },
					{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },					
					{ field: 'ResourceName', title: '机台编号', type: 'string', width: 110, align: 'center' },
					{ field: 'Capacity', title: '标准产能', type: 'string', width: 150, align: 'center' },
					{ field: 'StandardTime', title: '标准工时', type: 'string', width: 120, align: 'center' },
					{ field: 'PinTime', title: '换PIN标准工时', type: 'string', width: 120, align: 'center' },
					{ field: 'ChangeModelTime', title: '换型标准工时', type: 'string', width: 120, align: 'center' },
					{ field: 'Operator', title: '操作人', type: 'string', width: 80, align: 'center' },
					{ field: 'LastChangeDate', title: '操作时间', type: 'datetime', width: 120, align: 'center' },
					{ field: 'Notes', title: '备注', type: 'string', width: 120, align: 'center' }
				],
				tableData: [],
				pagination: { total: 0, size: 30, sortName: "" },

				//搜索框字段
				Product: null,
				Spec: null,
				Resource: null,
				Products: [],
				Specs:[],
				Resources:[],

				//编辑框字段
				ActionFlag: 0,
				Info_CDOName: null,				
				Info_Product: null,
				Info_Description: null,
				Info_Resource: null,
				Info_Capacity: null,
				Info_ChangeModelTime:null,
				Info_PinTime:null,
				Info_StandardTime: null,
				Info_Note: null,

				//接口地址
				ApiUrl: {
					getProductByType: "/api/query/GetProductByType",
					GetRevisionObject: "/api/query/GetRevisionObject",
					GetNameObject: "/api/query/GetNameObject",
					GetProductInfo: "/api/query/GetProductInfo",
					GetStandardTime: '/api/query/GetStandardTime',
					StandardTimeMaint: '/api/CDO/StandardTimeMaint',
				}

			}
		},
		created() {

		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
			getProduct(query) {
				if (query) {
					let params = {
						cdo: "Product",
						name: query
					};
					this.http.get(this.ApiUrl.GetRevisionObject, params).then(res => {
						if (res.Result == 1) {
							this.Products = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			getResource(query) {
				if (query) {
					let params = {
						objectCategory: "RESOURCE",
						cdo: "Resource",
						name: query
					};
					this.http.get(this.ApiUrl.GetNameObject, params).then(res => {
						if (res.Result == 1) {
							this.Resources = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			async ProductSelected(){
				if  (!this.Info_Product) { 
					this.Info_Description = null;
					return;
				}
				let params = {
					name: this.Info_Product
				};
				this.http.get(this.ApiUrl.GetProductInfo,params).then(res => {
					if (res.Result == 1) {
						this.Info_Description = res.Data[0].Description
					} else {
						this.$message.error(res.Message);
					}
				});
			},
			//清除数据
			ResetSeach() {
				this.Product = '',
				this.Spec = '',
				this.Resource = '',
				this.tableData = []
			},
			ResetInfo() {
				this.ActionFlag = 0,
				this.Info_CDOName = '',
				this.Info_Product = '',
				this.Info_Description = '',
				this.Info_Resource = '',
				this.Info_Capacity = '',
				this.Info_ChangeModelTime = '',
				this.Info_PinTime = '',
				this.Info_StandardTime = '',
				this.Info_Note = ''
			},

			rowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
				this.$refs.table.$refs.table.toggleRowSelection(row);
			},
			queryRow() {
				this.$refs.table.load(null, true);
			},			
			getRow() {
				const rows = this.$refs.table.getSelected();
				if (!rows.length) {
				    this.$message.error('请选中行')
				    return;
				}
				// this.$message.success(JSON.stringify(rows))
				this.ActionFlag = 1,
				this.Info_CDOName = rows[0].CDOName,
				this.Info_Product = rows[0].Product,
				this.Info_Description = rows[0].P_Description,
				this.Info_Resource = rows[0].ResourceName,
				this.Info_Capacity = rows[0].Capacity,
				this.Info_StandardTime = rows[0].StandardTime,
				this.Info_ChangeModelTime = rows[0].ChangeModelTime,
				this.Info_PinTime = rows[0].PinTime,
				this.Info_Note = rows[0].Notes,
				this.show = true;
			},
			addRow() {
				// this.tableData.push({ OrderNo: "D2022040600009" })
				this.ResetInfo();
				this.show = true;
			},
			delRow() {
				// this.$message.success('删除成功')
				const rows = this.$refs.table.getSelected();
				if (!rows.length) {
				    this.$message.error('请选中行')
				    return;
				}
				if(rows.length > 0){
					let params = {
						User:this.userInfo.userName,
						Password:this.userInfo.userPwd,
						EventName: 'delete',
						CDOName: rows[0].CDOName,
						CDOID: rows[0].CDOID,
					};
					this.http.post(this.ApiUrl.StandardTimeMaint, params, true).then(res => {
						if (res.Result == 1) {
							this.$refs.table.delRow();	
							this.$message.success(res.Message);
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			outputRow() {
				// this.tableData.splice(0);
				//导出
				let tableData = this.$refs.table.tableData
				let sortData = this.$refs.table.filterColumns
				let exportData = this.handleTableSortData(tableData, sortData)
				Excel.exportExcel(exportData, "机台产品标准工时" + '-' + this.base.getDate());
			},
			handleTableSortData(tableData, sortData) {
				let newArray = [];
				tableData.forEach(data => {
					let newItem = {};
					sortData.forEach(field => {
						if (data.hasOwnProperty(field.field)) {
							newItem[field.title] = data[field.field];
						}
					});
					newArray.push(newItem);
				});
				return newArray
			},
			onClose() {
				// alert('弹出框右上角点击x关闭事件')
			},
			save() {
				//保存		
				let params = {
					User:this.userInfo.userName,
					Password:this.userInfo.userPwd,
					CDOName: this.ActionFlag == 1?this.Info_CDOName : this.Info_Product + this.Info_Resource,
					Notes:this.Info_Note,
					Capacity:this.Info_Capacity,
					StandardTime:this.Info_StandardTime,
					PinTime:this.Info_PinTime,
					ChangeModelTime:this.Info_ChangeModelTime,
					Product:this.Info_Product,
					Resource:this.Info_Resource,
				};
				this.http.post(this.ApiUrl.StandardTimeMaint, params, true).then(res => {
					if (res.Result == 1) {
						this.show = false;
						this.ResetInfo();
						this.queryRow();
						this.$message.success(res.Message);
					} else {
						this.$message.error(res.Message);
					}
				});	
			},
			close() {
				this.show = false;
			},
			getCurrentDateTimeString() {
				const now = new Date();
				const year = now.getFullYear();
				const month = this.padNumber(now.getMonth() + 1);
				const day = this.padNumber(now.getDate());
				const hours = this.padNumber(now.getHours());
				const minutes = this.padNumber(now.getMinutes());
				const seconds = this.padNumber(now.getSeconds());
			
				return `${year}${month}${day}${hours}${minutes}${seconds}`;
			},
			padNumber(num) {
				return num < 10 ? '0' + num : num;
			},
			loadBefore(params, callBack) {
				params["Product"] = this.Product;
				params["Resource"] = this.Resource;
				callBack(true)
			},
			loadAfter(rows, callBack, result) {
				if (result.Result == 1) {
					//this.columns = result.Data.colums;
					this.tableData = result.Data.tableData;
					this.$refs.table.rowData = result.Data.tableData;
					this.$refs.table.paginations.total = result.Data.total;
				}
				else {
					this.$message.error(result.Message);
				}
				callBack(false);
			},
		}
	}
</script>
<style lang="less" scoped>
.table-item-header {
  display: flex;
  align-items: center;
  padding: 6px;

  .table-item-border {
    height: 15px;
    background: rgb(33, 150, 243);
    width: 5px;
    border-radius: 10px;
    position: relative;
    margin-right: 5px;
  }

  // .table-item-text {
  // 	font-weight: bolder;
  // 	border-bottom: 1px solid #0c0c0c;
  // }
  .table-item-text {
    margin-top: 3px;
    padding-bottom: 6px;
    font-weight: bold;
    font-size: 15px;
    color: #484848;
    white-space: nowrap;
    border-bottom: 2px solid #676767;
    margin-bottom: -1px;
    letter-spacing: 1px;
  }

  .table-item-buttons {
    flex: 1;
    text-align: right;
  }

  .small-text {
    font-size: 12px;
    color: #2196f3;
    margin-left: 10px;
    position: relative;
    top: 2px;
  }
}
</style>