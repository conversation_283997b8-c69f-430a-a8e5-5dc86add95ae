<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>状态</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchStatus" clearable filterable placeholder="请选择" style="width: 100px">
						<el-option v-for="item in status" :key="item.Name" :label="item.Name" :value="item.Value" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>检验单号</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchFQCNumber" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getFQCNumber">
						<el-option v-for="item in FQCNumbers" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>检验时间</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchTxnDate" type="daterange" range-separator="To" start-placeholder="开始"
						end-placeholder="结束" :size="size" style="width: 260px" format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss" />
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">FQC信息</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="success" icon="Check" @click="editRow" plain>执行</el-button>
					<el-button type="primary" icon="Upload" @click="outputRow" plain>导出</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="masterTable" index :tableData="masterTableData" @rowClick="rowClick" @rowDbClick="rowDbClick"
			:columns="masterColumns" :height="518" :pagination-hide="false" :load-key="true" :column-index="true"
			:single="true" :url="apiUrl.getFQC" @loadBefore="masterLoadBefore" @loadAfter="masterLoadAfter"
			:defaultLoadPage="false" :ck="true"></vol-table>
	</div>
	<!-- 编辑弹出框 -->
	<vol-box :lazy="true" v-model="showExe" :title="className+'FQC' + '检验报表'" :width="1200" :padding="5">
		<div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>入库单号</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="editFQCNumber"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="editMfgOrder"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品编号</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="editProduct"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>产品描述</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="editDescrition"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单数量</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="editOrderQty"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>入库数</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="editInventoryInspectionQty"></el-input>
				</div>
			</div>
		</div>
		<div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>抽样数</span>
				</label>
				<div style="margin-top: 5px">
					<el-input-number v-model="editAQLQty" :max="editInventoryInspectionQty"></el-input-number>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>客户编码</span>
				</label>
				<div style="margin-top: 5px">
					<el-input v-model="editCustomer"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>备注</span>
				</label>
				<div style="margin-top: 5px">
					<el-input v-model="editNotes" autosize type="textarea" style="width: 600px"
						width="300px"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<div style="margin-top: 27px">
					<el-button type="primary" @click="toQualityInspectionSheet" plain>检验表维护</el-button>
				</div>
			</div>
		</div>

		<div class="table-item" style="width: 500px;">
			<div class="table-item-header">
				<span class="table-item-text">产品类别</span>
				<div style="margin-left: 30px;">
					<el-radio-group v-model="productType">
						<el-radio value="1" size="large">非汽车类</el-radio>
						<el-radio value="2" size="large">汽车类</el-radio>
					</el-radio-group>
				</div>
			</div>
			<vol-table ref="noCarTable" index :tableData="noCarTableData" v-show="productType == 1"
				:columns="noCarTableCols" :pagination-hide="true" :load-key="true" :column-index="true" :single="true"
				:ck="false" :columnIndex="false"></vol-table>
			<vol-table ref="carTable" index :tableData="carTableData" v-show="productType == 2" :columns="carTableCols"
				:pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
				:max-height="30" :columnIndex="false"></vol-table>
		</div>

		<div class="table-item-header">
			<span class="table-item-text">定性检验项目</span>
		</div>
		<vol-table ref="qualitativeTable" index :tableData="qualitativeTableData" :columns="qualitativeCols"
			:max-height="600" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
			:edit="true"></vol-table>

		<div class="table-item-header">
			<span class="table-item-text">定量检验项目</span>
		</div>
		<vol-table ref="quantitativeTable" index :tableData="quantitativeTableData" :columns="quantitativeCols"
			:max-height="600" :pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
			:edit="true"></vol-table>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>综合判定</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="IAResult" style="width: 200px">
						<el-option v-for="item in InepectResults" :key="item.key" :label="item.value" :value="item.key" />
					</el-select>
					<!-- <el-input style="width: 200px;" disabled v-model="InepectResult" placeholder=" "></el-input> -->
				</div>
			</div>
		<template #footer>
			<div v-show="showExeBtn">
				<el-button type="success" icon="Check" size="small" @click="exeSave">保存</el-button>
				<!-- <el-button icon="Close" size="small" @click="exeClose">关闭</el-button> -->
			</div>
		</template>
	</vol-box>

	<vol-box :lazy="true" v-model="showCollect" title="定量项目实测值" :width="500" :padding="5">
		<div style="display: flex; margin-top: 5px; margin-bottom: 5px;">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>检验项目</span>
				</label>
				<div style="margin-top: 5px">
					<el-input disabled v-model="collectInspectionPoint"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 80px; margin-left: 5px; font-size: 16px">
					<span>下限</span>
				</label>
				<div style="margin-top: 5px;width: 100px;">
					<el-input disabled v-model="collectLowerLimit"></el-input>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 80px; margin-left: 5px; font-size: 16px">
					<span>上限</span>
				</label>
				<div style="margin-top: 5px;width: 100px;">
					<el-input disabled v-model="collectUpperLimit"></el-input>
				</div>
			</div>
		</div>
		<vol-table ref="collectTable" index :tableData="collectTableData" :columns="collectCols" :max-height="600"
			:pagination-hide="true" :load-key="true" :column-index="true" :single="true" :ck="false"
			:edit="true"></vol-table>
		<template #footer>
			<div v-show="showCollectBTN">
				<el-button v-show="showIOT" type="primary" :icon="Share" size="small"
					@click="collectIOT">获取数据</el-button>
				<el-button type="success" icon="Check" size="small" @click="collectSave">保存</el-button>
				<el-button type="info" icon="Refresh" @click="initCollectTableData" plain>重置</el-button>
				<el-button type="info" icon="Add" @click="addCollectTable" plain>新增行数</el-button>
				<!-- <el-button type="default" icon="Close" size="small" @click="collectClose">关闭</el-button> -->
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			//主页面
			searchMfgOrder: null,
			searchFQCNumber: null,
			searchTxnDate: null,
			searchStatus: null,
			status: [{ Name: '未检验', Value: '1' }, { Name: '已检验', Value: '2' }],
			mfgorders: [],
			FQCNumbers: [],
			masterTableData: [],
			tempMasterTableData: [],
			className: null,

			//执行页面
			productType: "1",
			showExeBtn: true,
			showExe: false,
			editFQCNumber: null,
			editMfgOrder: null,
			editProduct: null,
			editDescrition: null,
			editOrderQty: null,
			editInventoryInspectionQty: null,
			editAQLQty: null,
			editCustomer: null,
			editNotes: null,
			noCarTableData: [{ AllowStardard: 'AC' }, { AllowStardard: 'RE' }],
			carTableData: [],
			IAResults: [{ key: 'OK', value: 'OK' }, { key: 'NG', value: 'NG' }],
			qualitativeTableData: [],
			quantitativeTableData: [],
			collectTableData: [],

			//数采页面
			currentRowIndex: null,
			showIOT: false,
			showCollect: false,
			IAResult: null,
			InepectResults: [
				{ key: 1, value: '合格' },
				{ key: 0, value: '不合格' }
			],
			showCollectBTN: true,

			masterColumns: [
				{ field: 'CDOID', title: '建模ID', type: 'string', width: 0, hidden: true, align: 'center' },
				{ field: 'CDOName', title: '入库单号', type: 'string', width: 120, align: 'center' },
				{ field: 'MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
				{ field: 'Product', title: '产品编码', type: 'string', width: 100, align: 'center' },
				{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
				{ field: 'Customer', title: '客户编码', type: 'string', width: 80, align: 'center' },
				{ field: 'MfgOrderQty', title: '工单数量', type: 'string', width: 80, align: 'center' },
				{ field: 'InventoryInspectionQty', title: '入库数', type: 'string', width: 60, align: 'center' },
				{ field: 'AQLQty', title: '抽样数', type: 'string', width: 60, align: 'center' },
				{
					field: 'Status', title: '状态', type: 'string', width: 60, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						if (row.Status == '已检验') {
							return { background: "#2196F3", color: "#fff" };
						}
					},
				},
				{
					field: 'IAResult', title: '综合判定', type: 'string', width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.IAResult) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{
					field: 'OutCome', title: '处理结果', type: 'string', width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.OutCome) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{ field: 'CreateDate', title: '入库时间', type: 'datetime', width: 150, align: 'center' },
				{ field: 'Creator', title: '入库员工', type: 'datetime', width: 120, align: 'center' },
				{ field: 'InspectionDate', title: '检验时间', type: 'datetime', width: 150, align: 'center' },
				{ field: 'Inspector', title: '检验人', type: 'string', width: 80, align: 'center' },
				{ field: 'Notes', title: '备注', type: 'string', width: 200, align: 'center' },
				{ field: 'ProductType', title: '产品类别', hidden: true, type: 'string', width: 200, align: 'center' },
			],

			noCarTableCols: [
				{ field: 'AllowStardard', title: '允收标准', type: 'string', width: 80, align: 'center' },
				{ field: 'CR', title: 'CR=0.1', edit: { type: 'decimal' }, type: 'decimal', width: 80, align: 'center' },
				{ field: 'MIJ', title: 'MIJ=0.4', edit: { type: 'decimal' }, type: 'decimal', width: 80, align: 'center' },
				{ field: 'StardardMIN', title: 'MIN=0.65', edit: { type: 'decimal' }, type: 'decimal', width: 80, align: 'center' },
			],

			carTableCols: [
				{ field: 'AllowStardard', title: '允收标准', type: 'string', width: 80, align: 'center' },
				{ field: 'AC', title: 'AC=0', type: 'string', width: 80, align: 'center' },
				{ field: 'RE', title: 'RE=1', type: 'string', width: 80, align: 'center' },
			],

			qualitativeCols: [
				{ field: 'InspectionPoint', title: '检验项目', edit: false, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionPointContent', title: '检验内容', edit: false, type: 'string', width: 130, align: 'center' },
				{
					field: 'ItemResult', title: '检验结果', bind: { key: null, data: [] }, edit: { type: "select" }, width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				// { field: 'Defect', title: '不良明细', edit: true, type: 'string', width: 130, align: 'center' },
				{ field: 'Notes', title: '备注', edit: true, type: 'string', width: 130, align: 'center' }
			],

			quantitativeCols: [
				{ field: 'InspectionPoint', title: '检验项目', edit: false, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionPointContent', title: '检验内容', edit: false, type: 'string', width: 130, align: 'center' },
				{ field: 'InspectionTool', title: '检验工具', edit: false, type: 'string', width: 100, align: 'center' },
				{ field: 'LowerLimit', title: '下限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
				{ field: 'UpperLimit', title: '上限', edit: false, type: "string", precision: 2, width: 80, align: 'center' },
				{ field: 'DefaultValue', title: '标准值', edit: false, type: 'string', width: 80, align: 'center' },
				{
					field: 'ActualValue', title: '实测值', align: 'center', width: 80, render: (h, { row, column, index }) => {
						return (
							<div>
								<el-button
									onClick={($e) => {
										this.collectInspectionPoint = row.InspectionPoint;
										this.collectUpperLimit = row.UpperLimit;
										this.collectLowerLimit = row.LowerLimit;
										this.currentRowIndex = index;
										this.initCollectTableData();

										if (row.ActualValue != null) {
											for (let i = 0; i < row.ActualValue.length; i++) {
												this.collectTableData[i].ActualValue = row.ActualValue[i].ActualValue;
											}
										}

										this.showCollect = true;
										if (row.FromIOT == '1') {
											this.showIOT = true;
										} else {
											this.showIOT = false;
										}
									}}
									size="small"
									type="primary"
									icon="Edit"
									plain >
								</el-button>
								{/* 这里可以接着放按钮或者其他组件 */}
							</div>
						);
					}
				},
				{
					field: 'ItemResult', title: '检验结果', edit: false, type: 'string', width: 80, align: 'center',
					cellStyle: (row, rowIndex, columnIndex) => {
						switch (row.ItemResult) {
							case 'OK':
								return { background: "#82C256", color: "#fff" };
							case 'NG':
								return { background: "#E93F2C", color: "#fff" };
						}
					},
				},
				{ field: 'FromIOT', title: '来源IOT', hidden: true, edit: false, type: 'string', width: 130, align: 'center' },
				{ field: 'Notes', title: '备注', edit: true, type: 'string', width: 130, align: 'center' },
			],
			collectCols: [
				{ field: 'ActualValue', title: '实测值', edit: { type: 'decimal' }, type: 'decimal', width: 100, align: 'center' },
			],

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/GetRevisionObject",
				getNameObject: "/api/query/GetNameObject",
				getFQC: '/api/query/getFQC',
				inventoryInspectionOrderMaint: '/api/cdo/inventoryInspectionOrderMaint',
				getQualityInspectionSheet: '/api/query/getQualityInspectionSheet',
				getAllowStardDetails: '/api/query/getAllowStardDetails',
				getFQCCollectDetails: '/api/query/getFQCCollectDetails',
				getMenu: '/api/menu/getTreeMenu',
				GetClassNamebyEmployee: '/api/query/GetClassNamebyEmployee',
			}
		}
	},
	created() {
		this.getIAResults();
		this.initCollectTableData();
		this.getClassName();
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	methods: {
		getClassName(){
			this.http.get(this.apiUrl.GetClassNamebyEmployee, {employName:this.userInfo.userName}, false).then(res => {
				if (res.Result == 1) {
					this.className = res.Data.Name;
				} else {
					this.$message.error(res.Message);
				}
			}).catch(err => {
				this.$message.error('获取班组名称失败' + err);
			});
		},
		getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getFQCNumber(query) {
			if (query) {
				let params = {
					cdo: "W_WarehouseReceipt",
					name: query,
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.FQCNumbers = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getIAResults() {
			this.qualitativeCols[2].bind.data = this.IAResults;
		},
		initCollectTableData() {
			this.collectTableData = [];
			for (let i = 0; i < 7; i++) {
				this.collectTableData.push({ ActualValue: null });
			}
		},
		reset() {
			this.searchMfgOrder = null;
			this.searchFQCNumber = null;
			this.searchTxnDate = null;
			this.masterTableData = [];
			this.$refs.masterTable.rowData = [];
			this.$refs.masterTable.paginations.total = 0;
		},
		//清除数据
		resetMaster() {
			this.masterTableData = [];
			this.tempMasterTableData = [];
			this.$refs.masterTable.rowData = [];
		},
		resetEdit() {
			this.editFQCNumber = null;
			this.editMfgOrder = null;
			this.editProduct = null;
			this.editDescrition = null;
			this.editOrderQty = null;
			this.editInventoryInspectionQty = null;
			this.editAQLQty = null;
			this.editCustomer = null;
			this.editNotes = null;
			this.noCarTableData = [{ AllowStardard: 'AC' }, { AllowStardard: 'RE' }];
			this.qualitativeTableData = [];
			this.quantitativeTableData = [];
		},
		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			this.$refs.masterTable.$refs.table.toggleRowSelection(row);
		},
		rowDbClick({
			row,
			column,
			index
		}) {
			if (row.Status == '已检验') {
				this.editFQCNumber = row.CDOName;
				this.editMfgOrder = row.MfgOrder;
				this.editProduct = row.Product;
				this.editDescrition = row.P_Description;
				this.editOrderQty = row.MfgOrderQty;
				this.editInventoryInspectionQty = row.InventoryInspectionQty;
				this.editAQLQty = row.AQLQty;
				this.editCustomer = row.Customer;
				this.editNotes = row.Notes;
				this.productType = row.ProductType;

				if (row.ProductType == '1') {
					this.getAllowStardDetails(row);
				}
				this.getQualityInspectionSheetV2(row);
				this.showCollectBTN = false;
				this.showExeBtn = false;
				this.showExe = true;
			}
			else {
				this.$message.error('此检验单未检验');
				return;
			}
		},
		getAllowStardDetails(row) {
			let params = {
				cdoname: row.CDOName
			};
			this.http.post(this.apiUrl.getAllowStardDetails, params, true).then(res => {
				if (res.Result == 1) {
					this.noCarTableData = res.Data.tableData;
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		setFQCCollectDetails(row) {
			let params = {
				cdoname: row.CDOName
			};
			this.http.post(this.apiUrl.getFQCCollectDetails, params, true).then(res => {
				if (res.Result == 1) {
					//给检验表赋值
					this.qualitativeTableData.forEach(item => {
						const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
						if (tempData != null) {
							item.ItemResult = tempData.ItemResult == "0" ? 'NG' : 'OK';
							item.Notes = tempData.Notes;
						}
					});
					this.quantitativeTableData.forEach(item => {
						const tempData = res.Data.tableData.find(data => data.InspectionPoint == item.InspectionPoint);
						if (tempData != null) {
							item.ItemResult = tempData.ItemResult == "0" ? 'NG' : 'OK';
							item.Notes = tempData.Notes;
							item.ActualValue = tempData.Detail;
						}
					});
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		queryRow() {
			if (this.searchMfgOrder == null
				&& this.searchStatus == null
				&& this.searchFQCNumber == null
				&& this.searchTxnDate == null) {
				this.$message.error('请输入查询条件');
				return;
			}
			this.resetMaster();
			this.$refs.masterTable.load(null, true);
		},
		masterLoadBefore(params, callBack) {
			params["MfgOrder"] = this.searchMfgOrder != null ? this.searchMfgOrder : null;
			params["FQCNumber"] = this.searchFQCNumber != null ? this.searchFQCNumber : null;
			params["Status"] = this.searchStatus != null ? this.searchStatus : null;
			params["StartTime"] = this.searchTxnDate != null ? this.searchTxnDate[0] : null;
			params["EndTime"] = this.searchTxnDate != null ? this.searchTxnDate[1] : null;
			callBack(true)
		},
		masterLoadAfter(rows, callBack, result) {
			if (result.Result == 1) {
				//this.columns = result.Data.colums;
				this.masterTableData = result.Data.tableData;
				this.tempMasterTableData = result.Data.tableData;
				this.$refs.masterTable.rowData = result.Data.tableData;
				this.$refs.masterTable.paginations.total = result.Data.total;
			}
			else {
				this.$message.error(result.Message);
			}
			callBack(false);
		},
		editRow() {
			const rows = this.$refs.masterTable.getSelected();
			if (!rows.length) {
				this.$message.error('请选中即将执行的行')
				return;
			}
			if (rows[0].Status == '已检验') {
				this.$message.error('检验单已检验')
				return;
			}
			this.resetEdit();
			//查找检验表
			this.getQualityInspectionSheet(rows[0]);
			this.editFQCNumber = rows[0].CDOName;
			this.editMfgOrder = rows[0].MfgOrder;
			this.editProduct = rows[0].Product;
			this.editDescrition = rows[0].P_Description;
			this.editOrderQty = rows[0].MfgOrderQty;
			this.editInventoryInspectionQty = rows[0].InventoryInspectionQty;
			this.editAQLQty = rows[0].AQLQty;
			this.editCustomer = rows[0].Customer;
			this.editNotes = rows[0].Notes;
			this.showCollectBTN = true;
			this.showExeBtn = true;
			this.showExe = true;
		},
		getQualityInspectionSheet(row) {
			//查找检验表
			let params = {
				product: row.Product,
				inspectionType: '4',
			};
			this.http.post(this.apiUrl.getQualityInspectionSheet, params, true).then(res => {
				if (res.Result == 1) {
					if (res.Data.tableData != null) {
						this.qualitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '1');
						this.quantitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '2');
						this.qualitativeTableData.forEach(item => {
							item.ItemResult = 'OK';
						});

					}
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		getQualityInspectionSheetV2(row) {
			//查找检验表
			let params = {
				product: row.Product,
				inspectionType: '4',
			};
			this.http.post(this.apiUrl.getQualityInspectionSheet, params, true).then(res => {
				if (res.Result == 1) {
					if (res.Data.tableData != null) {
						this.qualitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '1');
						this.quantitativeTableData = res.Data.tableData[0].Detail.filter(item => item.InspectionPointType == '2');
						this.setFQCCollectDetails(row);
					}
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		outputRow() {
			// this.tableData.splice(0);
			//导出
			let tableData = this.$refs.masterTable.tableData;
			let sortData = this.$refs.table.filterColumns;
			let exportData = this.handleTableSortData(tableData, sortData);
			Excel.exportExcel(exportData, "FQC" + '-' + this.base.getDate());
		},
		handleTableSortData(tableData, sortData) {
			let newArray = [];
			tableData.forEach(data => {
				let newItem = {};
				sortData.forEach(field => {
					if (data.hasOwnProperty(field.field)) {
						newItem[field.title] = data[field.field];
					}
				});
				newArray.push(newItem);
			});
			return newArray
		},
		exeSave() {
			const rows = this.$refs.masterTable.getSelected();

			if (!this.editAQLQty) {
				this.$message.error('请输入抽检数量');
				return;
			}

			// 检查 noCarTableData 每个单元格是否都有值
			if (this.productType == "1") {
				for (let row of this.noCarTableData) {
					if (!row.AllowStardard || !row.CR || !row.MIJ || !row.StardardMIN) {
						this.$message.error('非汽车类表格中存在未填写的单元格');
						return;
					}
				}
			}

			// 检查 qualitativeTableData 是否都有检验结果
			for (let item of this.qualitativeTableData) {
				if (!item.ItemResult) {
					this.$message.error('定性检验项目中有未填写的检验结果');
					return;
				}
				// if (item.ItemResult == 'NG') {
				// 	this.IAResult = 0;
				// }
			}

			// 检查 quantitativeTableData 是否都有检验结果
			for (let item of this.quantitativeTableData) {
				if (!item.ItemResult) {
					this.$message.error('定量检验项目中有未填写的检验结果');
					return;
				}
			}

			let params = {
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				CDOName: this.editFQCNumber,
				Product: this.editProduct,
				AQLQty: this.editAQLQty,
				Notes: this.editNotes,
				productType: this.productType,
				MfgOrder: this.editMfgOrder,
				Customer: this.editCustomer,
				IAResult: this.IAResult,
				Status: 2,//2: 已检验
				CreateDate: rows[0].CreateDate,
				Creator: rows[0].Creator,
				AllowStardardDetails: this.noCarTableData,
				CollectDetails: this.quantitativeTableData.concat(this.qualitativeTableData)
			};
			this.http.post(this.apiUrl.inventoryInspectionOrderMaint, params, true).then(res => {
				if (res.Result == 1) {
					this.showExe = false;
					this.queryRow();
					this.$message.success(res.Message);
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		exeClose() {
			this.showExe = false;
		},
		collectIOT() {

		},
		collectSave() {
			// 赋值回 quantitativeTableData
			if (this.currentRowIndex != null) {

				this.quantitativeTableData[this.currentRowIndex].ActualValue = this.collectTableData.filter(item => item.ActualValue !== null && item.ActualValue !== undefined);
				//判断上下限
				for (let item of this.collectTableData) {
					if (parseFloat(item.ActualValue) > parseFloat(this.quantitativeTableData[this.currentRowIndex].UpperLimit)
						|| parseFloat(item.ActualValue) < parseFloat(this.quantitativeTableData[this.currentRowIndex].LowerLimit)) {
						this.quantitativeTableData[this.currentRowIndex].ItemResult = "NG"
						this.IAResult = 0;
						break;
					}
					else {
						this.quantitativeTableData[this.currentRowIndex].ItemResult = "OK"
					}
				}
				if (this.quantitativeTableData[this.currentRowIndex].ActualValue.length == 0) {
					this.quantitativeTableData[this.currentRowIndex].ItemResult = null;
				}
			}
			this.showCollect = false;
		},
		collectClose() {
			this.showCollect = false;
		},
		getCurrentDateTimeString() {
			const now = new Date();
			const year = now.getFullYear();
			const month = this.padNumber(now.getMonth() + 1);
			const day = this.padNumber(now.getDate());
			const hours = this.padNumber(now.getHours());
			const minutes = this.padNumber(now.getMinutes());
			const seconds = this.padNumber(now.getSeconds());

			return `${year}${month}${day}${hours}${minutes}${seconds}`;
		},
		padNumber(num) {
			return num < 10 ? '0' + num : num;
		},
		addCollectTable() {
			this.collectTableData.push({ ActualValue: null });
		},
		toQualityInspectionSheet(){
			this.http.get(this.apiUrl.getMenu, {}, false).then(res => {
				let found = false;
				res.menu.forEach(item => {
					if (item.name && item.name.includes("品质检验表")) {
						found = true;
						return this.$tabs.open({
						text: '品质检验表',
						path: item.url,
						query: {
							product: this.editProduct
                        	}
						});
					}
				});
				if (!found) {
					this.$message.error('没有访问品质检验表的权限');
				}
			}).catch(err => {
				this.$message.error('获取菜单失败'+err);
			});
		}
	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	// .table-item-text {
	// 	font-weight: bolder;
	// 	border-bottom: 1px solid #0c0c0c;
	// }
	.table-item-text {
		margin-top: 3px;
		padding-bottom: 6px;
		font-weight: bold;
		font-size: 15px;
		color: #484848;
		white-space: nowrap;
		border-bottom: 2px solid #676767;
		margin-bottom: -1px;
		letter-spacing: 1px;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196f3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>