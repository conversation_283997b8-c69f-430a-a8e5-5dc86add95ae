<template>
	<div class="page-header" style="height: 20%;">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px;">
		  <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>设备课别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="classname" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getclassname" :loading="loading">
						<el-option v-for="item in classnames" 
						:key="item.classname" 
						:label="item.classname" 
						:value="item.classname" />
					</el-select>
				</div>
			</div>
            <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>设备/模具编码</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="Resource" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getResource" :loading="loading">
						<el-option v-for="item in Resources" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px; ">
				<label style="width: 200px; margin-left: 5px; font-size: 16px;">
					<span>日期筛选</span>
				</label>
				<div style="margin-top: 5px;">
					<el-date-picker v-model="searchPlanDate" type="daterange" range-separator="To"
						start-placeholder="开始" end-placeholder="结束" :size="size" style="width: 240px"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD hh:mm:ss"/>
				</div>
			</div>
		</div>
		<div class="table-item-header">
			<div class="table-item-border"></div> <span class="table-item-text">待确认维修单信息</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="GetEquipmentAcknowledge" plain>查询</el-button>
					<el-button icon="Refresh" type="info" plain @click="reset">重置</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="AcknowedgeTable" index :tableData="AcknowedgeTableData" @rowClick="AckRowClick" 
		    :columns="AssignedColumns"
			:height="200" :pagination-hide="false" :load-key="false" :column-index="true" :single="true"
            :url="apiUrl.GetEquipmentAcknowledge" @loadBefore="AckLoadBefore" @loadAfter="AckLoadAfter" 
			:defaultLoadPage="false"
            :ck="true">
        </vol-table>
	</div>
	<div class="table-item">
		<div class="table-item-header">
			<div class="table-item-border"></div> 
            <span class="table-item-text">待执行维修单信息</span>
						    <div style="margin-left: 10px">
              <!--  <label style="width: 100px; margin-left: 5px; font-size: 16px">
                    <span>再分配维修负责人</span></label>
                <div style="margin-top: 5px">
                    <el-select v-model="Name" clearable filterable placeholder="键入搜索" style="width: 200px"
                        remote-show-suffix :remote="true" :remote-method="GetEmployee" :loading="loading">
                        <el-option v-for="item in Names" 
                            :key="item.value" 
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
					<el-button type="danger" icon="Check" @click="submitRow" plain>再分配</el-button>
                   <el-button type="primary" icon="Plus" @click="addClockonRow" plain>再确认</el-button>
                </div>-->
            </div>
			<div class="table-item-buttons">
                <el-button type="success" icon="Check" @click="Acknowledgesubmit" plain>确认分配</el-button>
                <el-button type="primary" icon="Plus" @click="addRow" plain>维修执行</el-button>
			</div>
		</div>
		<vol-table ref="InprogressTable" index :tableData="InprogressTableData"
		    @rowClick="InprogressTableRowClick" 
		    :columns="InprogressColumns"
			:url="apiUrl.GetEquipmentInprogress" @loadBefore="InprogressTableLoadBefore"
			@loadAfter="InprogressTableLoadAfter" 
			:defaultLoadPage="false"
            :height="200" :pagination-hide="false" :load-key="false" :column-index="true" :single="true" :ck="true">
        </vol-table>
	</div>
	<!-- 编辑弹出框 -->
	<vol-box :lazy="true" v-model="show" title="设备维修单执行" :width="800" :padding="5":onModelClose="onModelClose">
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>症状代码</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="SymptomCode" @change="ResourceSelected" 
					clearable filterable placeholder="请输入"
						style="width: 240px" remote-show-suffix :remote="true" :remote-method="GetSymptomCode"
						:loading="loading">
						<el-option v-for="item in SymptomCodes" 
						:key="item.Name" :label="item.Name" 
						:value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>原因代码</span>
				</label>
				<div style="margin-top: 5px">
					<!--<span>{{ Info_JobModel }}</span>-->
					<el-select v-model="CauseCode"
						clearable filterable placeholder="请输入"
						style="width: 240px" remote-show-suffix :remote="true" :remote-method="GetCauseCode"
						:loading="loading">
						<el-option v-for="item in CauseCodes" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
					</el-select>
					<!--<el-input  v-model="Info_JobModel" placeholder="请输入维修模式" style="width: 240px;" ></el-input>-->
				</div>
			</div>
						<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>维修代码</span>
				</label>
				<div style="margin-top: 5px">
					<!--<span>{{ Info_JobModel }}</span>-->
					<el-select v-model="RepairCode"
						clearable filterable placeholder="请输入"
						style="width: 240px" remote-show-suffix :remote="true" :remote-method="GetRepairCode"
						:loading="loading">
						<el-option v-for="item in RepairCodes" 
						:key="item.Name" 
						:label="item.Name" 
						:value="item.Name" />
					</el-select>
					<!--<el-input  v-model="Info_JobModel" placeholder="请输入维修模式" style="width: 240px;" ></el-input>-->
				</div>
			</div>
		</div>
		<div style="margin-left: 10px; margin-top: 10px">
			<label style="width: 200px; margin-left: 5px; font-size: 16px"> <span>维修处理备注</span></label>
			<div style="margin-top: 5px">
				<el-input v-model="Info_memo" type="textarea"></el-input>
			</div>
		</div>
		<template #footer>
			<div>
				<el-button type="save" icon="Check" size="small" @click="Inprogresssubmit">保存</el-button>
				<el-button type="default" icon="Close" size="small" @click="close">关闭</el-button>
			</div>
		</template>
	</vol-box>
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
    import VolForm from "@/components/basic/VolForm.vue";
    import VolHeader from "@/components/basic/VolHeader.vue";
	import { mapState } from 'vuex';

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox,
            VolForm,
            VolHeader,
		},
		data() {
			return {
				//搜索框字段
				searchPlanDate:null,
				Name: null,
                Names: [],
				classnames:[],
                classname: null,
				//Workcentername:'',
				Resource:'',
				Info_memo:'',
				Workcenternames:[],
				Resources:[],
				show: false,
				AutoClockOn:'',
				SymptomCode: null,
				SymptomCodes:[],
				CauseCode: null,
				CauseCodes:[],
				RepairCode: null,
				RepairCodes:[],
                //infoPrinter: null,
				AssignedColumns: [
					{ field: 'OrderName', title: '维修工单', type: 'string', width: 130, align: 'center' },
					{ field: 'Description', title: '设备/模具名称', type: 'int', width: 80, align: 'center' },
					{ field: 'ResourceName', title: '设备/模具编码', type: 'string', width: 130, align: 'center' },
					{ field: 'CCOMMENTS', title: '描述', type: 'string', width: 130, align: 'center' },
					//{ field: 'Jobmodelname', title: '维修模式', type: 'string', width: 150, align: 'center' },
					{ field: 'jobmaintenancename', title: '故障类型', type: 'string', width: 120, align: 'center' },
					{ field: 'employeename', title: '登记人', type: 'string', width: 150, align: 'center' },
					{ field: 'CreateDate', title: '登记时间', type: 'string', width: 100, align: 'center' },
					{ field: 'AssignedName', title: '被分配人', type: 'string', width: 150, align: 'center' },
					//{ field: 'AssignDate', title: '被分配时间', type: 'string', width: 120, align: 'center' },
					{ field: 'classnamename', title: '设备课别', type: 'string', width: 100, align: 'center' },
					{ field: 'COMMENTS', title: '分配描述', type: 'string', width: 120, align: 'center' },
				],
				InprogressColumns: [
					{ field: 'OrderName', title: '维修工单', type: 'string', width: 130, align: 'center' },
					{ field: 'Description', title: '设备/模具名称', type: 'int', width: 80, align: 'center' },
					{ field: 'ResourceName', title: '设备/模具编码', type: 'string', width: 130, align: 'center' },
					{ field: 'AssignedName', title: '待处理人', type: 'string', width: 150, align: 'center' },
					{ field: 'classnamename', title: '设备课别', type: 'string', width: 130, align: 'center' },
					//{ field: 'stagename', title: '阶段', type: 'string', width: 130, align: 'center' },
					{ field: 'jobmaintenancename', title: '故障类型', type: 'string', width: 120, align: 'center' },
					{ field: 'jobstatus', title: '状态', type: 'string', width: 130, align: 'center' },
					//{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
				],

				//创建窗口字段
				ActionFlag: 0,
				Info_WorkCenter: null,
				Info_ReleaseQty: null,
				Info_PlanDate: null,
				Info_Priority: null,
				Info_Notes: null,


				//接口地址
				apiUrl: {
                   // getRevisionObject: "/api/query/GetRevisionObject",
                    GetNameObject: "/api/query/GetNameObject",
					GetEquipmentAcknowledge:'/api/query/GetEquipmentAcknowledge',
					GetEquipmentInprogress:'/api/query/GetEquipmentInprogress',
					GetEmployee: "/api/query/GetEmployee",
					Getclassname: "/api/query/Getclassname",
					EquipmentRepairAcknowledgeMaint: "/api/cdo/EquipmentRepairAcknowledgeMaint",
					EquipmentRepairInprogressMaint: "/api/cdo/EquipmentRepairInprogressMaint",
					EquipmentRepairAssignMaint: "/api/cdo/EquipmentRepairAssignMaint",

					Getcode: '/api/query/Getcode',
				},
			}
		},
		created() {//this.getDefaultPrinter();
		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
        GetEmployee(query) {
            if (query) {
                let params = {
                    objectCategory: "FullName",
                    //cdo: "PrintQueue",
                    FullName: query
                };
                this.http.get(this.apiUrl.GetEmployee, params).then(res => {
                    if (res.Result == 1) {
                        this.Names = res.Data;

                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
		GetRepairCode(query) {
				if (query) {
					let params = {
						//objectCategory: "REPAIRCODE",
						cdo: "REPAIRCODE",
						name: query
					};
					this.http.get(this.apiUrl.Getcode, params).then(res => {
						if (res.Result == 1) {
							this.RepairCodes = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
		GetSymptomCode(query) {
				if (query) {
					let params = {
						//objectCategory: "SYMPTOMCODE",
						cdo: "SYMPTOMCODE",
						name: query
					};
					this.http.get(this.apiUrl.Getcode, params).then(res => {
						if (res.Result == 1) {
							this.SymptomCodes = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
		GetCauseCode(query) {
				if (query) {
					let params = {
						//objectCategory: "CAUSECODE",
						cdo: "CAUSECODE",
						name: query
					};
					this.http.get(this.apiUrl.Getcode, params).then(res => {
						if (res.Result == 1) {
							this.CauseCodes = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
					 getclassname(query) {
				if (query) {
					let params = {
						//objectCategory: "RESOURCE",
						//cdo: "RESOURCE",
						classname: query
					};
					this.http.get(this.apiUrl.Getclassname, params).then(res => {
						if (res.Result == 1) {
							this.classnames = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
		getResource(query) {
				if (query) {
					let params = {
						objectCategory: "RESOURCE",
						cdo: "RESOURCE",
						name: query
					};
					this.http.get(this.apiUrl.GetNameObject, params).then(res => {
						if (res.Result == 1) {
							this.Resources = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			reset() {				
				this.Workcentername = '',
				this.searchPlanDate = null,
				this.Info_memo= '',
                this.infoContainerQty='',
				this.AcknowedgeTableData = [],
				this.InprogressTableData = [],
				this.$refs.AcknowedgeTable.rowData = [];
				this.$refs.AcknowedgeTable.paginations.total = 0;
				this.$refs.InprogressTable.rowData = [];
				this.$refs.InprogressTable.paginations.total = 0;
			},
			GetEquipmentAcknowledge() {

                this.$refs.AcknowedgeTable.load(null, true);
				this.$refs.InprogressTable.load(null, true);
			},
            AckLoadBefore(params, callBack) {
				params["Resource"] = this.Resource;
                params["classnamename"] = this.classname;
                params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
                params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
                callBack(true)
            },
            AckLoadAfter(rows, callBack, result) {
                if (result.Result == 1) {
                    this.AcknowedgeTableData = result.Data.tableData;
                    this.$refs.AcknowedgeTable.rowData = result.Data.tableData;
                    this.$refs.AcknowedgeTable.paginations.total = result.Data.total;
                }
                else {
                    this.$message.error(result.Message);
                }
                callBack(false);
            },
			AckRowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行')
				this.$refs.AcknowedgeTable.$refs.table.toggleRowSelection(row);
				//this.infoMfgOrder = row.MfgOrder;
				//this.infoMfgOrderQty = row.Qty;
                this.$refs.InprogressTable.load(null, true);
			},
            InprogressTableLoadBefore(params, callBack) {
				params["Resource"] = this.Resource;
				params["classnamename"] = this.classname;
                params["startTime"] = this.searchPlanDate != null ? this.searchPlanDate[0] : '';
                params["endTime"] = this.searchPlanDate != null ? this.searchPlanDate[1] : '';
			    const rows = this.$refs.AcknowedgeTable.getSelected();
                callBack(true)
            },
            InprogressTableLoadAfter(rows, callBack, result) {
                if (result.Result == 1) {
                    this.InprogressTableData = result.Data.tableData;
                    this.$refs.InprogressTable.rowData = result.Data.tableData;
                    this.$refs.InprogressTable.paginations.total = result.Data.total;
                }
                else {
                    this.$message.error(result.Message);
                }
                callBack(false);
            },
			Acknowledgesubmit() {
				const rows = this.$refs.AcknowedgeTable.getSelected();
				if (!rows.length) {
				    this.$message.error('请选中需要确认的维修单。')
				    return;
				}
				let params = {
					User: this.userInfo.userName,
					Password: this.userInfo.userPwd,
					UserTrueName: this.userInfo.userTrueName,
					//PrintQueue: this.infoPrinter,
					OrderName: rows[0].OrderName,
					Resource: rows[0].ResourceName,
					stagename: rows[0].stagename,
					AutoClockOn: "True" //固定自动打开clock计时
				};
				this.http.post(this.apiUrl.EquipmentRepairAcknowledgeMaint, params, true).then(res => {
					if (res.Result == 1) {
						this.$refs.InprogressTable.load(null, true);
						this.reset();
						this.$message.success(res.Message);
					} else {
						this.$refs.InprogressTable.load(null, true);
						this.$message.error(res.Message);
					}
				});	
			},		
			Inprogresssubmit() {
				const rows = this.$refs.InprogressTable.getSelected();
				if (!rows.length) {
				    this.$message.error('请选中需要执行的维修单。')
				    return;
				}
				if (!this.Info_memo) {
				    this.$message.error('处理备注是必填项！')
				    return;
				}
				let params = {
					User: this.userInfo.userName,
					Password: this.userInfo.userPwd,
					UserTrueName: this.userInfo.userTrueName,
					//PrintQueue: this.infoPrinter,
					OrderName: rows[0].OrderName,
					Resource: rows[0].ResourceName,
					stagename: rows[0].stagename,
					Notes: this.Info_memo,
					CauseCode: this.CauseCode,
					RepairCode: this.RepairCode,
					SymptomCode: this.SymptomCode,

				};
				this.http.post(this.apiUrl.EquipmentRepairInprogressMaint, params, true).then(res => {
					if (res.Result == 1) {
						this.$refs.InprogressTable.load(null, true);
						this.reset();
						this.$message.success(res.Message);
					} else {
						this.$refs.InprogressTable.load(null, true);
						this.$message.error(res.Message);
						this.show = false;
					}
				});	
			},		
			submitRow() {
            //const rows = this.$refs.table.getSelected();
			//const rows = this.$refs.AcknowedgeTable.getSelected();
			const rows = this.$refs.InprogressTable.getSelected();
            if (rows.length == 0) {
                this.$message.error('请选分配的维修单')
                return;
            }
            if (!this.Name) {
                this.$message.error('请选择分配人员。')
                return;
            }
            // 只筛选InventoryQty不为0的行
            const validRows = rows.filter(row => row.OrderName && row.ResourceName);

            let params = {
                User: this.userInfo.userName,
                Password: this.userInfo.userPwd,
                Name: this.Name,
                requestData: validRows
            };
            this.http.post(this.apiUrl.EquipmentRepairAssignMaint, params, true).then(res => {
                if (res.Result == 1) {
                    this.reset();
					this.letClockOn();
                    this.$message.success("分配成功");
                } else {
                    this.$message.error(res.Message);
                }
            });
            },
			letClockOn(){
				let params = {
					User: this.userInfo.userName,
					Password: this.userInfo.userPwd,
					UserTrueName: this.userInfo.userTrueName,
					//PrintQueue: this.infoPrinter,
					Resource: this.Resource,
					workcentername: this.Workcentername,
					AutoClockOn: "True" //固定自动打开clock计时q
				};
			},
            addRow() {
				// this.tableData.push({ OrderNo: "D2022040600009" })
				//this.reset();
				this.show = true;
			},
            InprogressTableRowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行')
				this.$refs.InprogressTable.$refs.table.toggleRowSelection(row);
			},
			close() {
				this.show = false;
			},

		}
	}
</script>
<style lang="less" scoped>
	.table-item-header {
		display: flex;
		align-items: center;
		padding: 6px;

		.table-item-border {
			height: 15px;
			background: rgb(33, 150, 243);
			width: 5px;
			border-radius: 10px;
			position: relative;
			margin-right: 5px;
		}

		.table-item-text {
			font-weight: bolder;
		}

		.table-item-buttons {
			flex: 1;
			text-align: right;
            margin-top: 20px;
		}

		.small-text {
			font-size: 12px;
			color: #2196F3;
			margin-left: 10px;
			position: relative;
			top: 2px;
		}
	}
</style>