<template>
  <div class="opc-lot-scrap">
    <VolHeader :title="'批次（数量）报废'"></VolHeader>
    <div class="poc-scrap-lot" style="text-align: left;width: 100%; margin-top: 18px;">
      <span style="color: black; font-size: small;"><span style="color: red; font-size: small;">*
        </span>请先扫描批次:</span>&nbsp;
      <el-input type="text" style="width: auto;" v-model="headerFields.selectLot" placeholder="扫描批次" required
        @change="onselectLot" />
    </div>
    <VolForm ref="form" :loadKey="true" :formFields="headerFields" :formRules="headerRules">
      <div style="text-align: end; margin-top: 0px; width: 100%">
        <div style="margin-right: 20px; margin-top: -39px">
          <el-button type="danger" icon="Delete" @click="onScrap" plain>确定报废</el-button>
        </div>
      </div>
    </VolForm>
  </div>
  <div class="poc-scrap-query">
    <VolHeader icon="el-icon-s-operation" :title="'批次报废详情'"></VolHeader>
    <VolForm ref="form1" :loadKey="true" :formFields="headerFields1" :formRules="headerRules1">
      <div class="poc-scrap-table" style="text-align: end; margin-top: 0px;width: 100%">
        <div style="margin-right: 20px; margin-top: -39px;">
          <el-button type="primary" icon="Search" @click="onQuery" plain>查询</el-button>
          <el-button type="primary" icon="Download" @click="outputRow" plain>导出</el-button>
        </div>
      </div>
    </VolForm>
    <vol-table ref="table" row-key="Id" index :column-index="true" :reserveSelection="true" :loadKey="true"
      :columns="columns" :ck="false" :max-height="380"
      :row-style="rowStyle" :url="ApiUrl.GetScrapHistory" :defaultLoadPage="false" @loadBefore="loadBefore"
      @loadAfter="loadAfter"></vol-table>
  </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from '@/components/basic/VolForm.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import Excel from '@/uitils/xlsl.js';
import {
  mapState
} from 'vuex';

export default {
  components: {
    VolHeader,
    VolForm,
    'vol-table': VolTable
  },
  computed: {
    ...mapState({
      //获取当前用户的信息
      userInfo: state => state.userInfo,
      //获取当前用户的权限
      permission: state => state.permission,
    })
  },
  //初始化页面
  created() {
  },
  data() {
    return {
      ApiUrl: {
        GetRevisionObject: "/api/query/GetRevisionObject",
        GetNameObject: "/api/query/GetNameObject",
        GetScrapReason: '/api/Query/GetScrapReason', //获取报废原因
        GetContainerInfo: '/api/Query/GetContainerInfo', //获取批次信息
        GetScrapHistory: '/api/Query/GetScrapHistory', //获取批次报废详情
        LotScarp: 'api/CDO/ScrapBonus', //报废和取消报废
      },
      headerFields: {
        selectLot: '',
        currentLot: '',
        currentStatus: '',
        currentSpec: '',
        productNo: '',
        productDesc: '',
        mfgQty: '',
        mfgOrder: '',
        lotQty: '',
        scrapQty: '',
        lossReason: '',
      },
      headerRules: [
        [{
          title: this.$ts('当前批次'),
          placeholder: this.$ts(''),
          field: "currentLot",
          type: "text",
          readonly: true,
          colSize: 2,
        },
        {
          title: this.$ts('当前状态'),
          placeholder: this.$ts(''),
          field: "currentStatus",
          type: "text",
          readonly: true,
          colSize: 2,
        },
        {
          title: this.$ts('当前工序'),
          placeholder: this.$ts(''),
          field: "currentSpec",
          type: "text",
          readonly: true,
          colSize: 2,
        }
        ],
        [{
          title: this.$ts('产品编码'),
          placeholder: this.$ts(''),
          field: "productNo",
          type: "text",
          readonly: true,
          colSize: 2,
        },
        {
          title: this.$ts('产品描述'),
          placeholder: this.$ts(''),
          field: "productDesc",
          type: "text",
          readonly: true,
          colSize: 2,
        },
        {
          title: this.$ts('工单数量'),
          placeholder: this.$ts(''),
          field: "mfgQty",
          type: "text",
          readonly: true,
          colSize: 2,

        },
        {
          title: this.$ts('生产工单'),
          placeholder: this.$ts(''),
          field: "mfgOrder",
          type: "text",
          readonly: true,
          colSize: 2,
        }
        ],
        [{
          title: this.$ts('批次数量'),
          placeholder: this.$ts(''),
          field: "lotQty",
          type: "text",
          readonly: true,
          colSize: 2,
        },
        {
          title: this.$ts('报废数量'),
          placeholder: this.$ts(''),
          field: "scrapQty",
          type: "text",
          colSize: 2,
          required: true,
        },
        {
          title: this.$ts('报废原因'),
          placeholder: this.$ts(''),
          field: "lossReason",
          type: "select",
          colSize: 2,
          required: true,
          data: [],
        }
        ]
      ],
      BonusReasons: [],
      columns: [
        { field: 'MfgOrderName', title: '生产工单', type: 'string', width: 130, align: 'center' },
        { field: 'ProductName', title: '产品编码', type: 'string', width: 130, align: 'center' },
        { field: 'ProductDesc', title: '产品描述', type: 'string', width: 180, align: 'center' },
        { field: 'ContainerName', title: '批次码', type: 'string', width: 210, align: 'center' },
        { field: 'Qty', title: '批次数量', type: 'string', width: 90, align: 'center' },
        { field: 'ScrapQty', title: '报废数量', type: 'string', width: 90, align: 'center' },
        { field: 'Uom', title: '单位（UOM）', type: 'string', width: 105, align: 'center' },
        { field: 'ScrapReason', title: '报废原因', type: 'string', width: 130, align: 'center' },
        { field: 'ScrapSpec', title: '报废工序', type: 'string', width: 130, align: 'center' },
        { field: 'CurrentStatus', title: '批次当前状态', type: 'string', width: 130, align: 'center' },
        { field: 'WorkFlow', title: '工作流程', type: 'string', width: 130, align: 'center' },
        { field: 'User', title: '报废人员', type: 'string', width: 130, align: 'center' },
        { field: 'DateTime', title: '报废时间', type: 'string', width: 180, align: 'center' },
        { field: 'DetailsID', title: '', type: 'string', width: 130, align: 'center', hidden: true },
      ],
      tableData: [],
      headerFields1: {
        // productNo: '',
        mfgOrder: '',
        bonusReason: '',
        StartTime: null,
        EndTime: null,
        Container:null
      },
      headerRules1: [[
       
        {
          title: this.$ts('生产工单'),
          placeholder: this.$ts(''),
          field: "mfgOrder",
          type: "select",
          colSize: 2,
          data: [],
          url:"/api/query/GetNameObject",
          params: {
            cdo: "mfgOrder",
          },
    
        }, {
          title: this.$ts('开始时间'),
          placeholder: this.$ts(''),
          field: "StartTime",
          type: "datetime",
          colSize: 2,
        },
        {
          title: this.$ts('结束时间'),
          placeholder: this.$ts(''),
          field: "EndTime",
          type: "datetime",
          colSize: 2,
        },
        {
          title: this.$ts('批次号'),
          placeholder: this.$ts(''),
          field: "Container",
          type: "input",
          colSize: 2,
        },
        ],
      ],
    }
  },
  methods: {

    //扫描批次
    onselectLot(val) {
      let param = {
        Container: val,
      }
      this.reset();
      this.http.get(this.ApiUrl.GetContainerInfo, param).then(async (res) => {
        if (res.Result == 1) {
          this.headerFields.currentLot = val;
          this.headerFields.currentStatus = res.Data.status;
          this.headerFields.mfgQty = res.Data.mfgOrderQty;
          this.headerFields.productNo = res.Data.productName;
          this.headerFields.productDesc = res.Data.productDesc;
          this.headerFields.mfgOrder = res.Data.mfgOrderName;
          this.headerFields.currentSpec = res.Data.currentStep;
          this.headerFields.lotQty = res.Data.qty;
          this.headerRules[2][2].data = await this.getReason(val, "LossReason");
          // this.headerRules1[1][0].data = this.getReason(val,"BonusReason");
        } else {
          this.$message.error(res.Message);
        }
      })
    },
    reset() {
      this.headerFields.currentLot = null;
      this.$refs.table.rowData = [];
      this.$refs.table.paginations.total = 0;
      this.$refs.form.reset();
    },
    
    getProducts(val) {
      let param = {
        cdo: "Product",
        name:val,
      }
      this.http.get(this.ApiUrl.GetRevisionObject, param).then((res) => {
        if (res.Result == 1) {
          this.headerRules1[0][0].data = res.Data.map(item=>{
            return{key:item.Name+':'+item.Version,value:item.Name+':'+item.Version,label: item.Name + ':' + item.Version}
          })
        } else {
          this.$Message.error(res.Message);
        }
      }).catch((err) => {
        this.$Message.error(err);
      })
    },
    //获取NameObject
    getmfgOrder(val) {
      let data = {
        cdo: "mfgOrder",
        name:data
      }
      let dataArr = []
      this.http.get(this.ApiUrl.GetNameObject, data).then(res => {
        if (res.Result == 1) {
          res.Data.forEach(item => {
            dataArr.push({
              key: item.Name,
              label: item.Name,
              value: item.Name
            })
          })
          this.headerRules1[0][1].data = dataArr
        } else {
          this.$message.error(res.Message);
        }
      });
    },
    onQuery() {
      let param = {
        Container:this.headerFields1.Container,
        MfgOrder: { Name: this.headerFields1.mfgOrder },
        StarTime: this.headerFields1.StartTime,
        EndTime: this.headerFields1.EndTime,
        PageSize:this.$refs.table.paginations.size,
      }
      this.http.post(this.ApiUrl.GetScrapHistory, param).then(res => {
        if (res.Result == 1) {
          this.tableData = res.Data.tableData;
          this.$refs.table.rowData = res.Data.tableData;
          this.$refs.table.paginations.total = res.Data.total;
        } else {
          this.$Message.error(res.Message);
        }
      });
    },
    onReset() {
      this.headerFields.currentLot = null;
      this.$refs.table.rowData = [];
      this.$refs.table.paginations.total = 0;
      this.$refs.form.reset();
    },
    onScrap() {
      if (this.headerFields.currentLot == null || this.headerFields.currentLot == "" || this.headerFields.scrapQty == null || this.headerFields.scrapQty == ""
        || this.headerFields.lossReason == null || this.headerFields.scrapQty == "0" || this.headerFields.lossReason == "") {
        this.$Message.warning("请填写完整信息");
        return;
      }
      let param = {
        Container: this.headerFields.currentLot,
        ServiceDetails: [{ CDOTypeName: "LossDetails", EnteredQty: this.headerFields.scrapQty, ReasonCode: { Name: this.headerFields.lossReason }, RecordAllQty: false }],
      }
      this.http.post(this.ApiUrl.LotScarp, param).then(res => {
        if (res.Result == 1) {
          this.$Message.success(res.Message);
          this.reset();
        } else {
          this.$Message.error(res.Message);
        }
      })
    },
    onBonus() {
      let row = this.$refs.table.getSelected();
      if (!row.length) {
        this.$Message.warning("请选择数据");
        return;
      }
      let param = {
        Container: row[0].ContainerName,
        ServiceDetails: [{ CDOTypeName: "BonusDetails", EnteredQty: row[0].ScrapQty, ReasonCode: { Name: this.headerFields1.bonusReason }, RecordAllQty: false, YP_LossDetails: row[0].DetailsID }],
      }
      this.http.post(this.ApiUrl.LotScarp, param).then(res => {
        if (res.Result == 1) {
          this.$Message.success(res.Message);
          this.reset();
        } else {
          this.$Message.error(res.Message);
        }
      })
    },
    async getReason(val, typename) {
      //获取报废原因
      let dataArry = [];
      let param = {
        ContainerName: val,
        TypeName: typename
      }
      await this.http.get(this.ApiUrl.GetScrapReason, param).then((res) => {
        if (res.Result == 1) {
          res.Data.forEach(item => {
            dataArry.push({
              key: item.Name,
              label: item.Name,
              value: item.Name
            })
          });
        } else { this.$message.error(res.Message); }
      });
      return dataArry;
    },
    outputRow() {
      // this.tableData.splice(0);
      //导出
      let tableData = this.$refs.table.rowData
      let sortData = this.columns
      let exportData = this.handleTableSortData(tableData, sortData)
      Excel.exportExcel(exportData, "报废历史记录" + '-' + this.base.getDate());
    },
    handleTableSortData(tableData, sortData) {
      let newArray = [];
      tableData.forEach(data => {
        let newItem = {};
        sortData.forEach(field => {
          if (data.hasOwnProperty(field.field)) {
            newItem[field.title] = data[field.field];
          }
        });
        newArray.push(newItem);
      });
      return newArray;
    },
    loadBefore(params, callBack) {
      let param = {
        MfgOrder: { Name: this.headerFields1.mfgOrder },
        // Product: { Name: this.headerFields1.productNo.split(':')[0], Version: this.headerFields1.productNo.split(':')[1] },
        StarTime: this.headerFields1.StartTime,
        EndTime: this.headerFields1.EndTime,
        PageSize: this.$refs.table.paginations.size,
        PageCount: this.$refs.table.paginations.page
      }
      params = Object.assign(params, param);
      callBack(true);
    },
    loadAfter(rows, callBack, res) {
      if (res.Result == 1) {
        this.columns = res.Data.colums;
        this.tableData = res.Data.tableData;
        this.$refs.table.rowData = res.Data.tableData;
        this.$refs.table.paginations.total = res.Data.total;
      } else {
        this.$Message.error(res.Message);
      }
      callBack(false);
    }

  },
}
</script>