<template>
    <!-- 上料区域 -->
    <div class="table-item-header">
        <div class="table-item-border"></div> <span class="table-item-text">设备上料</span>
    </div>
    <div style="display: flex;margin-top: 5px;">
        <!-- <div style="margin-left: 10px; ">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>工序</span>
            </label>
            <div style="margin-top: 5px;">
                <el-select v-model="spec" @change="selectedSpec" clearable filterable placeholder="请选择"
                    style="width: 200px">
                    <el-option v-for="item in specs" :key="item.Name" :label="item.Name" :value="item.Name" />
                </el-select>
            </div>
        </div> -->
        <div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>机台编号</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="resource" @change="selectedResource" clearable filterable placeholder="键入搜索" style="width: 240px"
						remote-show-suffix :remote="true" :remote-method="getResource">
						<el-option v-for="item in resources" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
        <div style="margin-left: 10px;">
            <label style="width: 200px; margin-left: 5px; font-size: 16px;">
                <span>物料批次</span>
            </label>
            <div style="margin-top: 5px;">
                <el-input ref="inputContainer" style="width: 200px;" @change="changeContainer" v-model="container" clearable
                    placeholder="请输入"></el-input>
            </div>
        </div>
    </div>

    <el-divider />
    <!-- 已上料区域 -->
    <div class="table-item">
        <div class="table-item-header">
            <div class="table-item-border"></div> <span class="table-item-text">上料列表</span>
            <div class="table-item-buttons">
                <div>
                    <el-button type="success" icon="Plus" @click="addRow" plain>上料</el-button>
                    <el-button type="danger" icon="Delete" @click="delRow" plain>下料</el-button>
                </div>
            </div>
        </div>
        <vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="380"
            :pagination-hide="false" :load-key="true" :column-index="true" :ck="true"></vol-table>
    </div>
</template>
<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from "@/components/basic/VolForm.vue";
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
    components: {
        'vol-table': VolTable,
        VolForm,
    },
    data() {
        return {
            specs: null,
            resources: null,
            container: null,
            spec: null,
            resource: null,

            columns: [
                { field: 'CDOID', title: 'CDOID', type: 'string', width: 0, hidden: true, align: 'center' },
                { field: 'Container', title: '物料批次', type: 'string', width: 130, align: 'center' },
                { field: 'Qty', title: '批次数量', type: 'string', width: 80, align: 'center' },
				{ field: 'RealQty', title: '剩余实物数量', width: 100, align: 'center',edit: { type: "string",min: 0, max: (row) => row.Qty } },
                { field: 'Product', title: '物料编号', type: 'string', width: 100, align: 'center' },
                { field: 'Description', title: '物料描述', type: 'string', width: 100, align: 'center' },
                { field: 'ProductType', title: '物料类型', type: 'string', width: 100, align: 'center' },
                { field: 'Vendor', title: '供应商', type: 'string', width: 80, align: 'center' },
                { field: 'W_MfgOrder', title: '工单', type: 'string', width: 100, align: 'center' },
                // { field: 'Employee', title: '操作人', type: 'string', width: 130, align: 'center' },
                // { field: 'ReturnTime', title: '操作时间', type: 'datetime', width: 150, align: 'center' },
                // { field: 'Comments', title: '备注', type: 'string', width: 130, align: 'center' },
            ],
            tableData: [],

            //接口地址
            apiUrl: {
                getRevisionObject: "/api/query/GetRevisionObject",
                getNameObject: "/api/query/GetNameObject",
                getResources: "/api/query/GetResourceBySpec",
                getResourceComponent: "/api/query/getResourceComponent",
                resourceComponentSetup: '/api/CDO/resourceComponentSetup',
            },
        }
    },
    created() {
        // this.getSpecs();
        // this.getResources();
    },
    computed: {
        ...mapState({
            //获取当前用户的信息
            userInfo: state => state.userInfo,
            //获取当前用户的权限
            permission: state => state.permission,
        })
    },
    methods: {
        getResource(query) {
            if (query) {
                let params = {
                    objectCategory: "RESOURCE",
                    cdo: "Resource",
                    name: query
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.resources = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }
        },
        selectedResource(){
            if(this.resource == '' || this.resource == null){
                this.tableData = null;
                return;
            };
            let params = {
                resource: this.resource
            };
            this.http.get(this.apiUrl.getResourceComponent, params).then(res => {
                if (res.Result == 1) {
                    this.tableData = res.Data;
                    this.$refs.table.rowData = res.Data.tableData;
                    this.$refs.table.paginations.total = res.Data.length;
                    this.$refs.inputContainer.focus();
                } else {
                    this.$message.error(res.Message);
                }
            });
        },
        rowClick({
            row,
            column,
            index
        }) {
            // this.$message.error('点击了第['+(row.elementIndex+1)+']行');
            this.$refs.table.$refs.table.toggleRowSelection(row);
        },
        changeContainer(){
            this.addRow();
        },
        addRow() {
            if (this.resource == null || this.resource == '') {
                this.$message.error('上料设备不能为空。')
                return;
            }
            if (this.container == null || this.container == '') {
                this.$message.error('批次/设备不能为空。')
                return;
            }
            
			this.tableData.push({ Container: this.container })
            let params = {
                User:this.userInfo.userName,
                Password:this.userInfo.userPwd,
                resource: this.resource,
                ServiceDetails: this.tableData.map(sd => ({FromContainer: sd.Container, IssueControl:2}))
            };
            this.http.post(this.apiUrl.resourceComponentSetup, params, true).then(res => {
                if (res.Result == 1) {
                    this.$message.success('提交成功');
                    this.$refs.inputContainer.select();
                    this.selectedResource();//刷新上料表
                } else {
                    this.$message.error(res.Message);
                    this.tableData.pop();
                }
            });
            // this.tableData.push({ OrderNo: "D2022040600009" })

        },
        delRow() {
            const rows = this.$refs.table.getSelected();
            if (!rows.length) {
                this.$message.error('请选中下料批次。')
                return;
            }
            if (rows.length > 0) {
                let emptyRealQtyRows = [];
                let overflowQtyRows = [];
                let negativeQtyRows = [];
                rows.forEach((data, index) => {
                    console.log(data.RealQty == null || data.RealQty == '');
                    if(data.RealQty == null || data.RealQty == ''){
                        emptyRealQtyRows.push(index + 1);
                    }
                    if(data.RealQty > data.Qty) {
                        overflowQtyRows.push(index + 1);
                    }
                    if(data.RealQty < 0) {
                        negativeQtyRows.push(index + 1);
                    }
                });
                
                if(emptyRealQtyRows.length > 0){
                    this.$message.error(`下料请输入剩余实物数量(第${emptyRealQtyRows.join('、')}行)。`)
                    return;
                }
                if(overflowQtyRows.length > 0){
                    this.$message.error(`剩余实物数量不能大于批次数量(第${overflowQtyRows.join('、')}行)。`)
                    return;
                }
                if(negativeQtyRows.length > 0){
                    this.$message.error(`剩余实物数量不能为负数(第${negativeQtyRows.join('、')}行)。`)
                    return;
                }
                //删除勾选项
                this.$refs.table.delRow();
                let params = {
                    User:this.userInfo.userName,
                    Password:this.userInfo.userPwd,
                    resource: this.resource,
                    ServiceDetails: rows.map(sd => ({ FromContainer: sd.Container })),
                    OutToMaterialDetails: rows.map(sd => ({FromContainer: sd.Container, RealQty: sd.Qty - sd.RealQty}))
                };
                this.http.post(this.apiUrl.resourceComponentSetup, params,true).then(res => {
                    if (res.Result == 1) {
                        this.$message.success('提交成功');
                        this.selectedResource();//刷新上料表
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            }

        }
    }
}
</script>
<style lang="less" scoped>
.table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
        height: 15px;
        background: rgb(33, 150, 243);
        width: 5px;
        border-radius: 10px;
        position: relative;
        margin-right: 5px;
    }

    .table-item-text {
        font-weight: bolder;
    }

    .table-item-buttons {
        flex: 1;
        text-align: right;
    }

    .small-text {
        font-size: 12px;
        color: #2196F3;
        margin-left: 10px;
        position: relative;
        top: 2px;
    }
}
</style>