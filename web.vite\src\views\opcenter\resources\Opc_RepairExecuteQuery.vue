<template>
  <div class="container">

    <div class="form-content">
      <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
        <VolHeader :title="this.$ts(title)" text="text" icon="el-icon-s-grid"></VolHeader>
        <div style="text-align: end; margin-top: 0px;">
          <el-button type="primary" plain @click="getForm">{{ this.$ts('Search') }}</el-button>
          <el-button type="success" plain @click="reset">{{ this.$ts('Reset') }}</el-button>
          <!-- <el-button type="default" style="padding: 0px 10px" size="small" :plain="true" v-if="showCustom"
            @click="showCustomModel">
            <i class="el-icon-s-grid"></i>
          </el-button> -->
        </div>
      </VolForm>
      <CustomModel ref="CustomModel" :columns="columns" :tableName="tableName"></CustomModel>
    </div>

    <div class="table-item">
      <div class="table-item-header">
        <div class="table-item-border"></div> <span class="table-item-text">{{ this.$ts(tableTitleOne) }}</span>
        <div class="table-item-buttons">
          <el-button type="primary" @click="getRow" plain>{{ this.$ts('Repair Execution') }}</el-button>
        </div>
      </div>

      <vol-table @loadBefore="loadBefore" @loadAfter="loadAfter" ref="table" :url="url" index :tableData="tableData"
        :columns="columns" :max-height="500" :pagination-hide="true" :load-key="true" :column-index="true"
        @rowClick="rowClick"></vol-table>
    </div>

  </div>
  <Opc_RepairExecuteEdit ref="modalForm" @ok="modalFormOk" @handleModelOk="handleModelOk"></Opc_RepairExecuteEdit>

</template>
<script lang="jsx">
import ModelInfo from '@/components/ModelInfo/ModelInfo.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'
import VolTable from "@/components/basic/VolTable.vue";
import CustomModel from '@/components/CustomModel/CustomModel.vue';
import Opc_RepairExecuteEdit from './Opc_RepairExecuteEdit.vue'
import { useStore, mapState } from 'vuex'
export default {
  components: {
    ModelInfo,
    VolHeader,
    VolForm,
    CustomModel,
    'vol-table': VolTable,
    Opc_RepairExecuteEdit,
  },
  data() {
    return {
      title: this.$ts('Maint. Execution'),
      tableTitleOne: this.$ts('Repair Task List'),
      showCustom: true,
      tableName: 'RepairExecute',
      formFields: {
        UserName: null,
        YP_EquipIMRTech: null,
      },
      formRules: [
        // [
        //   {
        //     dataKey: "employee",
        //     data: [],
        //     title: this.$ts('technician'),
        //     placeholder: this.$ts('technician'),
        //     filter: true,
        //     required: false,
        //     field: "YP_EquipIMRTech",
        //     type: "select",
        //     colSize: 3,
        //   },
        // ],
      ],
      paginationHide: true,
      url: "api/Query/SearchJobOrder",
      columns: [
        // 维修单号
        {
          field: 'JobOrderName', title: this.$ts('Repair Order Number'), type: 'string', width: 80,
          render: (h, { row, column, index }) => {
            return (<div>  <i style="color: #2196F3;cursor: pointer;">{row.JobOrderName}</i> </div>)
          }
        },
        // 设备编码
        { field: 'ResourceName', title: this.$ts('Equipment code'), type: 'string', width: 80, align: 'center' },
        // 设备描述
        { field: 'ResourceDescription', title: this.$ts('Equipment Description'), type: 'string', width: 80, align: 'center' },
        // 区域
        { field: 'YP_PhysicalLocation', title: this.$ts('District'), type: 'string', width: 80, align: 'center' },
        // 位置
        { field: 'YP_PhysicalPosition', title: this.$ts('Location'), type: 'string', width: 80, align: 'center' },
        // 成本中心
        { field: 'YP_AssetCostCenter', title: this.$ts('Cost Center'), type: 'string', width: 80, align: 'center' },
        // 故障描述
        { field: 'Description', title: this.$ts('Malfunction Description'), type: 'string', width: 80, align: 'center' },
        // 附件
        { field: 'YP_RepairingAttach', title: this.$ts('Attachment'), type: 'img', width: 90, align: 'center' },
        // 技术员
        { field: 'YP_RepairingApplicant', title: this.$ts('Applicant'), type: 'string', width: 80, align: 'center' },
        // 申请时间
        { field: 'YP_TaskStartTime', title: this.$ts('Application Time'), type: 'string', width: 80, align: 'center' },
        // 申请人
        { field: 'YP_RepairingApplicant', title: this.$ts('Applicant'), type: 'string', width: 80, align: 'center' },
        //维修单状态
        { field: 'YP_TaskStatus', title: this.$ts('Repair Order Status'), type: 'string', width: 80, align: 'center' },
      ],
      tableData: [],
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.userInfo,
    }),
  },
  mounted() {

  },
  methods: {

    modalFormOk() {
      this.getForm(); 
    },

    showCustomModel() {
      this.$refs.CustomModel.showCustomModel()
    },

    // 获取表单数据
    getForm() {
      this.$refs.form.validate(() => {
        // let params = { YP_EquipIMRTech: this.formFields.YP_EquipIMRTech }
        // this.formFields.UserName = this.userInfo.userName
        // this.formFields.YP_EquipIMRTech = this.userInfo.userName
        // let params = this.formFields
        this.$refs.table.load(null, true);
      })
    },

    reset() {
      this.$refs.form.reset(this.formFields);
      this.$refs.table.load(null, true)
    },

    getRow() {
      const rows = this.$refs.table.getSelected();
      // console.log(rows, 'rows');
      if (!rows.length) {
        this.$message.error(this.$ts('Please select the row'))
        return;
      } else {
        if (rows.length > 1) {
          this.$message.warning(this.$ts('Only one row can be selected'))
        } else {
           
          console.log(this.$refs.modalForm.edit(rows),'r2eqweqwerqwerqwerwqer');
        }
      }
    },
    handleModelOk(selVal)
    {
      this.$refs.table.load(null, true)
    },
    rowClick({ row, column, event }) {
      // console.log(row, column, event, 'AAA');
      //table点击行时同时选中当前行
      this.$refs.table.$refs.table.toggleRowSelection(row);
    },

    //设置查询条件参数
    loadBefore(params, callBack) {
      params.UserName = this.userInfo.userName
      params.YP_EquipIMRTech = this.userInfo.userName
      params.ServiceName = "TechnicianPost"
      callBack(true)
    },
    //查询后方法
    loadAfter(rows, callBack, result) {
      callBack(true)
    },


  }
};
</script>
<style lang="less" scoped>
.container {
  padding: 10px;
  background: #F3F7FC;

  .form-content {
    border-radius: 5px;
    padding: 10px 0;
    background: #fff;
  }

  .table-item-header {
    display: flex;
    align-items: center;
    padding: 6px;

    .table-item-border {
      height: 15px;
      background: rgb(33, 150, 243);
      width: 5px;
      border-radius: 10px;
      position: relative;
      margin-right: 5px;
    }

    .table-item-text {
      font-weight: bolder;
    }

    .table-item-buttons {
      flex: 1;
      text-align: right;
    }

    .small-text {
      font-size: 12px;
      color: #2196F3;
      margin-left: 10px;
      position: relative;
      top: 2px;
    }
  }
}
</style>