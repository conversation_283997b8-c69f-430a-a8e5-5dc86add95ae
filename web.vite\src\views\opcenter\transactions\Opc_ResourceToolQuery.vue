<template>
    <div class="order-task">
      <div class="form-content">
        <VolHeader title="模具与设备绑定/解绑查询"></VolHeader>
        <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
          <div style="text-align: end; margin-top: 0px; width: 100%">
            <div style="margin-right: 20px; margin-top: -39px">
              <el-button type="primary" plain @click="search">查询</el-button>
              <el-button type="success" plain @click="outputRow">导出</el-button>
            </div>
          </div>
        </VolForm>
      </div>
      <div class="table-item">
        <vol-table
          ref="table1" row-key="Id" index :ck="false" :columns="columns"
          :tableData="tableData"
          @rowClick="rowClick"
          :column-index="true"
          :reserveSelection="true"
          :loadKey="true"
          :pagination-hide="false" 
          @loadBefore="loadBefore"
          @loadAfter="loadAfter"
          :defaultLoadPage="false"
            :url="ApiUrl.GetResourceToolHistory"
            :height="500"
        ></vol-table>
      </div>
    </div>
  </template>
  
  <script lang="jsx">
      import VolTable from "@/components/basic/VolTable.vue";
      import VolForm from '@/components/basic/VolForm.vue';
      import VolHeader from '@/components/basic/VolHeader.vue';
      import VolBox from '@/components/basic/VolBox.vue';
      import { mapState } from 'vuex';
      import Common from "@/uitils/common.js";
      import axios from 'axios'; 
      import Excel from '@/uitils/xlsl.js';
      export default {
          components: {
              VolHeader,
              VolForm,
              'vol-table': VolTable,
              'vol-box': VolBox
          },
          computed: {
              ...mapState({
                  //获取当前用户的信息
                  userInfo: state => state.userInfo,
                  //获取当前用户的权限
                  permission: state => state.permission,
              })
          },
          //初始化页面
          created() {
              this.GetResource();
              this.GetTool();
          },
  
          data() {
              return {
                  ApiUrl: {
                      GetRevisionObject: "/api/query/GetRevisionObject",
                      GetNameObject: "/api/query/GetNameObject",
                      GetCurrentShif: '/api/Query/GetCurrentShift', //获取当前班次
                      GetResourceByCategory: 'api/Query/GetResourceByCategory', //根据类型获取设备/模具/载具
                      GetResourceToolHistory: 'api/Query/GetResourceToolHistory', //获取设备模具绑定解绑记录
                  },
                  formFields: {
                      resource: '',
                      tool: '',
                      StartTime:null,
                      EndTime:null,
                  },
                  formRules: [
                      [
                          {
                              data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                              dataKey: '', //后台下拉框对应的数据字典编号
                              title: this.$ts('设备编码'),
                              placeholder: this.$ts(''),
                              field: "resource",
                              type: "select",
                              colSize: 2,
                          },
                          {
                              data: [], //loadKey设置为true,会根据dataKey从后台的下拉框数据源中自动加载数据
                              dataKey: '', //后台下拉框对应的数据字典编号
                              title: this.$ts('模具编码'),
                              placeholder: this.$ts(''),
                              field: "tool",
                              type: "select",
                              colSize: 2,
                          },
                          {
                              title: this.$ts('开始时间'),
                              placeholder: this.$ts(''),
                              field: "StartTime",
                              type: "date",
                              colSize: 2,
                          },
                          {
                              title: this.$ts('绑定时间'),
                              placeholder: this.$ts(''),
                              field: "EndTime",
                              type: "date",
                              colSize: 2,
                          }
                      ],
                  ],
                  columns: [					
                      { field: 'WorkCenter', title: '生产车间', type: 'string', width: 130, align: 'center'},
                      { field: 'Resource', title: '设备编码', type: 'string', width: 130, align: 'center'},
                      { field: 'ResourceDesc', title: '设备名称', type: 'string', width: 180, align: 'center'},
                      { field: 'Tool', title: '模具编码', type: 'string', width: 130, align: 'center'},
                      { field: 'ToolDesc', title: '模具名称', type: 'string', width: 180, align: 'center'},
                      { field: 'Product', title: '产品编码', type: 'string', width: 130, align: 'center'},
                      { field: 'Pressure', title: '机台气压', type: 'string', width: 130, align: 'center'},
                      { field: 'OilDrip', title: '用油点滴/分钟', type: 'string', width: 130, align: 'center'},
                      { field: 'Height', title: '模具闭合高度', type: 'string', width: 130, align: 'center'},
                      { field: 'Speed', title: '冲压速度', type: 'string', width: 130, align: 'center'},
                      { field: 'IsTest', title: '是否为试模', type: 'string', width: 130, align: 'center'},
                      { field: 'Remark', title: '试模情况记录', type: 'string', width: 180, align: 'center'},
                      { field: 'TestResult', title: '试模结果', type: 'string', width: 130, align: 'center'},
                      { field: 'Shift', title: '班组', type: 'string', width: 130, align: 'center'},
                      { field: 'BindUser', title: '绑定人', type: 'string', width: 130, align: 'center'},
                      { field: 'BindTime', title: '绑定时间', type: 'string', width: 180, align: 'center'},
                      { field: 'UnBindUser', title: '解绑人', type: 'string', width: 130, align: 'center'},
                      { field: 'UnBindTime', title: '解绑时间', type: 'string', width: 180, align: 'center'},
                  ],
                  tableData: [],
              }
          },
          methods: {
              //获取设备
              async GetResource(){
                let params = {
                    category: "RESOURCE"
                  };
                  this.http.get(this.ApiUrl.GetResourceByCategory,params).then(res => {
                      if (res.Result == 1) {
                          this.formRules[0][0].data = res.Data;
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
             //获取模具
              async GetTool(){
                  let params = {
                    category: "TOOL"
                  };
                  this.http.get(this.ApiUrl.GetResourceByCategory,params).then(res => {
                      if (res.Result == 1) {
                          this.formRules[0][1].data = res.Data;
                      } else {
                          this.$message.error(res.Message);
                      }
                  });
              },
              //查询
              search(){
                    this.$refs.table1.load(null, true);
              },
              //导出
              outputRow(){
                  let tableData = this.$refs.table1.tableData
                  let sortData = this.$refs.table1.filterColumns
                  let exportData = this.handleTableSortData(tableData, sortData)
                  Excel.exportExcel(exportData, "模具与设备绑定/解绑记录查询" + '-' + this.base.getDate());
              },
              handleTableSortData(tableData, sortData) {
                  let newArray = [];
                  tableData.forEach(data => {
                      let newItem = {};
                      sortData.forEach(field => {
                          if (data.hasOwnProperty(field.field)) {
                              newItem[field.title] = data[field.field];
                          }
                      });
                      newArray.push(newItem);
                  });
                  return newArray
              },
              loadBefore(params,callBack) {
            
			let param= {
                resource: this.formFields.resource,
                      tool: this.formFields.tool,
                      startTime: this.formFields.StartTime,
                      endTime: this.formFields.EndTime,
				pageInfo:{
					PageSize: this.$refs.table1.paginations.size,
					PageCount: this.$refs.table1.paginations.page
				}
			};
		params = Object.assign(params, param)
        callBack(true)
      },
      loadAfter(rows, callBack, result) {
           if(result.Result == 1) {
            this.tableData = result.Data.tableData;
            this.$refs.table1.rowData = result.Data.tableData;
            this.$refs.table1.paginations.total = result.Data.total;
           }
           else{
            this.$message.error(result.Message);
           }
            callBack(false);
        },
          },
  
  
      }
  </script>