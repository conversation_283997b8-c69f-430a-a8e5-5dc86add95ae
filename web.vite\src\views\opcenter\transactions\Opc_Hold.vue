<template>
  <div class="opc_hold">
    <VolForm ref="form">
      <div class="form-title" style="width: 100%">
        <VolHeader :title="'批次冻结'"></VolHeader>
        <VolForm
          ref="formHeader"
          :loadKey="true"
          :formFields="headerFields"
          :formRules="headerRules"
          :formItems="headerItems"
        >
          <div style="margin-top: 0px; width: 100%">
            <div style="margin-left: 40%; margin-top: -39px">
              <el-button type="primary" icon="Search" @click="queryData" plain>查询</el-button>
            </div>
          </div>
        </VolForm>
      </div>

      <div style="text-align: left; width: 100%">
        <div class="form-table">
          <!-- <vol-table ref="table" row-key="Id" index :column-index="true" :reserveSelection="true" :loadKey="true" :columns="columns" :tableData="tableData" @rowClick="rowClick" 
                :single="false" :pagination-hide="false"
                 :beginEdit="beginEdit" 
                :max-height="380"
                :row-style = "rowStyle" 
                :url = "ApiUrl.GetContainerInfo"
                :defaultLoadPage = "false"
                ></vol-table> -->
          <vol-table
            ref="table"
            index
            :column-index="true"
            :reserveSelection="true"
            :loadKey="true"
            :columns="columns"
            :tableData="tableData"
            :single="false"
            :pagination-hide="false"
            :beginEdit="beginEdit"
            :height="400"
            :row-style="rowStyle"
            :url="ApiUrl.GetContainerInfo"
            :defaultLoadPage="false"
            @loadBefore="loadBefore"
            @loadAfter="loadAfter"
          ></vol-table>
        </div>
        <VolForm
          ref="formDetail"
          :loadKey="true"
          :formFields="detailFields"
          :formRules="detailRules"
          :formItems="detailItems"
        >
          <div style="margin-top: 0px; width: 100%">
            <div style="margin-left: 20%; margin-top: -39px">
              <el-button type="primary" plain @click="submit" icon="Plus">冻结</el-button>
            </div>
          </div>
        </VolForm>
      </div>
    </VolForm>
  </div>
</template>
<script lang="jsx">
    import VolTable from "@/components/basic/VolTable.vue";
    import VolForm from '@/components/basic/VolForm.vue';
    import VolHeader from '@/components/basic/VolHeader.vue';
    import VolBox from '@/components/basic/VolBox.vue';
    import {
        mapState
    } from 'vuex';
    export default {
        components: {
            VolHeader,
            VolForm,
            'vol-table': VolTable,
            'vol-box': VolBox
        },
        computed: {
            ...mapState({
                //获取当前用户的信息
                userInfo: state => state.userInfo,
                //获取当前用户的权限
                permission: state => state.permission,
            })
        },
        //初始化页面
        created() {
            this.getmfgOrder();
            this.getReason();
        },
        data() {
            return {
                ApiUrl: {
                    GetContainerInfo: '/api/Query/GetContainerList', //获取批次list信息
                    GetNameObject: "/api/query/GetNameObject", //获取NameObject
                    AddHold: "/api/CDO/HoldRealease", //Hold
                },
                headerFields: {
                    selectContainer: '',
                    selectOrder: '',
                },
                headerRules: [
                    [{
                            title: this.$ts('扫描批次'),
                            placeholder: this.$ts(''),
                            field: "selectContainer",
                            type: "text",
                            readonly: false,
                            colSize: 2,
                        },
                        {
                            title: this.$ts('订单号'),
                            placeholder: this.$ts(''),
                            field: "selectOrder",
                            type: "select",
                            readonly: false,
                            colSize: 2,
                            data: []
                        }
                    ]
                ],
                detailFields: {
                    Reason: '',
                },
                detailRules: [
                    [{
                        title: this.$ts('冻结原因'),
                        placeholder: this.$ts(''),
                        field: "Reason",
                        type: "select",
                        readonly: false,
                        colSize: 2,
                        data: []
                    }]
                ],
                tableData: [],
                columns: [                    
                    { field: 'mfgOrderName', title: '生产工单', type: 'string', width: 130, align: 'center'},
					{ field: 'productName', title: '产品编码', type: 'string', width: 130, align: 'center'},
					{ field: 'productDesc', title: '产品名称描述', type: 'string', width: 180, align: 'center'},
                    { field: 'containerName', title: '批次码', type: 'string', width: 150, align: 'center'},
					{ field: 'qty', title: '批次数量', type: 'string', width: 90, align: 'center'},
					{ field: 'uom', title: '单位', type: 'datetime', width: 90, align: 'center'},
                    { field: 'currentStep', title: '当前工序', type: 'string', width: 150, align: 'center'},
					{ field: 'status', title: '批次状态', type: 'string', width: 120, align: 'center'},
                    { field: 'isHold', title: '是否Hold', type: 'string', width: 120, align: 'center'},
					{ field: 'resourceName', title: '设备/Line', type: 'string', width: 120, align: 'center'},
                    { field: 'inProcess',  title: '',type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'useQueue',  title: '',type: 'string', width: 120, align: 'center',hidden:true},
                    { field: 'nextStep', title: '下工序', type: 'string', width: 150, align: 'center'},
					{ field: 'workflowStepName',title: '', type: 'string', width: 150, align: 'center',hidden:true},
                    { field: 'employeeName', title: '操作人', type: 'string', width: 120, align: 'center',hidden:true},
                    { field: 'txnDatetime', title: '操作时间', type: 'string', width: 180, align: 'center',hidden:true},
                    { field: 'specRevision', title: '',type: 'string', width: 150, align: 'center',hidden:true},
                    { field: 'workflowName', title: '工作流程', type: 'string', width: 120, align: 'center'},
                    { field: 'workflowRevision', title: '', type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'productRevision', type: '', width: 180, align: 'center',hidden:true},
                    { field: 'isCollectData', title: '', type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'dataCollectionDefName', type: 'string', width: 120, align: 'center',hidden:true},
                    { field: 'dataCollectionDefRevision', type: '', width: 120, align: 'center',hidden:true},
                    { field: 'WorkflowStepId', title: '', type: 'string', width: 120, align: 'center',hidden:true},
					{ field: 'IsPassed', title: '执行是否成功',type: 'string', width: 120, align: 'center'},
                    { field: 'StatusMsg', title: '执行消息!',type: 'string', width: 150, align: 'center'},
					{ field: 'mfgOrderQty', type: 'string', width: 120, align: 'center',hidden:true},
                    { field: 'printerQueue', type: '',type: 'string',  width: 120, align: 'center',hidden:true},
                    { field: 'factoryName', title: '', type: 'string', width: 120, align: 'center',hidden:true} 
                ],
            }
        },
        methods: {
            //获取批次list信息
            queryData() {
                this.$refs.table.load(null, true);
            },
            //获取NameObject
            getmfgOrder() {
                let data = {
                    cdo: "mfgOrder"
                }
                let dataArr = []
                this.http.get(this.ApiUrl.GetNameObject, data).then(res => {
                    if (res.Result == 1) {
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        this.headerRules[0][1].data = dataArr
                    } else {
                        this.$message.error(res.Message);
                    }
                });
            },
            getReason() {
                let data = {
                    cdo: "HoldReason"
                }
                let dataArr = []
                this.http.get(this.ApiUrl.GetNameObject, data).then(res => {
                    if (res.Result == 1) {
                        res.Data.forEach(item => {
                            dataArr.push({
                                key: item.Name,
                                label: item.Name,
                                value: item.Name
                            })
                        })
                        this.detailRules[0][0].data = dataArr
                    } else {
                        this.$message.error(res.Message);
                    }
                })
            },
            submit() {
                let rows = this.$refs.table.getSelected();
                try {
                    if (rows.length != 0) {
                        this.$refs.table.loading = true;
                        let param = {
                            User: this.userInfo.userName,
                            Password: this.userInfo.userPwd,
                            ServerName: "Holds",
                            Containers: [], //Containers
                            Reason: this.detailFields.Reason,
                        }
                        let data = [];
                        rows.forEach(item => {
                            data.push(item.containerName)
                        })
                        param.Containers = data;
                        this.http.post(this.ApiUrl.AddHold, param).then(res => {
                            if (res.Result == 1) {
                                this.$message.success("提交成功");
                                res.Data.forEach(con => {
                                    rows.find(item => {
                                        if (item.containerName == con.Container) {
                                            item.StatusMsg = con.Msg
                                            item.IsPassed = true
                                        }
                                    })
                                })
                            } else {
                                this.$message.error("提交失败,详情看表格信息");
                                res.Data.forEach(con => {
                                    rows.find(item => {
                                        if (item.containerName == con.Container) {
                                            item.StatusMsg = con.Msg
                                            item.IsPassed = false
                                        }
                                    })
                                })
                            }
                        })
                    }
                    this.$refs.table.loading = false;
                } catch (e) {
                    this.$message.error(e);
                    this.$refs.table.loading = false;
                }
            },
            // rowClick({
            //     row,
            //     column,
            //     event
            // }) { //查询界面table点击行选中当前行
            //     this.$refs.table.$refs.table.toggleRowSelection(row);
            // },
            rowStyle({
                row,
                rowIndex
            }) {
                if (row.IsPassed) {
                    return 'background-color:green;font-size:15px;'
                } else {
                    return 'background-color:pink;font-size:15px;'
                }
            },
            loadBefore(params, callBack) {
                
                let param = {
                    Container: this.headerFields.selectContainer,
                    MfgOrder: {
                        Name: this.headerFields.selectOrder
                    },
                    ServiceName: "Holds",
                    PageSize: this.$refs.table.paginations.size,
                    PageCount: this.$refs.table.paginations.page
                }
                params = Object.assign(params, param);
                callBack(true);
            },
            loadAfter(rows, callBack, result) {
           if(result.Result == 1) {
            //this.columns = result.Data.colums;
            this.tableData = result.Data.tableData;
            this.$refs.table.rowData = result.Data.tableData;
            this.$refs.table.paginations.total = result.Data.total;
           }
           else{
            this.$message.error(result.Message);
           }
            callBack(false);
        }
           
        }
    }
</script>