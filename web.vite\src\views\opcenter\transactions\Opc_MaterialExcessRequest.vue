<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder" :loading="loading">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>物料料号</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMaterial" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMaterial" :loading="loading">
						<el-option v-for="item in materials" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<!-- <div style="margin-left: 10px">
				<label style="width: 100px; margin-left: 5px; font-size: 16px">
					<span>超领数量</span></label>
				<div style="margin-top: 5px">
					<el-input v-model="searchRequestQty" style="width: 100px" type="number" oninput="form.ekHour = form.ekHour
                .replace(/[^\d|\.]/g, '')
                .replace(/^00/g, '0')
                .replace(/^\./g, '0.')
						"></el-input>
				</div>
			</div> -->
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">工单物料超领</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="success" icon="Check" @click="submitRow" plain>超领</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="518"
			:pagination-hide="false" :load-key="true" :column-index="true" :single="true"
			:url="apiUrl.getMaterialRequestInfo" @loadBefore="loadBefore" @loadAfter="loadAfter"
			:defaultLoadPage="false" :ck="false"></vol-table>
	</div>
</template>
<script lang="jsx">
//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import { mapState } from 'vuex';
import Excel from '@/uitils/xlsl.js'

export default {
	components: {
		'vol-table': VolTable,
		'vol-box': VolBox
	},
	data() {
		return {
			show: false,
			columns: [
				{ field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },
				{ field: 'Department', title: '部门', type: 'string', width: 80, align: 'center' },
				{ field: 'Product', title: '产品编号', type: 'string', width: 120, align: 'center' },
				{ field: 'P_Description', title: '产品描述', type: 'string', width: 130, align: 'center' },
				{ field: 'P_Revision', hidden: true, title: '产品版本', type: 'string', width: 130, align: 'center' },
				{ field: 'Material', title: '物料编号', type: 'string', width: 120, align: 'center' },
				{ field: 'M_Description', title: '物料描述', type: 'string', width: 120, align: 'center' },
				{ field: 'M_Revision', hidden: true, title: '物料版本', type: 'string', width: 120, align: 'center' },
				{
					field: 'Reason', title: '原因', type: 'string', width: 120, align: 'center',
					bind: { key: null, data: [] }, edit: { type: "select" }
				},
				{ field: 'RequestQty', title: '领料数', width: 100, align: 'center', edit: { type: "decimal", min: 0 } },
			],
			tableData: [],
			pagination: { total: 0, size: 30, sortName: "" },

			//搜索框字段
			searchMfgOrder: null,
			searchMaterial: null,
			searchRequestQty: null,
			mfgorders: [],
			materials: [],

			//接口地址
			apiUrl: {
				getRevisionObject: "/api/query/GetRevisionObject",
				getNameObject: "/api/query/GetNameObject",
				getLossReason: '/api/query/getLossReason',
				getMaterialRequestInfo: '/api/query/GetMaterialRequestInfoV2',
				materialRequest: '/api/CDO/materialRequest',
			}

		}
	},
	created() {
		this.getReason();
	},
	computed: {
		...mapState({
			//获取当前用户的信息
			userInfo: state => state.userInfo,
			//获取当前用户的权限
			permission: state => state.permission,
		})
	},
	methods: {
		async getReason(){
			let params = {
				group: "超领原因组",
			};
			this.http.get(this.apiUrl.getLossReason, params).then(res => {
				if (res.Result == 1) {
					const reasons = res.Data.map(item => ({
						key: item.CDOName,
						value: item.CDOName
					}));
					this.columns[8].bind.data = reasons;
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		getMfgOrder(query) {
			if (query) {
				let params = {
					cdo: "mfgorder",
					name: query
				};
				this.http.get(this.apiUrl.getNameObject, params).then(res => {
					if (res.Result == 1) {
						this.mfgorders = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		getMaterial(query) {
			if (query) {
				let params = {
					cdo: "product",
					name: query
				};
				this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
					if (res.Result == 1) {
						this.materials = res.Data;
					} else {
						this.$message.error(res.Message);
					}
				});
			}
		},
		//清除数据
		reset() {
			this.searchMfgOrder = null;
			this.searchMaterial = null;
			this.tableData = [];
			this.$refs.table.rowData = [];
			this.$refs.table.paginations.total = 0;
		},
		submitRow() {
			// 过滤掉RequestQty为空或为0的数据
			const filteredData = this.tableData.filter(item =>
				item.RequestQty && Number(item.RequestQty) > 0
			);

			if (filteredData.length === 0) {
				this.$message.error('请填写有效的领料数量');
				return;
			}

			let params = {
				User: this.userInfo.userName,
				Password: this.userInfo.userPwd,
				requestData: filteredData,
				type: 'Z012'
			};
			this.http.post(this.apiUrl.materialRequest, params, true).then(res => {
				if (res.Result == 1) {
					this.reset();
					this.$message.success(res.Message);
				} else {
					this.$message.error(res.Message);
				}
			});
		},
		rowClick({
			row,
			column,
			index
		}) {
			// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
			this.$refs.table.$refs.table.toggleRowSelection(row);
		},
		queryRow() {
			if (!this.searchMfgOrder) {
				this.$message.error('请输入工单');
				return;
			}
			this.$refs.table.load(null, true);
		},
		loadBefore(params, callBack) {
			params["mfgorder"] = this.searchMfgOrder;
			params["product"] = this.searchMaterial;
			// params["qty"] = this.searchRequestQty;			
			callBack(true)
		},
		loadAfter(rows, callBack, result) {
			if (result.Result == 1) {
				//this.columns = result.Data.colums;
				result.Data.tableData.forEach(item => {
					item.RequestQty = this.searchRequestQty;
				});
				//将RequestQty = 0的数据过滤
				result.Data.tableData = result.Data.tableData.filter(item => item.RequestQty !== 0);
				this.tableData = result.Data.tableData;
				this.$refs.table.rowData = result.Data.tableData;
				this.$refs.table.paginations.total = result.Data.total;
			}
			else {
				this.$message.error(result.Message);
			}
			callBack(false);
		},
	}
}
</script>
<style lang="less" scoped>
.table-item-header {
	display: flex;
	align-items: center;
	padding: 6px;

	.table-item-border {
		height: 15px;
		background: rgb(33, 150, 243);
		width: 5px;
		border-radius: 10px;
		position: relative;
		margin-right: 5px;
	}

	// .table-item-text {
	// 	font-weight: bolder;
	// 	border-bottom: 1px solid #0c0c0c;
	// }
	.table-item-text {
		margin-top: 3px;
		padding-bottom: 6px;
		font-weight: bold;
		font-size: 15px;
		color: #484848;
		white-space: nowrap;
		border-bottom: 2px solid #676767;
		margin-bottom: -1px;
		letter-spacing: 1px;
	}

	.table-item-buttons {
		flex: 1;
		text-align: right;
	}

	.small-text {
		font-size: 12px;
		color: #2196f3;
		margin-left: 10px;
		position: relative;
		top: 2px;
	}
}
</style>