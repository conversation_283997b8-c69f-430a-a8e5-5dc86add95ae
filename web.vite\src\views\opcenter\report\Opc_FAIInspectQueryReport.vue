<template>
    <div class="form-content">
        <VolHeader title="IPOC首末件检验查询报表"></VolHeader>
        <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
            <div style="text-align: end; margin-top: 0px; width: 100%">
                <div style="margin-right: 20px; margin-top: -39px">
                    <el-button type="primary" plain @click="search">查询</el-button>
                    <el-button type="success" plain @click="outputRow">导出</el-button>
                </div>
            </div>
        </VolForm>
    </div>

    <!-- 数据执行 -->
    <div class="table-item">
        <vol-table @loadBefore="loadBefore" @loadAfter="loadAfter" ref="table" index :tableData="tableData"
            @rowClick="rowClick" :columns="columns" :height="400" :pagination-hide="false" :column-index="true"
            :single="true" :url="ApiUrl.GetFAIInspection" :defaultLoadPage="false"></vol-table>
    </div>
</template>

<script lang="jsx">
import VolTable from "@/components/basic/VolTable.vue";
import VolBox from '@/components/basic/VolBox.vue';
import VolHeader from '@/components/basic/VolHeader.vue';
import VolForm from '@/components/basic/VolForm.vue';
import Excel from '@/uitils/xlsl.js';

export default {
	components: {
		VolHeader,
		VolForm,
		'vol-table': VolTable,
		'vol-box': VolBox
	},
    data() {
        return {
            ApiUrl: {
                GetRevisionObject: '/api/Query/GetVolSelectRData', //获取带版本的建模
                GetCurrentShif: "/api/query/GetCurrentShift",
                GetNameObject: "/api/query/GetNameObject",
                GetEquipment: "/api/query/GetEquipment",
                GetFAIInspection: '/api/query/GetFAIInspection',
            },
            formFields: { Container: null, Spec: null, Resource: null, startDate: null, endDate: null },
            formRules: [
                [
                    {
                        dataKey: "",
                        data: [],
                        title: this.$ts('批次号'),//设备
                        placeholder: this.$ts('批次号'),
                        filter: true,
                        required: false,
                        field: "Container",
                        type: "input",
                        colSize: 2
                    },
                    {
                        dataKey: "",
                        data: [],
                        title: this.$ts('工序'),//设备
                        placeholder: this.$ts('工序'),
                        filter: true,
                        required: false,
                        field: "Spec",
                        type: "select",
                        colSize: 2
                    },
                    {
                        dataKey: "",
                        data: [],
                        title: this.$ts('设备'),//设备
                        placeholder: this.$ts('设备'),
                        filter: true,
                        required: false,
                        field: "Resource",
                        type: "select",
                        colSize: 2
                    },
                    {
                        title: "开始时间",
                        required: false,
                        placeholder: "",
                        field: "startDate",
                        type: "date",
                        colSize: 2,
                        min: "2021-07-01",
                        max: "",
                    },
                    {
                        title: "结束时间",
                        required: false,
                        placeholder: "",
                        field: "endDate",
                        type: "date",
                        colSize: 2,
                        min: "2021-07-01",
                        max: "",
                    },
                ],
            ],
            columns: [{ field: 'Data_Id', title: 'Data_Id', type: 'guid', width: 80, hidden: true },
            {
                field: 'Squence',
                title: '序号',
                type: 'string',
                width: 50,
                hidden: true,
                align: 'center'
            },
            {
                field: 'YP_FAIInspectionTaskName',
                title: '首件检验单号',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'FactoryName',
                title: '生产车间',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'SpecName',
                title: '工序',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'ResourceName',
                title: '设备',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'MouldNumber',
                title: '模具号',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'Container',
                title: '批次号',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'Product',
                title: '产品编码',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'P_Description',
                title: '产品描述',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'FirstInspectionCondition',
                title: '首件触发条件',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'MfgOrder',
                title: '生产工单',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'FAIInspectionStatus',
                title: '检验单状态',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'Creator',
                title: '创建人',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'YP_CreateTime',
                title: '创建时间',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'FirstInspectionResult',
                title: '检验结果',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'Inspector',
                title: '检验人',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'InspectionTime',
                title: '检验时间',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'Reviewer',
                title: '审核人',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'AuditTime',
                title: '审核时间',
                type: 'datetime',
                width: 100,
                align: 'center'
            },
            {
                field: 'AuditrRemarks', // 审核备注
                title: '审核备注',
                type: 'string',
                width: 100,
                align: 'center'
            },
            {
                field: 'FileNameList',
                title: '查看报告',
                type: 'string',
                width: 200,
                align: 'center',
                render: (h, { row, column, index }) => {
                    const files = (row.FileNameList || '').split(', ').filter(file => file.trim() !== '');
                    return (
                        <div>
                            {files.map((file, fileIndex) => (
                                <el-button
                                    //className="file-button" 
                                    key={fileIndex}
                                    type="primary"
                                    plain
                                    style={{ height: '26px', padding: '10px', display: 'block', marginBottom: '5px' }}
                                    onClick={($e) => {
                                        $e.stopPropagation();
                                        this.executeClick(file);
                                    }}
                                >
                                    {file}
                                </el-button>
                            ))}
                            {files.length === 0 && <div></div>}
                        </div>
                    );
                }
            }
            ],
            tableData: [],
        }
    },
    methods: {
        executeClick(file) {
            // 假设你有一个函数可以获取文件的下载链接  
            const getDownloadUrl = (fileName) => {
                // 这里应该是你的逻辑来获取文件的URL  
                //例如：return `https://example.com/downloads/${fileName}`;  
                return `${this.http.getDownloadUrl()}/${fileName}`;
            };


            const downloadUrl = getDownloadUrl(file);

            // 创建一个隐藏的a标签来触发下载  
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = downloadUrl;
            a.download = file; // 设置下载的文件名  

            // 添加到DOM中  
            document.body.appendChild(a);

            // 触发点击事件  
            a.click();

            // 从DOM中移除a标签（清理）  
            document.body.removeChild(a);

            // 提示用户文件已经触发下载，并建议他们手动打开  
            alert(`文件 "${file}" 已经触发下载到您的设备。下载完成后，请在您的文件系统中找到该文件并手动打开查看。`);
        },
        GetResources() {
			let params = {
				cdo: "Resource"
			}
			// 获取工单
			this.http.get(this.ApiUrl.GetEquipment, params).then(res => {
				if (res.Result == 1) {
					this.formRules[0][2].data = res.Data.map(s => ({ key: s.value, value: s.value }));
				} else {
					this.$message.error(res.Message);
				}
			})
		},
		GetSpecs() {
			let params = {
				cdo: "Spec"
			}
			// 获取规格
			this.http.get(this.ApiUrl.GetRevisionObject, params).then(res => {
				if (res.Result == 1) {
					this.formRules[0][1].data = res.Data.map(s => ({ key: s.value, value: s.value }));
				} else {
					this.$message.error(res.Message);
				}
			})
		},
        loadBefore(params, callBack) {

            let param = {
                Container: this.formFields.Container,
                spec: this.formFields.Spec,
                resource: this.formFields.Resource,
                starttime: this.formFields.startDate,
                endtime: this.formFields.endDate,
                pageInfo: {
                    PageSize: this.$refs.table.paginations.size,
                    PageCount: this.$refs.table.paginations.page
                }
            }
            params = Object.assign(params, param)
            callBack(true)
        },
        //查询后方法
        loadAfter(rows, callBack, result) {
            if (result.Result == 1) {
                // 处理数据，为每个对象添加formattedFileNameList字段  
                const processedData = result.Data.tableData.map(item => {
                    // 如果FileNameList存在且是字符串，则将其拆分为数组，然后重新组合为带有换行符的字符串  
                    if (item.FileNameList && typeof item.FileNameList === 'string') {
                        item.formattedFileNameList = item.FileNameList.split(', ').join('\n');
                    } else {
                        // 如果FileNameList不存在、为空或不是字符串，则设置为空字符串或适当的默认值  
                        item.formattedFileNameList = ''; // 或者您可以选择设置为null或其他默认值  
                    }
                    return item;
                });
                this.tableData = processedData;
                this.$refs.table.rowData = result.Data.tableData;
                this.$refs.table.paginations.total = result.Data.total;
            }
            else {
                this.$message.error(result.Message);
            }
            callBack(false);
        },
        outputRow() {
            // this.tableData.splice(0);
            //导出
            let tableData = this.$refs.table.tableData
            let sortData = this.$refs.table.filterColumns
            let exportData = this.handleTableSortData(tableData, sortData)
            Excel.exportExcel(exportData, "物料" + '-' + this.base.getDate());
        },
        handleTableSortData(tableData, sortData) {
            let newArray = [];
            tableData.forEach(data => {
                let newItem = {};
                sortData.forEach(field => {
                    if (data.hasOwnProperty(field.field)) {
                        newItem[field.title] = data[field.field];
                    }
                });
                newArray.push(newItem);
            });
            return newArray
        },
        search() {
            this.$refs.table.load(null, true);
        },
    },
    mounted: function () {
        this.GetSpecs(); //页面加载时获取工序
        this.GetResources();//页面加载时获取设备
    }
}

</script>