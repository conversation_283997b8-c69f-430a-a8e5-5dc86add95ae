<template>
	<div class="page-header">
		<!-- 搜索条件 -->
		<div style="display: flex; margin-top: 5px">
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>工单</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMfgOrder" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMfgOrder"	:loading="loading">
						<el-option v-for="item in mfgorders" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>物料料号</span></label>
				<div style="margin-top: 5px">
					<el-select v-model="searchMaterial" clearable filterable placeholder="键入搜索" style="width: 200px"
						remote-show-suffix :remote="true" :remote-method="getMaterial"	:loading="loading">
						<el-option v-for="item in materials" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
			<div style="margin-left: 10px">
				<label style="width: 200px; margin-left: 5px; font-size: 16px">
					<span>课别</span>
				</label>
				<div style="margin-top: 5px">
					<el-select v-model="searchWorkcenter" clearable filterable placeholder="请选择" style="width: 150px"
						remote-show-suffix>
						<el-option v-for="item in workcenters" :key="item.Name" :label="item.Name" :value="item.Name" />
					</el-select>
				</div>
			</div>
		</div>
	</div>
	<!-- 数据执行 -->
	<div class="table-item">
		<div class="table-item-header">
			<!-- <div class="table-item-border"></div>-->
			<span class="table-item-text">下料超领确认</span>
			<div class="table-item-buttons">
				<div>
					<el-button icon="Search" @click="queryRow" plain>查询</el-button>
					<el-button type="info" icon="Refresh" @click="reset" plain>重置</el-button>
					<el-button type="success" icon="Check" @click="submitRow" plain>提交</el-button>
				</div>
			</div>
		</div>
		<vol-table ref="table" index :tableData="tableData" @rowClick="rowClick" :columns="columns" :height="518"
			:pagination-hide="false" :load-key="true" :column-index="true" :url="apiUrl.getExcessRequestCheck"
			@loadBefore="loadBefore" @loadAfter="loadAfter" :defaultLoadPage="false" :span-method="spanMethod"></vol-table>
	</div>	
</template>
<script lang="jsx">
	//如果是自定义vue页面使用的配置，在vue页面的script后一定要加上lang="jsx"
	import VolTable from "@/components/basic/VolTable.vue";
	import VolBox from '@/components/basic/VolBox.vue';
	import { mapState } from 'vuex';
	import Excel from '@/uitils/xlsl.js'

	export default {
		components: {
			'vol-table': VolTable,
			'vol-box': VolBox
		},
		data() {
			return {
				show: false,
				columns: [
					{ field: 'CDOID',hidden: true, title: 'Id', type: 'string', width: 120, align: 'center' },
					{ field: 'CDOName', title: '超领单号', type: 'string', width: 120, align: 'center' },
					{ field: 'Indexs', hidden: true, title: 'Indexs', type: 'string', width: 120, align: 'center' },
					{ field: 'MfgOrder', title: '工单', type: 'string', width: 120, align: 'center' },		
					{ field: 'Container', title: '最小包装标签ID', type: 'string', width: 120, align: 'center' },
					{ field: 'Material', title: '物料编号', type: 'string', width: 120, align: 'center' },
					{ field: 'M_Description', title: '物料品名', type: 'string', width: 120, align: 'center' },
					{ field: 'Qty', title: '数量', type: 'string', width: 80, align: 'center' },
					{ field: 'WorkCenter', title: '课别', type: 'string', width: 100, align: 'center' },
				],
				tableData: [],
				pagination: { total: 0, size: 30, sortName: "" },

				//搜索框字段
				searchMfgOrder: null,
				searchMaterial: null,
				searchWorkcenter: null,
				workcenters: [],
				mfgorders:[],
				materials:[],

				//接口地址
				apiUrl: {
					getRevisionObject: "/api/query/GetRevisionObject",
					getNameObject: "/api/query/GetNameObject",
					getExcessRequestCheck: '/api/query/getExcessRequestCheck',
					excessRequestCheck: '/api/CDO/excessRequestCheck',
				}

			}
		},
		created() {
            this.getWorkCenter();
		},
		computed: {
			...mapState({
				//获取当前用户的信息
				userInfo: state => state.userInfo,
				//获取当前用户的权限
				permission: state => state.permission,
			})
		},
		methods: {
            async getWorkCenter() {
                let params = {
                    cdo: "WorkCenter"
                };
                this.http.get(this.apiUrl.getNameObject, params).then(res => {
                    if (res.Result == 1) {
                        this.workcenters = res.Data;
                    } else {
                        this.$message.error(res.Message);
                    }
                });
			},
			getMfgOrder(query) {
				if (query) {
					let params = {
						cdo: "mfgorder",
						name: query
					};
					this.http.get(this.apiUrl.getNameObject, params).then(res => {
						if (res.Result == 1) {
							this.mfgorders = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
			getMaterial(query) {
				if (query) {
					let params = {
						cdo: "product",
						name: query
					};
					this.http.get(this.apiUrl.getRevisionObject, params).then(res => {
						if (res.Result == 1) {
							this.materials = res.Data;
						} else {
							this.$message.error(res.Message);
						}
					});
				}
			},
            queryRow() {
				this.$refs.table.load(null, true);
			},	
			spanMethod({ row, column, rowIndex, columnIndex }, rows) {
				// 假设要合并第1列(columnIndex === 1)
				if (columnIndex === 1 || columnIndex === 2) {
					// 获取当前列的字段名
					let _field = this.columns[0].field
					if (columnIndex === 2) _field = this.columns[1].field;
					const field = _field;
					// 如果是第一行，从这一行开始统计
					if (rowIndex === 0) {
						let count = 1 // 记录相同值的数量
						// 向下查找相同值的行数
						for (let i = 1; i < rows.length; i++) {
							if (rows[i][field] === row[field]) {
								count++
							} else {
								break
							}
						}
						// 返回要合并的行数
						return { rowspan: count, colspan: 1 }
					} else {
						// 检查当前行的值是否与上一行相同
						const prevRow = rows[rowIndex - 1]
						if (prevRow && row[field] === prevRow[field]) {
							// 如果与上一行相同，则不显示
							return { rowspan: 0, colspan: 0 }
						}
						// 如果与上一行不同，需要计算向下合并的行数
						let count = 1
						for (let i = rowIndex + 1; i < rows.length; i++) {
							if (rows[i][field] === row[field]) {
								count++
							} else {
								break
							}
						}
						return { rowspan: count, colspan: 1 }
					}
				}
			},
			//清除数据
			reset() {
				this.searchWorkcenter = null;
				this.searchMfgOrder = null;
				this.searchMaterial = null;
				this.tableData = [];
				this.$refs.table.rowData = [];
				this.$refs.table.paginations.total = 0;
			}, 
            submitRow(){
                const rows = this.$refs.table.getSelected();
                if (!rows.length) {
                    this.$message.error('请选中列表中批次。')
                    return;
                }
                if (rows.length > 0) {
                    let params = {
                        User: this.userInfo.userName,
                        Password: this.userInfo.userPwd,
                        requestData: rows
                    };
                    this.http.post(this.apiUrl.excessRequestCheck, params, true).then(res => {
                        if (res.Result == 1) {
                            this.reset();
                            this.$message.success('提交成功');
                        } else {
                            this.$message.error(res.Message);
                        }
                    });
                }
            },
			rowClick({
				row,
				column,
				index
			}) {
				// this.$message.error('点击了第['+(row.elementIndex+1)+']行');
				//this.$refs.table.$refs.table.toggleRowSelection(row);
				// 获取表格的引用
				const table = this.$refs.table.$refs.table;

				// 选中当前行
				table.toggleRowSelection(row);

				// 获取第2列的字段名（假设是 CDOName）
				const columnField = this.columns[1].field;

				// 获取当前行的第2列值
				const currentValue = row[columnField];

				// 遍历表格数据，选中所有第2列值与当前行相同的行
				this.tableData.forEach(item => {
					if (item[columnField] === currentValue) {
						table.toggleRowSelection(item, true); // true 表示选中
					}
				});
			},		
			loadBefore(params, callBack) {	
				params["mfgorder"] = this.searchMfgOrder;			
				params["product"] = this.searchMaterial;			
				params["workcenter"] = this.searchWorkcenter;			
				callBack(true)
			},
			loadAfter(rows, callBack, result) {
				if (result.Result == 1) {
					//this.columns = result.Data.colums;
					this.tableData = result.Data.tableData;
					this.$refs.table.rowData = result.Data.tableData;
					this.$refs.table.paginations.total = result.Data.total;
				}
				else {
					this.$message.error(result.Message);
				}
				callBack(false);
			},
		}
	}
</script>
<style lang="less" scoped>
.table-item-header {
  display: flex;
  align-items: center;
  padding: 6px;

  .table-item-border {
    height: 15px;
    background: rgb(33, 150, 243);
    width: 5px;
    border-radius: 10px;
    position: relative;
    margin-right: 5px;
  }

  // .table-item-text {
  // 	font-weight: bolder;
  // 	border-bottom: 1px solid #0c0c0c;
  // }
  .table-item-text {
    margin-top: 3px;
    padding-bottom: 6px;
    font-weight: bold;
    font-size: 15px;
    color: #484848;
    white-space: nowrap;
    border-bottom: 2px solid #676767;
    margin-bottom: -1px;
    letter-spacing: 1px;
  }

  .table-item-buttons {
    flex: 1;
    text-align: right;
  }

  .small-text {
    font-size: 12px;
    color: #2196f3;
    margin-left: 10px;
    position: relative;
    top: 2px;
  }
}
</style>