<template>
  <vol-box :lazy="true" v-model="visible" :title="title" :width="width" :padding="5" :onModelClose="onModelClose">
    <!-- 弹出框内容 -->
    <div class="form-content" :style="style">
      <VolForm ref="form" :loadKey="true" :formFields="formFields" :formRules="formRules">
        <!-- <VolHeader :title="this.$ts(title)" :text="text" icon="el-icon-s-grid"></VolHeader> -->
        <!-- <div style="text-align: end;width: 100%; margin-top: 0px;">
          <el-button type="primary" plain @click="getForm">{{ this.$ts('查询') }}</el-button>
          <el-button type="success" plain @click="reset">{{ this.$ts('重置') }}</el-button>
        </div> -->
      </VolForm>
    </div>

    <template #footer>
      <div>
        <el-button type="primary" size="small" @click="click_confirm">{{ this.$ts('确认') }}</el-button>
        <el-button type="default" size="small" @click="closeModel">{{ this.$ts('关闭') }}</el-button>
      </div>
    </template>
  </vol-box>
</template>
<script>
import VolBox from '@/components/basic/VolBox.vue';
import VolHeader from '@/components/basic/VolHeader.vue'
import VolForm from '@/components/basic/VolForm.vue'

//这里使用的vue2语法，也可以写成vue3语法
export default {
  components: {
    'vol-box': VolBox,
    VolHeader,
    VolForm
  },
  methods: {},
  data() {
    return {
      visible: false,
      model: {},
      title: this.$ts('编辑'),
      text: this.$ts('默认'),
      width: 900,
      style: { 'max-height': '500px' },
      formFields: {
        resourceFamilyName: null,//分类代码
        description: null//描述
      },
      formRules: [
        [
          {
            title: "分类代码",
            placeholder: "分类代码",
            /* filter: true,
            required: true, //设置为必选项
            disabled: false, */
            field: "resourceFamilyName",
            type: "input",
            //colSize: 12,
          },
          {
            title: "描述",
            placeholder: "描述",
            required: true,
            field: "description",
            type: "input",
            /* min: 3,
            max: 5, */
            // colSize: 12,
          },
        ]
      ],


    };
  },
  methods: {
    click_confirm() {
      // this.$emit('confirm', ($parent) => {
      //   //如：回写编辑表单数据
      //   console.log($parent, 'this.parent')
      //   // $parent.curFormRules.resourceFamilyName = this.formFields.resourceFamilyName;

      // });
      this.$emit('confirm', this.formFields);
      this.closeModel();
    },
    add() {
      this.edit({});
    },
    edit(record) {
      this.model = Object.assign({}, record);
      this.model.resourceFamilyName = record.resourceFamilyName
      console.log(this.model, 'record')

      if (!this.model.id) {
        this.model = {};
      } else {
        this.formFields = Object.assign({}, this.model)

      }
      this.visible = true;
    },

    // 获取表单数据
    getForm() {
      this.$refs.form.validate((err) => {
        console.log(this.formFields, '表单数据');
      })
      this.search()
    },
    search() {
      //查询
      // let query = this.getSearchParameters();
      // this.$refs.table.load(query, true);
      // this.$refs.table.load(null, true)
    },
    /* reset() {
      this.$refs.form.reset(this.searchFormFields);
    }, */

    onModelClose() {
      alert('弹出框右上角点击x关闭事件')
    },
    closeModel() {
      this.model = {};
      this.visible = false;
    }
  }
};
</script>